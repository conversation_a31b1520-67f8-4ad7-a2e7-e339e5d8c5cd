= In-game Debug Console =

Online documentation available at: https://github.com/yasirkula/UnityIngameDebugConsole
E-mail: <EMAIL>

1. ABOUT
This asset helps you see debug messages (logs, warnings, errors, exceptions) runtime in a build (also assertions in editor) and execute commands using its built-in console.

2. HOW TO
You can simply place the IngameDebugConsole prefab to your scene. Hovering the cursor over its properties in the Inspector will reveal explanatory tooltips.