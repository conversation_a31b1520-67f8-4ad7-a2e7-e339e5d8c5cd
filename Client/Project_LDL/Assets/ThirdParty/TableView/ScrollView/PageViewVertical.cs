using UnityEngine;
using System.Collections;
using UnityEngine.UI;
using System.Collections.Generic;
using UnityEngine.EventSystems;
using System;

public class PageViewVertical : MonoBeh<PERSON>our, IBeginDragHandler, IEndDragHandler
{
    private ScrollRect rect;
    private float targetvertical = 0;
    private bool isDrag = false;
    private List<float> posList = new();
    private int currentPageIndex = 0;

    private bool stopMove = true;
    public float smooting = 4;
    public float sensitivity = 0;
    private float startTime;
    private int maxPage = 0;

    private float startDragVertical;

    public Action<int,GameObject> OnPageChanged;
    public Action<int> OnUpdatePageViewCell;

    void Awake() {

    }

    public void CreatePageView(int num) {
        for (int i = 0; i < num; i++) {
            OnUpdatePageViewCell?.Invoke(i);
        }

        maxPage = num;
        rect = transform.GetComponent<ScrollRect>();
        LayoutRebuilder.ForceRebuildLayoutImmediate(rect.content);
        float verticalLength = rect.content.rect.height - GetComponent<RectTransform>().rect.height;
        posList.Clear();
        posList.Add(1); // 垂直滚动从1开始（顶部）
        for (int i = 1; i < num - 1; i++) {
            posList.Add(1f - GetComponent<RectTransform>().rect.height * i / verticalLength);
        }
        posList.Add(0); // 垂直滚动到0结束（底部）
    }

    void Update() {
        if(!isDrag && !stopMove) {
            startTime += Time.deltaTime;
            float t = startTime * smooting;
            rect.verticalNormalizedPosition = Mathf.Lerp(rect.verticalNormalizedPosition, targetvertical, t);
            if(t >= 1)
                stopMove = true;
        }
    }

    /// <summary>
    /// 滚动到目标页数
    /// </summary>
    /// <param name="targetPage">目标页数</param>
    /// <param name="isPlayAnimation">是否播放滚动动画</param>
    public void pageTo(int targetPage, bool isPlayAnimation = true) {
        targetPage = Math.Max(0, targetPage);
        targetPage = Math.Min(targetPage, posList.Count - 1);
        SetPageIndex(targetPage);
        if (!isPlayAnimation) {
            rect.verticalNormalizedPosition = posList[targetPage];
            return;
        }
        targetvertical = posList[targetPage]; // 设置当前坐标，更新函数进行插值  
        isDrag = false;
        startTime = 0;
        stopMove = false;
    }

    public void pageToLast(bool isPlayAnimation = true) {
        pageTo(currentPageIndex - 1, isPlayAnimation);
    }

    public void pageToNext(bool isPlayAnimation = true) {
        pageTo(currentPageIndex + 1, isPlayAnimation);
    }

    private void SetPageIndex(int index) {
        if(currentPageIndex != index) {
            currentPageIndex = index;            
            OnPageChanged?.Invoke(index, rect.content.transform.GetChild(index).gameObject);
        }
    }

    public int GetPageIndex() {
        return currentPageIndex;
    }

    public void OnBeginDrag(PointerEventData eventData) {
        isDrag = true;
        startDragVertical = rect.verticalNormalizedPosition; 
    }

    public void OnEndDrag(PointerEventData eventData) {
        float posY = rect.verticalNormalizedPosition;
        posY += (posY - startDragVertical) * sensitivity;
        posY = posY < 1 ? posY : 1;
        posY = posY > 0 ? posY : 0;
        int index = 0;
        float offset = Mathf.Abs(posList[index] - posY);
        for(int i = 1; i < posList.Count; i++) {
            float temp = Mathf.Abs(posList[i] - posY);
            if(temp < offset) {
                index = i;
                offset = temp;
            }
        }

        if (index == currentPageIndex) {
            // 判断滑动方向
            float dragDelta = posY - startDragVertical;
            
            if (dragDelta > 0) {
                // 向上滑动，应该去上一页（索引减小）
                index--;
            } else if (dragDelta < 0) {
                // 向下滑动，应该去下一页（索引增加）
                index++;
            }

            index = Mathf.Max(index, 0);
            index = Mathf.Min(index, posList.Count - 1);
        }

        SetPageIndex(index);

        targetvertical = posList[index];  
        isDrag = false;
        startTime = 0;
        stopMove = false;
    } 
}

