/**
 * HtmlColor.cs
 * 
 * <AUTHOR> / https://github.com/mosframe
 * 
 */

namespace Mosframe {

    using UnityEngine;

	/// <summary>
	/// HTML Color Tags
    /// <para>Reference : https://docs.unity3d.com/Manual/StyledText.html </para>
	/// </summary>
	public class HtmlColor {

		public const string Aqua		= "#00ffffff";
		public const string Black		= "#000000ff";
		public const string Blue		= "#0000ffff";
		public const string Brown		= "#a52a2aff";
		public const string Cyan		= "#00ffffff";
		public const string DarkBlue	= "#0000a0ff";
		public const string Green		= "#008000ff";
		public const string LightGrey	= "#A0A0A0ff";
		public const string Grey		= "#808080ff";
		public const string DarkGrey	= "#404040ff";
		public const string LightBlue	= "#add8e6ff";
		public const string Lime		= "#00ff00ff";
		public const string Magenta		= "#ff00ffff";
		public const string Maroon		= "#800000ff";
		public const string Navy		= "#000080ff";
		public const string Olive		= "#808000ff";
		public const string Orange		= "#ffa500ff";
		public const string Purple		= "#800080ff";
		public const string Red			= "#ff0000ff";
		public const string Silver		= "#c0c0c0ff";
		public const string Teal		= "#008080ff";
		public const string White		= "#ffffffff";
		public const string Yellow		= "#ffff00ff";
	}
}

