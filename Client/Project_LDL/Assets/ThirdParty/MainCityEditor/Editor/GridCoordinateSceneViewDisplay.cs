// using UnityEditor;
// using UnityEngine;
//
// // 创建一个空的游戏对象并将其 Transform 组件作为目标
// [CustomEditor(typeof(Transform))] // 将这个编辑器脚本应用于所有的 Transform 组件
// public class GridCoordinateSceneViewDisplay : Editor
// {
//     public float GridSize = 1f; // 网格大小
//     private Vector3 _currentGridCoordinate = Vector3.zero;
//
//     private void OnEnable()
//     {
//         // 在 Scene 视图中添加委托，用于绘制 GUI
//         SceneView.duringSceneGui += OnSceneGUI;
//     }
//
//     private void OnDisable()
//     {
//         // 移除委托
//         SceneView.duringSceneGui -= OnSceneGUI;
//     }
//
//     private void OnSceneGUI(SceneView sceneView)
//     {
//         Event currentEvent = Event.current;
//
//         // 只在鼠标移动时更新坐标
//         if (currentEvent.type == EventType.MouseMove)
//         {
//             // 获取鼠标在 Scene 视图中的位置
//             Vector2 mousePosition = currentEvent.mousePosition;
//
//             // 将屏幕坐标转换为世界坐标
//             Ray ray = HandleUtility.GUIPointToWorldRay(mousePosition);
//             Plane plane = new Plane(Vector3.up, Vector3.zero); // 创建一个平面，用于计算相交点
//
//             float distance;
//             if (plane.Raycast(ray, out distance))
//             {
//                 Vector3 worldPosition = ray.GetPoint(distance);
//
//                 // 计算 Grid 坐标
//                 float gridX = Mathf.Round(worldPosition.x / GridSize) * GridSize;
//                 float gridY = Mathf.Round(worldPosition.y / GridSize) * GridSize;
//                 float gridZ = Mathf.Round(worldPosition.z / GridSize) * GridSize;
//
//                 _currentGridCoordinate = new Vector3(gridX, gridY, gridZ);
//
//                 // 刷新 Scene 视图，触发重绘
//                 sceneView.Repaint();
//             }
//         }
//
//         // 在 Scene 视图左上角绘制坐标
//         Handles.BeginGUI();
//         GUI.color = Color.white; // 设置文字颜色
//         GUI.Label(new Rect(10, 10, 200, 20), "Grid Coordinate: " + _currentGridCoordinate.ToString());
//         Handles.EndGUI();
//     }
//
//     // 添加 GridSize 到 Inspector 面板
//     public override void OnInspectorGUI()
//     {
//         GridSize = EditorGUILayout.FloatField("Grid Size", GridSize);
//         base.OnInspectorGUI();
//     }
// }