using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.UI;
using Coffee.UIEffects;

public static class CustomUIEditorMenu 
{
    [MenuItem("GameObject/UI/UI Button", priority = 0)]
    static void AddUIButton()
    {
        UIButton uiButton = CreateUIButton();
        uiButton.transform.SetParent(GetParent(), false);
        Selection.activeGameObject = uiButton.gameObject;
        SetPrefabDirty();
    }

    [MenuItem("GameObject/UI/UI Image", priority = 0)]
    static void AddUIImage()
    {
        UIImage uiImage = CreateUIImage();
        uiImage.transform.SetParent(GetParent(), false);
        Selection.activeGameObject = uiImage.gameObject;
        SetPrefabDirty();
    }

    [MenuItem("GameObject/UI/UI Text", priority = 0)]
    static void AddUIText()
    {
        UIText uiText = CreateUIText();
        uiText.transform.SetParent(GetParent(), false);
        Selection.activeGameObject = uiText.gameObject;
        SetPrefabDirty();
    }

    [MenuItem("GameObject/UI/UI Toggle", priority = 0)]
    static void AddUIToggle()
    {
        UIToggle uiToggle = CreateUIToggle();
        uiToggle.transform.SetParent(GetParent(), false);
        Selection.activeGameObject = uiToggle.gameObject;
        SetPrefabDirty();
    }

    [MenuItem("GameObject/UI/UI Toggle Group", priority = 0)]
    static void AddUIToggleGroup()
    {
        GameObject goToggleGroup = CreateUIToggleGroup();
        goToggleGroup.transform.SetParent(GetParent(), false);
        Selection.activeGameObject = goToggleGroup;
        SetPrefabDirty();
    }

    /// <summary>
    /// 创建 UIButton 节点
    /// </summary>
    /// <returns>UIButton 节点</returns>
    static UIButton CreateUIButton()
    {
        GameObject go = new("Button")
        {
            layer = LayerMask.NameToLayer("UI")
        };

        UIImage uiImage = go.AddComponent<UIImage>();
        uiImage.color = Color.white;
        uiImage.sprite = AssetDatabase.GetBuiltinExtraResource<Sprite>("UI/Skin/UISprite.psd");
        uiImage.type = Image.Type.Sliced;

        RectTransform goRect = go.transform as RectTransform;
        goRect.sizeDelta = new Vector2(160, 40);

        UIButton uiButton = go.AddComponent<UIButton>();
        uiButton.transition = Selectable.Transition.None;

        UIText uiText = CreateUIText();
        uiText.transform.SetParent(goRect, false);
        SetRectStretchFull(uiText.rectTransform);

        return uiButton;
    }

    /// <summary>
    /// 创建 UIImage 节点
    /// </summary>
    /// <returns>UIImage 节点</returns>
    static UIImage CreateUIImage()
    {
        GameObject go = new("Image")
        {
            layer = LayerMask.NameToLayer("UI")
        };
        UIImage uiImage = go.AddComponent<UIImage>();
        uiImage.color = Color.white;

        return uiImage;
    }

    /// <summary>
    /// 创建 UIText 节点
    /// </summary>
    /// <param name="nodeName">节点名称</param>
    /// <param name="content">文本内容</param>
    /// <returns>UIText 节点</returns>
    static UIText CreateUIText(string nodeName = "Text", string content = "Text")
    {
        GameObject go = new(nodeName)
        {
            layer = LayerMask.NameToLayer("UI")
        };
        UIText uiText = go.AddComponent<UIText>();
        
        // 设置字体属性
        uiText.rectTransform.sizeDelta = new Vector2(160, 30);
        uiText.text = content;
        uiText.font = Resources.Load<Font>("Font/Lalezar-Regular");
        uiText.fontSize = 24;
        uiText.alignment = TextAnchor.MiddleCenter;
        uiText.raycastTarget = false;
        uiText.color = Color.white;

        // 添加描边组件
        Outline outline = go.AddComponent<Outline>();
        ColorUtility.TryParseHtmlString("#000000", out Color color);
        outline.effectColor = color;
        outline.effectDistance = new Vector2(1.5f, -1.5f);

        // 添加投影组件
        UIShadow uiShadow = go.AddComponent<UIShadow>();
        uiShadow.effectColor = color;
        uiShadow.effectDistance = new Vector2(0, -3f);

        return uiText;
    }

    /// <summary>
    /// 创建 UIToggle 节点
    /// </summary>
    /// <returns>UIToggle 节点</returns>
    static UIToggle CreateUIToggle()
    {
        // 创建 Toggle
        GameObject goToggle = new("Toggle");
        UIToggle uiToggle = goToggle.AddComponent<UIToggle>();

        // 创建背景图片
        GameObject goBackground = new("Background");
        UIImage imgBackground = goBackground.AddComponent<UIImage>();
        imgBackground.color = Color.white;
        imgBackground.sprite = AssetDatabase.GetBuiltinExtraResource<Sprite>("UI/Skin/UISprite.psd");
        imgBackground.type = Image.Type.Sliced;

        // 创建选中图片
        GameObject goCheckMark = new("CheckMark");
        UIImage imgCheckMark = goCheckMark.AddComponent<UIImage>();
        imgCheckMark.sprite = AssetDatabase.GetBuiltinExtraResource<Sprite>("UI/Skin/UISprite.psd");
        imgCheckMark.type = Image.Type.Sliced;
        imgCheckMark.color = Color.yellow;
        
        goBackground.transform.SetParent(goToggle.transform, false);
        goCheckMark.transform.SetParent(goBackground.transform, false);

        // 创建未选中和选中文本
        UIText uiTextNormal = CreateUIText("TextNormal", "Normal");
        UIText uiTextLight = CreateUIText("TextLight", "Light");

        uiTextNormal.transform.SetParent(goToggle.transform, false);
        uiTextLight.transform.SetParent(goToggle.transform, false);

        // 子节点拉伸
        SetRectStretchFull(imgBackground.rectTransform);
        SetRectStretchFull(imgCheckMark.rectTransform);
        SetRectStretchFull(uiTextNormal.rectTransform);
        SetRectStretchFull(uiTextLight.rectTransform);

        // 初始隐藏选中文本
        uiTextLight.gameObject.SetActive(false);

        // 设置未选中和选中时需要显示隐藏的节点
        uiToggle.unCheckMarks = new GameObject[] { uiTextNormal.gameObject };
        uiToggle.checkMarks = new GameObject[] { uiTextLight.gameObject };
        
        // 设置 Toggle 的属性
        uiToggle.targetGraphic = imgBackground;
        uiToggle.graphic = imgCheckMark;
        uiToggle.transition = Selectable.Transition.None;

        // 递归修改节点的 Layer
        goToggle.SetLayerRecursively(LayerMask.NameToLayer("UI"));

        return uiToggle;
    }

    /// <summary>
    /// 创建 ToggleGroup 节点
    /// </summary>
    /// <returns>ToggleGroup 节点</returns>
    static GameObject CreateUIToggleGroup()
    {
        // 创建 ToggleGroup
        GameObject goToggleGroup = new("ToggleGroup")
        {
            layer = LayerMask.NameToLayer("UI")
        };

        // 默认生成三个 Toggle
        int count = 3;

        // 添加 RectTransform 组件，并修改宽度
        RectTransform rectTransform = goToggleGroup.AddComponent<RectTransform>();
        rectTransform.sizeDelta = new Vector2(100 * count, 100);

        // 添加水平布局组件
        HorizontalLayoutGroup horizontalLayoutGroup = goToggleGroup.AddComponent<HorizontalLayoutGroup>();
        horizontalLayoutGroup.childForceExpandWidth = false;
        horizontalLayoutGroup.childForceExpandHeight = false;

        // 添加 ToggleGroup 组件
        ToggleGroup toggleGroup = goToggleGroup.AddComponent<ToggleGroup>();

        // 生成多个 Toggle 子节点
        for (int i = 0; i < count; i++)
        {
            UIToggle uiToggle = CreateUIToggle();
            uiToggle.group = toggleGroup;
            uiToggle.transform.SetParent(goToggleGroup.transform, false);
        }

        return goToggleGroup;
    }

    /// <summary>
    /// 设置 RectTransform 拉伸到全屏
    /// </summary>
    /// <param name="node">节点</param>
    static void SetRectStretchFull(Transform node)
    {
        RectTransform rect = node as RectTransform;
        rect.anchorMin = new Vector2(0, 0);
        rect.anchorMax = new Vector2(1, 1);
        rect.sizeDelta = Vector2.zero;
    }

    /// <summary>
    /// 获取父节点
    /// </summary>
    /// <returns>选择的节点或根节点</returns>
    static Transform GetParent()
    {
        // 先获取当前选择的节点
        Transform parent = Selection.activeTransform;

        // 如果没有选择节点，就获取当前编辑预制体场景的根节点
        if (parent == null)
        {
            GameObject root = PrefabStageUtility.GetCurrentPrefabStage()?.prefabContentsRoot;
            parent = root.transform;
        }
        return parent;
    }

    /// <summary>
    /// 标记预制体为已修改状态
    /// </summary>
    static void SetPrefabDirty()
    {
        var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
        if (prefabStage != null)
        {
            EditorSceneManager.MarkSceneDirty(prefabStage.scene);
        }
    }
}
