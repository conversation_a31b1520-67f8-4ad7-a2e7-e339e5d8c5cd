using System.IO;
using UnityEditor;
using UnityEngine;

public class CheckBuild
{
    // Start is called once before the first execution of Update after the MonoBehaviour is created

    [MenuItem("打包/测试打包编译/测试预编译")]
    public static void TestBuild()
    {
        var test = "Assets/hxqtest/PlanarShadow.shader";
        BuildTarget currentBuildTarget = EditorUserBuildSettings.activeBuildTarget;
        var outputDir = "hxqbuildtest___";
        if(Directory.Exists(outputDir))
            Directory.Delete(outputDir, true);

        Directory.CreateDirectory(outputDir);

        AssetBundleBuild abb = new AssetBundleBuild();
        abb.assetNames = new string[] {test};
        abb.assetBundleName = "hxq";

        BuildPipeline.BuildAssetBundles(outputDir,new AssetBundleBuild[]{abb}, BuildAssetBundleOptions.None, currentBuildTarget);

        Debug.Log("测试完成!");
        Directory.Delete("hxqbuildtest___", true);
        AssetDatabase.Refresh();
    }

    [MenuItem("打包/测试打包编译/测试AB资源存在")]
    public static void CheckIsHasAB()     
    {

    }

    [MenuItem("打包/测试打包编译/复制aot dll")]
    public static void CopyHybridCLRDllToAOTCode()     
    {
        BuildHelper.CopyHybridCLRDllToAOTCode(EditorUserBuildSettings.activeBuildTarget);
    }

   [MenuItem("打包/测试打包编译/打包dll")]
    public static void BuildDll()     
    {
        BuildHelper.BuildDll();
    }
    [MenuItem("打包/测试打包编译/打包apk")]
    public static void BuilApk()     
    {
        BuildHelper.BuildPlayer();
    }

    }
