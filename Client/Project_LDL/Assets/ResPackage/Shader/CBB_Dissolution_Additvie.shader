// Shader created with Shader Forge v1.38 
// Shader Forge (c) <PERSON><PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:3,spmd:0,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:0,bdst:0,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:False,aust:False,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:False,fgod:False,fgor:False,fgmd:0,fgcr:0.5,fgcg:0.5,fgcb:0.5,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:False,fnfb:False,fsmp:False;n:type:ShaderForge.SFN_Final,id:3138,x:33009,y:32657,varname:node_3138,prsc:2|emission-3808-OUT;n:type:ShaderForge.SFN_Tex2d,id:3755,x:31082,y:33333,ptovrint:False,ptlb:Mask Texture,ptin:_MaskTexture,varname:node_3755,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Tex2d,id:5620,x:31902,y:32726,ptovrint:False,ptlb:Texture,ptin:_Texture,varname:_node_3755_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-5817-OUT;n:type:ShaderForge.SFN_Color,id:7519,x:31902,y:32532,ptovrint:False,ptlb:Color,ptin:_Color,varname:node_7519,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.6985294,c2:0.6985294,c3:0.6985294,c4:1;n:type:ShaderForge.SFN_Multiply,id:3808,x:32557,y:32628,varname:node_3808,prsc:2|A-7519-RGB,B-5620-RGB,C-8945-RGB,D-4059-OUT;n:type:ShaderForge.SFN_Multiply,id:4059,x:32452,y:32858,varname:node_4059,prsc:2|A-7519-A,B-5620-A,C-3680-OUT;n:type:ShaderForge.SFN_VertexColor,id:8945,x:31075,y:33071,varname:node_8945,prsc:2;n:type:ShaderForge.SFN_Append,id:5817,x:31366,y:32502,varname:node_5817,prsc:2|A-2030-OUT,B-923-OUT;n:type:ShaderForge.SFN_TexCoord,id:1625,x:30644,y:32326,varname:node_1625,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_ComponentMask,id:8341,x:30871,y:32326,varname:node_8341,prsc:2,cc1:0,cc2:-1,cc3:-1,cc4:-1|IN-1625-U;n:type:ShaderForge.SFN_Add,id:2030,x:31110,y:32435,varname:node_2030,prsc:2|A-8341-OUT,B-7399-OUT;n:type:ShaderForge.SFN_Time,id:6576,x:30474,y:32497,varname:node_6576,prsc:2;n:type:ShaderForge.SFN_Multiply,id:7399,x:30871,y:32514,varname:node_7399,prsc:2|A-6576-TSL,B-1109-OUT;n:type:ShaderForge.SFN_ValueProperty,id:1109,x:30644,y:32602,ptovrint:False,ptlb:U,ptin:_U,varname:node_1109,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ComponentMask,id:7303,x:31035,y:32573,varname:node_7303,prsc:2,cc1:0,cc2:-1,cc3:-1,cc4:-1|IN-1625-V;n:type:ShaderForge.SFN_Multiply,id:5212,x:30801,y:32652,varname:node_5212,prsc:2|A-6576-TSL,B-2660-OUT;n:type:ShaderForge.SFN_ValueProperty,id:2660,x:30619,y:32753,ptovrint:False,ptlb:V,ptin:_V,varname:node_2660,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Add,id:923,x:31197,y:32599,varname:node_923,prsc:2|A-7303-OUT,B-5212-OUT;n:type:ShaderForge.SFN_Smoothstep,id:3680,x:32059,y:33163,varname:node_3680,prsc:2|A-4645-OUT,B-4790-OUT,V-3576-OUT;n:type:ShaderForge.SFN_OneMinus,id:4645,x:31773,y:33081,varname:node_4645,prsc:2|IN-4790-OUT;n:type:ShaderForge.SFN_ValueProperty,id:4790,x:31445,y:33149,ptovrint:False,ptlb:Smooth,ptin:_Smooth,varname:node_4790,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0.5;n:type:ShaderForge.SFN_Add,id:3927,x:31579,y:33401,varname:node_3927,prsc:2|A-3755-R,B-1229-OUT,C-5368-OUT,D-8945-A;n:type:ShaderForge.SFN_Vector1,id:1229,x:31208,y:33466,varname:node_1229,prsc:2,v1:1;n:type:ShaderForge.SFN_ValueProperty,id:3835,x:31176,y:33585,ptovrint:False,ptlb:Out,ptin:_Out,varname:node_3835,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_OneMinus,id:5368,x:31384,y:33541,varname:node_5368,prsc:2|IN-3835-OUT;n:type:ShaderForge.SFN_Clamp01,id:3576,x:31791,y:33401,varname:node_3576,prsc:2|IN-3927-OUT;proporder:7519-5620-3755-4790-3835-1109-2660;pass:END;sub:END;*/

Shader "CBB/Dissolution_Additive" {
    Properties {
        [HDR]_Color ("Color", Color) = (0.6985294,0.6985294,0.6985294,1)
        _Texture ("Texture", 2D) = "white" {}
        _MaskTexture ("Mask Texture", 2D) = "white" {}
        _Smooth ("Smooth", Float ) = 0.5
        _Out ("Out", Float ) = 0
        _U ("U", Float ) = 0
        _V ("V", Float ) = 0
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        Pass {
            Name "FORWARD"
            Tags {
                "LightMode"="ForwardBase"
            }
            Blend One One
            //Cull Off
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_FORWARDBASE
            #include "UnityCG.cginc"
            #include "UnityPBSLighting.cginc"
            #include "UnityStandardBRDF.cginc"
            #pragma multi_compile_fwdbase
            //#pragma only_renderers d3d9 d3d11 glcore gles n3ds wiiu 
            #pragma target 3.0
            uniform sampler2D _MaskTexture; uniform float4 _MaskTexture_ST;
            uniform sampler2D _Texture; uniform float4 _Texture_ST;
            uniform float4 _Color;
            uniform float _U;
            uniform float _V;
            uniform float _Smooth;
            uniform float _Out;
            struct VertexInput {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 texcoord0 : TEXCOORD0;
                float4 vertexColor : COLOR;
                float4 texcoord1 : TEXCOORD1;

            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 posWorld : TEXCOORD1;
                float3 normalDir : TEXCOORD2;
                float4 vertexColor : COLOR;
                float4  texcoord1 : TEXCOORD3;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.vertexColor = v.vertexColor;
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                o.posWorld = mul(unity_ObjectToWorld, v.vertex);
                o.pos = UnityObjectToClipPos( v.vertex );
                o.texcoord1 = v.texcoord1;
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
                i.normalDir = normalize(i.normalDir);
                i.normalDir *= faceSign;
                float3 viewDirection = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
                float3 normalDirection = i.normalDir;
                float3 viewReflectDirection = reflect( -viewDirection, normalDirection );
////// Lighting:
////// Emissive:
                half4 node_6576 = _Time;
                half2 node_5817 = float2((i.uv0.r.r+(node_6576.r*_U)),(i.uv0.g.r+(node_6576.r*_V)));
                fixed4 _Texture_var = tex2D(_Texture,TRANSFORM_TEX(node_5817, _Texture));
                fixed4 _MaskTexture_var = tex2D(_MaskTexture,TRANSFORM_TEX(i.uv0, _MaskTexture));
                //   fixed3 emissive = (_Color.rgb * _Texture_var.rgb * i.vertexColor.rgb * (_Color.a * _Texture_var.a * smoothstep((1.0 - _Smooth), _Smooth, saturate((_MaskTexture_var.r + 1.0 + (1.0 - _Out) + i.vertexColor.a)))));

                  fixed3 emissive = (_Color.rgb*_Texture_var.rgb*i.vertexColor.rgb*(_Color.a*_Texture_var.a*smoothstep( (1.0 - _Smooth), _Smooth, saturate((_MaskTexture_var.r - 1.3 + (1.0 - i.texcoord1.x * _Out)+i.vertexColor.a)) )));
                fixed3 finalColor = emissive;
                return fixed4(finalColor,1);
            }
            ENDCG
        }
        Pass {
            Name "ShadowCaster"
            Tags {
                "LightMode"="ShadowCaster"
            }
            Offset 1, 1
            //Cull Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_SHADOWCASTER
            #include "UnityCG.cginc"
            #include "Lighting.cginc"
            #include "UnityPBSLighting.cginc"
            #include "UnityStandardBRDF.cginc"
            #pragma fragmentoption ARB_precision_hint_fastest
            #pragma multi_compile_shadowcaster
            //#pragma only_renderers d3d9 d3d11 glcore gles n3ds wiiu 
            #pragma target 3.0
            struct VertexInput {
                float4 vertex : POSITION;
            };
            struct VertexOutput {
                V2F_SHADOW_CASTER;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.pos = UnityObjectToClipPos( v.vertex );
                TRANSFER_SHADOW_CASTER(o)
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
                SHADOW_CASTER_FRAGMENT(i)
            }
            ENDCG
        }
    }
    FallBack "Diffuse"
    CustomEditor "ShaderForgeMaterialInspector"
}
