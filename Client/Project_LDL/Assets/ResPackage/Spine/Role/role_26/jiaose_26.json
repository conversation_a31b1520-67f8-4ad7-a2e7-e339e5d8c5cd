{"skeleton": {"hash": "RcmouCwCbYA", "spine": "4.2.40", "x": -534.09, "width": 927, "height": 1323, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 81.9, "y": 368.21, "icon": "square"}, {"name": "bone2", "parent": "bone", "x": -0.14, "y": -6.57}, {"name": "bone3", "parent": "bone2", "length": 63.38, "rotation": 3.72, "x": 3.24, "y": -1.32}, {"name": "bone4", "parent": "bone3", "length": 43.39, "rotation": 11.27, "x": 63.38}, {"name": "bone5", "parent": "bone2", "length": 89.82, "rotation": -178.75, "x": -1.9, "y": -1.16}, {"name": "bone6", "parent": "bone5", "length": 99.83, "rotation": -8.7, "x": 89.67, "y": 2.15}, {"name": "bone7", "parent": "bone6", "length": 77.25, "rotation": -8.34, "x": 99.83}, {"name": "bone8", "parent": "bone7", "length": 153.6, "rotation": 101.25, "x": 80.18, "y": 13.42}, {"name": "bone9", "parent": "bone4", "length": 109.66, "rotation": -102.31, "x": 25.3, "y": 46.38}, {"name": "bone10", "parent": "bone", "length": 203.09, "rotation": -98.48, "x": -182.85, "y": -13.67}, {"name": "bone11", "parent": "bone10", "length": 163.74, "rotation": -7.12, "x": 203.09}, {"name": "bone12", "parent": "bone", "length": 165.14, "rotation": -79.47, "x": 26.52, "y": -35.32}, {"name": "bone13", "parent": "bone12", "length": 179.18, "rotation": -1.78, "x": 158.65, "y": -0.51}, {"name": "target", "parent": "root", "x": 164.16, "y": -0.28, "color": "ff3f00ff", "icon": "ik"}, {"name": "target2", "parent": "root", "x": -174.96, "y": -4.04, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone14", "parent": "bone", "length": 124.12, "rotation": 98.7, "x": -5.16, "y": 14.54}, {"name": "bone15", "parent": "bone14", "length": 170.48, "rotation": -11.12, "x": 125.69, "y": 1.95, "inherit": "noScale"}, {"name": "bone16", "parent": "bone15", "length": 194, "rotation": 20.86, "x": 170.48, "inherit": "noScale"}, {"name": "bone17", "parent": "bone16", "length": 176.1, "rotation": -13.26, "x": 202.84, "y": -0.66, "inherit": "noScale"}, {"name": "bone18", "parent": "bone17", "length": 61.01, "rotation": -32.65, "x": 176.1, "inherit": "noScale"}, {"name": "bone21", "parent": "bone18", "length": 27.55, "rotation": -4.23, "x": 116.17, "y": -24.31}, {"name": "bone22", "parent": "bone21", "length": 30.55, "rotation": -47.24, "x": 27.55}, {"name": "bone23", "parent": "bone22", "length": 29.54, "rotation": -30.13, "x": 30.55}, {"name": "bone24", "parent": "bone23", "length": 36.29, "rotation": -40.71, "x": 29.54}, {"name": "bone25", "parent": "bone18", "length": 37.3, "rotation": 63.07, "x": 103.59, "y": 10.33}, {"name": "bone26", "parent": "bone25", "length": 40.48, "rotation": -19.79, "x": 37.3}, {"name": "bone27", "parent": "bone26", "length": 33.84, "rotation": -19.31, "x": 40.48}, {"name": "bone28", "parent": "bone27", "length": 36.37, "rotation": -4.97, "x": 33.36, "y": 0.36}, {"name": "bone29", "parent": "bone18", "length": 37.89, "rotation": 83.07, "x": 93.49, "y": 20.82}, {"name": "bone30", "parent": "bone29", "length": 36.16, "rotation": -15.17, "x": 37.89}, {"name": "bone31", "parent": "bone30", "length": 39.26, "rotation": -16.97, "x": 36.16}, {"name": "bone32", "parent": "bone31", "length": 40.21, "rotation": -19.1, "x": 39.26}, {"name": "bone33", "parent": "bone18", "length": 43.1, "rotation": 44.91, "x": 113.57, "y": -8.95}, {"name": "bone34", "parent": "bone33", "length": 49.21, "rotation": -30.61, "x": 43.1}, {"name": "bone35", "parent": "bone34", "length": 45.21, "rotation": -29.39, "x": 49.21}, {"name": "bone36", "parent": "bone35", "length": 39.96, "rotation": -5.88, "x": 45.03, "y": 0.62}, {"name": "bone37", "parent": "bone36", "length": 41.76, "rotation": 13.72, "x": 39.96}, {"name": "bone38", "parent": "bone18", "length": 31.47, "rotation": 12.28, "x": 121.87, "y": -12.14}, {"name": "bone39", "parent": "bone38", "length": 41.44, "rotation": -26.14, "x": 31.47}, {"name": "bone40", "parent": "bone39", "length": 39, "rotation": -10.7, "x": 41.45}, {"name": "bone41", "parent": "bone40", "length": 34.42, "rotation": 19.03, "x": 39}, {"name": "bone42", "parent": "bone18", "length": 33.08, "rotation": -7.84, "x": 119.08, "y": -15.71}, {"name": "bone43", "parent": "bone42", "length": 37.23, "rotation": -45.42, "x": 33.08}, {"name": "bone44", "parent": "bone43", "length": 38.47, "rotation": -3.12, "x": 37.23}, {"name": "bone45", "parent": "bone44", "length": 41.01, "rotation": 16.42, "x": 38.47}, {"name": "bone46", "parent": "bone18", "length": 42.55, "rotation": 93.91, "x": 72.12, "y": 30.46}, {"name": "bone47", "parent": "bone46", "length": 39.92, "rotation": -14.26, "x": 42.55}, {"name": "bone48", "parent": "bone47", "length": 38.74, "rotation": -8.91, "x": 39.92}, {"name": "bone49", "parent": "bone48", "length": 40.64, "rotation": -3.23, "x": 38.74}, {"name": "bone50", "parent": "bone18", "length": 29.45, "rotation": 99.55, "x": 55.98, "y": 56.08}, {"name": "bone51", "parent": "bone50", "length": 29.61, "rotation": -3.28, "x": 29.45}, {"name": "bone52", "parent": "bone51", "length": 25.95, "rotation": -11.47, "x": 29.61}, {"name": "bone53", "parent": "bone18", "length": 15.94, "rotation": 105.91, "x": 37.03, "y": 76.44}, {"name": "bone54", "parent": "bone53", "length": 14.92, "rotation": 14.29, "x": 15.94}, {"name": "bone55", "parent": "bone18", "length": 29.24, "rotation": -120.42, "x": -10.24, "y": -105.21}, {"name": "bone56", "parent": "bone55", "length": 24.87, "rotation": -19.7, "x": 29.24}, {"name": "bone57", "parent": "bone56", "length": 30.11, "rotation": -2.19, "x": 24.87}, {"name": "bone58", "parent": "bone18", "length": 31.44, "rotation": -172.32, "x": -18.05, "y": -21.16}, {"name": "bone59", "parent": "bone58", "length": 33.97, "rotation": 10.45, "x": 31.57, "y": -0.37}, {"name": "bone60", "parent": "bone18", "length": 16.33, "rotation": -99.67, "x": -0.39, "y": -133.77}, {"name": "bone61", "parent": "bone60", "length": 17.85, "rotation": -7.85, "x": 16.33}, {"name": "bone62", "parent": "bone18", "length": 23.51, "rotation": -175.22, "x": -43.24, "y": -89.82}, {"name": "bone63", "parent": "bone62", "length": 19.03, "rotation": -11.33, "x": 23.51}, {"name": "bone64", "parent": "bone18", "length": 27.46, "rotation": -120.43, "x": -6.44, "y": -130.69}, {"name": "bone65", "parent": "bone64", "length": 30.43, "rotation": -0.87, "x": 27.46}, {"name": "bone66", "parent": "bone16", "length": 137.59, "rotation": -124.84, "x": 166.49, "y": -29.33}, {"name": "bone67", "parent": "bone66", "length": 282.49, "rotation": -67.67, "x": 137.59}, {"name": "bone68", "parent": "bone67", "length": 266.91, "rotation": 24.92, "x": 280.44, "y": 1.03}, {"name": "bone69", "parent": "bone68", "length": 143.34, "rotation": -31.82, "x": 267.6, "y": -0.42}, {"name": "bone70", "parent": "bone69", "length": 54.81, "rotation": -76.22, "x": 144.25, "y": -2.76}, {"name": "bone71", "parent": "bone70", "length": 47.64, "rotation": -44.6, "x": 54.81}, {"name": "bone72", "parent": "bone16", "length": 237.39, "rotation": 76.94, "x": 199.93, "y": 17.13, "color": "abe323ff"}, {"name": "bone73", "parent": "bone72", "length": 334.48, "rotation": 50.01, "x": 237.39}, {"name": "bone74", "parent": "bone73", "length": 330.04, "rotation": 35.9, "x": 331.11, "y": 1.06}, {"name": "bone75", "parent": "bone74", "length": 178.14, "rotation": 20.07, "x": 330.04}, {"name": "bone76", "parent": "bone", "length": 95.53, "rotation": 105.02, "x": -200.31, "y": 48.17}, {"name": "bone77", "parent": "bone76", "length": 144.51, "rotation": 5.02, "x": 95.53}, {"name": "bone78", "parent": "bone77", "length": 136.53, "rotation": -11.5, "x": 144.51}, {"name": "bone79", "parent": "bone16", "length": 61.71, "rotation": -86.13, "x": 371.32, "y": 198.91}, {"name": "bone80", "parent": "bone79", "length": 120.3, "rotation": -70.76, "x": 61.71}, {"name": "bone81", "parent": "bone80", "length": 104.8, "rotation": -16.77, "x": 120.3}, {"name": "bone82", "parent": "bone81", "length": 54.04, "rotation": 0.05, "x": 104.8}, {"name": "bone83", "parent": "bone16", "length": 76.17, "rotation": -176.55, "x": 225.35, "y": -134.16}, {"name": "bone84", "parent": "bone83", "length": 58.01, "rotation": -13.5, "x": 76.17}, {"name": "bone85", "parent": "bone84", "length": 61.77, "rotation": -4.46, "x": 58.01}, {"name": "bone19", "parent": "bone18", "rotation": -62.53, "x": 3.2, "y": -36.92, "icon": "square"}, {"name": "bone20", "parent": "bone19", "x": 61.65, "y": 6.21, "color": "abe323ff", "icon": "square"}, {"name": "bone86", "parent": "bone18", "length": 61.35, "rotation": 59.48, "x": -22.81, "y": 18.46}, {"name": "bone87", "parent": "bone18", "x": 43.43, "y": -32.44}, {"name": "bone88", "parent": "bone87", "x": 19.15, "y": -46.75}], "slots": [{"name": "youshou2", "bone": "root", "attachment": "youshou2"}, {"name": "youshou1", "bone": "root", "attachment": "youshou1"}, {"name": "you<PERSON>ou", "bone": "root", "attachment": "you<PERSON>ou"}, {"name": "bao1", "bone": "root", "attachment": "bao1"}, {"name": "shenti", "bone": "root", "attachment": "shenti"}, {"name": "ji<PERSON>i", "bone": "root", "attachment": "ji<PERSON>i"}, {"name": "tui", "bone": "root", "attachment": "tui"}, {"name": "ya<PERSON>i", "bone": "root", "attachment": "ya<PERSON>i"}, {"name": "bao", "bone": "root", "attachment": "bao"}, {"name": "zuoshou1", "bone": "root", "attachment": "zuoshou1"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "yan<PERSON>i", "bone": "root", "attachment": "yan<PERSON>i"}, {"name": "tongkong", "bone": "root", "attachment": "tongkong"}, {"name": "lian", "bone": "root", "attachment": "lian"}, {"name": "biyan", "bone": "root"}, {"name": "toufa8", "bone": "root", "attachment": "toufa8"}, {"name": "toufa7", "bone": "root", "attachment": "toufa7"}, {"name": "toufa6", "bone": "root", "attachment": "toufa6"}, {"name": "toufa5", "bone": "root", "attachment": "toufa5"}, {"name": "toufa4", "bone": "root", "attachment": "toufa4"}, {"name": "toufa3", "bone": "root", "attachment": "toufa3"}, {"name": "toufa2", "bone": "root", "attachment": "toufa2"}, {"name": "toufa1", "bone": "root", "attachment": "toufa1"}, {"name": "humujing", "bone": "root", "attachment": "humujing"}, {"name": "erduo", "bone": "root", "attachment": "erduo"}], "ik": [{"name": "target", "bones": ["bone13"], "target": "target", "compress": true, "stretch": true}, {"name": "target2", "order": 1, "bones": ["bone11"], "target": "target2", "compress": true, "stretch": true}], "transform": [{"name": "1", "order": 7, "bones": ["bone33"], "target": "bone20", "rotation": 107.43, "x": -35.55, "y": 104.62, "mixRotate": 0, "mixX": 0.5, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "2", "order": 8, "bones": ["bone38"], "target": "bone20", "rotation": 74.8, "x": -28.89, "y": 110.52, "mixRotate": 0, "mixX": 0.4, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "3", "order": 9, "bones": ["bone42"], "target": "bone20", "rotation": 54.69, "x": -27.01, "y": 106.39, "mixRotate": 0, "mixX": 0.3, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "4", "order": 10, "bones": ["bone21"], "target": "bone20", "rotation": 58.3, "x": -20.72, "y": 99.84, "mixRotate": 0, "mixX": 0.2, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "10", "order": 11, "bones": ["bone25"], "target": "bone20", "rotation": 125.6, "x": -57.25, "y": 104.66, "mixRotate": 0, "mixX": 0.45, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "11", "order": 12, "bones": ["bone29"], "target": "bone20", "rotation": 145.6, "x": -71.23, "y": 100.54, "mixRotate": 0, "mixX": 0.4, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "12", "order": 13, "bones": ["bone46"], "target": "bone20", "rotation": 156.43, "x": -89.64, "y": 86.02, "mixRotate": 0, "mixX": 0.3, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "13", "order": 14, "bones": ["bone50"], "target": "bone20", "rotation": 162.07, "x": -119.81, "y": 83.53, "mixRotate": 0, "mixX": 0.2, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "14", "order": 15, "bones": ["bone53"], "target": "bone20", "rotation": 168.44, "x": -146.62, "y": 76.1, "mixRotate": 0, "mixX": 0.1, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "12334", "order": 17, "bones": ["bone87"], "target": "bone20", "rotation": 62.53, "x": -47.06, "y": 31.56, "mixRotate": 0, "mixX": 0.7, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "34343", "order": 18, "bones": ["bone86"], "target": "bone20", "rotation": 122.01, "x": -122.79, "y": -3.73, "mixRotate": 0, "mixX": 0.25, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "bone20", "order": 2, "bones": ["bone55"], "target": "bone20", "rotation": -57.89, "x": -7.27, "y": -49.63, "mixRotate": 0, "mixX": 0.6, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "bone201", "order": 3, "bones": ["bone64"], "target": "bone20", "rotation": -57.91, "x": 17.09, "y": -58.01, "mixRotate": 0, "mixX": 0.4, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "bone202", "order": 5, "bones": ["bone60"], "target": "bone20", "rotation": -37.15, "x": 22.62, "y": -54.07, "mixRotate": 0, "mixX": 0.15, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "bone205", "order": 6, "bones": ["bone62"], "target": "bone20", "rotation": -112.69, "x": -36.14, "y": -71.82, "mixRotate": 0, "mixX": 0.35, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "bone206", "order": 4, "bones": ["bone58"], "target": "bone20", "rotation": -109.8, "x": -85.44, "y": -17.79, "mixRotate": 0, "mixX": 0.15, "mixY": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "bone721", "order": 16, "bones": ["bone79"], "target": "bone72", "rotation": -163.07, "x": 215.8, "y": -125.89, "mixRotate": 0.4, "mixX": 0.4, "mixScaleX": 0.4, "mixShearY": 0.4}], "skins": [{"name": "default", "attachments": {"bao": {"bao": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 8, 161.02, 84.42, 1, 1, 8, 172.78, -64.12, 1, 1, 8, -5.67, -78.24, 1, 1, 8, -17.42, 70.29, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 149, "height": 179}}, "bao1": {"bao1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, 127.29, 29.29, 1, 1, 9, 123.82, -44.63, 1, 1, 9, -18.02, -37.97, 1, 1, 9, -14.55, 35.95, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 74, "height": 142}}, "biyan": {"biyan": {"type": "mesh", "uvs": [1, 1, 0.80069, 1, 0.5, 1, 0, 1, 0, 0, 0.5, 0, 0.79875, 0, 1, 0], "triangles": [2, 5, 6, 2, 6, 1, 1, 6, 7, 1, 7, 0, 3, 5, 2, 3, 4, 5], "vertices": [2, 87, 10.39, 20.93, 0.374, 86, 72.05, 27.14, 0.626, 2, 87, -3.76, 20.93, 0.1908, 86, 57.89, 27.14, 0.8092, 2, 87, -25.11, 20.93, 0.2061, 86, 36.55, 27.14, 0.7939, 2, 87, -60.61, 20.93, 0.4885, 86, 1.05, 27.14, 0.5115, 2, 87, -60.61, 35.93, 0.4885, 86, 1.05, 42.14, 0.5115, 2, 87, -25.11, 35.93, 0.2061, 86, 36.55, 42.14, 0.7939, 2, 87, -3.9, 35.93, 0.1908, 86, 57.76, 42.14, 0.8092, 2, 87, 10.39, 35.93, 0.3588, 86, 72.05, 42.14, 0.6412], "hull": 8, "edges": [6, 8, 0, 14, 8, 10, 4, 6, 10, 4, 10, 12, 12, 14, 0, 2, 2, 4, 12, 2], "width": 71, "height": 15}}, "erduo": {"erduo": {"type": "mesh", "uvs": [0.85713, 0.11831, 0.94478, 0.346, 1, 0.56207, 1, 1, 0.63127, 1, 0.42226, 0.93845, 0.17616, 0.79905, 0, 0.59865, 0, 0.1973, 0.19976, 0, 0.5571, 0], "triangles": [10, 7, 8, 10, 6, 7, 9, 10, 8, 10, 1, 6, 1, 10, 0, 2, 5, 6, 2, 6, 1, 4, 5, 2, 4, 2, 3], "vertices": [1, 88, 42.91, -25.51, 1, 1, 88, 26.25, -20.37, 1, 1, 88, 11.2, -14.29, 1, 1, 88, -16.28, 2.89, 1, 1, 88, -6.31, 18.84, 1, 1, 88, 3.2, 25.46, 1, 1, 88, 18.6, 30.64, 1, 1, 88, 35.94, 30.4, 1, 1, 88, 61.12, 14.65, 1, 1, 88, 68.1, -1.72, 1, 1, 88, 58.44, -17.18, 1], "hull": 11, "edges": [20, 0, 0, 2, 6, 4, 2, 4, 18, 20, 18, 16, 14, 16, 14, 12, 12, 10, 6, 8, 10, 8], "width": 51, "height": 74}}, "humujing": {"humujing": {"type": "mesh", "uvs": [0.87443, 0.09112, 0.92792, 0.11097, 0.96447, 0.20526, 0.98672, 0.3376, 1, 0.45671, 1, 0.70755, 0.98938, 0.82679, 0.95535, 0.91881, 0.89351, 0.97065, 0.83334, 0.9538, 0.78501, 0.85737, 0.75464, 0.68518, 0.72827, 0.6752, 0.69791, 0.82992, 0.64997, 0.92724, 0.582, 1, 0.48611, 0.95719, 0.41979, 0.85987, 0.35347, 0.8948, 0.24321, 0.9497, 0.11933, 1, 0, 1, 0, 0.38297, 0.1286, 0.21903, 0.26202, 0.1029, 0.39763, 0, 0.49048, 0, 0.58333, 0, 0.6308, 0.01374, 0.67017, 0.07012, 0.70218, 0.13872, 0.73289, 0.1393, 0.75765, 0.05792, 0.79451, 0.02594, 0.86629, 0.02448], "triangles": [2, 3, 0, 2, 0, 1, 5, 8, 4, 7, 8, 5, 4, 9, 3, 17, 25, 26, 16, 17, 26, 16, 26, 27, 18, 24, 25, 17, 18, 25, 19, 24, 18, 27, 28, 29, 14, 15, 27, 8, 9, 4, 27, 29, 12, 0, 3, 11, 33, 34, 0, 9, 11, 3, 12, 14, 27, 12, 30, 31, 33, 11, 31, 12, 31, 11, 12, 29, 30, 32, 33, 31, 11, 9, 10, 0, 11, 33, 13, 14, 12, 16, 27, 15, 6, 7, 5, 19, 23, 24, 20, 21, 22, 23, 20, 22, 19, 20, 23], "vertices": [2, 86, 81.55, 104.22, 0.3533, 87, 19.9, 98.01, 0.6467, 2, 86, 92.41, 102.93, 0.4, 87, 30.76, 96.72, 0.6, 2, 86, 99.83, 96.8, 0.45, 87, 38.18, 90.59, 0.55, 2, 86, 104.35, 88.2, 0.55, 87, 42.7, 81.99, 0.45, 2, 86, 107.05, 80.45, 0.6, 87, 45.39, 74.25, 0.4, 2, 86, 107.05, 64.15, 0.55, 87, 45.39, 57.94, 0.45, 2, 86, 104.89, 56.4, 0.5, 87, 43.24, 50.19, 0.5, 2, 86, 97.98, 50.42, 0.45, 87, 36.33, 44.21, 0.55, 2, 86, 85.43, 47.05, 0.33798, 87, 23.77, 40.84, 0.66202, 2, 86, 73.21, 48.14, 0.11838, 87, 11.56, 41.94, 0.88162, 2, 86, 63.4, 54.41, 0.1, 87, 1.75, 48.21, 0.9, 2, 86, 57.24, 65.6, 0.07599, 87, -4.42, 59.4, 0.92401, 2, 86, 51.89, 66.25, 0.06081, 87, -9.77, 60.05, 0.93919, 2, 86, 45.72, 56.2, 0.1, 87, -15.93, 49.99, 0.9, 2, 86, 35.99, 49.87, 0.2, 87, -25.66, 43.66, 0.8, 2, 86, 22.19, 45.14, 0.3, 87, -39.46, 38.93, 0.7, 2, 86, 2.73, 47.92, 0.35, 87, -58.93, 41.72, 0.65, 2, 86, -10.74, 54.25, 0.4, 87, -72.39, 48.04, 0.6, 2, 86, -24.2, 51.98, 0.45, 87, -85.85, 45.77, 0.55, 2, 86, -46.58, 48.41, 0.48769, 87, -108.24, 42.2, 0.51231, 2, 86, -71.73, 45.14, 0.65024, 87, -133.38, 38.93, 0.34976, 2, 86, -95.95, 45.14, 0.73869, 87, -157.61, 38.93, 0.26131, 2, 86, -95.95, 85.25, 0.7597, 87, -157.61, 79.04, 0.2403, 2, 86, -69.85, 95.9, 0.66357, 87, -131.5, 89.7, 0.33643, 2, 86, -42.76, 103.45, 0.52176, 87, -104.42, 97.25, 0.47824, 2, 86, -15.24, 110.14, 0.45, 87, -76.89, 103.93, 0.55, 2, 86, 3.61, 110.14, 0.35, 87, -58.04, 103.93, 0.65, 2, 86, 22.46, 110.14, 0.3, 87, -39.19, 103.93, 0.7, 2, 86, 32.1, 109.25, 0.2, 87, -29.56, 103.04, 0.8, 2, 86, 40.09, 105.58, 0.18003, 87, -21.56, 99.38, 0.81997, 2, 86, 46.59, 101.12, 0.05626, 87, -15.06, 94.92, 0.94374, 2, 86, 52.82, 101.09, 0.05821, 87, -8.83, 94.88, 0.94179, 2, 86, 57.85, 106.38, 0.13286, 87, -3.8, 100.17, 0.86714, 2, 86, 65.33, 108.45, 0.22827, 87, 3.68, 102.25, 0.77173, 2, 86, 79.9, 108.55, 0.40799, 87, 18.25, 102.34, 0.59201], "hull": 35, "edges": [42, 44, 44, 46, 46, 48, 48, 50, 40, 42, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 68, 0, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 50, 52, 52, 54], "width": 203, "height": 65}}, "jiandai": {"jiandai": {"type": "mesh", "uvs": [0.34421, 0.02111, 0.40061, 0.09872, 0.44969, 0.19928, 0.49776, 0.31325, 0.74661, 0.24748, 0.74794, 0.17977, 0.77148, 0.13915, 0.80332, 0.15783, 0.83569, 0.20966, 0.87073, 0.26709, 0.90201, 0.32038, 0.93651, 0.38575, 0.96007, 0.46719, 0.97946, 0.54784, 1, 0.78831, 1, 0.96606, 1, 1, 0.97996, 1, 0.93125, 0.96271, 0.92856, 1, 0.90798, 1, 0.90263, 0.96223, 0.87535, 0.89732, 0.84472, 0.86648, 0.84258, 0.75209, 0.83108, 0.66477, 0.8177, 0.5875, 0.79256, 0.50952, 0.7955, 0.43902, 0.78212, 0.37084, 0.76132, 0.31471, 0.49136, 0.38178, 0.46319, 0.42797, 0.48922, 0.48235, 0.52379, 0.57844, 0.5653, 0.6703, 0.6195, 0.79843, 0.60883, 0.86547, 0.56914, 0.91091, 0.50512, 0.96827, 0.49275, 1, 0.46283, 1, 0.43354, 0.93431, 0.40488, 0.85318, 0.40425, 0.79539, 0.36727, 0.6972, 0.32652, 0.58939, 0.28323, 0.46159, 0.23356, 0.36379, 0.17817, 0.29377, 0.1145, 0.28488, 0.06709, 0.3365, 0, 0.41922, 0, 0.39285, 0.02529, 0.28631, 0.05088, 0.22149, 0.08726, 0.17352, 0.092, 0.13214, 0.15218, 0.08185, 0.2098, 0.03411, 0.2532, 0, 0.31049, 0, 0.00785, 0.36794, 0.97705, 0.7645, 0.98106, 0.70566, 0.97919, 0.64917, 0.961, 0.58987], "triangles": [52, 62, 51, 52, 53, 62, 62, 53, 54, 62, 54, 51, 49, 59, 48, 54, 55, 51, 51, 55, 50, 50, 58, 49, 49, 58, 59, 55, 56, 50, 50, 56, 58, 58, 56, 57, 47, 48, 2, 47, 2, 32, 2, 48, 1, 1, 48, 0, 0, 48, 61, 61, 48, 60, 48, 59, 60, 44, 34, 35, 44, 45, 34, 45, 33, 34, 33, 46, 32, 33, 45, 46, 46, 47, 32, 31, 32, 3, 30, 31, 3, 3, 32, 2, 40, 41, 39, 41, 42, 39, 38, 39, 44, 44, 39, 42, 36, 37, 38, 35, 38, 44, 36, 38, 35, 44, 42, 43, 27, 28, 26, 26, 11, 12, 11, 28, 10, 11, 26, 28, 10, 29, 9, 10, 28, 29, 30, 3, 4, 29, 8, 9, 29, 30, 8, 8, 30, 4, 8, 4, 7, 7, 5, 6, 5, 7, 4, 24, 65, 64, 65, 25, 66, 65, 24, 25, 64, 65, 14, 13, 14, 65, 25, 26, 66, 65, 66, 13, 13, 66, 12, 66, 26, 12, 17, 15, 16, 17, 18, 15, 19, 20, 18, 20, 21, 18, 18, 14, 15, 14, 18, 63, 63, 18, 22, 18, 21, 22, 63, 22, 24, 22, 23, 24, 63, 64, 14, 63, 24, 64], "vertices": [1, 80, 29.55, 47.47, 1, 3, 83, -124.46, -190.15, 0.00032, 81, -69.86, 34.91, 0.00174, 80, 63.48, 53.58, 0.99794, 3, 83, -90.02, -178.6, 0.01104, 81, -34.88, 44.71, 0.13457, 80, 99.8, 52.87, 0.85439, 3, 83, -52.33, -168.87, 0.10516, 81, 3.24, 52.52, 0.63869, 80, 138.56, 49.35, 0.25615, 2, 83, -24.59, -51.29, 0.89022, 81, 36.88, 168.56, 0.10978, 2, 83, -41.63, -43.76, 0.9492, 81, 20.24, 176.94, 0.0508, 2, 83, -47.78, -29.11, 0.96116, 81, 14.83, 191.88, 0.03884, 2, 83, -37.32, -16.84, 0.97043, 81, 25.9, 203.6, 0.02957, 2, 83, -18.31, -7.74, 0.9889, 81, 45.35, 211.74, 0.0111, 1, 83, 2.62, 1.98, 1, 1, 83, 21.82, 10.45, 1, 1, 83, 44.67, 19.12, 1, 2, 84, -11.29, 19.16, 0.26025, 83, 69.67, 21.27, 0.73975, 2, 84, 12.01, 25.13, 0.94454, 83, 93.72, 21.63, 0.05546, 3, 85, 18.83, 26.79, 0.96151, 84, 78.87, 25.24, 0.03849, 83, 158.76, 6.13, 0, 1, 85, 67.6, 23.44, 1, 1, 85, 76.91, 22.8, 1, 1, 85, 76.25, 13.21, 1, 1, 85, 64.42, -9.42, 1, 2, 85, 74.56, -11.41, 1, 84, 131.46, -17.17, 0, 1, 85, 73.88, -21.26, 1, 2, 85, 63.34, -23.11, 0.99948, 84, 119.37, -27.96, 0.00052, 2, 85, 44.64, -34.96, 0.9601, 84, 99.8, -38.32, 0.0399, 2, 85, 35.17, -49.04, 0.89493, 84, 89.27, -51.63, 0.10507, 3, 85, 3.72, -47.91, 0.5399, 84, 58, -48.06, 0.44742, 83, 121.35, -60.27, 0.01268, 3, 85, -20.62, -51.78, 0.15952, 84, 33.44, -50.02, 0.70425, 83, 97.01, -56.44, 0.13623, 3, 85, -42.26, -56.73, 0.02506, 84, 11.48, -53.27, 0.53204, 83, 74.9, -54.48, 0.4429, 4, 85, -64.48, -67.3, 5e-05, 84, -11.5, -62.09, 0.2456, 83, 50.5, -57.69, 0.75394, 81, 111.55, 158.38, 0.00041, 3, 84, -30.47, -57.86, 0.09474, 83, 33.03, -49.15, 0.90057, 81, 94.54, 167.79, 0.00469, 3, 84, -49.96, -61.48, 0.015, 83, 13.24, -48.12, 0.95936, 81, 74.83, 169.82, 0.02564, 3, 84, -66.68, -69.11, 0.00058, 83, -4.8, -51.63, 0.90907, 81, 56.63, 167.22, 0.09035, 3, 83, -35.99, -178.75, 0.08498, 81, 19.07, 41.84, 0.72383, 80, 150.63, 34.55, 0.19119, 3, 83, -29.24, -196.03, 0.01647, 81, 24.94, 24.24, 0.93511, 80, 151.17, 16.01, 0.04842, 2, 83, -10.71, -190.01, 0.00171, 81, 43.75, 29.32, 0.99829, 2, 82, -30.08, 33.34, 0.0613, 81, 74.7, 33.31, 0.9387, 2, 82, 1.22, 40.81, 0.59966, 81, 105.98, 40.81, 0.40034, 2, 82, 44.12, 49.63, 0.99435, 81, 148.88, 49.67, 0.00565, 1, 82, 58.7, 37.24, 1, 1, 82, 62.04, 14.7, 1, 1, 82, 63.45, -19.81, 1, 1, 82, 68.88, -28.87, 1, 2, 82, 62.85, -41.9, 0.99975, 81, 167.69, -41.84, 0.00025, 2, 82, 40.55, -47.08, 0.96864, 81, 145.4, -47.04, 0.03136, 2, 82, 14.53, -50.19, 0.78857, 81, 119.38, -50.17, 0.21143, 2, 82, -0.03, -43.79, 0.53043, 81, 104.82, -43.79, 0.46957, 3, 82, -31.99, -48.56, 0.06253, 81, 72.87, -48.59, 0.9272, 80, 176.05, -67.55, 0.01027, 2, 81, 37.75, -53.93, 0.84578, 80, 140.89, -62.53, 0.15422, 3, 81, -2.87, -58.07, 0.31684, 80, 100.8, -54.77, 0.66973, 79, 43.22, -113.22, 0.01343, 3, 81, -37.28, -68.44, 0.03103, 80, 64.86, -54.77, 0.81793, 79, 31.37, -79.29, 0.15105, 3, 81, -65.9, -84.51, 9e-05, 80, 32.81, -61.9, 0.41384, 79, 14.08, -51.38, 0.58608, 2, 80, 10.71, -83.15, 0.05443, 79, -13.27, -37.52, 0.94557, 2, 80, 6.25, -109.6, 0.00103, 79, -39.71, -42.01, 0.99897, 1, 79, -78.13, -50.84, 1, 1, 79, -75.38, -44.13, 1, 1, 79, -53.03, -21.63, 1, 1, 79, -34.9, -9.8, 1, 2, 80, -20.88, -72.63, 0.00348, 79, -13.74, -4.22, 0.99652, 1, 79, -7.31, 5.44, 1, 1, 79, 24.66, 7.27, 1, 2, 80, -10.56, -3.18, 0.01967, 79, 55.23, 8.92, 0.98033, 2, 80, -3.76, 18.63, 0.90108, 79, 78.06, 9.69, 0.09892, 1, 80, 14.48, 39.21, 1, 1, 79, -69.3, -39.22, 1, 3, 85, 11.54, 16.25, 0.89159, 84, 70.78, 15.3, 0.10841, 83, 148.57, -1.65, 0, 3, 85, -4.47, 19.28, 0.3978, 84, 55.06, 19.56, 0.6022, 83, 134.28, 6.17, 0, 3, 85, -20.03, 19.44, 0.05789, 84, 39.55, 20.94, 0.94211, 83, 119.53, 11.13, 0, 1, 84, 22.15, 14.68, 1], "hull": 62, "edges": [104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 80, 82, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 6, 6, 4, 4, 2, 2, 0, 0, 122, 120, 122, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 124, 104, 106, 124, 106, 38, 36, 32, 34, 36, 34, 32, 30, 34, 30, 30, 28, 28, 126, 126, 128, 128, 130, 130, 132, 132, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 38, 40, 42, 40, 106, 108, 26, 28, 62, 60, 6, 8], "width": 480, "height": 275}}, "lian": {"lian": {"type": "mesh", "uvs": [0.86829, 0.02175, 0.87513, 0.07943, 0.87102, 0.1476, 0.87151, 0.21801, 0.87055, 0.27444, 0.89018, 0.3173, 0.87055, 0.35262, 0.83943, 0.37097, 0.85666, 0.40813, 0.84996, 0.45813, 0.8265, 0.50495, 0.84182, 0.54394, 0.85523, 0.59073, 0.87821, 0.62468, 0.89834, 0.67207, 0.92851, 0.6973, 0.97303, 0.734, 1, 0.78142, 1, 0.84463, 1, 0.91175, 0.97802, 0.91566, 0.9772, 0.95624, 0.95439, 0.98434, 0.87294, 1, 0.79475, 1, 0.71141, 1, 0.64542, 0.97965, 0.56398, 0.94844, 0.49368, 0.90016, 0.42608, 0.86504, 0.34707, 0.81978, 0.26399, 0.76348, 0.23711, 0.70963, 0.26073, 0.61988, 0.28516, 0.54495, 0.22082, 0.51686, 0.20452, 0.41893, 0.19068, 0.3362, 0.16706, 0.24176, 0.12552, 0.23006, 0.11493, 0.20977, 0.05954, 0.19416, 0.08153, 0.15357, 0, 0.07943, 0, 0.02175, 0.06494, 0.02502, 0.14156, 0.03027, 0.2216, 0.03289, 0.30575, 0.03748, 0.38579, 0.03289, 0.44056, 0, 0.54591, 0, 0.6369, 0, 0.72652, 0, 0.80193, 0, 0.77614, 0.47464, 0.63524, 0.29722, 0.60846, 0.32997, 0.57609, 0.41956, 0.57427, 0.49688, 0.6009, 0.56448, 0.63928, 0.59139, 0.71141, 0.59383, 0.76747, 0.59873, 0.8085, 0.58234, 0.81814, 0.56191, 0.68765, 0.27565, 0.73284, 0.26001, 0.77037, 0.27048, 0.79779, 0.29436, 0.81018, 0.31756, 0.81972, 0.34514, 0.80719, 0.42894, 0.81038, 0.47414, 0.80592, 0.52496, 0.49418, 0.64601, 0.85372, 0.64687, 0.83865, 0.61954, 0.83131, 0.57857, 0.82505, 0.54617, 0.81249, 0.51693, 0.81525, 0.49434, 0.82672, 0.47466, 0.83691, 0.42976, 0.82778, 0.39908, 0.83988, 0.33081, 0.85326, 0.28287, 0.83719, 0.21149, 0.78739, 0.21445, 0.74427, 0.18702, 0.67762, 0.17755, 0.60647, 0.19842, 0.54586, 0.23373, 0.53705, 0.23973, 0.47451, 0.24085, 0.43277, 0.25568, 0.41526, 0.30923, 0.39237, 0.34858, 0.3816, 0.39438, 0.38901, 0.43566, 0.39305, 0.47738, 0.40719, 0.51286, 0.43509, 0.5299, 0.46778, 0.54641, 0.47579, 0.6147, 0.5075, 0.69865, 0.54049, 0.74187, 0.58358, 0.74703, 0.61546, 0.72593, 0.62595, 0.74853, 0.64853, 0.75057, 0.6692, 0.73039, 0.69104, 0.7022, 0.71628, 0.67427, 0.74099, 0.65594, 0.76383, 0.65314, 0.79571, 0.67248, 0.82142, 0.68648, 0.83541, 0.69032, 0.85224, 0.69896, 0.87322, 0.6903, 0.86366, 0.67325, 0.84929, 0.6574, 0.54549, 0.36936, 0.55788, 0.38343, 0.57372, 0.39113, 0.58427, 0.39204, 0.60539, 0.39443, 0.62399, 0.39091, 0.6435, 0.38519, 0.65497, 0.37287, 0.65015, 0.36122, 0.6311, 0.3522, 0.60631, 0.34494, 0.60216, 0.34357, 0.57808, 0.34516, 0.55306, 0.34978, 0.54113, 0.36408, 0.542, 0.35207, 0.55652, 0.34047, 0.57976, 0.33467, 0.63569, 0.34325, 0.65582, 0.35462, 0.66406, 0.37364, 0.77808, 0.38816, 0.7846, 0.39553, 0.79369, 0.39754, 0.80145, 0.39652, 0.81303, 0.39285, 0.81934, 0.38124, 0.78251, 0.37566, 0.79625, 0.36495, 0.80917, 0.36282, 0.81959, 0.36976, 0.77692, 0.37566, 0.7902, 0.3616, 0.80534, 0.35535, 0.81229, 0.35759, 0.82196, 0.36521, 0.7615, 0.55369], "triangles": [43, 45, 42, 43, 44, 45, 95, 48, 49, 48, 38, 47, 94, 49, 50, 51, 94, 50, 46, 47, 42, 42, 45, 46, 41, 42, 40, 47, 40, 42, 36, 37, 98, 48, 95, 38, 95, 37, 38, 47, 38, 40, 40, 38, 39, 111, 24, 110, 117, 111, 112, 117, 112, 116, 112, 113, 116, 113, 114, 116, 114, 115, 116, 27, 109, 110, 27, 107, 109, 111, 110, 108, 110, 109, 108, 111, 108, 112, 112, 108, 61, 61, 108, 75, 113, 61, 62, 113, 112, 61, 61, 75, 60, 113, 62, 114, 77, 63, 64, 63, 77, 115, 115, 114, 63, 63, 114, 62, 62, 159, 63, 63, 159, 64, 62, 61, 159, 61, 60, 159, 127, 128, 58, 55, 159, 60, 129, 60, 128, 64, 65, 78, 64, 159, 65, 103, 59, 60, 128, 60, 59, 159, 74, 65, 65, 74, 79, 129, 55, 60, 159, 55, 74, 55, 129, 143, 55, 143, 144, 144, 142, 154, 154, 66, 67, 59, 102, 58, 73, 81, 74, 55, 73, 74, 128, 59, 58, 58, 126, 127, 125, 58, 124, 55, 72, 73, 72, 145, 146, 146, 147, 72, 72, 55, 145, 55, 144, 145, 154, 142, 66, 68, 154, 67, 82, 73, 72, 72, 147, 148, 58, 125, 126, 147, 146, 150, 151, 148, 147, 145, 150, 146, 128, 127, 133, 145, 144, 150, 147, 150, 151, 149, 151, 152, 127, 126, 133, 126, 134, 133, 125, 135, 126, 126, 135, 134, 125, 124, 135, 128, 132, 129, 128, 133, 132, 144, 154, 150, 129, 130, 143, 129, 131, 130, 129, 132, 131, 123, 136, 124, 124, 136, 135, 150, 154, 155, 150, 155, 151, 155, 154, 68, 143, 131, 142, 144, 143, 142, 143, 130, 131, 123, 137, 136, 151, 156, 152, 151, 155, 156, 137, 138, 136, 152, 156, 157, 156, 155, 70, 155, 68, 69, 142, 131, 141, 157, 156, 71, 156, 70, 71, 70, 155, 69, 131, 132, 141, 66, 142, 56, 132, 133, 141, 136, 138, 139, 136, 139, 135, 135, 140, 134, 135, 139, 140, 141, 133, 57, 133, 134, 57, 134, 140, 57, 57, 56, 141, 142, 141, 56, 56, 57, 140, 56, 91, 66, 68, 88, 69, 66, 90, 67, 91, 51, 52, 90, 52, 53, 1, 89, 54, 0, 1, 54, 111, 119, 24, 119, 120, 15, 119, 111, 117, 119, 117, 118, 28, 106, 107, 106, 29, 105, 30, 75, 105, 75, 31, 104, 104, 102, 103, 102, 104, 34, 102, 34, 101, 107, 108, 109, 107, 106, 108, 106, 105, 108, 120, 119, 121, 75, 108, 105, 119, 118, 121, 121, 118, 122, 120, 121, 14, 118, 117, 122, 117, 116, 122, 14, 121, 76, 116, 115, 77, 116, 77, 122, 121, 122, 76, 76, 13, 14, 122, 77, 76, 76, 77, 13, 75, 104, 60, 77, 12, 13, 64, 78, 77, 77, 78, 12, 104, 103, 60, 78, 11, 12, 11, 78, 79, 79, 78, 65, 103, 102, 59, 11, 79, 80, 34, 100, 101, 100, 34, 99, 79, 74, 80, 80, 10, 11, 124, 58, 100, 100, 58, 102, 80, 74, 81, 80, 81, 10, 99, 36, 98, 123, 124, 99, 100, 102, 101, 10, 82, 9, 10, 81, 82, 81, 73, 82, 99, 124, 100, 123, 99, 137, 137, 97, 96, 98, 97, 137, 9, 82, 83, 83, 82, 72, 9, 83, 8, 137, 96, 138, 94, 138, 96, 94, 96, 95, 137, 99, 98, 72, 84, 83, 83, 84, 8, 72, 148, 84, 84, 7, 8, 148, 149, 84, 84, 149, 7, 7, 149, 158, 98, 37, 97, 148, 151, 149, 149, 153, 158, 149, 152, 153, 6, 7, 85, 153, 152, 158, 152, 157, 158, 157, 71, 158, 7, 158, 85, 158, 71, 85, 139, 138, 94, 6, 85, 5, 93, 139, 94, 97, 37, 96, 71, 70, 85, 139, 93, 140, 96, 37, 95, 85, 86, 5, 85, 70, 86, 140, 93, 92, 56, 140, 92, 70, 69, 86, 86, 4, 5, 92, 91, 56, 69, 87, 86, 69, 88, 87, 86, 87, 4, 91, 90, 66, 4, 87, 3, 68, 67, 88, 67, 89, 88, 67, 90, 89, 95, 49, 94, 93, 94, 92, 94, 51, 92, 92, 51, 91, 87, 2, 3, 87, 88, 2, 2, 88, 89, 91, 52, 90, 90, 53, 89, 2, 89, 1, 89, 53, 54, 104, 33, 34, 99, 34, 36, 36, 34, 35, 75, 30, 31, 29, 30, 105, 32, 33, 31, 104, 31, 33, 26, 110, 25, 26, 27, 110, 27, 28, 107, 28, 29, 106, 22, 23, 20, 20, 23, 24, 119, 20, 24, 25, 110, 24, 18, 15, 16, 16, 17, 18, 120, 14, 15, 21, 22, 20, 15, 20, 119, 15, 18, 20, 19, 20, 18], "vertices": [1, 86, 83.85, 126.42, 1, 1, 86, 85.58, 111.25, 1, 1, 86, 84.54, 93.32, 1, 2, 86, 84.66, 74.8, 0.9, 87, 23.01, 68.6, 0.1, 2, 86, 84.42, 59.96, 0.9, 87, 22.77, 53.76, 0.1, 2, 86, 89.37, 48.69, 0.9, 87, 27.72, 42.49, 0.1, 2, 86, 84.42, 39.4, 0.9, 87, 22.77, 33.2, 0.1, 2, 86, 76.58, 34.58, 0.9, 87, 14.93, 28.37, 0.1, 2, 86, 80.92, 24.8, 0.9, 87, 19.27, 18.6, 0.1, 2, 86, 79.24, 11.65, 0.9, 87, 17.58, 5.45, 0.1, 2, 86, 73.32, -0.66, 0.9, 87, 11.67, -6.87, 0.1, 2, 86, 77.18, -10.92, 0.9, 87, 15.53, -17.12, 0.1, 2, 86, 80.56, -23.22, 0.9, 87, 18.91, -29.43, 0.1, 2, 86, 86.35, -32.15, 0.9, 87, 24.7, -38.36, 0.1, 3, 60, 3.74, 6.91, 0.9, 87, 29.77, -50.82, 0.1, 50, -183.68, 81.78, 0, 3, 60, 13.81, 6.21, 0.70711, 61, -3.34, 5.81, 0.19289, 87, 37.38, -57.46, 0.1, 2, 61, 11.42, 6.92, 0.9, 87, 48.6, -67.11, 0.1, 3, 65, 10.83, 21.16, 0.12847, 61, 25.04, 2.91, 0.77153, 87, 55.39, -79.58, 0.1, 3, 65, 25.05, 12.54, 0.7495, 61, 36.8, -8.85, 0.1505, 87, 55.39, -96.2, 0.1, 3, 65, 40.15, 3.39, 0.88335, 57, 22.34, 38.42, 0.01665, 87, 55.39, -113.86, 0.1, 3, 65, 38.15, -1.88, 0.80082, 57, 22.37, 32.79, 0.09918, 87, 49.85, -114.88, 0.1, 3, 65, 47.17, -7.59, 0.55406, 57, 32.83, 30.69, 0.34594, 87, 49.65, -125.56, 0.1, 3, 65, 50.52, -16.33, 0.41575, 57, 39.09, 23.72, 0.48425, 87, 43.9, -132.95, 0.1, 4, 65, 43.4, -36.02, 0.04541, 57, 39.5, 2.79, 0.85446, 63, -2.27, 81.22, 0.00013, 87, 23.37, -137.07, 0.1, 5, 57, 36, -16.6, 0.78778, 56, 60.21, -17.96, 0.00624, 62, 44.84, 61.9, 0.00232, 63, 8.76, 64.89, 0.10366, 87, 3.67, -137.07, 0.1, 5, 57, 32.27, -37.27, 0.5169, 56, 55.7, -38.47, 0.03343, 62, 52.94, 42.53, 0.01005, 63, 20.51, 47.48, 0.33962, 87, -17.33, -137.07, 0.1, 5, 57, 24.06, -52.68, 0.28425, 56, 46.9, -53.56, 0.03271, 62, 54.42, 25.12, 0.00633, 63, 25.37, 30.7, 0.57672, 87, -33.96, -131.71, 0.1, 4, 57, 12.33, -71.43, 0.04827, 56, 34.47, -71.84, 0.00484, 63, 30.05, 9.1, 0.84689, 87, -54.49, -123.5, 0.1, 4, 62, 49.88, -18.22, 4e-05, 63, 29.44, -12.69, 0.7413, 59, 58.65, 34.27, 0.15866, 87, -72.2, -110.81, 0.1, 4, 62, 47.94, -37.5, 0.01034, 63, 31.32, -31.97, 0.34133, 59, 52.31, 15.96, 0.54833, 87, -89.24, -101.57, 0.1, 3, 63, 32.59, -55.14, 0.00985, 59, 43.8, -5.62, 0.89015, 87, -109.15, -89.67, 0.1, 3, 59, 32.59, -28.69, 0.87801, 58, 68.82, -22.67, 0.02199, 87, -130.08, -74.86, 0.1, 4, 59, 19.72, -37.67, 0.79336, 58, 57.79, -33.84, 0.10512, 87, -136.86, -60.7, 0.1, 53, -36.98, 132.07, 0.00151, 4, 59, -4.54, -35.63, 0.40879, 58, 33.56, -36.24, 0.46659, 87, -130.9, -37.09, 0.1, 53, -38.08, 107.75, 0.02462, 6, 59, -24.99, -32.76, 0.05602, 58, 12.94, -37.12, 0.71281, 86, -63.09, -11.18, 0.00045, 87, -124.75, -17.39, 0.1, 53, -40.16, 87.21, 0.12996, 50, -26.37, 97.54, 0.00076, 5, 59, -29.64, -49.96, 0.00287, 58, 11.48, -54.88, 0.6543, 86, -79.31, -3.79, 0.00026, 87, -140.96, -10, 0.1, 53, -22.8, 83.22, 0.24257, 5, 58, -11.36, -67.47, 0.48392, 86, -83.41, 21.96, 0.01607, 87, -145.07, 15.76, 0.1, 53, -13.61, 58.81, 0.38732, 50, 3.17, 72.26, 0.0127, 5, 58, -30.65, -78.12, 0.26766, 86, -86.9, 43.72, 0.01839, 87, -148.56, 37.51, 0.1, 53, -5.83, 38.19, 0.59047, 50, 13.19, 52.63, 0.02347, 5, 58, -52.01, -92.13, 0.0359, 86, -92.86, 68.56, 0.00175, 87, -154.51, 62.35, 0.1, 53, 4.97, 15.06, 0.81355, 54, -6.91, 17.29, 0.0488, 5, 58, -51.36, -103.02, 0.00386, 86, -103.32, 71.64, 0, 87, -164.98, 65.43, 0.1, 53, 15.85, 14.14, 0.59554, 54, 3.4, 13.72, 0.30059, 4, 58, -55.47, -107.34, 0.00082, 87, -167.65, 70.77, 0.1, 53, 19.53, 9.44, 0.30996, 54, 5.81, 8.26, 0.58922, 4, 87, -181.6, 74.87, 0.1, 54, 19.56, 3.5, 0.8987, 51, 25.08, 28.74, 0.00102, 52, -10.16, 27.26, 0.00028, 4, 87, -176.06, 85.54, 0.1, 54, 13.52, -6.9, 0.62864, 51, 23.77, 16.78, 0.20569, 52, -9.06, 15.29, 0.06567, 3, 87, -196.61, 105.04, 0.1, 54, 33.11, -27.35, 0.00167, 52, 18.76, 9.97, 0.89833, 2, 87, -196.61, 120.21, 0.1, 52, 26.95, -2.8, 0.9, 3, 87, -180.24, 119.35, 0.1, 51, 39.9, -13.22, 0.09106, 52, 12.71, -10.91, 0.80894, 4, 87, -160.93, 117.97, 0.1, 51, 21.4, -18.92, 0.73331, 50, 49.73, -20.11, 0.06168, 52, -4.29, -20.18, 0.10501, 3, 87, -140.76, 117.28, 0.1, 51, 2.35, -25.58, 0.4422, 50, 30.33, -25.67, 0.4578, 4, 86, -57.91, 122.28, 0.00886, 87, -119.56, 116.08, 0.1, 51, -17.86, -32.12, 0.08323, 50, 9.78, -31.05, 0.80792, 4, 86, -37.73, 123.49, 0.06327, 87, -99.39, 117.28, 0.1, 51, -36.23, -40.54, 0.00341, 50, -9.04, -38.4, 0.83332, 3, 86, -23.93, 132.14, 0.1072, 87, -85.59, 125.93, 0.1, 50, -19.51, -50.88, 0.7928, 3, 86, 2.62, 132.14, 0.17309, 87, -59.04, 125.93, 0.1, 50, -44.77, -59.05, 0.72691, 1, 86, 25.54, 132.14, 1, 1, 86, 48.13, 132.14, 1, 1, 86, 67.13, 132.14, 1, 1, 87, -1.02, 1.1, 1, 2, 86, 25.13, 53.97, 0.4, 87, -36.53, 47.76, 0.6, 2, 86, 18.38, 45.36, 0.4, 87, -43.28, 39.15, 0.6, 2, 86, 10.22, 21.8, 0.4, 87, -51.43, 15.59, 0.6, 2, 86, 9.76, 1.46, 0.4, 87, -51.89, -4.74, 0.6, 2, 86, 16.47, -16.32, 0.4, 87, -45.18, -22.52, 0.6, 2, 86, 26.14, -23.4, 0.2824, 87, -35.51, -29.6, 0.7176, 2, 86, 44.32, -24.04, 0.15, 87, -17.33, -30.24, 0.85, 2, 86, 58.45, -25.32, 0.15, 87, -3.2, -31.53, 0.85, 2, 86, 68.79, -21.01, 0.1908, 87, 7.13, -27.22, 0.8092, 2, 86, 71.22, -15.64, 0.3053, 87, 9.56, -21.85, 0.6947, 2, 86, 38.33, 59.65, 0.4, 87, -23.32, 53.44, 0.6, 2, 86, 49.72, 63.76, 0.4, 87, -11.93, 57.55, 0.6, 2, 86, 59.18, 61, 0.4, 87, -2.47, 54.8, 0.6, 2, 86, 66.09, 54.72, 0.4, 87, 4.43, 48.52, 0.6, 2, 86, 69.21, 48.62, 0.4, 87, 7.56, 42.42, 0.6, 2, 86, 71.62, 41.37, 0.4, 87, 9.96, 35.16, 0.6, 2, 86, 68.46, 19.33, 0.4, 87, 6.8, 13.12, 0.6, 2, 86, 69.26, 7.44, 0.4, 87, 7.61, 1.24, 0.6, 2, 86, 68.14, -5.92, 0.4, 87, 6.48, -12.13, 0.6, 2, 86, -10.42, -37.76, 0.7, 87, -72.08, -43.97, 0.3, 2, 86, 80.18, -37.99, 0.7, 87, 18.53, -44.19, 0.3, 2, 86, 76.38, -30.8, 0.7, 87, 14.73, -37.01, 0.3, 2, 86, 74.53, -20.02, 0.7, 87, 12.88, -26.23, 0.3, 2, 86, 72.96, -11.5, 0.7, 87, 11.3, -17.71, 0.3, 2, 86, 69.79, -3.81, 0.7, 87, 8.14, -10.02, 0.3, 2, 86, 70.49, 2.13, 0.7, 87, 8.83, -4.08, 0.3, 2, 86, 73.38, 7.31, 0.7, 87, 11.73, 1.1, 0.3, 2, 86, 75.95, 19.11, 0.7, 87, 14.29, 12.91, 0.3, 2, 86, 73.65, 27.18, 0.7, 87, 11.99, 20.98, 0.3, 2, 86, 76.69, 45.14, 0.7, 87, 15.04, 38.93, 0.3, 2, 86, 80.07, 57.75, 0.7, 87, 18.41, 51.54, 0.3, 2, 86, 76.02, 76.52, 0.7, 87, 14.36, 70.31, 0.3, 2, 86, 63.47, 75.74, 0.7, 87, 1.81, 69.53, 0.3, 2, 86, 52.6, 82.95, 0.7, 87, -9.05, 76.75, 0.3, 2, 86, 35.81, 85.45, 0.7, 87, -25.85, 79.24, 0.3, 2, 86, 17.87, 79.96, 0.7, 87, -43.78, 73.75, 0.3, 2, 86, 2.6, 70.67, 0.7, 87, -59.05, 64.46, 0.3, 2, 86, 0.38, 69.09, 0.7, 87, -61.27, 62.88, 0.3, 2, 86, -15.38, 68.8, 0.7, 87, -77.03, 62.59, 0.3, 2, 86, -25.9, 64.9, 0.7, 87, -87.55, 58.69, 0.3, 2, 86, -30.31, 50.81, 0.7, 87, -91.96, 44.61, 0.3, 2, 86, -36.08, 40.46, 0.7, 87, -97.73, 34.26, 0.3, 2, 86, -38.79, 28.42, 0.7, 87, -100.44, 22.21, 0.3, 2, 86, -36.92, 17.56, 0.7, 87, -98.58, 11.35, 0.3, 2, 86, -35.91, 6.59, 0.7, 87, -97.56, 0.38, 0.3, 2, 86, -32.34, -2.74, 0.7, 87, -94, -8.95, 0.3, 2, 86, -25.31, -7.22, 0.7, 87, -86.97, -13.43, 0.3, 2, 86, -17.07, -11.56, 0.7, 87, -78.73, -17.77, 0.3, 2, 86, -15.05, -29.53, 0.7, 87, -76.71, -35.73, 0.3, 2, 86, -7.06, -51.6, 0.7, 87, -68.72, -57.81, 0.3, 2, 86, 1.25, -62.97, 0.7, 87, -60.4, -69.18, 0.3, 2, 86, 12.11, -64.33, 0.542, 87, -49.55, -70.53, 0.458, 2, 86, 20.14, -58.78, 0.4733, 87, -41.51, -64.99, 0.5267, 2, 86, 22.78, -64.72, 0.5038, 87, -38.87, -70.93, 0.4962, 2, 86, 28.48, -65.26, 0.4733, 87, -33.18, -71.47, 0.5267, 2, 86, 33.68, -59.95, 0.4427, 87, -27.97, -66.16, 0.5573, 2, 86, 39.19, -52.54, 0.3969, 87, -22.47, -58.75, 0.6031, 2, 86, 45.55, -45.19, 0.3664, 87, -16.11, -51.4, 0.6336, 2, 86, 51.77, -40.37, 0.374, 87, -9.88, -46.58, 0.626, 2, 86, 57.53, -39.63, 0.3969, 87, -4.12, -45.84, 0.6031, 2, 86, 65.57, -44.72, 0.5115, 87, 3.91, -50.93, 0.4885, 2, 86, 72.04, -48.4, 0.5115, 87, 10.39, -54.61, 0.4885, 2, 86, 75.57, -49.41, 0.5649, 87, 13.91, -55.62, 0.4351, 2, 86, 79.81, -51.69, 0.7, 87, 18.16, -57.89, 0.3, 2, 86, 85.1, -49.41, 0.7, 87, 23.44, -55.62, 0.3, 2, 86, 82.69, -44.92, 0.7, 87, 21.03, -51.13, 0.3, 2, 86, 79.07, -40.75, 0.7, 87, 17.41, -46.96, 0.3, 2, 86, 2.51, 35, 0.47054, 87, -59.15, 28.79, 0.52946, 2, 86, 5.63, 31.3, 0.44485, 87, -56.02, 25.09, 0.55515, 2, 86, 9.62, 29.27, 0.41677, 87, -52.03, 23.07, 0.58323, 2, 86, 12.28, 29.03, 0.4, 87, -49.37, 22.83, 0.6, 2, 86, 17.6, 28.41, 0.35965, 87, -44.05, 22.2, 0.64035, 2, 86, 22.29, 29.33, 0.36398, 87, -39.36, 23.13, 0.63602, 2, 86, 27.21, 30.84, 0.36894, 87, -34.45, 24.63, 0.63106, 2, 86, 30.1, 34.07, 0.37369, 87, -31.55, 27.87, 0.62631, 2, 86, 28.88, 37.14, 0.37832, 87, -32.77, 30.93, 0.62168, 2, 86, 24.08, 39.51, 0.38729, 87, -37.57, 33.31, 0.61271, 2, 86, 17.84, 41.42, 0.39815, 87, -43.82, 35.21, 0.60185, 2, 86, 16.79, 41.78, 0.4, 87, -44.86, 35.58, 0.6, 2, 86, 10.72, 41.36, 0.44023, 87, -50.93, 35.16, 0.55977, 2, 86, 4.42, 40.15, 0.48625, 87, -57.24, 33.94, 0.51375, 2, 86, 1.41, 36.39, 0.48234, 87, -60.24, 30.18, 0.51766, 2, 86, 1.63, 39.55, 0.49643, 87, -60.02, 33.34, 0.50357, 2, 86, 5.29, 42.6, 0.49403, 87, -56.36, 36.39, 0.50597, 2, 86, 11.15, 44.12, 0.45374, 87, -50.51, 37.92, 0.54626, 2, 86, 25.24, 41.87, 0.38799, 87, -36.41, 35.66, 0.61201, 2, 86, 30.31, 38.88, 0.37952, 87, -31.34, 32.67, 0.62048, 2, 86, 32.39, 33.87, 0.37418, 87, -29.27, 27.67, 0.62582, 2, 86, 61.12, 30.05, 0.35991, 87, -0.53, 23.85, 0.64009, 2, 86, 62.77, 28.12, 0.36947, 87, 1.11, 21.91, 0.63053, 2, 86, 65.06, 27.59, 0.38546, 87, 3.4, 21.38, 0.61454, 2, 86, 67.01, 27.86, 0.4, 87, 5.36, 21.65, 0.6, 2, 86, 69.93, 28.82, 0.55901, 87, 8.28, 22.61, 0.44099, 2, 86, 71.52, 31.88, 0.7, 87, 9.86, 25.67, 0.3, 2, 86, 62.24, 33.34, 0.36629, 87, 0.58, 27.13, 0.63371, 2, 86, 65.7, 36.16, 0.37933, 87, 4.05, 29.95, 0.62067, 2, 86, 68.96, 36.72, 0.4, 87, 7.3, 30.51, 0.6, 2, 86, 71.58, 34.89, 0.7, 87, 9.93, 28.69, 0.3, 2, 86, 60.83, 33.34, 0.36554, 87, -0.83, 27.13, 0.63446, 2, 86, 64.18, 37.04, 0.37683, 87, 2.52, 30.83, 0.62317, 2, 86, 67.99, 38.68, 0.39025, 87, 6.34, 32.48, 0.60975, 2, 86, 69.74, 38.09, 0.4, 87, 8.09, 31.89, 0.6, 2, 86, 72.18, 36.09, 0.7, 87, 10.53, 29.89, 0.3, 2, 86, 56.94, -13.48, 0.1221, 87, -4.71, -19.69, 0.8779], "hull": 55, "edges": [86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 48, 50, 46, 48, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 108, 106, 108, 104, 106, 102, 104, 100, 102, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 86, 88, 90, 88, 112, 114, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 112, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 144, 146, 146, 148, 148, 130, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 208, 150, 206, 208, 150, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 152, 244, 242, 244, 246, 248, 248, 250, 252, 116, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 114, 268, 266, 268, 268, 270, 270, 272, 272, 274, 274, 246, 274, 276, 276, 278, 278, 280, 280, 114, 114, 282, 282, 284, 284, 286, 286, 258, 288, 290, 290, 292, 294, 144, 292, 294, 294, 296, 168, 298, 296, 298, 288, 300, 300, 302, 302, 304, 304, 306, 288, 308, 308, 310, 310, 312, 142, 314, 314, 304, 312, 314, 170, 316, 316, 306, 314, 316, 306, 298, 316, 298], "width": 252, "height": 263}}, "shenti": {"shenti": {"type": "mesh", "uvs": [0.6864, 0.01972, 0.74199, 0.06596, 0.76764, 0.16542, 0.85851, 0.21602, 0.8863, 0.30413, 0.94509, 0.37829, 1, 0.43762, 1, 0.48109, 1, 0.52456, 0.97032, 0.5672, 0.95673, 0.61918, 0.9324, 0.677, 0.93311, 0.7195, 0.9188, 0.76271, 0.90663, 0.82462, 0.8823, 0.84331, 0.89232, 0.89332, 0.90592, 0.94588, 0.90162, 1, 0.54359, 1, 0.43937, 0.98798, 0.34807, 0.96548, 0.34462, 0.91417, 0.33687, 0.86777, 0.31533, 0.84036, 0.29208, 0.83754, 0.21369, 0.7799, 0.14112, 0.72802, 0.07135, 0.66194, 0.03173, 0.58883, 0, 0.5, 0, 0.42008, 0, 0.33605, 0.03745, 0.26517, 0.12081, 0.22687, 0.21667, 0.17556, 0.32594, 0.11919, 0.43521, 0.07255, 0.50428, 0, 0.63936, 0, 0.61097, 0.04811, 0.57816, 0.1631, 0.46235, 0.27337, 0.41216, 0.41672, 0.44884, 0.53486, 0.53569, 0.653, 0.54728, 0.7743, 0.5299, 0.90505, 0.18705, 0.36827], "triangles": [30, 31, 48, 29, 30, 48, 43, 28, 29, 48, 43, 29, 44, 28, 43, 28, 44, 27, 26, 27, 44, 45, 26, 44, 26, 24, 25, 26, 45, 24, 46, 24, 45, 23, 24, 46, 47, 23, 46, 47, 46, 15, 22, 23, 47, 20, 21, 22, 47, 20, 22, 19, 47, 16, 20, 47, 19, 13, 45, 11, 13, 11, 12, 13, 15, 46, 14, 15, 13, 47, 15, 16, 19, 16, 17, 18, 19, 17, 9, 45, 44, 10, 45, 9, 11, 45, 10, 46, 45, 13, 36, 37, 42, 48, 34, 35, 33, 34, 48, 32, 33, 48, 35, 36, 42, 42, 48, 35, 43, 48, 42, 31, 32, 48, 43, 42, 3, 40, 38, 39, 40, 39, 0, 40, 37, 38, 41, 37, 40, 40, 0, 1, 42, 37, 41, 41, 40, 1, 41, 1, 2, 2, 42, 41, 5, 6, 7, 44, 5, 7, 44, 43, 4, 2, 3, 42, 4, 43, 3, 44, 4, 5, 7, 9, 44, 8, 9, 7], "vertices": [2, 66, -72.19, 191.19, 0.00164, 19, 175.54, -39.44, 0.99836, 2, 66, -32.19, 168.89, 0.02868, 19, 140.09, -68.43, 0.97132, 2, 66, 1.88, 105.62, 0.32506, 19, 68.73, -76.84, 0.67494, 2, 66, 62.28, 86.12, 0.72964, 19, 28.37, -125.83, 0.27036, 2, 66, 95.26, 30.89, 0.96499, 19, -35.12, -136.18, 0.03501, 4, 66, 142.61, -9.82, 0.94807, 19, -90.39, -165.24, 7e-05, 18, 76.95, -140.76, 0.03154, 17, 292.52, -104.13, 0.02032, 5, 66, 184.85, -41.11, 0.7231, 19, -135.02, -193.01, 0.00011, 18, 27.14, -157.55, 0.12298, 17, 251.95, -137.56, 0.15046, 16, 346.38, -181.63, 0.00334, 5, 66, 193.53, -70.59, 0.58977, 19, -165.63, -190.24, 0.0001, 18, -2.02, -147.83, 0.15036, 17, 221.25, -138.86, 0.24914, 16, 316, -176.98, 0.01063, 5, 66, 202.21, -100.08, 0.4549, 19, -196.24, -187.47, 9e-05, 18, -31.17, -138.11, 0.15386, 17, 190.54, -140.16, 0.36684, 16, 285.62, -172.33, 0.02431, 5, 66, 194.3, -133.83, 0.29102, 19, -224.71, -167.7, 6e-05, 18, -54.36, -112.34, 0.12077, 17, 159.7, -124.33, 0.52806, 16, 258.41, -150.85, 0.06009, 5, 66, 197.16, -171.3, 0.14149, 19, -260.61, -156.57, 3e-05, 18, -86.74, -93.27, 0.04585, 17, 122.65, -118.05, 0.66103, 16, 223.27, -137.54, 0.15159, 5, 66, 195.24, -214.48, 0.0461, 19, -300.05, -138.9, 1e-05, 18, -121.08, -67.03, 0.00366, 17, 81.21, -105.75, 0.58508, 16, 184.98, -117.48, 0.36515, 4, 66, 204.12, -243.18, 0.01659, 19, -330.01, -136.6, 1e-05, 17, 51.21, -107.44, 0.39524, 16, 155.22, -113.35, 0.58816, 4, 66, 204.83, -274.83, 0.00446, 19, -359.7, -125.62, 0, 17, 20.33, -100.48, 0.18187, 16, 126.26, -100.56, 0.81367, 4, 66, 210.46, -318.8, 5e-05, 19, -402.66, -114.69, 0, 17, -23.7, -95.32, 0.01108, 16, 84.06, -87.01, 0.98886, 2, 19, -414.55, -99.51, 0, 16, 73.12, -71.13, 1, 2, 19, -450.29, -102.08, 0, 16, 37.29, -71.5, 1, 1, 16, -0.63, -73.64, 1, 1, 16, -38.07, -65.4, 1, 3, 17, -156.45, 108.72, 0.07946, 16, -6.84, 138.8, 0.22641, 76, -74.56, -41.06, 0.69413, 3, 17, -150.5, 169.16, 0.00841, 16, 10.65, 196.97, 0.03132, 76, -50.77, 14.82, 0.96028, 1, 76, -21.76, 61.58, 1, 2, 76, 13.8, 54.1, 0.99936, 77, -76.69, 61.04, 0.00064, 2, 76, 46.64, 49.92, 0.94679, 77, -44.34, 54, 0.05321, 2, 76, 68.58, 56.9, 0.71894, 77, -21.87, 59.04, 0.28106, 2, 76, 73.98, 69.35, 0.56153, 77, -15.4, 70.96, 0.43847, 3, 76, 125.06, 102.47, 0.11208, 77, 38.38, 99.49, 0.84713, 78, -123.85, 76.33, 0.04079, 4, 72, 303.42, 286.83, 1e-05, 76, 171.34, 133.41, 0.00952, 77, 87.18, 126.27, 0.77187, 78, -81.36, 112.3, 0.2186, 3, 72, 339.13, 236.55, 0.00945, 77, 144.86, 148.09, 0.47876, 78, -29.19, 145.18, 0.51179, 3, 72, 357.04, 182.94, 0.05554, 77, 201.26, 151.86, 0.22801, 78, 25.32, 160.12, 0.71645, 3, 72, 369.39, 118.7, 0.20015, 77, 266.53, 147.54, 0.05752, 78, 90.14, 168.91, 0.74233, 3, 72, 364.09, 62.45, 0.4368, 77, 319.61, 128.19, 0.00759, 78, 146.02, 160.53, 0.55561, 2, 72, 358.52, 3.3, 0.73145, 78, 204.77, 151.72, 0.26855, 2, 72, 332.31, -44.57, 0.90681, 78, 251.13, 122.91, 0.09319, 3, 19, 59.11, 298.78, 0.002, 72, 281.89, -67.02, 0.99153, 78, 270.77, 71.33, 0.00647, 2, 19, 90.25, 240.43, 0.04336, 72, 223.42, -97.95, 0.95664, 2, 19, 124.26, 174.04, 0.23471, 72, 156.92, -131.73, 0.76529, 2, 19, 151.42, 108.28, 0.6373, 72, 91.06, -158.65, 0.3627, 1, 19, 198.91, 63.96, 1, 1, 19, 191.88, -13.67, 1, 2, 19, 159.48, 5.71, 0.99603, 72, -11.53, -166.35, 0.00397, 2, 19, 80.22, 31.9, 0.84057, 72, 14.94, -87.18, 0.15943, 2, 19, 8.6, 105.48, 0.03795, 72, 88.77, -15.83, 0.96205, 5, 72, 127.1, 82.36, 0.36522, 18, 148.42, 159.55, 0.11792, 17, 252.36, 201.94, 0.00439, 77, 240.38, -96.06, 0.02705, 78, 113.1, -75.01, 0.48541, 5, 72, 113.86, 167.5, 0.07439, 18, 62.49, 165.89, 0.18347, 17, 169.8, 177.27, 0.07445, 77, 154.66, -87.32, 0.29336, 78, 27.35, -83.55, 0.37432, 6, 72, 71.79, 255.36, 0.00611, 18, -32.61, 144.76, 0.09091, 17, 88.47, 123.66, 0.39284, 76, 163.56, -100.23, 0.05126, 77, 59.02, -105.79, 0.43273, 78, -62.68, -120.72, 0.02614, 5, 18, -116.07, 165.54, 0.00434, 17, 3.07, 113.35, 0.43886, 16, 150.57, 112.58, 0.00193, 76, 79.01, -84.46, 0.38331, 77, -23.84, -82.69, 0.17155, 3, 17, -89.71, 119.45, 0.14737, 16, 60.71, 136.46, 0.11145, 76, -7.68, -50.83, 0.74118, 2, 72, 253.21, 36.09, 0.58196, 78, 166.23, 48.36, 0.41804], "hull": 40, "edges": [78, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 76, 78, 74, 76, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 38, 12, 14, 14, 16, 88, 14], "width": 577, "height": 707}}, "tongkong": {"tongkong": {"type": "mesh", "uvs": [0.82865, 0.34784, 1, 0.30904, 1, 1, 0.83418, 1, 0.23566, 1, 0, 1, 0, 0, 0.23197, 0], "triangles": [2, 3, 0, 4, 0, 3, 2, 0, 1, 5, 6, 7, 4, 7, 0, 5, 7, 4], "vertices": [1, 90, 1.29, 8.53, 1, 1, 90, 6.44, -0.19, 1, 1, 90, -2.14, -4.66, 1, 1, 90, -6.66, 4.02, 1, 1, 89, -3.79, -11.4, 1, 1, 89, -10.21, 0.94, 1, 1, 89, 2.21, 7.4, 1, 1, 89, 8.53, -4.74, 1], "hull": 8, "edges": [10, 12, 12, 14, 8, 10, 14, 8, 4, 2, 2, 0, 4, 6, 6, 8, 0, 6, 0, 14], "width": 59, "height": 14}}, "toufa1": {"toufa1": {"type": "mesh", "uvs": [0.7255, 0.05268, 0.85611, 0.21827, 0.95334, 0.43153, 1, 0.72507, 1, 1, 0.97802, 1, 0.89094, 0.79783, 0.74437, 0.60715, 0.56441, 0.50429, 0.37865, 0.61217, 0.23353, 0.85303, 0.13775, 1, 0.09421, 1, 0, 0.86808, 0, 0.71755, 0, 0.55698, 0.0768, 0.27096, 0.21902, 0.06523, 0.35689, 0, 0.56877, 0], "triangles": [10, 15, 16, 10, 14, 15, 16, 9, 10, 12, 13, 14, 10, 12, 14, 11, 12, 10, 8, 18, 19, 9, 18, 8, 17, 18, 9, 16, 17, 9, 8, 19, 0, 7, 8, 0, 1, 7, 0, 7, 1, 2, 6, 7, 2, 3, 6, 2, 5, 6, 3, 5, 3, 4], "vertices": [2, 23, 13.15, 19.03, 0.99779, 22, 51.48, 9.86, 0.00221, 2, 24, -9.69, 10.34, 0.17635, 23, 28.94, 14.15, 0.82365, 2, 24, 6.18, 12.57, 0.97789, 23, 42.42, 5.5, 0.02211, 1, 24, 23.54, 7.97, 1, 1, 24, 37.55, -0.19, 1, 1, 24, 36.43, -2.13, 1, 1, 24, 21.65, -3.8, 1, 2, 24, 4.4, -11.06, 0.42011, 23, 25.66, -11.26, 0.57989, 3, 23, 6.33, -11.52, 0.67977, 22, 30.25, -13.14, 0.31948, 21, 38.44, -31.13, 0.00075, 3, 23, -9.5, -23.72, 0.00679, 22, 10.43, -15.75, 0.65055, 21, 23.06, -18.35, 0.34266, 2, 22, -6.82, -26.86, 0.01988, 21, 3.19, -13.23, 0.98012, 1, 21, -9.32, -9.47, 1, 1, 21, -11.65, -5.69, 1, 1, 21, -10.08, 6.57, 1, 1, 21, -2.52, 11.24, 1, 1, 21, 5.54, 16.22, 1, 2, 22, -15.93, 9.91, 0.12288, 21, 24.01, 18.42, 0.87712, 2, 22, 0.64, 19.04, 0.86407, 21, 41.96, 12.46, 0.13593, 3, 23, -23.4, 9.69, 0.00829, 22, 15.18, 20.12, 0.99167, 21, 52.63, 2.52, 3e-05, 2, 23, -2.97, 16.75, 0.63956, 22, 36.39, 15.98, 0.36044], "hull": 20, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 8, 10, 12, 10, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4, 8, 6, 4, 6], "width": 102, "height": 59}}, "toufa2": {"toufa2": {"type": "mesh", "uvs": [0.45012, 0.17018, 0.53163, 0.39168, 0.6288, 0.58532, 0.83256, 0.78628, 1, 0.91684, 1, 1, 0.60059, 1, 0.29025, 0.86843, 0, 0.63519, 0, 0.40048, 0.11783, 0.18778, 0.1749, 0, 0.34981, 0], "triangles": [7, 2, 3, 6, 7, 3, 6, 3, 4, 6, 4, 5, 1, 8, 9, 2, 7, 8, 2, 8, 1, 1, 9, 10, 10, 11, 12, 0, 10, 12, 10, 0, 1], "vertices": [2, 28, 12.16, -12.92, 0.82705, 27, 44.36, -13.56, 0.17295, 2, 27, 10.23, -21.61, 0.63142, 26, 42.99, -23.78, 0.36858, 3, 27, -19.48, -30.54, 0.00837, 26, 11.99, -22.37, 0.73481, 25, 41.01, -25.11, 0.25682, 2, 26, -22.23, -28.14, 0.00638, 25, 6.86, -18.96, 0.99362, 1, 25, -16.82, -17.04, 1, 1, 25, -27.37, -9.49, 1, 1, 25, -10.39, 14.22, 1, 2, 26, -23.77, 13.44, 0.00909, 25, 19.48, 20.69, 0.99091, 2, 26, 17.01, 23.91, 0.99897, 25, 61.4, 16.74, 0.00103, 3, 28, -28.21, 14.29, 0.02253, 27, 6.49, 17.04, 0.76604, 26, 52.24, 13.94, 0.21143, 1, 28, 5.87, 10.67, 1, 1, 28, 35.46, 10.86, 1, 1, 28, 37.34, -1.77, 1], "hull": 13, "edges": [24, 0, 0, 2, 2, 4, 4, 6, 10, 8, 6, 8, 22, 24, 22, 20, 20, 18, 16, 18, 16, 14, 10, 12, 14, 12], "width": 73, "height": 156}}, "toufa3": {"toufa3": {"type": "mesh", "uvs": [1, 0.05334, 0.90928, 0.20567, 0.79254, 0.34921, 0.6692, 0.50593, 0.61524, 0.5843, 0.56127, 0.66266, 0.4996, 0.84575, 0.5, 1, 0.25, 1, 0.12494, 0.89066, 0, 0.72738, 0, 0.59106, 0, 0.45475, 0.18703, 0.33605, 0.36553, 0.26637, 0.54597, 0.19799, 0.72642, 0.10509, 0.92432, 0, 1, 0], "triangles": [0, 16, 17, 0, 1, 16, 17, 18, 0, 2, 14, 15, 2, 15, 1, 1, 15, 16, 4, 13, 3, 3, 13, 14, 3, 14, 2, 10, 11, 5, 5, 11, 4, 11, 12, 4, 12, 13, 4, 8, 6, 7, 8, 9, 6, 9, 10, 6, 6, 10, 5], "vertices": [1, 37, 31.22, -7.24, 1, 3, 35, 86.09, -18.12, 0.00867, 36, 42.77, -14.43, 0.41177, 37, -0.69, -14.68, 0.57955, 3, 34, 83.84, -49.46, 0.00255, 35, 54.44, -26.1, 0.57044, 36, 12.11, -25.61, 0.427, 3, 33, 65.08, -60.21, 0.00102, 34, 49.58, -40.63, 0.26388, 35, 20.26, -35.22, 0.73509, 3, 33, 52.28, -48.67, 0.03284, 34, 32.68, -37.21, 0.59026, 35, 3.86, -40.53, 0.3769, 3, 33, 39.48, -37.13, 0.21261, 34, 15.79, -33.8, 0.68311, 35, -12.53, -45.85, 0.10428, 2, 33, 7, -18.33, 0.95952, 34, -21.74, -34.15, 0.04048, 1, 33, -22.45, -9.14, 1, 1, 33, -12.49, 22.59, 1, 2, 33, 13.36, 31.9, 0.98436, 34, -41.84, 12.32, 0.01564, 2, 33, 49.49, 37.97, 0.45857, 34, -13.83, 35.94, 0.54143, 3, 33, 75.5, 29.8, 0.06234, 34, 12.72, 42.15, 0.93435, 35, -52.48, 18.82, 0.00331, 3, 34, 39.26, 48.36, 0.9014, 35, -32.4, 37.26, 0.09859, 36, -80.78, 28.51, 1e-05, 3, 34, 68.05, 29.55, 0.34098, 35, 1.91, 35, 0.62086, 36, -46.41, 29.78, 0.03815, 3, 34, 87.03, 9.61, 0.00567, 35, 28.23, 26.94, 0.65772, 36, -19.4, 24.46, 0.33661, 3, 35, 54.54, 18.51, 0.00343, 36, 7.63, 18.77, 0.99544, 37, -26.95, 25.91, 0.00113, 2, 36, 37.91, 16.76, 0.48462, 37, 1.99, 16.77, 0.51538, 1, 37, 34.25, 7.11, 1, 1, 37, 39.99, -1.17, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 34, 36, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 6, 8, 8, 10, 24, 8], "width": 133, "height": 200}}, "toufa4": {"toufa4": {"type": "mesh", "uvs": [1, 0.06125, 0.93352, 0.27665, 0.78184, 0.51257, 0.49869, 0.67669, 0.31414, 0.84697, 0.16245, 1, 0.08123, 1, 0, 0.81373, 0, 0.62746, 0.06639, 0.41205, 0.25852, 0.23767, 0.55178, 0.18228, 0.73886, 0.12689, 0.91836, 0, 1, 0], "triangles": [4, 7, 8, 5, 6, 7, 4, 5, 7, 3, 10, 11, 9, 10, 3, 8, 9, 3, 3, 4, 8, 1, 11, 12, 2, 11, 1, 3, 11, 2, 13, 14, 0, 0, 12, 13, 1, 12, 0], "vertices": [1, 41, 28.84, -5.01, 1, 2, 41, 3.22, -13.81, 0.69679, 40, 46.54, -12, 0.30321, 2, 40, 17, -25.45, 0.96739, 39, 53.42, -28.17, 0.03261, 3, 40, -17.42, -23.99, 0.10714, 39, 19.88, -20.34, 0.84425, 38, 40.35, -27.02, 0.0486, 2, 39, -7.79, -20.34, 0.11109, 38, 15.52, -14.83, 0.88891, 1, 38, -6.44, -5.23, 1, 1, 38, -8.54, 2.53, 1, 2, 39, -25.28, 5.69, 0.00509, 38, 11.28, 16.25, 0.99491, 2, 39, -8.22, 20.7, 0.55711, 38, 33.21, 22.2, 0.44289, 3, 40, -31.29, 27.79, 0.0123, 39, 15.85, 33.12, 0.98551, 38, 60.29, 22.75, 0.00219, 2, 40, -3.21, 32.86, 0.34827, 39, 44.39, 32.89, 0.65173, 3, 41, -7.71, 24.16, 0.03197, 40, 23.84, 20.33, 0.92512, 39, 68.64, 15.55, 0.04291, 2, 41, 8.04, 12.31, 0.68707, 40, 42.59, 14.26, 0.31293, 1, 41, 30.71, 5.83, 1, 1, 41, 35.11, -0.94, 1], "hull": 15, "edges": [10, 8, 8, 6, 6, 4, 4, 2, 0, 28, 2, 0, 26, 28, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 14, 16, 10, 12, 14, 12], "width": 99, "height": 122}}, "toufa5": {"toufa5": {"type": "mesh", "uvs": [1, 0.1056, 0.88147, 0.31514, 0.75406, 0.4852, 0.61772, 0.65222, 0.51579, 0.83746, 0.36162, 1, 0.20617, 1, 0.07748, 1, 0, 1, 0, 0.64007, 0.05964, 0.2787, 0.16922, 0.08738, 0.31829, 0, 0.48266, 0, 0.62792, 0, 0.81396, 0, 1, 0], "triangles": [7, 8, 9, 10, 6, 9, 6, 7, 9, 5, 6, 11, 6, 10, 11, 12, 13, 4, 12, 5, 11, 4, 5, 12, 3, 13, 14, 3, 14, 2, 4, 13, 3, 15, 16, 0, 1, 15, 0, 2, 14, 15, 2, 15, 1], "vertices": [1, 45, 42.77, -4.69, 1, 1, 45, 22.29, -9.79, 1, 2, 45, 1.55, -12.22, 0.62561, 44, 43.41, -11.28, 0.37439, 3, 44, 22.95, -19.16, 0.91976, 43, 59.1, -20.38, 0.07828, 42, 60.06, -56.4, 0.00197, 3, 44, 7.26, -28.64, 0.51237, 43, 42.93, -29, 0.44059, 42, 42.56, -50.93, 0.04705, 3, 44, -15.7, -35.98, 0.07574, 43, 19.6, -35.07, 0.62173, 42, 21.86, -38.57, 0.30253, 3, 44, -37.8, -33.59, 0.0007, 43, -2.34, -31.49, 0.21964, 42, 9.01, -20.43, 0.77965, 2, 43, -20.5, -28.52, 0.00554, 42, -1.62, -5.42, 0.99446, 1, 42, -8.03, 3.62, 1, 2, 43, -27.96, -5.42, 1e-05, 42, 9.59, 16.11, 0.99999, 2, 43, -16.05, 14.6, 0.35772, 42, 32.22, 21.68, 0.64228, 2, 43, 1.27, 23.41, 0.95951, 42, 50.64, 15.53, 0.04049, 2, 44, -15.43, 24.34, 0.04877, 43, 23.15, 25.15, 0.95123, 3, 45, -23.11, 29.56, 0.00045, 44, 7.94, 21.82, 0.71982, 43, 46.35, 21.36, 0.27974, 3, 45, -3.93, 21.59, 0.16856, 44, 28.59, 19.59, 0.82952, 43, 66.85, 18.01, 0.00192, 2, 45, 20.64, 11.37, 0.97482, 44, 55.04, 16.74, 0.02518, 1, 45, 45.2, 1.16, 1], "hull": 17, "edges": [16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 143, "height": 60}}, "toufa6": {"toufa6": {"type": "mesh", "uvs": [0.30006, 0.13108, 0.4797, 0.25304, 0.70726, 0.35974, 0.90127, 0.48, 1, 0.67987, 1, 1, 0.78057, 1, 0.56114, 1, 0.46773, 0.90515, 0.29527, 0.85433, 0.13, 0.74932, 0, 0.6172, 0, 0.47831, 0, 0.31401, 0, 0.16665, 0.10811, 0, 0.21623, 0], "triangles": [4, 8, 3, 6, 7, 8, 4, 6, 8, 6, 4, 5, 10, 1, 2, 9, 10, 2, 8, 9, 2, 3, 8, 2, 0, 12, 13, 0, 11, 12, 1, 11, 0, 1, 10, 11, 0, 13, 14, 15, 0, 14, 0, 15, 16], "vertices": [3, 32, 18.23, -12.09, 0.91498, 31, 52.53, -17.39, 0.08354, 30, 81.33, -31.96, 0.00148, 4, 32, -0.15, -28.52, 0.17485, 31, 29.79, -26.9, 0.68904, 30, 56.8, -34.43, 0.13583, 29, 83.7, -48.09, 0.00027, 4, 32, -16.76, -49.85, 0.0021, 31, 7.12, -41.62, 0.35986, 30, 30.82, -41.89, 0.57559, 29, 56.67, -48.49, 0.06245, 3, 31, -15.97, -52.54, 0.0842, 30, 5.55, -45.59, 0.60655, 29, 31.31, -45.45, 0.30925, 3, 31, -45.53, -50.37, 0.00249, 30, -22.09, -34.89, 0.205, 29, 7.44, -27.89, 0.79251, 1, 29, -17.89, 9.09, 1, 1, 29, 0.04, 21.36, 1, 2, 30, -28.04, 27.25, 0.00845, 29, 17.96, 33.64, 0.99155, 2, 30, -11.93, 25.68, 0.19715, 29, 33.1, 27.91, 0.80285, 3, 31, -40.16, 23.36, 0.01406, 30, 4.56, 34.06, 0.70264, 29, 51.2, 31.68, 0.28331, 3, 31, -20.16, 32.51, 0.20251, 30, 26.36, 36.98, 0.76882, 29, 73.01, 28.8, 0.02867, 2, 31, 1.93, 36.96, 0.60289, 30, 48.78, 34.79, 0.39711, 3, 32, -27.99, 21.23, 0.00254, 31, 19.76, 29.22, 0.89679, 30, 63.59, 22.18, 0.10068, 3, 32, -5.05, 19.48, 0.37532, 31, 40.86, 20.06, 0.62466, 30, 81.1, 7.26, 1e-05, 2, 32, 15.52, 17.91, 0.98481, 31, 59.79, 11.85, 0.01519, 1, 32, 37.97, 5.46, 1, 1, 32, 37.16, -5.21, 1], "hull": 17, "edges": [32, 0, 0, 2, 2, 4, 4, 6, 10, 8, 6, 8, 30, 32, 30, 28, 26, 28, 24, 26, 22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 10, 12, 12, 14], "width": 99, "height": 140}}, "toufa7": {"toufa7": {"type": "mesh", "uvs": [0.21542, 0.0959, 0.43299, 0.15775, 0.61856, 0.23241, 0.90225, 0.45424, 1, 0.58649, 1, 1, 0.73374, 1, 0.66122, 0.87018, 0.51191, 0.78486, 0.3218, 0.64811, 0.16183, 0.44974, 0.05091, 0.24924, 0, 0, 0.05331, 0, 0.76787, 0.35399], "triangles": [7, 8, 3, 4, 7, 3, 5, 6, 7, 5, 7, 4, 3, 14, 2, 9, 1, 2, 9, 2, 14, 8, 9, 14, 8, 14, 3, 10, 0, 1, 9, 10, 1, 11, 12, 13, 11, 13, 0, 10, 11, 0], "vertices": [3, 49, 13.07, -12.62, 0.85363, 48, 51.08, -13.34, 0.14548, 47, 88.32, -21.09, 0.00089, 4, 49, -10.53, -28.6, 0.01588, 48, 26.62, -27.96, 0.82742, 47, 61.88, -31.75, 0.15663, 46, 94.71, -46.01, 7e-05, 3, 48, 3.74, -38.54, 0.36012, 47, 37.65, -38.65, 0.60507, 46, 69.51, -46.73, 0.03481, 3, 48, -41.11, -45.41, 0.00016, 47, -7.73, -38.49, 0.32399, 46, 25.57, -35.4, 0.67586, 2, 47, -27.68, -32.88, 0.07375, 46, 7.62, -25.06, 0.92625, 1, 46, -13.21, 22.7, 1, 1, 46, 17.54, 36.11, 1, 2, 47, -15.88, 21.53, 0.09672, 46, 32.46, 24.77, 0.90328, 2, 47, 5.57, 24.57, 0.76327, 46, 54, 22.44, 0.23673, 2, 48, -8.77, 24.59, 0.23287, 47, 35.06, 25.65, 0.76713, 3, 49, -16.72, 21.23, 0.02501, 48, 23.24, 22.14, 0.95737, 47, 66.31, 18.27, 0.01762, 2, 49, 11.61, 15.68, 0.91412, 48, 51.22, 15, 0.08588, 1, 49, 39.78, 0.39, 1, 1, 49, 35.46, -4.75, 1, 3, 48, -20.3, -41.74, 0.0431, 47, 13.39, -38.09, 0.68218, 46, 46.14, -40.21, 0.27472], "hull": 14, "edges": [24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 10, 12, 14, 12, 24, 26, 26, 0, 0, 2, 2, 4, 4, 28, 28, 6, 10, 8, 6, 8, 4, 6], "width": 126, "height": 126}}, "toufa8": {"toufa8": {"type": "mesh", "uvs": [0.40806, 0.23596, 0.58302, 0.5643, 0.66813, 0.55767, 0.71132, 0.57505, 0.79085, 0.52612, 0.87876, 0.40085, 0.96178, 0.18947, 1, 0.28733, 1, 0.64366, 0.97643, 0.76686, 0.90666, 0.89017, 0.8083, 0.9254, 0.71481, 0.91366, 0.67523, 0.80642, 0.6374, 0.94572, 0.58538, 1, 0.54046, 1, 0.40688, 0.91587, 0.28393, 0.74009, 0.17044, 0.54772, 0, 0.62069, 0, 0.39184, 0.13616, 0, 0.26029, 0], "triangles": [10, 11, 4, 1, 15, 16, 14, 15, 1, 13, 2, 3, 13, 1, 2, 13, 14, 1, 13, 3, 12, 4, 11, 3, 11, 12, 3, 4, 5, 10, 17, 18, 0, 18, 19, 0, 17, 0, 1, 16, 17, 1, 19, 23, 0, 19, 22, 23, 20, 21, 19, 21, 22, 19, 10, 5, 9, 9, 5, 8, 8, 5, 7, 5, 6, 7], "vertices": [2, 86, 28.26, 57.65, 0.3, 87, -33.39, 51.44, 0.7, 2, 86, 45.93, 45.83, 0.15, 87, -15.72, 39.62, 0.85, 2, 86, 54.53, 46.06, 0.05609, 87, -7.13, 39.86, 0.94391, 2, 86, 58.89, 45.44, 0.1, 87, -2.76, 39.23, 0.9, 2, 86, 66.92, 47.2, 0.28724, 87, 5.27, 40.99, 0.71276, 2, 86, 75.8, 51.71, 0.52947, 87, 14.15, 45.5, 0.47053, 2, 86, 84.19, 59.32, 0.75, 87, 22.53, 53.11, 0.25, 2, 86, 88.05, 55.8, 0.8, 87, 26.39, 49.59, 0.2, 2, 86, 88.05, 42.97, 0.8, 87, 26.39, 36.76, 0.2, 2, 86, 85.66, 38.53, 0.7, 87, 24.01, 32.33, 0.3, 2, 86, 78.62, 34.09, 0.5, 87, 16.97, 27.89, 0.5, 2, 86, 68.68, 32.83, 0.3, 87, 7.03, 26.62, 0.7, 2, 86, 59.24, 33.25, 0.1, 87, -2.41, 27.04, 0.9, 2, 86, 55.24, 37.11, 0.05, 87, -6.41, 30.9, 0.95, 2, 86, 51.42, 32.1, 0.1, 87, -10.23, 25.89, 0.9, 2, 86, 46.17, 30.14, 0.2, 87, -15.48, 23.93, 0.8, 2, 86, 41.63, 30.14, 0.24, 87, -20.02, 23.93, 0.76, 2, 86, 28.14, 33.17, 0.25, 87, -33.51, 26.96, 0.75, 2, 86, 15.72, 39.5, 0.3, 87, -45.93, 33.29, 0.7, 2, 86, 4.26, 46.42, 0.5, 87, -57.39, 40.22, 0.5, 2, 86, -12.95, 43.8, 0.5, 87, -74.61, 37.59, 0.5, 2, 86, -12.95, 52.03, 0.5, 87, -74.61, 45.83, 0.5, 2, 86, 0.8, 66.14, 0.5, 87, -60.86, 59.93, 0.5, 2, 86, 13.33, 66.14, 0.4, 87, -48.32, 59.93, 0.6], "hull": 24, "edges": [42, 44, 44, 46, 46, 0, 0, 2, 2, 4, 4, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 40, 42, 38, 40, 14, 12, 12, 10, 10, 8, 8, 6, 6, 24, 24, 22, 22, 20, 20, 18, 14, 16, 18, 16, 26, 24, 4, 6], "width": 101, "height": 36}}, "tui": {"tui": {"type": "mesh", "uvs": [0.8208, 0.33368, 0.87657, 0.57147, 0.91115, 0.74338, 1, 0.82853, 1, 1, 0.5, 1, 0, 1, 0, 0.82582, 0, 0.65165, 0.05386, 0.61148, 0.10852, 0.28854, 0.15759, 0, 0.6243, 0, 0.77953, 0, 0.64626, 0.33608, 0.64077, 0.57645, 0.55404, 0.65867], "triangles": [7, 8, 9, 5, 6, 7, 5, 9, 16, 5, 7, 9, 11, 14, 10, 16, 10, 14, 9, 10, 16, 14, 12, 13, 14, 13, 0, 12, 14, 11, 15, 14, 0, 0, 1, 15, 15, 16, 14, 2, 3, 4, 2, 15, 1, 16, 15, 2, 5, 16, 2, 5, 2, 4], "vertices": [1, 12, 87.73, 57.8, 1, 2, 13, 22.73, 72.76, 0.26438, 12, 183.63, 71.51, 0.73562, 2, 13, 91.24, 81.67, 0.87586, 12, 252.39, 78.28, 0.12414, 2, 13, 131.24, 125.5, 0.98242, 12, 293.73, 120.84, 0.01758, 1, 13, 196.65, 115.43, 1, 3, 13, 154.38, -159.33, 0.54649, 10, 335.34, 155.02, 0.05836, 11, 112, 170.22, 0.39516, 2, 13, 112.11, -434.1, 0.00028, 11, 186.79, -97.53, 0.99972, 3, 13, 45.66, -423.88, 0.00016, 10, 309.85, -129.86, 0.01706, 11, 122.04, -115.61, 0.98278, 3, 13, -20.79, -413.66, 7e-05, 10, 243.35, -139.77, 0.11975, 11, 57.28, -133.7, 0.88018, 3, 13, -31.56, -381.7, 4e-05, 10, 223.6, -112.44, 0.2459, 11, 34.29, -109.03, 0.75406, 2, 10, 95.82, -100.77, 0.98634, 11, -93.94, -113.3, 0.01366, 1, 10, -18.36, -90.21, 1, 3, 13, -216.62, -32.33, 0.00021, 12, -58.87, -26.08, 0.8665, 10, -56.63, 166.44, 0.13329, 1, 12, -43.1, 58.78, 1, 4, 13, -86.54, -39.99, 0.11146, 12, 70.9, -37.79, 0.79673, 10, 69.88, 197.65, 0.09154, 11, -156.7, 179.6, 0.00027, 4, 13, 4.69, -57.12, 0.80714, 12, 161.56, -57.74, 0.06471, 10, 162.09, 208.31, 0.09912, 11, -66.52, 201.62, 0.02903, 4, 13, 28.73, -109.61, 0.65577, 12, 183.95, -110.95, 0.01592, 10, 200.6, 165.3, 0.20445, 11, -22.98, 163.72, 0.12387], "hull": 14, "edges": [26, 0, 0, 2, 2, 4, 8, 6, 4, 6, 22, 20, 20, 18, 18, 16, 12, 14, 14, 16, 22, 24, 24, 26, 24, 28, 28, 30, 30, 32, 8, 10, 10, 12, 32, 10], "width": 556, "height": 386}}, "yanbai": {"yanbai": {"type": "mesh", "uvs": [1, 1, 0.76378, 1, 0.44333, 1, 0, 1, 0, 0, 0.44912, 0, 0.76146, 0, 1, 0], "triangles": [2, 3, 4, 5, 2, 4, 2, 5, 1, 1, 7, 0, 1, 6, 7, 5, 6, 1], "vertices": [2, 86, 74.05, 24.14, 0.6, 87, 12.39, 17.93, 0.4, 2, 86, 56.57, 24.14, 0.1, 87, -5.09, 17.93, 0.9, 2, 86, 32.85, 24.14, 0.3, 87, -28.8, 17.93, 0.7, 2, 86, 0.05, 24.14, 0.5, 87, -61.61, 17.93, 0.5, 2, 86, 0.05, 43.14, 0.5, 87, -61.61, 36.93, 0.5, 2, 86, 33.28, 43.14, 0.3, 87, -28.37, 36.93, 0.7, 2, 86, 56.39, 43.14, 0.1, 87, -5.26, 36.93, 0.9, 2, 86, 74.05, 43.14, 0.6, 87, 12.39, 36.93, 0.4], "hull": 8, "edges": [6, 8, 0, 14, 8, 10, 4, 6, 10, 4, 10, 12, 12, 14, 0, 2, 2, 4, 12, 2], "width": 74, "height": 19}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [0.92271, 0.15621, 1, 0.15284, 1, 0.8173, 0.9115, 0.86452, 0.8846, 1, 0.65928, 1, 0.62341, 0.86789, 0.54158, 0.87801, 0.52364, 1, 0.2546, 1, 0.21202, 0.87464, 0, 0.79032, 0, 0, 0.07188, 0, 0.27478, 0.07526, 0.29048, 0, 0.47656, 0, 0.54158, 0.17308, 0.6391, 0.15959, 0.64135, 0, 0.92271, 0], "triangles": [11, 12, 13, 10, 13, 14, 11, 13, 10, 6, 17, 18, 16, 14, 15, 7, 17, 6, 9, 10, 14, 16, 17, 14, 7, 14, 17, 9, 14, 7, 8, 9, 7, 19, 20, 0, 18, 19, 0, 0, 1, 2, 3, 18, 0, 3, 0, 2, 6, 18, 3, 5, 6, 3, 4, 5, 3], "vertices": [3, 3, 48.66, 44.97, 0.71845, 6, -132.47, -69.73, 2e-05, 4, -5.65, 46.98, 0.28153, 2, 3, 74.91, 43.65, 0.37493, 4, 19.83, 40.55, 0.62507, 2, 3, 70.03, -31.28, 0.80213, 4, 0.41, -31.97, 0.19787, 2, 3, 39.66, -34.65, 0.99356, 4, -30.04, -29.34, 0.00644, 2, 3, 29.53, -49.33, 0.99987, 6, -131.97, 26.49, 0.00013, 2, 3, -46.91, -44.36, 0.68924, 6, -56, 36.41, 0.31076, 2, 3, -58.11, -28.67, 0.4785, 6, -41.98, 23.19, 0.5215, 2, 3, -85.95, -28, 0.07186, 6, -14.54, 27.93, 0.92814, 2, 3, -92.93, -41.36, 0.01318, 6, -10.27, 42.39, 0.98682, 2, 6, 80.43, 54.23, 0.89002, 7, -27.06, 50.85, 0.10998, 2, 6, 96.62, 42.06, 0.63601, 7, -9.27, 41.15, 0.36399, 1, 7, 62.69, 51.59, 1, 1, 7, 86.97, -34.35, 1, 2, 6, 156.66, -49.77, 0.00041, 7, 63.46, -41, 0.99959, 2, 6, 87.16, -50.27, 0.7011, 7, -5.24, -51.58, 0.2989, 2, 6, 82.97, -59.39, 0.80908, 7, -8.06, -61.21, 0.19092, 3, 3, -101.57, 72.44, 0.01508, 6, 20.23, -67.59, 0.98456, 7, -68.95, -78.42, 0.00036, 2, 3, -80.78, 51.49, 0.13867, 6, -4.22, -51.06, 0.86133, 2, 3, -47.59, 50.86, 0.5968, 6, -36.9, -56.86, 0.40319, 3, 3, -45.66, 68.8, 0.70978, 6, -35.32, -74.84, 0.29018, 4, -93.49, 88.79, 4e-05, 3, 3, 49.8, 62.59, 0.8122, 6, -130.18, -87.23, 3e-05, 4, -1.08, 64.03, 0.18777], "hull": 21, "edges": [22, 24, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 8, 10, 8, 6, 6, 4, 40, 0, 4, 2, 0, 2, 38, 40, 38, 36, 36, 34, 34, 32, 30, 32, 30, 28, 24, 26, 28, 26], "width": 340, "height": 113}}, "youshou": {"youshou": {"type": "mesh", "uvs": [0.93386, 0.10669, 1, 0.23405, 1, 0.33946, 1, 0.52722, 1, 0.69212, 0.84755, 0.86519, 0.69388, 1, 0.2475, 1, 0, 0.87351, 0, 0.67912, 0.04906, 0.64542, 0.14761, 0.65693, 0.27053, 0.74159, 0.3161, 0.69885, 0.22709, 0.66351, 0.16351, 0.62405, 0.1741, 0.39639, 0.20801, 0.19797, 0.35743, 0.05825, 0.46128, 0, 0.76125, 0], "triangles": [0, 1, 2, 2, 16, 0, 17, 0, 16, 16, 2, 14, 15, 16, 14, 18, 19, 20, 0, 18, 20, 18, 0, 17, 3, 14, 2, 3, 13, 14, 4, 13, 3, 5, 13, 4, 11, 8, 9, 11, 9, 10, 8, 11, 12, 7, 8, 12, 6, 13, 5, 12, 13, 6, 7, 12, 6], "vertices": [2, 69, 6.69, 45.52, 0.99211, 68, 297.29, 34.73, 0.00789, 1, 69, 32.62, 56.47, 1, 1, 69, 54.22, 56.84, 1, 1, 69, 92.71, 57.49, 1, 1, 69, 126.51, 58.07, 1, 1, 69, 162.4, 34.43, 1, 1, 69, 190.44, 10.47, 1, 1, 69, 191.64, -60.49, 1, 1, 69, 166.38, -100.28, 1, 1, 69, 126.54, -100.96, 1, 1, 69, 119.5, -93.27, 1, 1, 69, 121.59, -77.57, 1, 1, 69, 138.61, -57.73, 1, 1, 69, 129.73, -50.64, 1, 1, 69, 122.73, -64.91, 1, 1, 69, 114.81, -75.15, 1, 1, 69, 68.12, -74.26, 1, 2, 69, 27.36, -69.56, 0.99791, 68, 254.18, -73.95, 0.00209, 2, 69, -1.68, -46.29, 0.87403, 68, 241.77, -38.86, 0.12597, 2, 69, -13.9, -29.98, 0.61143, 68, 239.98, -18.56, 0.38857, 2, 69, -14.71, 17.7, 0.46899, 68, 264.44, 22.38, 0.53101], "hull": 21, "edges": [40, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 14, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 38, 40, 36, 38], "width": 159, "height": 205}}, "youshou1": {"youshou1": {"type": "mesh", "uvs": [0.58348, 0.03447, 0.79869, 0.12536, 0.91629, 0.24746, 0.90649, 0.39265, 0.81176, 0.49299, 0.92282, 0.56228, 1, 0.67706, 1, 0.83073, 0.89506, 0.93303, 0.7188, 1, 0.43761, 1, 0.29387, 0.92431, 0.15014, 0.84863, 0.07507, 0.75046, 0, 0.65229, 0, 0.51948, 0.09134, 0.29428, 0.15341, 0.09463, 0.25304, 0, 0.37278, 0], "triangles": [17, 18, 19, 0, 17, 19, 16, 17, 0, 1, 16, 0, 2, 16, 1, 2, 3, 16, 4, 16, 3, 4, 14, 15, 4, 15, 16, 13, 14, 4, 6, 13, 5, 13, 6, 12, 5, 13, 4, 6, 11, 12, 7, 11, 6, 8, 11, 7, 9, 10, 11, 8, 9, 11], "vertices": [2, 67, -63.08, 12.41, 0.03846, 66, 125.1, 63.07, 0.96154, 2, 67, -23.04, 51.31, 0.69133, 66, 176.3, 40.81, 0.30867, 2, 67, 27.22, 69.62, 0.99919, 66, 212.34, 1.28, 0.00081, 1, 67, 83.92, 61.78, 1, 1, 67, 121.29, 38.95, 1, 1, 67, 150.73, 58.12, 1, 1, 67, 197.3, 68.73, 1, 1, 67, 257.52, 62.48, 1, 1, 67, 295.46, 37.55, 1, 1, 67, 318.08, -0.06, 1, 1, 67, 312.31, -55.72, 1, 1, 67, 279.7, -81.09, 1, 2, 67, 247.08, -106.47, 0.99979, 66, 132.99, -269.01, 0.00021, 2, 67, 207.07, -117.34, 0.99447, 66, 107.74, -236.12, 0.00553, 2, 67, 167.05, -128.2, 0.97515, 66, 82.48, -203.24, 0.02485, 2, 67, 115.01, -122.8, 0.90725, 66, 67.7, -153.04, 0.09275, 2, 67, 28.63, -95.57, 0.42905, 66, 60.07, -62.79, 0.57095, 1, 66, 49.7, 16.16, 1, 1, 66, 58.18, 57.52, 1, 1, 66, 81.04, 64.25, 1], "hull": 20, "edges": [36, 34, 34, 32, 32, 30, 28, 30, 18, 20, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 36, 38, 0, 38, 28, 26, 26, 24, 20, 22, 22, 24], "width": 199, "height": 394}}, "youshou2": {"youshou2": {"type": "mesh", "uvs": [0.6282, 0.06761, 0.77463, 0.18051, 0.87124, 0.38713, 0.8803, 0.55648, 0.93615, 0.70786, 1, 0.76644, 1, 0.91819, 0.83048, 1, 0.65877, 1, 0.55417, 0.96612, 0.52851, 0.8905, 0.45907, 0.81382, 0.47115, 0.77121, 0.35491, 0.69665, 0.21452, 0.60611, 0.06507, 0.45594, 0, 0.28272, 0, 0.1387, 0.05289, 0.06414, 0.15554, 0.02367, 0.31405, 0, 0.46818, 0, 0.79451, 0.88697], "triangles": [8, 22, 7, 7, 22, 6, 22, 8, 10, 8, 9, 10, 22, 5, 6, 22, 10, 12, 10, 11, 12, 19, 20, 16, 20, 15, 16, 18, 19, 17, 19, 16, 17, 22, 4, 5, 22, 12, 4, 4, 12, 3, 12, 13, 3, 13, 2, 3, 1, 2, 15, 2, 13, 14, 1, 15, 0, 0, 15, 20, 20, 21, 0, 14, 15, 2], "vertices": [2, 68, 21.26, 90.71, 0.98579, 67, 261.5, 92.25, 0.01421, 1, 68, 72.68, 101.28, 1, 1, 68, 145.51, 85.02, 1, 1, 68, 196.51, 57.12, 1, 1, 68, 248.02, 42.1, 1, 2, 68, 273.19, 45.07, 0.99046, 69, -19.23, 41.59, 0.00954, 2, 68, 317.88, 18.38, 0.36655, 69, 32.82, 42.48, 0.63345, 2, 68, 320.94, -31.23, 3e-05, 69, 61.57, 1.93, 0.99997, 1, 69, 62.27, -39.62, 1, 2, 68, 276.68, -82.68, 0.01714, 69, 51.08, -65.12, 0.98286, 2, 68, 251.23, -74.71, 0.14628, 69, 25.25, -71.77, 0.85372, 2, 68, 220.03, -75.66, 0.4499, 69, -0.76, -89.02, 0.5501, 3, 68, 208.98, -65.66, 0.62552, 67, 497.62, 29.54, 0.0001, 69, -15.42, -86.34, 0.37438, 3, 68, 172.6, -76.69, 0.91006, 67, 469.28, 4.2, 0.00762, 69, -40.52, -114.9, 0.08232, 3, 68, 128.52, -89.94, 0.93381, 67, 434.89, -26.38, 0.05859, 69, -70.99, -149.4, 0.0076, 2, 68, 65.75, -94.58, 0.69759, 67, 379.92, -57.04, 0.30241, 2, 68, 6.67, -77.64, 0.20249, 67, 319.2, -66.57, 0.79751, 2, 68, -35.74, -52.31, 0.00034, 67, 270.06, -61.48, 0.99966, 2, 68, -51.14, -28.21, 0.00011, 67, 245.95, -46.11, 0.99989, 2, 68, -50.32, 0.23, 0.00072, 67, 234.7, -19.97, 0.99928, 2, 68, -37.62, 37.33, 0.26411, 67, 230.59, 19.03, 0.73589, 2, 68, -18.5, 69.35, 0.78866, 67, 234.43, 56.13, 0.21134, 1, 69, 22.95, -7.43, 1], "hull": 22, "edges": [34, 36, 36, 38, 38, 40, 40, 42, 42, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 32, 34, 30, 32], "width": 242, "height": 343}}, "zuoshou": {"zuoshou": {"type": "mesh", "uvs": [0.54982, 0.04725, 0.65078, 0.17792, 0.67602, 0.31343, 0.59021, 0.42717, 0.62326, 0.51263, 0.69393, 0.59128, 0.84033, 0.63605, 1, 0.68444, 1, 0.87852, 0.80771, 1, 0.22236, 1, 0.10056, 0.89533, 0.11488, 0.72238, 0.06243, 0.62994, 0.1098, 0.56912, 0, 0.42153, 0, 0.23557, 0, 0.11676, 0, 0.06619, 0.05755, 0.03277, 0.20928, 0, 0.41857, 0], "triangles": [17, 20, 0, 17, 19, 20, 20, 21, 0, 17, 18, 19, 13, 14, 12, 5, 14, 4, 5, 12, 14, 14, 3, 4, 14, 15, 3, 2, 16, 1, 16, 2, 3, 1, 16, 0, 15, 16, 3, 0, 16, 17, 9, 6, 8, 9, 10, 11, 6, 9, 12, 6, 12, 5, 9, 11, 12, 6, 7, 8], "vertices": [2, 74, -4.55, 52.85, 0.74984, 73, 296.44, 41.19, 0.25016, 1, 74, 68.82, 78.16, 1, 1, 74, 144.43, 83.2, 1, 2, 75, -95.13, 97.44, 0.00441, 74, 207.25, 58.87, 0.99559, 2, 75, -47.58, 88.32, 0.11561, 74, 255.04, 66.63, 0.88439, 2, 75, 0.09, 89.94, 0.55127, 74, 299.26, 84.51, 0.44873, 2, 75, 37.55, 117.26, 0.88024, 74, 325.07, 123.03, 0.11976, 2, 75, 78.18, 147.15, 0.97349, 74, 352.97, 165.04, 0.02651, 1, 75, 178.86, 107.77, 1, 1, 75, 223.18, 35.32, 1, 1, 75, 166.26, -110.24, 1, 1, 75, 100.12, -119.29, 1, 2, 75, 11.79, -80.65, 0.77603, 74, 368.79, -71.7, 0.22397, 2, 75, -41.26, -74.93, 0.27152, 74, 317, -84.54, 0.72848, 2, 75, -68.21, -50.82, 0.05829, 74, 283.42, -71.14, 0.94171, 2, 74, 200.57, -98.6, 0.99559, 73, 551.4, 38.8, 0.00441, 2, 74, 97.02, -96.27, 0.79206, 73, 466.16, -20.04, 0.20794, 2, 74, 30.86, -94.79, 0.28324, 73, 411.69, -57.63, 0.71676, 2, 74, 2.7, -94.16, 0.10594, 73, 388.51, -73.63, 0.89406, 2, 74, -15.57, -78.37, 0.03453, 73, 364.46, -71.56, 0.96547, 1, 73, 326.43, -48.59, 1, 1, 73, 294.68, -2.6, 1], "hull": 22, "edges": [42, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 18, 20, 14, 16, 18, 16, 30, 32, 36, 38, 40, 42, 38, 40, 32, 34, 34, 36], "width": 267, "height": 557}}, "zuoshou1": {"zuoshou1": {"type": "mesh", "uvs": [0.79005, 0.02722, 0.89362, 0.03228, 1, 0.1095, 1, 0.2684, 0.93702, 0.41544, 0.79628, 0.47493, 0.78964, 0.60406, 0.7445, 0.77206, 0.60243, 0.92524, 0.38999, 0.97967, 0.20808, 1, 0.07165, 1, 0.03328, 0.97001, 0, 0.92834, 0, 0.81927, 0.06729, 0.70787, 0.11244, 0.58888, 0.12837, 0.46482, 0.30807, 0.15761, 0.43288, 0.03481, 0.55769, 0, 0.73428, 0, 0.28816, 0.28547, 0.18812, 0.36354], "triangles": [1, 2, 3, 1, 3, 0, 3, 5, 0, 4, 5, 3, 0, 20, 21, 22, 23, 18, 23, 17, 18, 5, 20, 0, 20, 5, 19, 19, 5, 6, 19, 22, 18, 22, 19, 6, 22, 6, 23, 23, 6, 7, 23, 16, 17, 23, 7, 16, 8, 16, 7, 9, 15, 16, 12, 13, 14, 15, 12, 14, 11, 12, 10, 16, 8, 9, 12, 15, 10, 9, 10, 15], "vertices": [2, 73, -63.4, -1.19, 0.04092, 72, 197.56, -49.33, 0.95908, 1, 72, 157.64, -43.5, 1, 1, 72, 119.39, -8.26, 1, 2, 73, -28.81, 121.93, 0.12459, 72, 125.46, 56.29, 0.87541, 2, 73, 34.48, 135.84, 0.41146, 72, 155.48, 113.72, 0.58854, 2, 73, 85.56, 104.57, 0.78775, 72, 212.26, 132.76, 0.21225, 2, 73, 130.39, 132.38, 0.96334, 72, 219.77, 184.97, 0.03666, 2, 73, 196.77, 156.86, 0.99877, 72, 243.67, 251.57, 0.00123, 1, 73, 279.6, 146.88, 1, 1, 73, 344.82, 91.48, 1, 1, 73, 391.84, 37.96, 1, 1, 73, 421.99, -5.72, 1, 1, 73, 420.4, -24.95, 1, 1, 73, 413.76, -45.26, 1, 1, 73, 377.14, -70.54, 1, 1, 73, 324.86, -74.82, 1, 1, 73, 274.93, -87.95, 1, 1, 73, 229.75, -111.6, 1, 1, 73, 86.89, -125.27, 1, 2, 73, 18.08, -113.77, 0.99975, 72, 336.17, -59.27, 0.00025, 2, 73, -21.19, -81.88, 0.92233, 72, 286.5, -68.86, 0.07767, 2, 73, -60.21, -25.35, 0.22248, 72, 218.11, -62.42, 0.77752, 1, 73, 134.22, -102.01, 1, 1, 73, 182.54, -115.94, 1], "hull": 22, "edges": [20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 42, 0, 0, 2, 6, 4, 2, 4, 40, 42, 40, 38, 38, 36, 36, 44, 44, 46, 46, 34, 34, 32, 32, 30, 30, 28, 26, 28, 26, 24, 20, 22, 24, 22, 34, 36], "width": 389, "height": 408}}}}], "animations": {"idle": {"slots": {"biyan": {"rgba": [{"color": "ffffff02", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff02"}, {"time": 3.4, "color": "ffffffff", "curve": "stepped"}, {"time": 3.5333, "color": "ffffffff"}, {"time": 3.6333, "color": "ffffff02"}]}}, "bones": {"bone": {"rotate": [{"curve": [0.387, 0, 0.78, -1.39]}, {"time": 1.1667, "value": -1.39}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -1.39]}, {"time": 3.5, "value": -1.39}, {"time": 4.6667}], "translate": [{"curve": [0.387, 0, 0.78, 11.73, 0.387, 0, 0.78, -15.31]}, {"time": 1.1667, "x": 11.73, "y": -15.31, "curve": [1.554, 11.73, 1.944, 0, 1.554, -15.31, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 11.73, 2.72, 0, 3.113, -15.31]}, {"time": 3.5, "x": 11.73, "y": -15.31, "curve": [3.887, 11.73, 4.28, 0, 3.887, -15.31, 4.28, 0]}, {"time": 4.6667}]}, "bone10": {"rotate": [{"curve": [0.387, 0, 0.78, -1.26]}, {"time": 1.1667, "value": -1.26, "curve": [1.554, -1.26, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -1.26]}, {"time": 3.5, "value": -1.26, "curve": [3.887, -1.26, 4.28, 0]}, {"time": 4.6667}]}, "bone12": {"rotate": [{"curve": [0.387, 0, 0.78, 1.77]}, {"time": 1.1667, "value": 1.77, "curve": [1.554, 1.77, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 1.77]}, {"time": 3.5, "value": 1.77, "curve": [3.887, 1.77, 4.28, 0]}, {"time": 4.6667}]}, "bone14": {"rotate": [{"curve": [0.387, 0, 0.78, 1.39]}, {"time": 1.1667, "value": 1.39, "curve": [1.554, 1.39, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 1.39]}, {"time": 3.5, "value": 1.39, "curve": [3.887, 1.39, 4.28, 0]}, {"time": 4.6667}]}, "bone15": {"rotate": [{"curve": [0.387, 0, 0.78, -2.44]}, {"time": 1.1667, "value": -2.44, "curve": [1.554, -2.44, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -2.44]}, {"time": 3.5, "value": -2.44, "curve": [3.887, -2.44, 4.28, 0]}, {"time": 4.6667}], "translate": [{"curve": [0.193, 0, 0.68, -3.46, 0.193, 0, 0.68, 0]}, {"time": 1.1667, "x": -3.46, "curve": [1.653, -3.46, 1.944, 0, 1.653, 0, 1.944, 0]}, {"time": 2.3333, "curve": [2.527, 0, 3.013, -3.46, 2.527, 0, 3.013, 0]}, {"time": 3.5, "x": -3.46, "curve": [3.987, -3.46, 4.473, 0, 3.987, 0, 4.473, 0]}, {"time": 4.6667}], "scale": [{"curve": [0.387, 1, 0.78, 0.973, 0.387, 1, 0.78, 1]}, {"time": 1.1667, "x": 0.973, "curve": [1.554, 0.973, 1.944, 1, 1.554, 1, 1.944, 1]}, {"time": 2.3333, "curve": [2.72, 1, 3.113, 0.973, 2.72, 1, 3.113, 1]}, {"time": 3.5, "x": 0.973, "curve": [3.887, 0.973, 4.28, 1, 3.887, 1, 4.28, 1]}, {"time": 4.6667}]}, "bone16": {"rotate": [{"curve": [0.387, 0, 0.78, -0.27]}, {"time": 1.1667, "value": -0.27, "curve": [1.554, -0.27, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -0.27]}, {"time": 3.5, "value": -0.27, "curve": [3.887, -0.27, 4.28, 0]}, {"time": 4.6667}], "translate": [{"curve": [0.193, 0, 0.68, -2.96, 0.193, 0, 0.68, 0]}, {"time": 1.1667, "x": -2.96, "curve": [1.653, -2.96, 1.944, 0, 1.653, 0, 1.944, 0]}, {"time": 2.3333, "curve": [2.527, 0, 3.013, -2.96, 2.527, 0, 3.013, 0]}, {"time": 3.5, "x": -2.96, "curve": [3.987, -2.96, 4.473, 0, 3.987, 0, 4.473, 0]}, {"time": 4.6667}]}, "bone17": {"rotate": [{"curve": [0.387, 0, 0.78, 0.9]}, {"time": 1.1667, "value": 0.9, "curve": [1.554, 0.9, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 0.9]}, {"time": 3.5, "value": 0.9, "curve": [3.887, 0.9, 4.28, 0]}, {"time": 4.6667}], "scale": [{"curve": [0.387, 1, 0.78, 0.986, 0.387, 1, 0.78, 1]}, {"time": 1.1667, "x": 0.986, "curve": [1.554, 0.986, 1.944, 1, 1.554, 1, 1.944, 1]}, {"time": 2.3333, "curve": [2.72, 1, 3.113, 0.986, 2.72, 1, 3.113, 1]}, {"time": 3.5, "x": 0.986, "curve": [3.887, 0.986, 4.28, 1, 3.887, 1, 4.28, 1]}, {"time": 4.6667}]}, "bone18": {"rotate": [{"curve": [0.387, 0, 0.78, -0.64]}, {"time": 1.1667, "value": -0.64, "curve": [1.554, -0.64, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -0.64]}, {"time": 3.5, "value": -0.64, "curve": [3.887, -0.64, 4.28, 0]}, {"time": 4.6667}]}, "bone55": {"rotate": [{"curve": [0.387, 0, 0.78, -5.19]}, {"time": 1.1667, "value": -5.19, "curve": [1.554, -5.19, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -5.19]}, {"time": 3.5, "value": -5.19, "curve": [3.887, -5.19, 4.28, 0]}, {"time": 4.6667}]}, "bone56": {"rotate": [{"value": -0.16, "curve": [0.067, -0.07, 0.103, 0]}, {"time": 0.1667, "curve": [0.554, 0, 0.946, -2.09]}, {"time": 1.3333, "value": -2.09, "curve": [1.657, -2.09, 2.003, -0.62]}, {"time": 2.3333, "value": -0.16, "curve": [2.4, -0.07, 2.437, 0]}, {"time": 2.5, "curve": [2.887, 0, 3.28, -2.09]}, {"time": 3.6667, "value": -2.09, "curve": [3.99, -2.09, 4.344, -0.61]}, {"time": 4.6667, "value": -0.16}]}, "bone57": {"rotate": [{"value": -0.55, "curve": [0.132, -0.24, 0.272, 0]}, {"time": 0.4, "curve": [0.787, 0, 1.146, -2.09]}, {"time": 1.5333, "value": -2.09, "curve": [1.793, -2.09, 2.071, -1.15]}, {"time": 2.3333, "value": -0.55, "curve": [2.465, -0.24, 2.606, 0]}, {"time": 2.7333, "curve": [3.12, 0, 3.48, -2.09]}, {"time": 3.8667, "value": -2.09, "curve": [4.126, -2.09, 4.409, -1.14]}, {"time": 4.6667, "value": -0.55}]}, "bone58": {"rotate": [{"curve": [0.387, 0, 0.78, -3.11]}, {"time": 1.1667, "value": -3.11, "curve": [1.554, -3.11, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -3.11]}, {"time": 3.5, "value": -3.11, "curve": [3.887, -3.11, 4.28, 0]}, {"time": 4.6667}]}, "bone59": {"rotate": [{"value": -0.24, "curve": [0.067, -0.11, 0.103, 0]}, {"time": 0.1667, "curve": [0.554, 0, 0.946, -3.11]}, {"time": 1.3333, "value": -3.11, "curve": [1.657, -3.11, 2.003, -0.93]}, {"time": 2.3333, "value": -0.24, "curve": [2.4, -0.11, 2.437, 0]}, {"time": 2.5, "curve": [2.887, 0, 3.28, -3.11]}, {"time": 3.6667, "value": -3.11, "curve": [3.99, -3.11, 4.344, -0.91]}, {"time": 4.6667, "value": -0.24}]}, "bone60": {"rotate": [{"curve": [0.387, 0, 0.78, 4.39]}, {"time": 1.1667, "value": 4.39, "curve": [1.554, 4.39, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 4.39]}, {"time": 3.5, "value": 4.39, "curve": [3.887, 4.39, 4.28, 0]}, {"time": 4.6667}]}, "bone61": {"rotate": [{"value": 0.35, "curve": [0.067, 0.15, 0.103, 0]}, {"time": 0.1667, "curve": [0.554, 0, 0.946, 4.39]}, {"time": 1.3333, "value": 4.39, "curve": [1.657, 4.39, 2.003, 1.31]}, {"time": 2.3333, "value": 0.35, "curve": [2.4, 0.15, 2.437, 0]}, {"time": 2.5, "curve": [2.887, 0, 3.28, 4.39]}, {"time": 3.6667, "value": 4.39, "curve": [3.99, 4.39, 4.344, 1.29]}, {"time": 4.6667, "value": 0.35}]}, "bone62": {"rotate": [{"value": -0.26, "curve": [0.067, -0.11, 0.103, 0]}, {"time": 0.1667, "curve": [0.554, 0, 0.946, -3.31]}, {"time": 1.3333, "value": -3.31, "curve": [1.657, -3.31, 2.003, -0.99]}, {"time": 2.3333, "value": -0.26, "curve": [2.4, -0.11, 2.437, 0]}, {"time": 2.5, "curve": [2.887, 0, 3.28, -3.31]}, {"time": 3.6667, "value": -3.31, "curve": [3.99, -3.31, 4.344, -0.97]}, {"time": 4.6667, "value": -0.26}]}, "bone63": {"rotate": [{"value": -0.87, "curve": [0.132, -0.38, 0.272, 0]}, {"time": 0.4, "curve": [0.787, 0, 1.146, -3.31]}, {"time": 1.5333, "value": -3.31, "curve": [1.793, -3.31, 2.071, -1.83]}, {"time": 2.3333, "value": -0.87, "curve": [2.465, -0.38, 2.606, 0]}, {"time": 2.7333, "curve": [3.12, 0, 3.48, -3.31]}, {"time": 3.8667, "value": -3.31, "curve": [4.126, -3.31, 4.409, -1.81]}, {"time": 4.6667, "value": -0.87}]}, "bone64": {"rotate": [{"value": -0.26, "curve": [0.067, -0.11, 0.103, 0]}, {"time": 0.1667, "curve": [0.554, 0, 0.946, -3.37]}, {"time": 1.3333, "value": -3.37, "curve": [1.657, -3.37, 2.003, -1.01]}, {"time": 2.3333, "value": -0.26, "curve": [2.4, -0.11, 2.437, 0]}, {"time": 2.5, "curve": [2.887, 0, 3.28, -3.37]}, {"time": 3.6667, "value": -3.37, "curve": [3.99, -3.37, 4.344, -0.99]}, {"time": 4.6667, "value": -0.26}]}, "bone65": {"rotate": [{"value": -0.88, "curve": [0.132, -0.39, 0.272, 0]}, {"time": 0.4, "curve": [0.787, 0, 1.146, -3.37]}, {"time": 1.5333, "value": -3.37, "curve": [1.793, -3.37, 2.071, -1.86]}, {"time": 2.3333, "value": -0.88, "curve": [2.465, -0.39, 2.606, 0]}, {"time": 2.7333, "curve": [3.12, 0, 3.48, -3.37]}, {"time": 3.8667, "value": -3.37, "curve": [4.126, -3.37, 4.409, -1.84]}, {"time": 4.6667, "value": -0.88}]}, "bone66": {"rotate": [{"curve": [0.387, 0, 0.78, 1]}, {"time": 1.1667, "value": 1, "curve": [1.554, 1, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 1]}, {"time": 3.5, "value": 1, "curve": [3.887, 1, 4.28, 0]}, {"time": 4.6667}]}, "bone67": {"rotate": [{"curve": [0.387, 0, 0.78, 3.12]}, {"time": 1.1667, "value": 3.12, "curve": [1.554, 3.12, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 3.12]}, {"time": 3.5, "value": 3.12, "curve": [3.887, 3.12, 4.28, 0]}, {"time": 4.6667}]}, "bone68": {"rotate": [{"curve": [0.387, 0, 0.78, 2.43]}, {"time": 1.1667, "value": 2.43, "curve": [1.554, 2.43, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 2.43]}, {"time": 3.5, "value": 2.43, "curve": [3.887, 2.43, 4.28, 0]}, {"time": 4.6667}]}, "bone69": {"rotate": [{"curve": [0.387, 0, 0.78, 3.42]}, {"time": 1.1667, "value": 3.42, "curve": [1.554, 3.42, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 3.42]}, {"time": 3.5, "value": 3.42, "curve": [3.887, 3.42, 4.28, 0]}, {"time": 4.6667}]}, "bone72": {"rotate": [{"curve": [0.387, 0, 0.78, -0.61]}, {"time": 1.1667, "value": -0.61, "curve": [1.554, -0.61, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -0.61]}, {"time": 3.5, "value": -0.61, "curve": [3.887, -0.61, 4.28, 0]}, {"time": 4.6667}]}, "bone73": {"rotate": [{"curve": [0.387, 0, 0.78, -3.37]}, {"time": 1.1667, "value": -3.37, "curve": [1.554, -3.37, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -3.37]}, {"time": 3.5, "value": -3.37, "curve": [3.887, -3.37, 4.28, 0]}, {"time": 4.6667}]}, "bone74": {"rotate": [{"curve": [0.387, 0, 0.78, 1.22]}, {"time": 1.1667, "value": 1.22, "curve": [1.554, 1.22, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 1.22]}, {"time": 3.5, "value": 1.22, "curve": [3.887, 1.22, 4.28, 0]}, {"time": 4.6667}]}, "bone75": {"rotate": [{"curve": [0.387, 0, 0.78, 3.35]}, {"time": 1.1667, "value": 3.35, "curve": [1.554, 3.35, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 3.35]}, {"time": 3.5, "value": 3.35, "curve": [3.887, 3.35, 4.28, 0]}, {"time": 4.6667}]}, "bone76": {"rotate": [{"curve": [0.387, 0, 0.78, -0.94]}, {"time": 1.1667, "value": -0.94, "curve": [1.554, -0.94, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, -0.94]}, {"time": 3.5, "value": -0.94, "curve": [3.887, -0.94, 4.28, 0]}, {"time": 4.6667}]}, "bone77": {"rotate": [{"curve": [0.387, 0, 0.78, 0.88]}, {"time": 1.1667, "value": 0.88, "curve": [1.554, 0.88, 1.944, 0]}, {"time": 2.3333, "curve": [2.72, 0, 3.113, 0.88]}, {"time": 3.5, "value": 0.88, "curve": [3.887, 0.88, 4.28, 0]}, {"time": 4.6667}], "scale": [{"curve": [0.387, 1, 0.78, 0.978, 0.387, 1, 0.78, 1]}, {"time": 1.1667, "x": 0.978}, {"time": 2.3333, "curve": [2.72, 1, 3.113, 0.978, 2.72, 1, 3.113, 1]}, {"time": 3.5, "x": 0.978}, {"time": 4.6667}]}, "bone78": {"translate": [{"curve": [0.193, 0, 0.68, 1.13, 0.193, 0, 0.68, 0]}, {"time": 1.1667, "x": 1.13, "curve": [1.653, 1.13, 1.944, 0, 1.653, 0, 1.944, 0]}, {"time": 2.3333, "curve": [2.527, 0, 3.013, 1.13, 2.527, 0, 3.013, 0]}, {"time": 3.5, "x": 1.13, "curve": [3.987, 1.13, 4.473, 0, 3.987, 0, 4.473, 0]}, {"time": 4.6667}]}, "bone20": {"translate": [{"curve": [0.193, 0, 0.683, -3.6, 0.193, 0, 0.683, 0]}, {"time": 1.1667, "x": -3.6, "curve": [1.65, -3.6, 1.944, 0, 1.65, 0, 1.944, 0]}, {"time": 2.3333, "curve": [2.527, 0, 3.016, -3.6, 2.527, 0, 3.016, 0]}, {"time": 3.5, "x": -3.6, "curve": [3.984, -3.6, 4.28, 0, 3.984, 0, 4.28, 0]}, {"time": 4.6667}]}, "bone21": {"rotate": [{"value": 0.57, "curve": [0.058, 0.25, 0.112, 0]}, {"time": 0.1667, "curve": [0.556, 0, 0.944, 9.42]}, {"time": 1.3333, "value": 9.42, "curve": [1.722, 9.42, 2.111, 0]}, {"time": 2.5, "curve": [2.889, 0, 3.278, 9.42]}, {"time": 3.6667, "value": 9.42, "curve": [4.001, 9.42, 4.336, 2.52]}, {"time": 4.6667, "value": 0.57}]}, "bone22": {"rotate": [{"value": 1.88, "curve": [0.113, 0.8, 0.223, 0]}, {"time": 0.3333, "curve": [0.722, 0, 1.111, 9.42]}, {"time": 1.5, "value": 9.42, "curve": [1.889, 9.42, 2.278, 0]}, {"time": 2.6667, "curve": [3.056, 0, 3.444, 9.42]}, {"time": 3.8333, "value": 9.42, "curve": [4.112, 9.42, 4.391, 4.62]}, {"time": 4.6667, "value": 1.88}]}, "bone23": {"rotate": [{"value": 5.45, "curve": [0.168, 2.56, 0.334, 0]}, {"time": 0.5, "curve": [0.889, 0, 1.278, 13.81]}, {"time": 1.6667, "value": 13.81, "curve": [2.056, 13.81, 2.444, 0]}, {"time": 2.8333, "curve": [3.222, 0, 3.611, 13.81]}, {"time": 4, "value": 13.81, "curve": [4.223, 13.81, 4.446, 9.32]}, {"time": 4.6667, "value": 5.45}]}, "bone24": {"rotate": [{"value": 11.38, "curve": [0.223, 6.13, 0.445, 0]}, {"time": 0.6667, "curve": [1.056, 0, 1.444, 18.79]}, {"time": 1.8333, "value": 18.79, "curve": [2.222, 18.79, 2.611, 0]}, {"time": 3, "curve": [3.389, 0, 3.778, 18.79]}, {"time": 4.1667, "value": 18.79, "curve": [4.334, 18.79, 4.501, 15.34]}, {"time": 4.6667, "value": 11.38}]}, "bone25": {"rotate": [{"value": -0.25, "curve": [0.058, -0.11, 0.112, 0]}, {"time": 0.1667, "curve": [0.556, 0, 0.944, -4.2]}, {"time": 1.3333, "value": -4.2, "curve": [1.722, -4.2, 2.111, 0]}, {"time": 2.5, "curve": [2.889, 0, 3.278, -4.2]}, {"time": 3.6667, "value": -4.2, "curve": [4.001, -4.2, 4.336, -1.12]}, {"time": 4.6667, "value": -0.25}]}, "bone26": {"rotate": [{"value": -0.84, "curve": [0.113, -0.36, 0.223, 0]}, {"time": 0.3333, "curve": [0.722, 0, 1.111, -4.2]}, {"time": 1.5, "value": -4.2, "curve": [1.889, -4.2, 2.278, 0]}, {"time": 2.6667, "curve": [3.056, 0, 3.444, -4.2]}, {"time": 3.8333, "value": -4.2, "curve": [4.112, -4.2, 4.391, -2.06]}, {"time": 4.6667, "value": -0.84}]}, "bone27": {"rotate": [{"value": -1.65, "curve": [0.168, -0.78, 0.334, 0]}, {"time": 0.5, "curve": [0.889, 0, 1.278, -4.2]}, {"time": 1.6667, "value": -4.2, "curve": [2.056, -4.2, 2.444, 0]}, {"time": 2.8333, "curve": [3.222, 0, 3.611, -4.2]}, {"time": 4, "value": -4.2, "curve": [4.223, -4.2, 4.446, -2.83]}, {"time": 4.6667, "value": -1.65}]}, "bone28": {"rotate": [{"value": -6.26, "curve": [0.223, -3.37, 0.445, 0]}, {"time": 0.6667, "curve": [1.056, 0, 1.444, -10.34]}, {"time": 1.8333, "value": -10.34, "curve": [2.222, -10.34, 2.611, 0]}, {"time": 3, "curve": [3.389, 0, 3.778, -10.34]}, {"time": 4.1667, "value": -10.34, "curve": [4.334, -10.34, 4.501, -8.44]}, {"time": 4.6667, "value": -6.26}]}, "bone29": {"rotate": [{"curve": [0.389, 0, 0.778, -7.37]}, {"time": 1.1667, "value": -7.37, "curve": [1.556, -7.37, 1.944, 0]}, {"time": 2.3333, "curve": [2.722, 0, 3.111, -7.37]}, {"time": 3.5, "value": -7.37, "curve": [3.889, -7.37, 4.278, 0]}, {"time": 4.6667}]}, "bone30": {"rotate": [{"value": -0.45, "curve": [0.058, -0.2, 0.112, 0]}, {"time": 0.1667, "curve": [0.556, 0, 0.944, -7.37]}, {"time": 1.3333, "value": -7.37, "curve": [1.722, -7.37, 2.111, 0]}, {"time": 2.5, "curve": [2.889, 0, 3.278, -7.37]}, {"time": 3.6667, "value": -7.37, "curve": [4.001, -7.37, 4.336, -1.97]}, {"time": 4.6667, "value": -0.45}]}, "bone31": {"rotate": [{"value": -2.13, "curve": [0.113, -0.9, 0.223, 0]}, {"time": 0.3333, "curve": [0.722, 0, 1.111, -10.65]}, {"time": 1.5, "value": -10.65, "curve": [1.889, -10.65, 2.278, 0]}, {"time": 2.6667, "curve": [3.056, 0, 3.444, -10.65]}, {"time": 3.8333, "value": -10.65, "curve": [4.112, -10.65, 4.391, -5.23]}, {"time": 4.6667, "value": -2.13}]}, "bone32": {"rotate": [{"value": -4.85, "curve": [0.168, -2.28, 0.334, 0]}, {"time": 0.5, "curve": [0.889, 0, 1.278, -12.3]}, {"time": 1.6667, "value": -12.3, "curve": [2.056, -12.3, 2.444, 0]}, {"time": 2.8333, "curve": [3.222, 0, 3.611, -12.3]}, {"time": 4, "value": -12.3, "curve": [4.223, -12.3, 4.446, -8.3]}, {"time": 4.6667, "value": -4.85}]}, "bone33": {"rotate": [{"curve": [0.389, 0, 0.778, 3.1]}, {"time": 1.1667, "value": 3.1, "curve": [1.556, 3.1, 1.944, 0]}, {"time": 2.3333, "curve": [2.722, 0, 3.111, 3.1]}, {"time": 3.5, "value": 3.1, "curve": [3.889, 3.1, 4.278, 0]}, {"time": 4.6667}]}, "bone34": {"rotate": [{"value": 0.19, "curve": [0.058, 0.08, 0.112, 0]}, {"time": 0.1667, "curve": [0.556, 0, 0.944, 3.1]}, {"time": 1.3333, "value": 3.1, "curve": [1.722, 3.1, 2.111, 0]}, {"time": 2.5, "curve": [2.889, 0, 3.278, 3.1]}, {"time": 3.6667, "value": 3.1, "curve": [4.001, 3.1, 4.336, 0.83]}, {"time": 4.6667, "value": 0.19}]}, "bone35": {"rotate": [{"value": 0.62, "curve": [0.113, 0.26, 0.223, 0]}, {"time": 0.3333, "curve": [0.722, 0, 1.111, 3.1]}, {"time": 1.5, "value": 3.1, "curve": [1.889, 3.1, 2.278, 0]}, {"time": 2.6667, "curve": [3.056, 0, 3.444, 3.1]}, {"time": 3.8333, "value": 3.1, "curve": [4.112, 3.1, 4.391, 1.52]}, {"time": 4.6667, "value": 0.62}]}, "bone36": {"rotate": [{"value": 2.35, "curve": [0.168, 1.11, 0.334, 0]}, {"time": 0.5, "curve": [0.889, 0, 1.278, 5.96]}, {"time": 1.6667, "value": 5.96, "curve": [2.056, 5.96, 2.444, 0]}, {"time": 2.8333, "curve": [3.222, 0, 3.611, 5.96]}, {"time": 4, "value": 5.96, "curve": [4.223, 5.96, 4.446, 4.02]}, {"time": 4.6667, "value": 2.35}]}, "bone37": {"rotate": [{"value": 7.6, "curve": [0.223, 4.1, 0.445, 0]}, {"time": 0.6667, "curve": [1.056, 0, 1.444, 12.55]}, {"time": 1.8333, "value": 12.55, "curve": [2.222, 12.55, 2.611, 0]}, {"time": 3, "curve": [3.389, 0, 3.778, 12.55]}, {"time": 4.1667, "value": 12.55, "curve": [4.334, 12.55, 4.501, 10.25]}, {"time": 4.6667, "value": 7.6}]}, "bone38": {"rotate": [{"value": 0.26, "curve": [0.058, 0.12, 0.112, 0]}, {"time": 0.1667, "curve": [0.556, 0, 0.944, 4.3]}, {"time": 1.3333, "value": 4.3, "curve": [1.722, 4.3, 2.111, 0]}, {"time": 2.5, "curve": [2.889, 0, 3.278, 4.3]}, {"time": 3.6667, "value": 4.3, "curve": [4.001, 4.3, 4.336, 1.15]}, {"time": 4.6667, "value": 0.26}]}, "bone39": {"rotate": [{"value": 0.86, "curve": [0.113, 0.36, 0.223, 0]}, {"time": 0.3333, "curve": [0.722, 0, 1.111, 4.3]}, {"time": 1.5, "value": 4.3, "curve": [1.889, 4.3, 2.278, 0]}, {"time": 2.6667, "curve": [3.056, 0, 3.444, 4.3]}, {"time": 3.8333, "value": 4.3, "curve": [4.112, 4.3, 4.391, 2.11]}, {"time": 4.6667, "value": 0.86}]}, "bone40": {"rotate": [{"value": 1.69, "curve": [0.168, 0.8, 0.334, 0]}, {"time": 0.5, "curve": [0.889, 0, 1.278, 4.3]}, {"time": 1.6667, "value": 4.3, "curve": [2.056, 4.3, 2.444, 0]}, {"time": 2.8333, "curve": [3.222, 0, 3.611, 4.3]}, {"time": 4, "value": 4.3, "curve": [4.223, 4.3, 4.446, 2.9]}, {"time": 4.6667, "value": 1.69}]}, "bone41": {"rotate": [{"value": 2.6, "curve": [0.223, 1.4, 0.445, 0]}, {"time": 0.6667, "curve": [1.056, 0, 1.444, 4.3]}, {"time": 1.8333, "value": 4.3, "curve": [2.222, 4.3, 2.611, 0]}, {"time": 3, "curve": [3.389, 0, 3.778, 4.3]}, {"time": 4.1667, "value": 4.3, "curve": [4.334, 4.3, 4.501, 3.51]}, {"time": 4.6667, "value": 2.6}]}, "bone42": {"rotate": [{"curve": [0.389, 0, 0.778, 7.23]}, {"time": 1.1667, "value": 7.23, "curve": [1.556, 7.23, 1.944, 0]}, {"time": 2.3333, "curve": [2.722, 0, 3.111, 7.23]}, {"time": 3.5, "value": 7.23, "curve": [3.889, 7.23, 4.278, 0]}, {"time": 4.6667}]}, "bone43": {"rotate": [{"value": 0.44, "curve": [0.058, 0.19, 0.112, 0]}, {"time": 0.1667, "curve": [0.556, 0, 0.944, 7.23]}, {"time": 1.3333, "value": 7.23, "curve": [1.722, 7.23, 2.111, 0]}, {"time": 2.5, "curve": [2.889, 0, 3.278, 7.23]}, {"time": 3.6667, "value": 7.23, "curve": [4.001, 7.23, 4.336, 1.94]}, {"time": 4.6667, "value": 0.44}]}, "bone44": {"rotate": [{"value": 2.71, "curve": [0.113, 1.15, 0.223, 0]}, {"time": 0.3333, "curve": [0.722, 0, 1.111, 13.55]}, {"time": 1.5, "value": 13.55, "curve": [1.889, 13.55, 2.278, 0]}, {"time": 2.6667, "curve": [3.056, 0, 3.444, 13.55]}, {"time": 3.8333, "value": 13.55, "curve": [4.112, 13.55, 4.391, 6.65]}, {"time": 4.6667, "value": 2.71}]}, "bone45": {"rotate": [{"value": 5.34, "curve": [0.168, 2.51, 0.334, 0]}, {"time": 0.5, "curve": [0.889, 0, 1.278, 13.55]}, {"time": 1.6667, "value": 13.55, "curve": [2.056, 13.55, 2.444, 0]}, {"time": 2.8333, "curve": [3.222, 0, 3.611, 13.55]}, {"time": 4, "value": 13.55, "curve": [4.223, 13.55, 4.446, 9.14]}, {"time": 4.6667, "value": 5.34}]}, "bone47": {"rotate": [{"value": -2.21, "curve": [0.113, -0.94, 0.223, 0]}, {"time": 0.3333, "curve": [0.722, 0, 1.111, -11.03]}, {"time": 1.5, "value": -11.03, "curve": [1.889, -11.03, 2.278, 0]}, {"time": 2.6667, "curve": [3.056, 0, 3.444, -11.03]}, {"time": 3.8333, "value": -11.03, "curve": [4.112, -11.03, 4.391, -5.41]}, {"time": 4.6667, "value": -2.21}]}, "bone48": {"rotate": [{"value": -5.26, "curve": [0.168, -2.48, 0.334, 0]}, {"time": 0.5, "curve": [0.889, 0, 1.278, -13.34]}, {"time": 1.6667, "value": -13.34, "curve": [2.056, -13.34, 2.444, 0]}, {"time": 2.8333, "curve": [3.222, 0, 3.611, -13.34]}, {"time": 4, "value": -13.34, "curve": [4.223, -13.34, 4.446, -9]}, {"time": 4.6667, "value": -5.26}]}, "bone49": {"rotate": [{"value": -8.08, "curve": [0.223, -4.35, 0.445, 0]}, {"time": 0.6667, "curve": [1.056, 0, 1.444, -13.34]}, {"time": 1.8333, "value": -13.34, "curve": [2.222, -13.34, 2.611, 0]}, {"time": 3, "curve": [3.389, 0, 3.778, -13.34]}, {"time": 4.1667, "value": -13.34, "curve": [4.334, -13.34, 4.501, -10.89]}, {"time": 4.6667, "value": -8.08}]}, "bone52": {"rotate": [{"value": -3.63, "curve": [0.113, -1.54, 0.223, 0]}, {"time": 0.3333, "curve": [0.722, 0, 1.111, -18.17]}, {"time": 1.5, "value": -18.17, "curve": [1.889, -18.17, 2.278, 0]}, {"time": 2.6667, "curve": [3.056, 0, 3.444, -18.17]}, {"time": 3.8333, "value": -18.17, "curve": [4.112, -18.17, 4.391, -8.92]}, {"time": 4.6667, "value": -3.63}]}, "bone46": {"rotate": [{"value": -0.67, "curve": [0.058, -0.3, 0.112, 0]}, {"time": 0.1667, "curve": [0.556, 0, 0.944, -11.03]}, {"time": 1.3333, "value": -11.03, "curve": [1.722, -11.03, 2.111, 0]}, {"time": 2.5, "curve": [2.889, 0, 3.278, -11.03]}, {"time": 3.6667, "value": -11.03, "curve": [4.001, -11.03, 4.336, -2.95]}, {"time": 4.6667, "value": -0.67}]}, "bone50": {"rotate": [{"curve": [0.389, 0, 0.778, -7.08]}, {"time": 1.1667, "value": -7.08, "curve": [1.556, -7.08, 1.944, 0]}, {"time": 2.3333, "curve": [2.722, 0, 3.111, -7.08]}, {"time": 3.5, "value": -7.08, "curve": [3.889, -7.08, 4.278, 0]}, {"time": 4.6667}]}, "bone51": {"rotate": [{"value": -0.85, "curve": [0.058, -0.38, 0.112, 0]}, {"time": 0.1667, "curve": [0.556, 0, 0.944, -14.07]}, {"time": 1.3333, "value": -14.07, "curve": [1.722, -14.07, 2.111, 0]}, {"time": 2.5, "curve": [2.889, 0, 3.278, -14.07]}, {"time": 3.6667, "value": -14.07, "curve": [4.001, -14.07, 4.336, -3.77]}, {"time": 4.6667, "value": -0.85}]}, "bone53": {"rotate": [{}, {"time": 1.1667, "value": -5.51}, {"time": 2.3333}]}, "bone54": {"rotate": [{"value": -0.87, "curve": [0.058, -0.39, 0.112, 0]}, {"time": 0.1667, "curve": [0.556, 0, 0.944, -14.44]}, {"time": 1.3333, "value": -14.44, "curve": [1.722, -14.44, 2.111, 0]}, {"time": 2.5, "curve": [2.889, 0, 3.278, -14.44]}, {"time": 3.6667, "value": -14.44, "curve": [4.001, -14.44, 4.336, -3.86]}, {"time": 4.6667, "value": -0.87}]}}, "attachments": {"default": {"biyan": {"biyan": {"deform": [{"time": 3.4333, "offset": 8, "vertices": [0.18136, 0.25812, 0.18135, 0.25787]}, {"time": 3.7333, "vertices": [0.23815, 6.0943, 0.23789, 6.09369, 0.15271, 5.9339, 0.15243, 5.93323, -1.50801, 11.63104, -1.50846, 11.6308, 0.54411, 10.01306, 0.54391, 10.01123, 0.70148, -2.58948, 0.70121, -2.59009]}]}}, "lian": {"lian": {"deform": [{"time": 3.4333, "offset": 644, "vertices": [-1.25528, -4.86279, -1.25534, -4.86279, -0.1406, -9.12164, -0.14073, -9.12164, 1.411, -12.48285, 1.41079, -12.48254, -1.08952, -12.93732, -1.08972, -12.93701, -0.57095, -11.89612, -0.57118, -11.89612, 1.05582, -8.46338, 1.0558, -8.4632, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.95751, -5.1134, 0.95734, -5.11279, 1.37794, -7.97833, 1.37778, -7.97791, 1.24369, -7.76141, 1.24355, -7.76105, -0.63678, -3.46399, -0.63697, -3.46326]}, {"time": 3.6333, "offset": 657, "vertices": [-0.22546, -0.00522, -0.22504]}]}}, "toufa8": {"toufa8": {"deform": [{"time": 3.6333, "offset": 8, "vertices": [0.01308, 0.50909, 0.01312, 0.50922]}]}}}}}, "xiuxian": {"slots": {"biyan": {"rgba": [{"color": "ffffff02", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff02"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff02", "curve": "stepped"}, {"time": 2.5667, "color": "ffffff02"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 2.9, "color": "ffffff02"}], "attachment": [{"time": 0.0667, "name": "biyan"}, {"time": 0.3333}, {"time": 2.5667, "name": "biyan"}, {"time": 2.9}]}}, "bones": {"bone": {"rotate": [{}, {"time": 1.1667, "value": 2.93, "curve": [1.169, 2.93, 1.404, 3.83]}, {"time": 1.6333, "value": 3.83, "curve": [1.864, 3.83, 2.098, 2.93]}, {"time": 2.1, "value": 2.93, "curve": [2.884, 4.08, 3.132, 0]}, {"time": 3.6667}], "translate": [{"curve": [0.198, 0, 0.611, -21.66, 0.198, 0, 0.611, 0.24]}, {"time": 1.1667, "x": -21.63, "y": 0.16, "curve": [1.321, -21.63, 1.479, -15.76, 1.321, 0.13, 1.479, -21.76]}, {"time": 1.6333, "x": -15.76, "y": -21.76, "curve": [1.787, -15.76, 1.833, -18.75, 1.787, -21.76, 1.833, 0.14]}, {"time": 2.1, "x": -21.63, "y": 0.16, "curve": [2.884, -30.1, 3.132, 0, 2.884, 0.22, 3.132, 0]}, {"time": 3.6667}]}, "bone10": {"rotate": [{"time": 1.1667, "curve": [1.321, 0, 1.479, -2.29]}, {"time": 1.6333, "value": -2.29, "curve": [1.787, -2.29, 1.833, 0]}, {"time": 2.1}]}, "bone11": {"rotate": [{"curve": [0.149, 0, 0.973, 1.97]}, {"time": 1.1667, "value": 1.97, "curve": [1.475, 1.97, 1.565, -0.14]}, {"time": 2.1, "value": -0.2, "curve": [2.884, -0.27, 3.132, 0]}, {"time": 3.6667}]}, "bone12": {"rotate": [{"time": 1.1667, "curve": [1.321, 0, 1.479, 3.27]}, {"time": 1.6333, "value": 3.27, "curve": [1.787, 3.27, 1.833, 0]}, {"time": 2.1}]}, "bone13": {"rotate": [{"curve": [0.149, 0, 0.973, 2.05]}, {"time": 1.1667, "value": 2.05, "curve": [1.475, 2.05, 1.565, 0.43]}, {"time": 2.1, "value": 0.58, "curve": [2.884, 0.81, 3.132, 0]}, {"time": 3.6667}]}, "bone14": {"rotate": [{"curve": [0.158, 0, 0.611, 1.27]}, {"time": 1.1667, "value": 0.63, "curve": [1.321, 0.45, 1.479, 1.19]}, {"time": 1.6333, "value": 1.19, "curve": [1.787, 1.19, 1.833, 0.55]}, {"time": 2.1, "value": 0.63, "curve": [2.884, 0.88, 3.132, 0]}, {"time": 3.6667}]}, "bone15": {"rotate": [{"curve": [0.158, 0, 0.611, 1.85]}, {"time": 1.1667, "value": 1.23, "curve": [1.321, 1.06, 1.479, 0.86]}, {"time": 1.6333, "value": 0.86, "curve": [1.787, 0.86, 1.833, 1.07]}, {"time": 2.1, "value": 1.23, "curve": [2.884, 1.72, 3.132, 0]}, {"time": 3.6667}], "translate": [{"time": 1.1667, "curve": [1.321, 0, 1.479, -3.42, 1.321, 0, 1.479, 0]}, {"time": 1.6333, "x": -3.42, "curve": [1.787, -3.42, 1.833, 0, 1.787, 0, 1.833, 0]}, {"time": 2.1}], "scale": [{"time": 1.1667, "curve": [1.321, 1, 1.479, 0.965, 1.321, 1, 1.479, 1]}, {"time": 1.6333, "x": 0.965, "curve": [1.787, 0.965, 1.833, 1, 1.787, 1, 1.833, 1]}, {"time": 2.1}]}, "bone16": {"rotate": [{"curve": [0.198, 0, 0.961, 0.3]}, {"time": 1.1667, "value": 0.3, "curve": [1.321, 0.3, 1.479, 0.9]}, {"time": 1.6333, "value": 0.9, "curve": [1.787, 0.9, 1.833, 0.26]}, {"time": 2.1, "value": 0.3, "curve": [2.884, 0.41, 3.132, 0]}, {"time": 3.6667}], "translate": [{"time": 1.1667, "curve": [1.321, 0, 1.479, -5.93, 1.321, 0, 1.479, 0]}, {"time": 1.6333, "x": -5.93, "curve": [1.787, -5.93, 1.833, 0, 1.787, 0, 1.833, 0]}, {"time": 2.1}]}, "bone17": {"rotate": [{"curve": [0.158, 0, 0.611, 4.39]}, {"time": 1.1667, "value": 3.29, "curve": [1.321, 2.98, 1.479, 0.43]}, {"time": 1.6333, "value": 0.51, "curve": [1.787, 0.59, 1.833, 2.85]}, {"time": 2.1, "value": 3.29, "curve": [2.884, 4.57, 3.132, 0]}, {"time": 3.6667}]}, "bone18": {"rotate": [{"curve": [0.475, 0, 0.611, 1.66]}, {"time": 1.1667, "value": 0.84, "curve": [1.321, 0.61, 1.479, -2.38]}, {"time": 1.6333, "value": -2.37, "curve": [1.79, -2.35, 1.833, 0.73]}, {"time": 2.1, "value": 0.84, "curve": [2.884, 1.17, 3.132, 0]}, {"time": 3.6667}]}, "bone21": {"rotate": [{"value": 0.57, "curve": [0.058, 0.25, 0.222, -6.98]}, {"time": 0.3333, "value": -6.98, "curve": [0.63, -6.98, 0.704, 12.81]}, {"time": 1, "value": 12.81, "curve": [1.111, 12.81, 1.19, -4.21]}, {"time": 1.2667, "value": -4.21, "curve": [1.344, -4.21, 1.415, 3.33]}, {"time": 1.5, "value": 3.33, "curve": [1.577, 3.33, 1.644, -0.73]}, {"time": 1.7333, "value": -0.73, "curve": [1.837, -0.73, 2.089, 6.2]}, {"time": 2.2667, "value": 6.2, "curve": [2.536, 6.2, 2.836, -5.12]}, {"time": 3.1, "value": -5.12, "curve": [3.371, -5.12, 3.478, 1.61]}, {"time": 3.6667, "value": 0.57}]}, "bone22": {"rotate": [{"value": 1.88, "curve": [0.113, 0.8, 0.357, -6.98]}, {"time": 0.5, "value": -6.98, "curve": [0.796, -6.98, 0.961, 12.81]}, {"time": 1.1667, "value": 12.81, "curve": [1.244, 12.81, 1.323, -4.21]}, {"time": 1.4, "value": -4.21, "curve": [1.477, -4.21, 1.549, 3.33]}, {"time": 1.6333, "value": 3.33, "curve": [1.711, 3.33, 1.791, -0.33]}, {"time": 1.8667, "value": -0.73, "curve": [1.97, -1.25, 2.323, 6.2]}, {"time": 2.5, "value": 6.2, "curve": [2.769, 6.2, 3.036, -5.12]}, {"time": 3.3, "value": -5.12, "curve": [3.481, -5.12, 3.544, 3.06]}, {"time": 3.6667, "value": 1.88}]}, "bone23": {"rotate": [{"value": 5.45, "curve": [0.168, 2.56, 0.5, -6.98]}, {"time": 0.6667, "value": -6.98, "curve": [0.963, -6.98, 1.061, 12.81]}, {"time": 1.2667, "value": 12.81, "curve": [1.344, 12.81, 1.423, -4.21]}, {"time": 1.5, "value": -4.21, "curve": [1.577, -4.21, 1.649, 3.33]}, {"time": 1.7333, "value": 3.33, "curve": [1.811, 3.33, 1.891, -0.33]}, {"time": 1.9667, "value": -0.73, "curve": [2.07, -1.25, 2.489, 6.2]}, {"time": 2.6667, "value": 6.2, "curve": [2.936, 6.2, 3.623, 6.2]}, {"time": 3.6667, "value": 5.45}]}, "bone24": {"rotate": [{"value": 11.38, "curve": [0.223, 6.13, 0.73, -6.98]}, {"time": 0.8333, "value": -6.98, "curve": [1.13, -6.98, 1.195, 12.81]}, {"time": 1.4, "value": 12.81, "curve": [1.477, 12.81, 1.556, -4.21]}, {"time": 1.6333, "value": -4.21, "curve": [1.71, -4.21, 1.782, 3.33]}, {"time": 1.8667, "value": 3.33, "curve": [1.945, 3.33, 1.968, -0.73]}, {"time": 2.1, "value": -0.73, "curve": [2.369, -0.73, 2.636, 6.2]}, {"time": 2.9, "value": 6.2, "curve": [3.169, 6.2, 3.446, 16.56]}, {"time": 3.6667, "value": 11.38}]}, "bone25": {"rotate": [{"value": -0.25, "curve": [0.058, -0.11, 0.222, -2.28]}, {"time": 0.3333, "value": -2.28, "curve": [0.556, -2.28, 0.778, 7.01]}, {"time": 1, "value": 7.01, "curve": [1.111, 7.01, 1.19, -2.08]}, {"time": 1.2667, "value": -2.08, "curve": [1.344, -2.08, 1.415, 1.36]}, {"time": 1.5, "value": 1.37, "curve": [1.578, 1.38, 1.658, -1.73]}, {"time": 1.7333, "value": -1.97, "curve": [1.889, -2.44, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -6.34]}, {"time": 3.1, "value": -6.34, "curve": [3.371, -6.34, 3.478, -0.72]}, {"time": 3.6667, "value": -0.25}]}, "bone26": {"rotate": [{"value": -0.84, "curve": [0.113, -0.36, 0.357, -2.28]}, {"time": 0.5, "value": -2.28, "curve": [0.722, -2.28, 1.013, 7.01]}, {"time": 1.1667, "value": 7.01, "curve": [1.244, 7.01, 1.323, -2.08]}, {"time": 1.4, "value": -2.08, "curve": [1.477, -2.08, 1.549, 1.36]}, {"time": 1.6333, "value": 1.37, "curve": [1.711, 1.38, 1.791, -1.73]}, {"time": 1.8667, "value": -1.97, "curve": [2.022, -2.44, 2.234, 0]}, {"time": 2.5, "curve": [2.769, 0, 3.036, -6.33]}, {"time": 3.3, "value": -6.34, "curve": [3.481, -6.35, 3.544, -1.36]}, {"time": 3.6667, "value": -0.84}]}, "bone27": {"rotate": [{"value": -1.65, "curve": [0.168, -0.78, 0.5, -2.28]}, {"time": 0.6667, "value": -2.28, "curve": [0.889, -2.28, 1.113, 7.01]}, {"time": 1.2667, "value": 7.01, "curve": [1.344, 7.01, 1.423, -2.08]}, {"time": 1.5, "value": -2.08, "curve": [1.577, -2.08, 1.649, 1.36]}, {"time": 1.7333, "value": 1.37, "curve": [1.811, 1.38, 1.891, -1.73]}, {"time": 1.9667, "value": -1.97, "curve": [2.122, -2.44, 2.401, 0]}, {"time": 2.6667, "curve": [2.936, 0, 3.349, -3.31]}, {"time": 3.6667, "value": -1.65}]}, "bone28": {"rotate": [{"value": -6.26, "curve": [0.223, -3.37, 0.73, -2.28]}, {"time": 0.8333, "value": -2.28, "curve": [1.056, -2.28, 1.246, 7.01]}, {"time": 1.4, "value": 7.01, "curve": [1.477, 7.01, 1.556, -2.08]}, {"time": 1.6333, "value": -2.08, "curve": [1.71, -2.08, 1.782, 1.36]}, {"time": 1.8667, "value": 1.37, "curve": [1.944, 1.38, 1.969, -1.97]}, {"time": 2.1, "value": -1.97, "curve": [2.369, -1.97, 2.636, 0]}, {"time": 2.9, "curve": [3.169, 0, 3.411, -9.57]}, {"time": 3.6667, "value": -6.26}]}, "bone29": {"rotate": [{"curve": [0.389, 0, 0.635, 5.99]}, {"time": 0.8333, "value": 5.99, "curve": [0.944, 5.99, 1.09, -6.01]}, {"time": 1.1667, "value": -6.01, "curve": [1.244, -6.01, 1.315, -2.27]}, {"time": 1.4, "value": -2.26, "curve": [1.478, -2.25, 1.558, -6.09]}, {"time": 1.6333, "value": -5.78, "curve": [1.789, -5.17, 1.834, 0]}, {"time": 2.1, "curve": [2.369, 0, 2.636, -9.98]}, {"time": 2.9, "value": -9.98, "curve": [3.171, -9.98, 3.411, 0]}, {"time": 3.6667}]}, "bone30": {"rotate": [{"value": -0.45, "curve": [0.058, -0.2, 0.222, -2.17]}, {"time": 0.3333, "value": -2.17, "curve": [0.556, -2.17, 0.778, 5.99]}, {"time": 1, "value": 5.99, "curve": [1.111, 5.99, 1.19, -6.01]}, {"time": 1.2667, "value": -6.01, "curve": [1.344, -6.01, 1.415, -2.27]}, {"time": 1.5, "value": -2.26, "curve": [1.578, -2.25, 1.658, -6.09]}, {"time": 1.7333, "value": -5.78, "curve": [1.889, -5.17, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -9.98]}, {"time": 3.1, "value": -9.98, "curve": [3.371, -9.98, 3.478, -1.26]}, {"time": 3.6667, "value": -0.45}]}, "bone31": {"rotate": [{"value": -2.13, "curve": [0.113, -0.9, 0.357, -2.17]}, {"time": 0.5, "value": -2.17, "curve": [0.722, -2.17, 1.013, 5.99]}, {"time": 1.1667, "value": 5.99, "curve": [1.244, 5.99, 1.323, -6.01]}, {"time": 1.4, "value": -6.01, "curve": [1.477, -6.01, 1.549, -2.27]}, {"time": 1.6333, "value": -2.26, "curve": [1.711, -2.25, 1.791, -6.09]}, {"time": 1.8667, "value": -5.78, "curve": [2.022, -5.17, 2.234, 0]}, {"time": 2.5, "curve": [2.769, 0, 3.036, -9.96]}, {"time": 3.3, "value": -9.98, "curve": [3.481, -9.99, 3.544, -3.46]}, {"time": 3.6667, "value": -2.13}]}, "bone32": {"rotate": [{"value": -4.85, "curve": [0.168, -2.28, 0.5, -2.17]}, {"time": 0.6667, "value": -2.17, "curve": [0.889, -2.17, 1.113, 5.99]}, {"time": 1.2667, "value": 5.99, "curve": [1.344, 5.99, 1.423, -6.01]}, {"time": 1.5, "value": -6.01, "curve": [1.577, -6.01, 1.649, -2.27]}, {"time": 1.7333, "value": -2.26, "curve": [1.811, -2.25, 1.891, -6.09]}, {"time": 1.9667, "value": -5.78, "curve": [2.122, -5.17, 2.401, 0]}, {"time": 2.6667, "curve": [2.936, 0, 3.349, -9.7]}, {"time": 3.6667, "value": -4.85}]}, "bone33": {"rotate": [{"curve": [0.389, 0, 0.635, 8.4]}, {"time": 0.8333, "value": 8.4, "curve": [0.944, 8.4, 1.09, -4.46]}, {"time": 1.1667, "value": -4.46, "curve": [1.244, -4.46, 1.315, -0.34]}, {"time": 1.4, "value": -0.32, "curve": [1.478, -0.31, 1.558, -4.1]}, {"time": 1.6333, "value": -4.08, "curve": [1.789, -4.05, 1.834, 0]}, {"time": 2.1, "curve": [2.369, 0, 2.636, -7.18]}, {"time": 2.9, "value": -7.19, "curve": [3.171, -7.19, 3.411, 0]}, {"time": 3.6667}]}, "bone34": {"rotate": [{"value": 0.19, "curve": [0.058, 0.08, 0.222, -2.85]}, {"time": 0.3333, "value": -2.85, "curve": [0.556, -2.85, 0.778, 8.4]}, {"time": 1, "value": 8.4, "curve": [1.111, 8.4, 1.19, -4.46]}, {"time": 1.2667, "value": -4.46, "curve": [1.344, -4.46, 1.415, -0.34]}, {"time": 1.5, "value": -0.32, "curve": [1.578, -0.31, 1.658, -4.1]}, {"time": 1.7333, "value": -4.08, "curve": [1.889, -4.05, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -7.18]}, {"time": 3.1, "value": -7.19, "curve": [3.371, -7.19, 3.478, 0.53]}, {"time": 3.6667, "value": 0.19}]}, "bone35": {"rotate": [{"value": 0.62, "curve": [0.113, 0.26, 0.357, -2.85]}, {"time": 0.5, "value": -2.85, "curve": [0.722, -2.85, 1.013, 8.4]}, {"time": 1.1667, "value": 8.4, "curve": [1.244, 8.4, 1.323, -4.46]}, {"time": 1.4, "value": -4.46, "curve": [1.477, -4.46, 1.549, -0.34]}, {"time": 1.6333, "value": -0.32, "curve": [1.711, -0.31, 1.791, -4.1]}, {"time": 1.8667, "value": -4.08, "curve": [2.022, -4.05, 2.234, 0]}, {"time": 2.5, "curve": [2.769, 0, 3.036, -7.18]}, {"time": 3.3, "value": -7.19, "curve": [3.481, -7.19, 3.544, 1.01]}, {"time": 3.6667, "value": 0.62}]}, "bone36": {"rotate": [{"value": 2.35, "curve": [0.168, 1.11, 0.5, -2.85]}, {"time": 0.6667, "value": -2.85, "curve": [0.889, -2.85, 1.113, 8.4]}, {"time": 1.2667, "value": 8.4, "curve": [1.344, 8.4, 1.423, -4.46]}, {"time": 1.5, "value": -4.46, "curve": [1.577, -4.46, 1.649, -0.34]}, {"time": 1.7333, "value": -0.32, "curve": [1.811, -0.31, 1.891, -4.1]}, {"time": 1.9667, "value": -4.08, "curve": [2.122, -4.05, 2.401, 0]}, {"time": 2.6667, "curve": [2.936, 0, 3.349, 4.7]}, {"time": 3.6667, "value": 2.35}]}, "bone37": {"rotate": [{"value": 7.6, "curve": [0.223, 4.1, 0.73, -2.85]}, {"time": 0.8333, "value": -2.85, "curve": [1.056, -2.85, 1.246, 8.4]}, {"time": 1.4, "value": 8.4, "curve": [1.477, 8.4, 1.556, -4.46]}, {"time": 1.6333, "value": -4.46, "curve": [1.71, -4.46, 1.782, -0.34]}, {"time": 1.8667, "value": -0.32, "curve": [1.945, -0.31, 1.969, -4.08]}, {"time": 2.1, "value": -4.08, "curve": [2.369, -4.08, 2.636, 0]}, {"time": 2.9, "curve": [3.169, 0, 3.411, 11.61]}, {"time": 3.6667, "value": 7.6}]}, "bone38": {"rotate": [{"value": 0.26, "curve": [0.058, 0.12, 0.222, -3.39]}, {"time": 0.3333, "value": -3.39, "curve": [0.556, -3.39, 0.778, 5.26]}, {"time": 1, "value": 5.26, "curve": [1.111, 5.26, 1.19, -8.78]}, {"time": 1.2667, "value": -8.78, "curve": [1.344, -8.78, 1.415, -4.11]}, {"time": 1.5, "value": -4.1, "curve": [1.578, -4.09, 1.658, -9.54]}, {"time": 1.7333, "value": -8.76, "curve": [1.889, -7.23, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -8.01]}, {"time": 3.1, "value": -8.02, "curve": [3.371, -8.02, 3.478, 0.73]}, {"time": 3.6667, "value": 0.26}]}, "bone39": {"rotate": [{"value": 0.86, "curve": [0.113, 0.36, 0.357, -3.39]}, {"time": 0.5, "value": -3.39, "curve": [0.722, -3.39, 1.013, 5.26]}, {"time": 1.1667, "value": 5.26, "curve": [1.244, 5.26, 1.323, -8.78]}, {"time": 1.4, "value": -8.78, "curve": [1.477, -8.78, 1.549, -4.11]}, {"time": 1.6333, "value": -4.1, "curve": [1.711, -4.09, 1.791, -9.54]}, {"time": 1.8667, "value": -8.76, "curve": [2.022, -7.23, 2.234, 0]}, {"time": 2.5, "curve": [2.769, 0, 3.036, -8.01]}, {"time": 3.3, "value": -8.02, "curve": [3.481, -8.02, 3.544, 1.39]}, {"time": 3.6667, "value": 0.86}]}, "bone40": {"rotate": [{"value": 1.69, "curve": [0.168, 0.8, 0.5, -3.39]}, {"time": 0.6667, "value": -3.39, "curve": [0.889, -3.39, 1.113, 5.26]}, {"time": 1.2667, "value": 5.26, "curve": [1.344, 5.26, 1.423, -8.78]}, {"time": 1.5, "value": -8.78, "curve": [1.577, -8.78, 1.649, -4.11]}, {"time": 1.7333, "value": -4.1, "curve": [1.811, -4.09, 1.891, -9.54]}, {"time": 1.9667, "value": -8.76, "curve": [2.122, -7.23, 2.401, 0]}, {"time": 2.6667, "curve": [2.936, 0, 3.349, 3.39]}, {"time": 3.6667, "value": 1.69}]}, "bone41": {"rotate": [{"value": 2.6, "curve": [0.223, 1.4, 0.73, -3.39]}, {"time": 0.8333, "value": -3.39, "curve": [1.056, -3.39, 1.246, 5.26]}, {"time": 1.4, "value": 5.26, "curve": [1.477, 5.26, 1.556, -8.78]}, {"time": 1.6333, "value": -8.78, "curve": [1.71, -8.78, 1.782, -4.11]}, {"time": 1.8667, "value": -4.1, "curve": [1.945, -4.09, 1.969, -8.76]}, {"time": 2.1, "value": -8.76, "curve": [2.369, -8.76, 2.636, 0]}, {"time": 2.9, "curve": [3.169, 0, 3.411, 3.98]}, {"time": 3.6667, "value": 2.6}]}, "bone42": {"rotate": [{"curve": [0.389, 0, 0.635, 7.98]}, {"time": 0.8333, "value": 7.98, "curve": [0.944, 7.98, 1.09, -7.65]}, {"time": 1.1667, "value": -7.65, "curve": [1.244, -7.65, 1.315, -3.86]}, {"time": 1.4, "value": -3.86, "curve": [1.478, -3.86, 1.558, -8.06]}, {"time": 1.6333, "value": -7.18, "curve": [1.789, -5.45, 1.834, 2.42]}, {"time": 2.1, "value": 2.42, "curve": [2.369, 2.42, 2.636, -4.87]}, {"time": 2.9, "value": -4.87, "curve": [3.171, -4.88, 3.411, 0]}, {"time": 3.6667}]}, "bone43": {"rotate": [{"value": 0.44, "curve": [0.058, 0.19, 0.222, -2.45]}, {"time": 0.3333, "value": -2.45, "curve": [0.556, -2.45, 0.778, 7.98]}, {"time": 1, "value": 7.98, "curve": [1.111, 7.98, 1.19, -7.65]}, {"time": 1.2667, "value": -7.65, "curve": [1.344, -7.65, 1.415, -3.86]}, {"time": 1.5, "value": -3.86, "curve": [1.578, -3.86, 1.658, -8.06]}, {"time": 1.7333, "value": -7.18, "curve": [1.889, -5.45, 2.001, 2.42]}, {"time": 2.2667, "value": 2.42, "curve": [2.536, 2.42, 2.836, -4.87]}, {"time": 3.1, "value": -4.87, "curve": [3.371, -4.88, 3.478, 1.24]}, {"time": 3.6667, "value": 0.44}]}, "bone44": {"rotate": [{"value": 2.71, "curve": [0.113, 1.15, 0.357, -2.45]}, {"time": 0.5, "value": -2.45, "curve": [0.722, -2.45, 1.013, 7.98]}, {"time": 1.1667, "value": 7.98, "curve": [1.244, 7.98, 1.323, -7.65]}, {"time": 1.4, "value": -7.65, "curve": [1.477, -7.65, 1.549, -3.86]}, {"time": 1.6333, "value": -3.86, "curve": [1.711, -3.86, 1.791, -8.06]}, {"time": 1.8667, "value": -7.18, "curve": [2.022, -5.45, 2.234, 2.42]}, {"time": 2.5, "value": 2.42, "curve": [2.769, 2.42, 3.036, -4.87]}, {"time": 3.3, "value": -4.87, "curve": [3.481, -4.88, 3.544, 4.4]}, {"time": 3.6667, "value": 2.71}]}, "bone45": {"rotate": [{"value": 5.34, "curve": [0.168, 2.51, 0.5, -2.45]}, {"time": 0.6667, "value": -2.45, "curve": [0.889, -2.45, 1.113, 7.98]}, {"time": 1.2667, "value": 7.98, "curve": [1.344, 7.98, 1.423, -7.65]}, {"time": 1.5, "value": -7.65, "curve": [1.577, -7.65, 1.649, -3.86]}, {"time": 1.7333, "value": -3.86, "curve": [1.811, -3.86, 1.891, -8.06]}, {"time": 1.9667, "value": -7.18, "curve": [2.122, -5.45, 2.401, 2.42]}, {"time": 2.6667, "value": 2.42, "curve": [2.936, 2.42, 3.493, 8.27]}, {"time": 3.6667, "value": 5.34}]}, "bone46": {"rotate": [{"value": -0.67, "curve": [0.058, -0.3, 0.222, -1.67]}, {"time": 0.3333, "value": -1.67, "curve": [0.556, -1.67, 0.778, 4.88]}, {"time": 1, "value": 4.88, "curve": [1.111, 4.88, 1.19, -3.21]}, {"time": 1.2667, "value": -3.21, "curve": [1.344, -3.21, 1.415, -0.14]}, {"time": 1.5, "value": -0.13, "curve": [1.578, -0.13, 1.658, -4]}, {"time": 1.7333, "value": -3.82, "curve": [1.889, -3.47, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -6.97]}, {"time": 3.1, "value": -6.97, "curve": [3.371, -6.97, 3.478, -1.88]}, {"time": 3.6667, "value": -0.67}]}, "bone47": {"rotate": [{"value": -2.21, "curve": [0.113, -0.94, 0.357, -1.67]}, {"time": 0.5, "value": -1.67, "curve": [0.722, -1.67, 1.013, 4.88]}, {"time": 1.1667, "value": 4.88, "curve": [1.244, 4.88, 1.323, -3.21]}, {"time": 1.4, "value": -3.21, "curve": [1.477, -3.21, 1.549, -0.14]}, {"time": 1.6333, "value": -0.13, "curve": [1.711, -0.13, 1.791, -4]}, {"time": 1.8667, "value": -3.82, "curve": [2.022, -3.47, 2.234, 0]}, {"time": 2.5, "curve": [2.769, 0, 3.036, -6.96]}, {"time": 3.3, "value": -6.97, "curve": [3.481, -6.98, 3.544, -3.58]}, {"time": 3.6667, "value": -2.21}]}, "bone48": {"rotate": [{"value": -5.26, "curve": [0.168, -2.48, 0.5, -1.67]}, {"time": 0.6667, "value": -1.67, "curve": [0.889, -1.67, 1.113, 4.88]}, {"time": 1.2667, "value": 4.88, "curve": [1.344, 4.88, 1.423, -3.21]}, {"time": 1.5, "value": -3.21, "curve": [1.577, -3.21, 1.649, -0.14]}, {"time": 1.7333, "value": -0.13, "curve": [1.811, -0.13, 1.891, -4]}, {"time": 1.9667, "value": -3.82, "curve": [2.122, -3.47, 2.401, 0]}, {"time": 2.6667, "curve": [2.936, 0, 3.349, -10.52]}, {"time": 3.6667, "value": -5.26}]}, "bone49": {"rotate": [{"value": -8.08, "curve": [0.223, -4.35, 0.73, -1.67]}, {"time": 0.8333, "value": -1.67, "curve": [1.056, -1.67, 1.246, 4.88]}, {"time": 1.4, "value": 4.88, "curve": [1.477, 4.88, 1.556, -3.21]}, {"time": 1.6333, "value": -3.21, "curve": [1.71, -3.21, 1.782, -0.14]}, {"time": 1.8667, "value": -0.13, "curve": [1.945, -0.13, 1.969, -3.82]}, {"time": 2.1, "value": -3.82, "curve": [2.369, -3.82, 2.636, 0]}, {"time": 2.9, "curve": [3.169, 0, 3.411, -12.35]}, {"time": 3.6667, "value": -8.08}]}, "bone50": {"rotate": [{"curve": [0.389, 0, 0.635, 5.33]}, {"time": 0.8333, "value": 5.33, "curve": [0.944, 5.33, 1.09, -4.56]}, {"time": 1.1667, "value": -4.56, "curve": [1.244, -4.56, 1.315, -2.18]}, {"time": 1.4, "value": -2.17, "curve": [1.478, -2.16, 1.558, -4.16]}, {"time": 1.6333, "value": -3.86, "curve": [1.789, -3.28, 1.834, 0]}, {"time": 2.1, "curve": [2.369, 0, 2.636, -8.31]}, {"time": 2.9, "value": -8.31, "curve": [3.171, -8.32, 3.411, 0]}, {"time": 3.6667}]}, "bone51": {"rotate": [{"value": -0.85, "curve": [0.058, -0.38, 0.222, -2.74]}, {"time": 0.3333, "value": -2.74, "curve": [0.556, -2.74, 0.778, 5.33]}, {"time": 1, "value": 5.33, "curve": [1.111, 5.33, 1.19, -4.56]}, {"time": 1.2667, "value": -4.56, "curve": [1.344, -4.56, 1.415, -2.18]}, {"time": 1.5, "value": -2.17, "curve": [1.578, -2.16, 1.658, -4.16]}, {"time": 1.7333, "value": -3.86, "curve": [1.889, -3.28, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -8.31]}, {"time": 3.1, "value": -8.31, "curve": [3.371, -8.32, 3.478, -2.4]}, {"time": 3.6667, "value": -0.85}]}, "bone52": {"rotate": [{"value": -3.63, "curve": [0.113, -1.54, 0.357, -2.74]}, {"time": 0.5, "value": -2.74, "curve": [0.722, -2.74, 1.013, 5.33]}, {"time": 1.1667, "value": 5.33, "curve": [1.244, 5.33, 1.323, -4.56]}, {"time": 1.4, "value": -4.56, "curve": [1.477, -4.56, 1.549, -2.18]}, {"time": 1.6333, "value": -2.17, "curve": [1.711, -2.16, 1.791, -4.16]}, {"time": 1.8667, "value": -3.86, "curve": [2.022, -3.28, 2.234, 0]}, {"time": 2.5, "curve": [2.769, 0, 3.036, -8.3]}, {"time": 3.3, "value": -8.31, "curve": [3.481, -8.32, 3.544, -5.9]}, {"time": 3.6667, "value": -3.63}]}, "bone53": {"rotate": [{}, {"time": 0.8333, "value": 5.33, "curve": [0.944, 5.33, 1.09, -4.56]}, {"time": 1.1667, "value": -4.56, "curve": [1.244, -4.56, 1.315, -2.18]}, {"time": 1.4, "value": -2.17, "curve": [1.478, -2.16, 1.558, -4.16]}, {"time": 1.6333, "value": -3.86, "curve": [1.789, -3.28, 1.834, 0]}, {"time": 2.1, "curve": [2.369, 0, 2.636, -8.31]}, {"time": 2.9, "value": -8.31, "curve": [3.171, -8.32, 3.411, 0]}, {"time": 3.6667}]}, "bone54": {"rotate": [{"value": -0.87, "curve": [0.058, -0.39, 0.222, -2.74]}, {"time": 0.3333, "value": -2.74, "curve": [0.556, -2.74, 0.778, 5.33]}, {"time": 1, "value": 5.33, "curve": [1.111, 5.33, 1.19, -4.56]}, {"time": 1.2667, "value": -4.56, "curve": [1.344, -4.56, 1.415, -2.18]}, {"time": 1.5, "value": -2.17, "curve": [1.578, -2.16, 1.658, -4.16]}, {"time": 1.7333, "value": -3.86, "curve": [1.889, -3.28, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -8.31]}, {"time": 3.1, "value": -8.31, "curve": [3.371, -8.32, 3.478, -2.47]}, {"time": 3.6667, "value": -0.87}]}, "bone55": {"rotate": [{"curve": [0.446, 0, 0.635, 2.91]}, {"time": 0.8333, "value": 3.43, "curve": [0.944, 3.72, 1.09, -1.61]}, {"time": 1.1667, "value": -1.61, "curve": [1.244, -1.61, 1.321, 0.82]}, {"time": 1.4, "value": 0.82, "curve": [1.478, 0.83, 1.547, -1.14]}, {"time": 1.6333, "value": -1.14, "curve": [1.789, -1.14, 1.834, 0]}, {"time": 2.1, "curve": [2.369, 0, 2.636, -3.05]}, {"time": 2.9, "value": -3.05, "curve": [3.171, -3.05, 3.399, 0]}, {"time": 3.6667}]}, "bone56": {"rotate": [{"value": -0.16, "curve": [0.045, -0.16, 0.222, -1.98]}, {"time": 0.3333, "value": -1.98, "curve": [0.556, -1.98, 0.778, 2.84]}, {"time": 1, "value": 3.43, "curve": [1.111, 3.72, 1.19, -1.61]}, {"time": 1.2667, "value": -1.61, "curve": [1.344, -1.61, 1.421, 0.82]}, {"time": 1.5, "value": 0.82, "curve": [1.578, 0.83, 1.647, -1.14]}, {"time": 1.7333, "value": -1.14, "curve": [1.889, -1.14, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -3.01]}, {"time": 3.1, "value": -3.01, "curve": [3.371, -3.01, 3.466, -0.37]}, {"time": 3.6667, "value": -0.16}]}, "bone57": {"rotate": [{"value": -0.55, "curve": [0.097, -0.54, 0.357, -1.98]}, {"time": 0.5, "value": -1.98, "curve": [0.722, -1.98, 1.013, 2.84]}, {"time": 1.1667, "value": 3.43, "curve": [1.244, 3.72, 1.323, -1.61]}, {"time": 1.4, "value": -1.61, "curve": [1.477, -1.61, 1.555, 0.82]}, {"time": 1.6333, "value": 0.82, "curve": [1.711, 0.83, 1.78, -1.14]}, {"time": 1.8667, "value": -1.14, "curve": [2.022, -1.14, 2.234, 0]}, {"time": 2.5, "curve": [2.769, 0, 3.036, -3.05]}, {"time": 3.3, "value": -3.05, "curve": [3.481, -3.05, 3.533, -0.77]}, {"time": 3.6667, "value": -0.55}]}, "bone58": {"rotate": [{"curve": [0.446, 0, 0.635, 1.8]}, {"time": 0.8333, "value": 2.1, "curve": [0.944, 2.27, 1.09, -1.66]}, {"time": 1.1667, "value": -1.66, "curve": [1.244, -1.66, 1.323, 0.98]}, {"time": 1.4, "value": 1.13, "curve": [1.478, 1.29, 1.558, -0.12]}, {"time": 1.6333, "value": -0.29, "curve": [1.789, -0.62, 1.834, 0]}, {"time": 2.1, "curve": [2.369, 0, 2.636, -1.65]}, {"time": 2.9, "value": -1.65, "curve": [3.171, -1.65, 3.399, 0]}, {"time": 3.6667}]}, "bone59": {"rotate": [{"value": -0.24, "curve": [0.045, -0.14, 0.222, -1.12]}, {"time": 0.3333, "value": -1.12, "curve": [0.556, -1.12, 0.778, 1.77]}, {"time": 1, "value": 2.1, "curve": [1.111, 2.27, 1.19, -1.66]}, {"time": 1.2667, "value": -1.66, "curve": [1.344, -1.66, 1.423, 0.98]}, {"time": 1.5, "value": 1.13, "curve": [1.578, 1.29, 1.658, -0.12]}, {"time": 1.7333, "value": -0.29, "curve": [1.889, -0.62, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -1.65]}, {"time": 3.1, "value": -1.65, "curve": [3.371, -1.65, 3.466, -0.55]}, {"time": 3.6667, "value": -0.24}]}, "bone60": {"rotate": [{"curve": [0.446, 0, 0.635, -3.29]}, {"time": 0.8333, "value": -3.83, "curve": [0.944, -4.14, 1.09, 0.98]}, {"time": 1.1667, "value": 0.98, "curve": [1.244, 0.98, 1.323, -0.97]}, {"time": 1.4, "value": -1.06, "curve": [1.478, -1.15, 1.558, 1.6]}, {"time": 1.6333, "value": 1.76, "curve": [1.789, 2.07, 1.834, 0]}, {"time": 2.1, "curve": [2.369, 0, 2.636, 2.95]}, {"time": 2.9, "value": 2.95, "curve": [3.171, 2.95, 3.399, 0]}, {"time": 3.6667}]}, "bone61": {"rotate": [{"value": 0.35, "curve": [0.077, 0.08, 0.635, -3.29]}, {"time": 0.8333, "value": -3.83, "curve": [0.944, -4.14, 1.09, 0.98]}, {"time": 1.1667, "value": 0.98, "curve": [1.244, 0.98, 1.323, -0.97]}, {"time": 1.4, "value": -1.06, "curve": [1.478, -1.15, 1.558, 1.6]}, {"time": 1.6333, "value": 1.76, "curve": [1.789, 2.07, 1.834, 0]}, {"time": 2.1, "curve": [2.369, 0, 2.636, 2.95]}, {"time": 2.9, "value": 2.95, "curve": [3.171, 2.95, 3.399, 0.91]}, {"time": 3.6667, "value": 0.35}]}, "bone62": {"rotate": [{"value": -0.26, "curve": [0.045, -0.15, 0.222, -1.56]}, {"time": 0.3333, "value": -1.56, "curve": [0.556, -1.56, 0.778, 2.39]}, {"time": 1, "value": 2.85, "curve": [1.111, 3.08, 1.19, -1.78]}, {"time": 1.2667, "value": -1.78, "curve": [1.344, -1.78, 1.423, 0.86]}, {"time": 1.5, "value": 1.02, "curve": [1.578, 1.19, 1.658, -0.29]}, {"time": 1.7333, "value": -0.45, "curve": [1.889, -0.75, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, -3]}, {"time": 3.1, "value": -3, "curve": [3.371, -3, 3.466, -0.58]}, {"time": 3.6667, "value": -0.26}]}, "bone63": {"rotate": [{"value": -0.87, "curve": [0.097, -0.45, 0.357, -1.56]}, {"time": 0.5, "value": -1.56, "curve": [0.722, -1.56, 1.013, 2.39]}, {"time": 1.1667, "value": 2.85, "curve": [1.244, 3.08, 1.323, -1.78]}, {"time": 1.4, "value": -1.78, "curve": [1.477, -1.78, 1.556, 0.86]}, {"time": 1.6333, "value": 1.02, "curve": [1.711, 1.19, 1.791, -0.29]}, {"time": 1.8667, "value": -0.45, "curve": [2.022, -0.75, 2.234, 0]}, {"time": 2.5, "curve": [2.769, 0, 3.036, -3]}, {"time": 3.3, "value": -3, "curve": [3.571, -3, 3.533, -1.22]}, {"time": 3.6667, "value": -0.87}]}, "bone64": {"rotate": [{"value": -0.26, "curve": [0.077, -0.06, 0.635, -0.9]}, {"time": 0.8333, "value": -1.38, "curve": [0.944, -1.65, 1.09, 1.15]}, {"time": 1.1667, "value": 1.15, "curve": [1.244, 1.15, 1.323, -1.08]}, {"time": 1.4, "value": -1.19, "curve": [1.478, -1.3, 1.558, 0.63]}, {"time": 1.6333, "value": 0.81, "curve": [1.789, 1.16, 1.834, 0]}, {"time": 2.1, "curve": [2.369, 0, 2.636, 2.77]}, {"time": 2.9, "value": 2.77, "curve": [3.171, 2.77, 3.399, -0.7]}, {"time": 3.6667, "value": -0.26}]}, "bone65": {"rotate": [{"value": -0.88, "curve": [0.088, -0.5, 0.222, 1.81]}, {"time": 0.3333, "value": 1.81, "curve": [0.556, 1.81, 0.778, -0.84]}, {"time": 1, "value": -1.38, "curve": [1.111, -1.65, 1.19, 1.15]}, {"time": 1.2667, "value": 1.15, "curve": [1.344, 1.15, 1.423, -1.08]}, {"time": 1.5, "value": -1.19, "curve": [1.578, -1.3, 1.658, 0.63]}, {"time": 1.7333, "value": 0.81, "curve": [1.889, 1.16, 2.001, 0]}, {"time": 2.2667, "curve": [2.536, 0, 2.836, 2.77]}, {"time": 3.1, "value": 2.77, "curve": [3.371, 2.77, 3.466, -1.42]}, {"time": 3.6667, "value": -0.88}]}, "bone66": {"rotate": [{"curve": [0.149, 0, 0.973, 1.41]}, {"time": 1.1667, "value": 1.41, "curve": [1.475, 1.41, 1.565, 1.03]}, {"time": 2.1, "value": 1.41, "curve": [2.884, 1.96, 3.132, 0]}, {"time": 3.6667}]}, "bone67": {"rotate": [{"curve": [0.158, 0, 0.611, 6.64]}, {"time": 1.1667, "value": 6.04, "curve": [1.321, 5.87, 1.479, 4.86]}, {"time": 1.6333, "value": 4.91, "curve": [1.787, 4.95, 1.833, 3.54]}, {"time": 2.1, "value": 4.09, "curve": [2.884, 5.69, 3.132, 0]}, {"time": 3.6667}]}, "bone68": {"rotate": [{"curve": [0.158, 0, 0.611, 6.35]}, {"time": 1.1667, "value": 6.34, "curve": [1.339, 6.34, 1.507, 13.42]}, {"time": 1.6333, "value": 13.42, "curve": [1.828, 13.42, 1.967, 5.92]}, {"time": 2.1, "value": 6.34, "curve": [2.884, 8.83, 3.132, 0]}, {"time": 3.6667}]}, "bone72": {"rotate": [{"curve": [0.274, -0.01, 0.813, -1.03]}, {"time": 1.1667, "value": -1.05, "curve": [1.475, -1.06, 1.565, -0.77]}, {"time": 2.1, "value": -1.05, "curve": [2.884, -1.45, 3.132, 0]}, {"time": 3.6667}]}, "bone73": {"rotate": [{"curve": [0.198, 0, 0.611, 68.01]}, {"time": 1.1667, "value": 52.29, "curve": [1.321, 47.93, 1.479, 44.04]}, {"time": 1.6333, "value": 45.04, "curve": [1.787, 46.05, 1.834, 45.36]}, {"time": 2.1, "value": 52.29, "curve": [2.884, 72.75, 3.132, 0]}, {"time": 3.6667}]}, "bone74": {"rotate": [{"curve": [0.198, 0, 0.611, 89.23]}, {"time": 1.1667, "value": 69.02, "curve": [1.321, 63.42, 1.479, 71.56]}, {"time": 1.6333, "value": 72.87, "curve": [1.787, 74.19, 1.836, 59.91]}, {"time": 2.1, "value": 69.02, "curve": [2.884, 96.02, 3.132, 0]}, {"time": 3.6667}]}, "bone76": {"rotate": [{"curve": [0.149, 0, 0.973, 0.01]}, {"time": 1.1667, "value": 0.01, "curve": [1.475, 0.01, 1.565, 0]}, {"time": 2.1, "value": 0.01, "curve": [2.884, 0.01, 3.132, 0]}, {"time": 3.6667}]}, "bone77": {"rotate": [{"curve": [0.149, 0, 0.973, 0.36]}, {"time": 1.1667, "value": 0.36, "curve": [1.475, 0.36, 1.565, 0.27]}, {"time": 2.1, "value": 0.36, "curve": [2.884, 0.51, 3.132, 0]}, {"time": 3.6667}]}, "bone78": {"rotate": [{"curve": [0.149, 0, 0.973, 1.38]}, {"time": 1.1667, "value": 1.38, "curve": [1.475, 1.38, 1.565, 1.01]}, {"time": 2.1, "value": 1.38, "curve": [2.884, 1.91, 3.132, 0]}, {"time": 3.6667}]}, "bone20": {"translate": [{}, {"time": 1.1667, "x": 13.72, "y": 1.88, "curve": [1.321, 10.44, 1.479, 14.73, 1.321, 1.43, 1.479, 1.27]}, {"time": 1.6333, "x": 14.98, "y": 1.31, "curve": [1.787, 15.23, 1.833, 11.44, 1.787, 1.34, 1.833, 1.57]}, {"time": 2.1, "x": 13.72, "y": 1.88, "curve": [3.058, 21.88, 3.132, 0, 3.058, 3, 3.132, 0]}, {"time": 3.6667}]}, "bone87": {"translate": [{"curve": [0.149, 0, 0.973, 3.87, 0.149, 0, 0.973, -6.87]}, {"time": 1.1667, "x": 3.87, "y": -6.87, "curve": [1.475, 3.87, 1.565, 2.84, 1.475, -6.87, 1.565, -5.04]}, {"time": 2.1, "x": 3.87, "y": -6.87, "curve": [2.884, 5.38, 2.321, 0, 2.884, -9.56, 2.321, 0.01]}, {"time": 3.6667}]}, "bone88": {"translate": [{"curve": [0.149, 0, 0.973, -1.16, 0.149, 0, 0.973, 1.9]}, {"time": 1.1667, "x": -1.16, "y": 1.9, "curve": [1.475, -1.16, 1.565, -0.85, 1.475, 1.9, 1.565, 1.4]}, {"time": 2.1, "x": -1.16, "y": 1.9, "curve": [2.884, -1.61, 2.321, 0, 2.884, 2.65, 2.321, 0]}, {"time": 3.6667}]}}, "attachments": {"default": {"biyan": {"biyan": {"deform": [{"time": 0.0667, "vertices": [0.23815, 6.0943, 0.23789, 6.09369, 0.15271, 5.9339, 0.15243, 5.93323, -1.50801, 11.63104, -1.50846, 11.6308, 0.54411, 10.01306, 0.54391, 10.01123, 0.70148, -2.58948, 0.70121, -2.59009]}, {"time": 0.2, "offset": 8, "vertices": [0.18136, 0.25812, 0.18135, 0.25787]}, {"time": 0.4667, "vertices": [0.23815, 6.0943, 0.23789, 6.09369, 0.15271, 5.9339, 0.15243, 5.93323, -1.50801, 11.63104, -1.50846, 11.6308, 0.54411, 10.01306, 0.54391, 10.01123, 0.70148, -2.58948, 0.70121, -2.59009], "curve": "stepped"}, {"time": 2.5667, "vertices": [0.23815, 6.0943, 0.23789, 6.09369, 0.15271, 5.9339, 0.15243, 5.93323, -1.50801, 11.63104, -1.50846, 11.6308, 0.54411, 10.01306, 0.54391, 10.01123, 0.70148, -2.58948, 0.70121, -2.59009]}, {"time": 2.7333, "offset": 8, "vertices": [0.18136, 0.25812, 0.18135, 0.25787]}, {"time": 3.0333, "vertices": [0.23815, 6.0943, 0.23789, 6.09369, 0.15271, 5.9339, 0.15243, 5.93323, -1.50801, 11.63104, -1.50846, 11.6308, 0.54411, 10.01306, 0.54391, 10.01123, 0.70148, -2.58948, 0.70121, -2.59009]}]}}, "lian": {"lian": {"deform": [{"time": 0.0667, "offset": 657, "vertices": [-0.22546, -0.00522, -0.22504]}, {"time": 0.2333, "offset": 644, "vertices": [-1.25528, -4.86279, -1.25534, -4.86279, -0.1406, -9.12164, -0.14073, -9.12164, 1.411, -12.48285, 1.41079, -12.48254, -1.08952, -12.93732, -1.08972, -12.93701, -0.57095, -11.89612, -0.57118, -11.89612, 1.05582, -8.46338, 1.0558, -8.4632, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.95751, -5.1134, 0.95734, -5.11279, 1.37794, -7.97833, 1.37778, -7.97791, 1.24369, -7.76141, 1.24355, -7.76105, -0.63678, -3.46399, -0.63697, -3.46326]}, {"time": 0.3667, "offset": 657, "vertices": [-0.22546, -0.00522, -0.22504], "curve": "stepped"}, {"time": 2.5667, "offset": 657, "vertices": [-0.22546, -0.00522, -0.22504]}, {"time": 2.8, "offset": 644, "vertices": [-1.25528, -4.86279, -1.25534, -4.86279, -0.1406, -9.12164, -0.14073, -9.12164, 1.411, -12.48285, 1.41079, -12.48254, -1.08952, -12.93732, -1.08972, -12.93701, -0.57095, -11.89612, -0.57118, -11.89612, 1.05582, -8.46338, 1.0558, -8.4632, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.95751, -5.1134, 0.95734, -5.11279, 1.37794, -7.97833, 1.37778, -7.97791, 1.24369, -7.76141, 1.24355, -7.76105, -0.63678, -3.46399, -0.63697, -3.46326]}, {"time": 2.9, "offset": 657, "vertices": [-0.22546, -0.00522, -0.22504]}]}}, "shenti": {"shenti": {"deform": [{"time": 0.0333, "offset": 196, "vertices": [0.17483, 3.56677, -3.56354, 0.22823], "curve": [0.595, -0.01, 0.46, 1.42]}, {"time": 1.1667, "offset": 154, "vertices": [4.47101, -24.20292, 2.18195, -24.5154, 6.45886, -23.74967, -26.70404, -18.66107, 14.49167, -29.17774, 11.69183, -30.4079, 16.85663, -27.87822, -32.05876, -20.97827, 12.6535, -36.16269, 18.81512, -33.37421, -53.15278, -20.19617, 6.82166, -56.44966, 16.6416, -54.37058, -40.39755, -7.75806, -2.18427, -41.07765, 5.073, -40.82168, -16.86282, 2.23914, -6.2287, -15.8295, -3.34814, -16.67804, -21.61489, 11.117, -12.52026, -20.8334, -19.62083, 19.92389, -21.17584, -18.262, -3.53345, -0.93837, -1.19029, 3.45685, -3.52783, -0.95938], "curve": "stepped"}, {"time": 2.9, "offset": 154, "vertices": [4.47101, -24.20292, 2.18195, -24.5154, 6.45886, -23.74967, -26.70404, -18.66107, 14.49167, -29.17774, 11.69183, -30.4079, 16.85663, -27.87822, -32.05876, -20.97827, 12.6535, -36.16269, 18.81512, -33.37421, -53.15278, -20.19617, 6.82166, -56.44966, 16.6416, -54.37058, -40.39755, -7.75806, -2.18427, -41.07765, 5.073, -40.82168, -16.86282, 2.23914, -6.2287, -15.8295, -3.34814, -16.67804, -21.61489, 11.117, -12.52026, -20.8334, -19.62083, 19.92389, -21.17584, -18.262, -3.53345, -0.93837, -1.19029, 3.45685, -3.52783, -0.95938], "curve": [3.167, -0.09, 3.399, 1.21]}, {"time": 3.6667, "offset": 196, "vertices": [0.17483, 3.56677, -3.56354, 0.22823]}]}}, "toufa8": {"toufa8": {"deform": [{"time": 2.8, "offset": 8, "vertices": [0.01308, 0.50909, 0.01312, 0.50922]}]}}, "zuoshou1": {"zuoshou1": {"deform": [{"time": 0.1333, "offset": 40, "vertices": [0.45599, -0.54578]}, {"time": 1.1667, "offset": 34, "vertices": [-10.57202, 4.21107, -4.177, 1.66411, -4.36621, 3.89071, -0.3772, 4.45327], "curve": "stepped"}, {"time": 2.1, "offset": 34, "vertices": [-10.57202, 4.21107, -4.177, 1.66411, -4.36621, 3.89071, -0.3772, 4.45327]}, {"time": 3.6667, "offset": 40, "vertices": [0.45599, -0.54578]}]}}}}}}}