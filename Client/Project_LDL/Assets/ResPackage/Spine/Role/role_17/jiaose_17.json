{"skeleton": {"hash": "oRrzAjN7rdA", "spine": "4.2.42", "x": -492.38, "width": 1051, "height": 1221, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -71.1, "y": 281.6}, {"name": "bone2", "parent": "bone", "length": 146.31, "rotation": 87.66, "x": -0.99, "y": 2.98, "inherit": "noScale"}, {"name": "bone3", "parent": "bone2", "length": 184.05, "rotation": 0.79, "x": 146.31, "inherit": "noScale"}, {"name": "bone4", "parent": "bone3", "length": 256.68, "rotation": -18.15, "x": 184.89, "y": -0.97, "inherit": "noScale"}, {"name": "bone5", "parent": "bone4", "length": 171.24, "rotation": 30.75, "x": 256.68, "inherit": "noScale"}, {"name": "bone6", "parent": "bone5", "length": 85.37, "rotation": -3.01, "x": 171.24, "inherit": "noScale"}, {"name": "bone7", "parent": "bone6", "x": 7.22, "y": -27.13}, {"name": "bone8", "parent": "bone7", "x": -19.15, "y": 99.12, "color": "abe323ff"}, {"name": "bone132", "parent": "bone6", "x": 7.84, "y": -65.04}, {"name": "bone9", "parent": "bone132", "length": 39.11, "rotation": -33.38, "x": -0.41, "y": -5.55}, {"name": "bone10", "parent": "bone132", "length": 34.24, "rotation": -169.01, "x": -0.51, "y": -6.28}, {"name": "bone11", "parent": "bone4", "length": 312.43, "rotation": -98.63, "x": 253.5, "y": -5.9, "inherit": "noScale"}, {"name": "bone12", "parent": "bone11", "length": 324.13, "rotation": -47.87, "x": 312.43}, {"name": "bone13", "parent": "bone12", "length": 298.25, "rotation": -70.12, "x": 320.63, "y": 0.4}, {"name": "bone14", "parent": "bone13", "length": 145.13, "rotation": 17.32, "x": 298.25}, {"name": "bone15", "parent": "bone4", "length": 213.02, "rotation": 152.01, "x": 243.74, "y": 11.35, "inherit": "noScale"}, {"name": "bone16", "parent": "bone15", "length": 323.99, "rotation": 40.84, "x": 213.02}, {"name": "bone17", "parent": "bone16", "length": 238.42, "rotation": -33.01, "x": 317.4, "y": -1.24}, {"name": "bone18", "parent": "bone17", "length": 106.23, "rotation": -1.9, "x": 238.42}, {"name": "bone19", "parent": "bone18", "length": 131.12, "rotation": 41.76, "x": 103.24, "y": 0.03}, {"name": "bone20", "parent": "bone11", "length": 152.23, "rotation": -30.28, "x": 75.64, "y": -16.51}, {"name": "bone21", "parent": "bone20", "length": 135.3, "rotation": 4.99, "x": 152.23}, {"name": "bone22", "parent": "bone21", "length": 127.85, "rotation": -0.2, "x": 135.3}, {"name": "bone23", "parent": "bone22", "length": 103.02, "rotation": -7.96, "x": 127.85}, {"name": "bone24", "parent": "bone23", "length": 128.04, "rotation": -27.37, "x": 103.02}, {"name": "bone25", "parent": "bone24", "length": 138.33, "rotation": -6.8, "x": 126.38, "y": 2.89}, {"name": "bone26", "parent": "bone25", "length": 130, "rotation": -1.23, "x": 137.38, "y": -0.1}, {"name": "bone27", "parent": "bone11", "length": 182.43, "rotation": 4.59, "x": 155.67, "y": 60.79}, {"name": "bone28", "parent": "bone27", "length": 123.75, "rotation": -49.12, "x": 181.83, "y": -0.99}, {"name": "bone29", "parent": "bone28", "length": 136.71, "rotation": -3.1, "x": 123.75}, {"name": "bone30", "parent": "bone29", "length": 142.65, "rotation": -1.51, "x": 136.71}, {"name": "bone31", "parent": "bone30", "length": 147.55, "rotation": -3.91, "x": 142.65}, {"name": "bone32", "parent": "bone31", "length": 125.94, "rotation": 2, "x": 143.18, "y": 0.66}, {"name": "bone33", "parent": "bone32", "length": 107.53, "rotation": 1.85, "x": 125.94}, {"name": "bone34", "parent": "bone27", "length": 58.53, "rotation": -88.33, "x": 84.04, "y": -14.43}, {"name": "bone35", "parent": "bone34", "length": 44.58, "rotation": -5.34, "x": 58.53}, {"name": "bone36", "parent": "bone27", "length": 58.02, "rotation": -69.88, "x": 151.99, "y": -14.18}, {"name": "bone37", "parent": "bone36", "length": 59.37, "rotation": 3.62, "x": 58.02}, {"name": "bone38", "parent": "bone27", "length": 63.43, "rotation": -56.8, "x": 188.67, "y": 0.05}, {"name": "bone39", "parent": "bone38", "length": 62.1, "rotation": -1.33, "x": 63.43}, {"name": "bone40", "parent": "bone27", "length": 66.03, "rotation": -40.12, "x": 206.52, "y": 3.11}, {"name": "bone41", "parent": "bone40", "length": 52.15, "rotation": 4.2, "x": 66.03}, {"name": "bone42", "parent": "bone27", "length": 60.09, "rotation": -27.19, "x": 199.5, "y": 5.42}, {"name": "bone43", "parent": "bone42", "length": 46.8, "rotation": 11.64, "x": 60.09}, {"name": "bone44", "parent": "bone20", "length": 43.72, "rotation": 153, "x": -3.23, "y": -0.51}, {"name": "bone45", "parent": "bone44", "length": 36.43, "rotation": -36.87, "x": 43.72}, {"name": "bone46", "parent": "bone45", "length": 52.54, "rotation": -109.44, "x": 36.43}, {"name": "bone47", "parent": "bone46", "length": 30.49, "rotation": 3.2, "x": 52.54}, {"name": "bone48", "parent": "bone47", "length": 38.01, "rotation": 7.29, "x": 30.49}, {"name": "bone49", "parent": "bone45", "length": 39.43, "rotation": -106.34, "x": 102.49, "y": 23.78}, {"name": "bone50", "parent": "bone49", "length": 27.73, "rotation": 0.8, "x": 39.43}, {"name": "bone51", "parent": "bone50", "length": 24.69, "rotation": 14.59, "x": 27.73}, {"name": "bone52", "parent": "bone45", "length": 31.92, "rotation": 11.75, "x": 40.69, "y": 4.07}, {"name": "bone53", "parent": "bone52", "length": 32.13, "rotation": 13.51, "x": 31.92}, {"name": "bone54", "parent": "bone53", "length": 26.16, "rotation": 77.38, "x": 32.13}, {"name": "bone55", "parent": "bone15", "length": 37.05, "rotation": -125.02, "x": 91.87, "y": -23.1}, {"name": "bone56", "parent": "bone55", "length": 33.64, "rotation": 19.93, "x": 37.48, "y": -0.05}, {"name": "bone57", "parent": "bone56", "length": 40.82, "rotation": 133.21, "x": 33.64}, {"name": "bone58", "parent": "bone57", "length": 29.28, "rotation": -14.12, "x": 40.82}, {"name": "bone59", "parent": "bone58", "length": 32.04, "rotation": -10.23, "x": 29.28}, {"name": "bone60", "parent": "bone56", "length": 29.52, "rotation": -82.84, "x": 32.47, "y": -2.28}, {"name": "bone61", "parent": "bone60", "length": 30.35, "rotation": -2.03, "x": 29.52}, {"name": "bone62", "parent": "bone61", "length": 35.76, "rotation": -7.62, "x": 30.35}, {"name": "bone63", "parent": "bone3", "length": 79.46, "rotation": -46.56, "x": 147.64, "y": 44.59}, {"name": "bone64", "parent": "bone63", "length": 70.13, "rotation": -12.15, "x": 79.46}, {"name": "bone65", "parent": "bone64", "length": 64.19, "rotation": -3.18, "x": 70.13}, {"name": "bone66", "parent": "bone65", "length": 65.36, "rotation": 0.34, "x": 64.19}, {"name": "bone67", "parent": "bone4", "length": 52.75, "rotation": 62.64, "x": 10.73, "y": 18.56}, {"name": "bone68", "parent": "bone67", "length": 44.82, "rotation": -21.9, "x": 52.75}, {"name": "bone69", "parent": "bone68", "length": 21.72, "rotation": 11.87, "x": 44.82}, {"name": "bone70", "parent": "bone3", "length": 68.14, "rotation": -23.6, "x": 148, "y": 44.09}, {"name": "bone71", "parent": "bone70", "length": 63.83, "rotation": -12.68, "x": 68.14}, {"name": "bone72", "parent": "bone71", "length": 62.31, "rotation": -3.68, "x": 63.83}, {"name": "bone73", "parent": "bone72", "length": 70.68, "rotation": -7.18, "x": 62.31}, {"name": "bone74", "parent": "bone73", "length": 85.85, "rotation": 3.19, "x": 70.68}, {"name": "bone75", "parent": "bone4", "length": 49.09, "rotation": 51.05, "x": 42.56, "y": -11.96}, {"name": "bone76", "parent": "bone75", "length": 51.32, "rotation": -18.88, "x": 49.09}, {"name": "bone77", "parent": "bone76", "length": 39.14, "rotation": -16.71, "x": 51.32}, {"name": "bone78", "parent": "bone77", "length": 38.41, "rotation": -15.56, "x": 39.14}, {"name": "bone79", "parent": "bone78", "length": 59.83, "rotation": -10.69, "x": 38.41}, {"name": "bone80", "parent": "bone15", "length": 73.09, "rotation": 28.03, "x": 202.94, "y": 3.52}, {"name": "bone81", "parent": "bone80", "length": 47.32, "rotation": 11.39, "x": 73.09}, {"name": "bone82", "parent": "bone81", "length": 50.39, "rotation": 15.78, "x": 47.32}, {"name": "bone83", "parent": "bone82", "length": 47.14, "rotation": -3.64, "x": 50.39}, {"name": "bone84", "parent": "bone83", "length": 43.24, "rotation": 17.45, "x": 47.14}, {"name": "bone85", "parent": "bone15", "length": 102.69, "rotation": -20.98, "x": 120.56, "y": -42.37}, {"name": "bone86", "parent": "bone85", "length": 50.33, "rotation": 43.5, "x": 51.1, "y": 20.34}, {"name": "bone87", "parent": "bone86", "length": 52.15, "rotation": 11.02, "x": 50.33}, {"name": "bone88", "parent": "bone85", "length": 57.64, "rotation": 43.74, "x": 101.15, "y": 19.65}, {"name": "bone89", "parent": "bone88", "length": 62.07, "rotation": 2.62, "x": 58.48, "y": 0.8}, {"name": "bone90", "parent": "bone85", "length": 56.8, "rotation": 29.72, "x": 103.61, "y": 28.97}, {"name": "bone91", "parent": "bone90", "length": 50.5, "rotation": -5.46, "x": 56.8}, {"name": "bone92", "parent": "bone15", "length": 61.61, "rotation": 178.63, "x": 85.61, "y": -20.81}, {"name": "bone93", "parent": "bone92", "length": 62.23, "rotation": -14.18, "x": 61.61}, {"name": "bone94", "parent": "bone93", "length": 42.62, "rotation": -12.57, "x": 62.23}, {"name": "bone95", "parent": "bone94", "length": 19.37, "rotation": -2.88, "x": 42.62}, {"name": "bone96", "parent": "bone62", "length": 43.97, "rotation": 1.56, "x": 35, "y": 1.52}, {"name": "bone97", "parent": "bone96", "length": 28.7, "rotation": -9.95, "x": 43.97}, {"name": "bone98", "parent": "bone97", "length": 37.17, "rotation": -6.03, "x": 29.29, "y": 0.32}, {"name": "bone99", "parent": "bone63", "length": 40.8, "rotation": 43.64, "x": 330.9, "y": -21.77}, {"name": "bone100", "parent": "bone99", "length": 37.63, "rotation": -0.38, "x": 40.8}, {"name": "bone101", "parent": "bone74", "length": 34.84, "rotation": 30.7, "x": 85.56, "y": 0.06}, {"name": "bone102", "parent": "bone", "length": 143.41, "rotation": -110.56, "x": -25.14, "y": -19.8}, {"name": "bone103", "parent": "bone102", "length": 125, "rotation": 17.21, "x": 143.41}, {"name": "bone104", "parent": "bone", "length": 193.16, "rotation": -60.34, "x": 67.54, "y": 1.37}, {"name": "bone105", "parent": "bone104", "length": 117.42, "rotation": -23.6, "x": 193.16}, {"name": "1", "parent": "root", "x": 104.45, "y": -1.64, "color": "ff3f00ff", "icon": "ik"}, {"name": "2", "parent": "root", "x": -153.89, "y": 2.74, "color": "ff3f00ff", "icon": "ik"}, {"name": "3", "parent": "root", "x": -406.9, "y": -0.65, "color": "ff3f00ff", "icon": "ik"}, {"name": "4", "parent": "root", "x": 43.29, "y": 114.49, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone106", "parent": "bone85", "length": 120.66, "rotation": 60.14, "x": 101.84, "y": 7.05}, {"name": "bone107", "parent": "bone106", "length": 125.41, "rotation": -1.98, "x": 120.66}, {"name": "bone108", "parent": "bone107", "length": 111.37, "rotation": -12.62, "x": 125.41}, {"name": "bone109", "parent": "bone108", "length": 106.96, "rotation": 3.01, "x": 111.37}, {"name": "bone110", "parent": "bone109", "length": 118.16, "rotation": 13.35, "x": 106.96}, {"name": "bone111", "parent": "bone110", "length": 100.84, "rotation": 19.29, "x": 117.81, "y": -1.04}, {"name": "bone112", "parent": "bone111", "length": 97.19, "rotation": 1.08, "x": 100.84}, {"name": "bone113", "parent": "bone85", "length": 120.93, "rotation": 71.22, "x": -1.95, "y": 10.17}, {"name": "bone114", "parent": "bone113", "length": 120.81, "rotation": -2.54, "x": 120.93}, {"name": "bone115", "parent": "bone114", "length": 122.15, "x": 120.81}, {"name": "bone116", "parent": "bone115", "length": 129.2, "rotation": -4.17, "x": 122.15}, {"name": "bone117", "parent": "bone116", "length": 109.26, "rotation": -1.47, "x": 129.2}, {"name": "bone118", "parent": "bone117", "length": 100.67, "rotation": 5.64, "x": 109.26}, {"name": "bone119", "parent": "bone118", "length": 90.02, "rotation": -2.56, "x": 100.67}, {"name": "bone120", "parent": "bone85", "length": 140.48, "rotation": 48.69, "x": 24.92, "y": 9.63}, {"name": "bone121", "parent": "bone120", "length": 94.19, "rotation": 3.7, "x": 141.23, "y": 0.27}, {"name": "bone122", "parent": "bone121", "length": 98.24, "rotation": 4.05, "x": 94.19}, {"name": "bone123", "parent": "bone122", "length": 83.84, "rotation": -11.4, "x": 98.24}, {"name": "bone124", "parent": "bone123", "length": 85.18, "rotation": -1.97, "x": 83.84}, {"name": "bone125", "parent": "bone124", "length": 122.95, "rotation": -14.39, "x": 84.58, "y": -0.89, "scaleX": 0.5888}, {"name": "bone126", "parent": "bone27", "length": 159.31, "rotation": -66.26, "x": 11.71, "y": -148.8}, {"name": "bone127", "parent": "bone126", "length": 127.94, "rotation": 0.54, "x": 159.31}, {"name": "bone128", "parent": "bone127", "length": 135.44, "rotation": 3.04, "x": 127.94}, {"name": "bone129", "parent": "bone128", "length": 134.1, "rotation": -1, "x": 135.44}, {"name": "bone130", "parent": "bone129", "length": 130.37, "rotation": -1.52, "x": 134.1}, {"name": "bone131", "parent": "bone6", "x": 112.49, "y": 0.28}, {"name": "bone133", "parent": "bone6", "x": 20.46, "y": 72.94}, {"name": "bone134", "parent": "bone133", "x": 3.43, "y": -57.69}], "slots": [{"name": "pifeng4", "bone": "root", "attachment": "pifeng4"}, {"name": "pifeng3", "bone": "root", "attachment": "pifeng3"}, {"name": "zuoshou2", "bone": "root", "attachment": "zuoshou2"}, {"name": "pifeng2", "bone": "root", "attachment": "pifeng2"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "tun", "bone": "root", "attachment": "tun"}, {"name": "youshou3", "bone": "root", "attachment": "youshou3"}, {"name": "kuzi", "bone": "root", "attachment": "kuzi"}, {"name": "shenti", "bone": "root", "attachment": "shenti"}, {"name": "liusu5", "bone": "root", "attachment": "liusu5"}, {"name": "jianzhang1", "bone": "root", "attachment": "jianzhang1"}, {"name": "<PERSON><PERSON>i", "bone": "root", "attachment": "<PERSON><PERSON>i"}, {"name": "youshou2", "bone": "root", "attachment": "youshou2"}, {"name": "you<PERSON>ou", "bone": "root", "attachment": "you<PERSON>ou"}, {"name": "yiling5", "bone": "root", "attachment": "yiling5"}, {"name": "yiling4", "bone": "root", "attachment": "yiling4"}, {"name": "bozi", "bone": "root", "attachment": "bozi"}, {"name": "yiling3", "bone": "root", "attachment": "yiling3"}, {"name": "yiling2", "bone": "root", "attachment": "yiling2"}, {"name": "pifeng", "bone": "root", "attachment": "pifeng"}, {"name": "yiling", "bone": "root", "attachment": "yiling"}, {"name": "liusu4", "bone": "root", "attachment": "liusu4"}, {"name": "liusu3", "bone": "root", "attachment": "liusu3"}, {"name": "liusu2", "bone": "root", "attachment": "liusu2"}, {"name": "liusu1", "bone": "root", "attachment": "liusu1"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "yan<PERSON>i", "bone": "root", "attachment": "yan<PERSON>i"}, {"name": "tongkong", "bone": "root", "attachment": "tongkong"}, {"name": "tou", "bone": "root", "attachment": "tou"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "biyan", "bone": "root"}, {"name": "meimao", "bone": "root", "attachment": "meimao"}, {"name": "<PERSON><PERSON>i", "bone": "root", "attachment": "<PERSON><PERSON>i"}, {"name": "erduo", "bone": "root", "attachment": "erduo"}], "ik": [{"name": "1", "bones": ["bone105"], "target": "1", "compress": true, "stretch": true}, {"name": "2", "order": 1, "bones": ["bone103"], "target": "2", "compress": true, "stretch": true}, {"name": "3", "order": 2, "bones": ["bone19"], "target": "3", "compress": true, "stretch": true}, {"name": "4", "order": 3, "bones": ["bone14"], "target": "4", "compress": true, "stretch": true}], "transform": [{"name": "11", "order": 4, "bones": ["bone132"], "target": "bone8", "x": 19.77, "y": -137.03, "mixRotate": 0, "mixX": 0.3, "mixScaleX": 0, "mixShearY": 0}, {"name": "22", "order": 5, "bones": ["bone131"], "target": "bone8", "x": 124.42, "y": -71.71, "mixRotate": 0, "mixX": 0.3, "mixScaleX": 0, "mixShearY": 0}, {"name": "33", "order": 6, "bones": ["bone133"], "target": "bone8", "x": 32.4, "y": 0.96, "mixRotate": 0, "mixX": 0.5, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"biyan": {"biyan": {"type": "mesh", "uvs": [0.58238, 0, 0.68917, 0, 0.8041, 0, 0.91903, 0, 1, 0.16416, 1, 0.45945, 0.92837, 0.57931, 0.85673, 0.69917, 0.7627, 0.74126, 0.66866, 0.78334, 0.55557, 0.71149, 0.41028, 0.61919, 0.27539, 0.62047, 0.21004, 1, 0.16589, 1, 0.12175, 1, 0.0776, 1, 0.03345, 1, 0.01673, 0.91425, 0, 0.8285, 0, 0.50993, 0.04239, 0.47335, 0.13968, 0.38942, 0.19972, 0.42495, 0.37369, 0.52793, 0.47179, 0.5252], "triangles": [6, 3, 4, 5, 6, 4, 11, 24, 25, 12, 23, 24, 12, 24, 11, 6, 7, 2, 6, 2, 3, 10, 25, 0, 11, 25, 10, 8, 1, 2, 8, 2, 7, 9, 0, 1, 9, 1, 8, 10, 0, 9, 22, 15, 21, 14, 15, 22, 23, 14, 22, 23, 13, 14, 12, 13, 23, 19, 20, 21, 18, 19, 21, 16, 17, 18, 15, 16, 21, 16, 18, 21], "vertices": [2, 7, 26.12, 57.95, 0.2, 8, 45.27, -41.17, 0.8, 2, 7, 24.72, 48.01, 0.27931, 8, 43.87, -51.11, 0.72069, 2, 7, 23.21, 37.32, 0.36465, 8, 42.36, -61.8, 0.63535, 2, 7, 21.7, 26.62, 0.45, 8, 40.85, -72.5, 0.55, 2, 7, 16.08, 19.72, 0.5, 8, 35.23, -79.4, 0.5, 2, 7, 7.89, 20.88, 0.5, 8, 27.04, -78.24, 0.5, 2, 7, 5.51, 28.02, 0.45, 8, 24.66, -71.1, 0.55, 2, 7, 3.13, 35.15, 0.4, 8, 22.28, -63.97, 0.6, 2, 7, 3.2, 44.07, 0.325, 8, 22.35, -55.05, 0.675, 2, 7, 3.27, 52.99, 0.25, 8, 22.42, -46.13, 0.75, 2, 7, 6.75, 63.23, 0.14058, 8, 25.9, -35.89, 0.85942, 1, 8, 30.36, -22.73, 1, 2, 7, 12.95, 88.95, 0.1, 8, 32.1, -10.17, 0.9, 2, 7, 3.29, 96.52, 0.3, 8, 22.44, -2.6, 0.7, 2, 7, 3.87, 100.63, 0.375, 8, 23.02, 1.51, 0.625, 2, 7, 4.45, 104.74, 0.45, 8, 23.6, 5.62, 0.55, 2, 7, 5.03, 108.85, 0.525, 8, 24.18, 9.73, 0.475, 2, 7, 5.61, 112.96, 0.6, 8, 24.76, 13.84, 0.4, 2, 7, 8.21, 114.18, 0.65, 8, 27.36, 15.06, 0.35, 2, 7, 10.8, 115.4, 0.7, 8, 29.95, 16.28, 0.3, 2, 7, 19.63, 114.15, 0.6, 8, 38.78, 15.03, 0.4, 2, 7, 20.09, 110.07, 0.5393, 8, 39.24, 10.95, 0.4607, 2, 7, 21.14, 100.68, 0.4, 8, 40.29, 1.56, 0.6, 2, 7, 19.37, 95.23, 0.29737, 8, 38.52, -3.89, 0.70263, 1, 8, 33.38, -19.68, 1, 2, 7, 13.01, 70.3, 0.1, 8, 32.16, -28.82, 0.9], "hull": 26, "edges": [0, 50, 6, 8, 8, 10, 22, 24, 24, 26, 38, 40, 48, 50, 14, 16, 16, 18, 10, 12, 12, 14, 18, 20, 20, 22, 30, 32, 32, 34, 26, 28, 28, 30, 34, 36, 36, 38, 40, 42, 42, 44, 44, 46, 46, 48, 0, 2, 2, 4, 4, 6], "width": 94, "height": 28}}, "bozi": {"bozi": {"type": "mesh", "uvs": [0.78846, 0, 0.833, 0.00516, 0.83504, 0.10492, 0.80301, 0.16612, 0.79672, 0.20346, 0.82493, 0.2898, 0.82985, 0.30352, 0.97361, 0.38003, 0.93526, 0.41977, 0.95161, 0.44023, 1, 0.49592, 1, 0.53815, 0.82531, 0.68176, 0.41325, 0.87468, 0.17482, 0.98187, 0.10699, 1, 0.08197, 1, 0, 0.86735, 0, 0.7595, 0.06529, 0.64932, 0.14083, 0.55808, 0.16966, 0.45521, 0.2051, 0.43107, 0.12385, 0.24964, 0.27934, 0.15572, 0.47046, 0.08053, 0.72386, 0], "triangles": [8, 12, 22, 12, 9, 11, 12, 8, 9, 9, 10, 11, 8, 6, 7, 13, 18, 19, 19, 20, 13, 22, 12, 20, 22, 20, 21, 5, 24, 4, 5, 22, 24, 8, 22, 6, 6, 22, 5, 24, 25, 4, 4, 25, 3, 3, 25, 26, 22, 23, 24, 2, 3, 26, 2, 0, 1, 0, 2, 26, 15, 16, 14, 16, 17, 14, 14, 17, 13, 13, 20, 12, 13, 17, 18], "vertices": [1, 5, 183.46, -102.84, 1, 3, 4, 468.21, -4.31, 0, 5, 179.59, -111.85, 0.99994, 12, -33.79, 212.04, 6e-05, 3, 4, 430.7, -18.2, 0, 5, 140.25, -104.61, 0.98837, 12, -14.42, 177.03, 0.01162, 3, 4, 405.27, -19.99, 0, 5, 117.48, -93.15, 0.95653, 12, -8.83, 152.16, 0.04346, 3, 4, 390.72, -23.77, 1e-05, 5, 103.04, -88.95, 0.91377, 12, -2.92, 138.34, 0.08622, 3, 4, 360.16, -41.15, 2e-05, 5, 67.9, -88.27, 0.73213, 12, 18.85, 110.74, 0.26785, 3, 4, 355.34, -44, 2e-05, 5, 62.3, -88.25, 0.68303, 12, 22.39, 106.4, 0.31695, 3, 4, 336.87, -83.44, 4e-05, 5, 26.26, -112.71, 0.43453, 12, 64.15, 94.06, 0.56543, 3, 4, 319.09, -81.05, 5e-05, 5, 12.2, -101.56, 0.35384, 12, 64.46, 76.12, 0.64611, 3, 4, 312.55, -87.12, 6e-05, 5, 3.48, -103.44, 0.27778, 12, 71.45, 70.57, 0.72216, 3, 4, 295.03, -104.44, 9e-05, 5, -20.44, -109.37, 0.13699, 12, 91.2, 55.84, 0.86291, 3, 4, 279.09, -110.15, 0.00011, 5, -37.05, -106.13, 0.08534, 12, 99.23, 40.94, 0.91455, 3, 4, 212.21, -94.2, 0.1322, 5, -86.38, -58.23, 5e-05, 12, 93.5, -27.57, 0.86775, 3, 4, 109.52, -36.87, 0.9895, 5, -145.33, 43.55, 0, 12, 52.23, -137.71, 0.01049, 1, 4, 51.77, -3.1, 1, 1, 4, 40.01, 8.18, 1, 2, 4, 38.2, 13.25, 0.9998, 16, 182.38, 94.8, 0.0002, 2, 4, 82.34, 47.77, 0.83876, 16, 159.61, 43.61, 0.16124, 2, 4, 123.06, 62.35, 0.34767, 16, 130.5, 11.62, 0.65233, 1, 16, 90.38, -11.6, 1, 2, 5, -9.5, 76.7, 0.03584, 16, 53.74, -27.73, 0.96416, 2, 5, 29.8, 62.72, 0.35376, 16, 21.39, -54.06, 0.64624, 2, 5, 37.84, 53.38, 0.52208, 16, 9.24, -56.09, 0.47792, 2, 5, 112.6, 56.58, 0.98699, 16, -26.81, -121.66, 0.01301, 1, 5, 143.15, 16.55, 1, 2, 4, 413.48, 58.89, 0, 5, 164.87, -29.55, 1, 1, 5, 186.12, -89.21, 1], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 215, "height": 401}}, "erduo": {"erduo": {"type": "mesh", "uvs": [0.64656, 0, 0.78104, 0.0594, 0.93284, 0.17488, 1, 0.27525, 1, 0.54227, 0.89576, 0.71116, 0.55582, 0.9657, 0.45992, 1, 0.18462, 1, 0.0778, 0.9688, 5e-05, 0.87642, 0, 0.83059, 0, 0.24946, 0.14254, 0.11582, 0.34934, 0], "triangles": [1, 2, 3, 1, 4, 13, 3, 4, 1, 14, 0, 1, 13, 14, 1, 4, 12, 13, 5, 11, 12, 4, 5, 12, 10, 8, 9, 5, 6, 11, 10, 11, 8, 11, 7, 8, 6, 7, 11], "vertices": [2, 11, -31.8, 31.92, 0.00093, 10, 45.37, -1.25, 0.99907, 2, 11, -25.01, 36.69, 0.02126, 10, 43.85, -9.41, 0.97874, 2, 11, -13.58, 40.78, 0.12352, 10, 38.54, -20.33, 0.87648, 2, 11, -4.7, 41.27, 0.24665, 10, 32.54, -26.88, 0.75335, 2, 11, 16, 34.13, 0.72063, 10, 12.75, -36.26, 0.27937, 2, 11, 27.39, 24.69, 0.93368, 10, -1.99, -37.48, 0.06632, 1, 11, 41.58, 1.82, 1, 1, 11, 42.68, -3.63, 1, 2, 11, 38.19, -16.65, 0.99541, 10, -38.62, -15.48, 0.00459, 2, 11, 34.03, -20.86, 0.98684, 10, -38.59, -9.56, 0.01316, 2, 11, 25.6, -22.07, 0.95442, 10, -33.41, -2.8, 0.04558, 2, 11, 22.05, -20.84, 0.9266, 10, -30.02, -1.19, 0.0734, 1, 10, 13.05, 19.21, 1, 1, 10, 26, 17.46, 1, 1, 10, 39.01, 12.18, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 50, "height": 82}}, "jianzhang": {"jianzhang": {"type": "mesh", "uvs": [0.38951, 0, 0.63913, 0.19104, 0.75376, 0.33815, 0.96295, 0.74092, 1, 0.82609, 1, 0.9724, 0.98622, 1, 0.77939, 1, 0.48841, 0.83847, 0.21088, 0.61189, 0.06562, 0.41688, 0, 0.27324, 0, 0.08118, 0.02994, 0.05351, 0.19804, 0], "triangles": [11, 12, 13, 10, 13, 14, 11, 13, 10, 9, 14, 0, 10, 14, 9, 8, 0, 1, 8, 1, 2, 9, 0, 8, 6, 3, 4, 7, 2, 3, 6, 7, 3, 8, 2, 7, 4, 5, 6], "vertices": [1, 28, 98.74, 34.8, 1, 1, 28, 141.6, 38.41, 1, 1, 28, 163.02, 36.1, 1, 1, 28, 206.07, 22.91, 1, 1, 28, 214.1, 19.65, 1, 1, 28, 218.4, 9.88, 1, 1, 28, 217.16, 7.13, 1, 1, 28, 186.3, -6.45, 1, 1, 28, 138.13, -14.74, 1, 1, 28, 90.06, -17.81, 1, 1, 28, 62.66, -14.31, 1, 1, 28, 48.65, -9.02, 1, 1, 28, 43, 3.81, 1, 1, 28, 46.66, 7.63, 1, 1, 28, 70.17, 22.23, 1], "hull": 15, "edges": [0, 28, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28], "width": 163, "height": 73}}, "jianzhang1": {"jianzhang1": {"type": "mesh", "uvs": [0.95725, 0, 0.9996, 0, 1, 0.01177, 1, 0.08396, 0.70983, 0.27831, 0.64007, 0.36817, 0.53372, 0.65913, 0.40399, 0.78666, 0.18781, 0.99918, 0.02602, 1, 0, 0.98564, 0, 0.9394, 0.03995, 0.86308, 0.17521, 0.46968, 0.17168, 0.3604, 0.31446, 0.21655, 0.4198, 0.14123, 0.60412, 0.05475, 0.77082, 0.06543, 0.80855, 0.0696, 0.31289, 0.36902, 0.58039, 0.29008, 0.2208, 0.61992, 0.17402, 0.87504, 0.49498, 0.31529, 0.40351, 0.57622], "triangles": [9, 23, 8, 9, 12, 23, 12, 9, 11, 8, 23, 7, 9, 10, 11, 23, 12, 22, 12, 13, 22, 25, 22, 20, 22, 13, 20, 13, 14, 20, 7, 23, 22, 22, 25, 7, 7, 25, 6, 6, 25, 5, 25, 20, 24, 25, 24, 5, 24, 21, 5, 5, 21, 4, 24, 16, 21, 20, 15, 24, 20, 14, 15, 15, 16, 24, 4, 21, 17, 3, 4, 2, 0, 2, 19, 19, 2, 4, 4, 18, 19, 21, 16, 17, 4, 17, 18, 1, 2, 0], "vertices": [1, 86, -44.23, -6.28, 1, 1, 86, -51.69, -3.37, 1, 1, 86, -50.92, -1.19, 1, 1, 86, -45.77, 11.99, 1, 2, 86, 19.17, 27.52, 0.64085, 87, -18.22, 27.19, 0.35915, 2, 86, 37.86, 39.13, 0.06751, 87, 3.32, 22.75, 0.93249, 2, 87, 63.49, 28.81, 0.14883, 88, 18.42, 25.77, 0.85117, 1, 88, 48.65, 8.11, 1, 2, 88, 99.04, -21.32, 0.11913, 90, 58.83, 28.99, 0.88087, 2, 88, 106.67, -50.93, 0.00065, 90, 70.58, 0.76, 0.99935, 1, 90, 69.85, -4.85, 1, 1, 90, 61.46, -8.29, 1, 1, 90, 44.76, -6.99, 1, 2, 86, 126.94, 25.71, 0.0293, 89, 22.82, -13.45, 0.9707, 2, 86, 119.77, 5.51, 0.40939, 89, 3.67, -23.08, 0.59061, 1, 86, 84.37, -10.93, 1, 1, 86, 60.46, -17.44, 1, 1, 86, 21.84, -20.56, 1, 1, 86, -6.74, -7.15, 1, 1, 86, -13.09, -3.8, 1, 3, 86, 95.52, 16.79, 0.21811, 87, 29.78, -33.15, 0.06126, 89, -6.04, 1.83, 0.72062, 2, 86, 42.8, 20.77, 0.17429, 87, -5.73, 6.03, 0.82571, 3, 88, 25.43, -33.46, 0.09749, 89, 45.89, 6.78, 0.68751, 90, -12.31, 6.55, 0.215, 2, 88, 76.08, -29.8, 0.15837, 90, 37.31, 17.35, 0.84163, 3, 86, 59.63, 19.5, 0.15474, 87, 5.61, -6.48, 0.83647, 89, -30.1, 28.6, 0.00879, 4, 87, 59.25, -0.37, 0.00221, 88, 8.68, -2.07, 0.97471, 89, 23.57, 34.48, 0.02118, 90, -33.34, 35.24, 0.0019], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 24, 26, 26, 40, 42, 8, 40, 44, 44, 46, 46, 16, 40, 48, 48, 42, 48, 50, 12, 14, 14, 16, 50, 14, 16, 18], "width": 189, "height": 196}}, "jiemao": {"jiemao": {"type": "mesh", "uvs": [0.89183, 0, 0.95743, 0.04734, 1, 0.26317, 1, 0.51948, 0.9642, 0.69948, 0.66527, 0.53467, 0.62942, 0.6795, 0.55909, 0.91359, 0.30753, 0.81187, 0.1749, 0.83903, 0.14915, 1, 0.09894, 1, 0, 0.71425, 0, 0.53226, 0.17803, 0.37279, 0.29733, 0.74369, 0.43052, 0.73871, 0.53478, 0], "triangles": [9, 15, 8, 8, 15, 16, 8, 16, 7, 3, 4, 0, 2, 3, 0, 2, 0, 1, 7, 16, 6, 5, 0, 4, 16, 17, 6, 6, 17, 5, 5, 17, 0, 9, 14, 15, 10, 11, 9, 11, 12, 9, 14, 9, 13, 9, 12, 13], "vertices": [2, 7, 30.85, 19.89, 0.4, 8, 49.99, -79.23, 0.6, 2, 7, 28.12, 12.78, 0.45, 8, 47.27, -86.34, 0.55, 2, 7, 19.76, 9.11, 0.5, 8, 38.9, -90.01, 0.5, 2, 7, 10.62, 10.4, 0.5, 8, 29.77, -88.72, 0.5, 2, 7, 4.77, 15.31, 0.45, 8, 23.92, -83.81, 0.55, 2, 7, 15.36, 47.93, 0.3, 8, 34.51, -51.19, 0.7, 2, 7, 10.77, 52.67, 0.25, 8, 29.92, -46.45, 0.75, 2, 7, 3.53, 61.71, 0.2, 8, 22.68, -37.41, 0.8, 2, 7, 11.13, 89.35, 0.1, 8, 30.28, -9.77, 0.9, 2, 7, 12.26, 104.33, 0.5, 8, 31.41, 5.21, 0.5, 2, 7, 6.93, 108.02, 0.6, 8, 26.08, 8.9, 0.4, 2, 7, 7.72, 113.64, 0.7, 8, 26.87, 14.51, 0.3, 2, 7, 19.47, 123.27, 0.7, 8, 38.62, 24.15, 0.3, 2, 7, 25.96, 122.35, 0.6, 8, 45.11, 23.23, 0.4, 2, 7, 28.83, 101.63, 0.4, 8, 47.98, 2.51, 0.6, 2, 7, 13.73, 90.15, 0.1, 8, 32.88, -8.97, 0.9, 2, 7, 11.8, 75.22, 0.2, 8, 30.95, -23.9, 0.8, 2, 7, 36.49, 59.84, 0.3, 8, 55.64, -39.28, 0.7], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 113, "height": 36}}, "kuzi": {"kuzi": {"type": "mesh", "uvs": [0.69772, 0, 0.71125, 0.07298, 0.6319, 0.10031, 0.59869, 0.20434, 0.63506, 0.38843, 0.65788, 0.45087, 0.80031, 0.6906, 0.85304, 0.76234, 1, 0.83529, 1, 0.93787, 0.94157, 0.95742, 0.73879, 1, 0.23891, 1, 0.07147, 0.95169, 0, 0.83681, 0, 0.6249, 0.02903, 0.41857, 0.05806, 0.21224, 0.16386, 0.13394, 0.47832, 0], "triangles": [7, 12, 6, 14, 12, 13, 6, 12, 14, 6, 14, 5, 5, 16, 4, 5, 14, 15, 10, 11, 7, 7, 11, 12, 9, 10, 8, 10, 7, 8, 16, 5, 15, 4, 16, 17, 4, 17, 3, 17, 18, 3, 18, 19, 3, 3, 19, 2, 2, 0, 1, 2, 19, 0], "vertices": [3, 105, 39.08, 113.13, 0.81787, 106, -186.49, 41.99, 0, 15, -47.49, -64.83, 0.18213, 3, 105, 53.99, 106.88, 0.8154, 106, -170.32, 42.23, 0, 15, -36.24, -53.22, 0.1846, 3, 105, 53.56, 93.98, 0.80712, 106, -165.54, 30.23, 0, 15, -24.38, -58.31, 0.19288, 3, 105, 71.09, 78.49, 0.72866, 106, -143.29, 23.06, 0, 15, -3.58, -47.63, 0.27134, 3, 105, 108.87, 63, 0.3093, 106, -102.46, 23.99, 0, 15, 24.6, -18.08, 0.6907, 3, 105, 122.43, 59.06, 0.0938, 106, -88.46, 25.8, 0, 15, 33.21, -6.88, 0.9062, 3, 105, 178.41, 50.78, 0.35343, 106, -33.84, 40.62, 0.15215, 15, 61.3, 42.25, 0.49443, 3, 105, 195.88, 49.56, 0.37351, 106, -17.35, 46.51, 0.43912, 15, 68.79, 58.08, 0.18736, 3, 105, 220.3, 60.01, 0.22691, 106, 0.85, 65.86, 0.74506, 15, 67.95, 84.62, 0.02803, 3, 105, 239.91, 48.84, 0.16817, 106, 23.29, 63.47, 0.82846, 15, 85.49, 98.82, 0.00337, 3, 105, 239.48, 39.4, 0.15349, 106, 26.68, 54.65, 0.84494, 15, 94.13, 94.99, 0.00157, 2, 105, 233.17, 9.39, 0.03584, 106, 32.91, 24.62, 0.96416, 3, 105, 197.55, -53.16, 0.00164, 106, 25.3, -46.96, 0.21138, 15, 165.07, 22.24, 0.78697, 2, 106, 12.19, -69.81, 0.01659, 15, 171.98, -3.19, 0.98341, 2, 105, 149.32, -65.28, 0.05446, 15, 158.81, -27.09, 0.94554, 2, 105, 108.82, -42.21, 0.73876, 15, 122.58, -56.42, 0.26124, 2, 105, 71.44, -16.11, 0.99991, 15, 84.67, -81.73, 9e-05, 3, 105, 34.06, 9.98, 0.98097, 106, -149.79, -54.54, 0, 15, 46.75, -107.05, 0.01903, 3, 105, 26.64, 31.75, 0.9306, 106, -165.31, -37.57, 0, 15, 23.78, -106.04, 0.0694, 3, 105, 23.44, 85.68, 0.8275, 106, -189.82, 10.57, 0, 15, -27.61, -89.39, 0.1725], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 36, 38, 30, 32, 32, 34], "width": 144, "height": 220}}, "liusu1": {"liusu1": {"type": "mesh", "uvs": [0.36419, 0, 0.55781, 0, 0.68329, 0.03918, 0.83176, 0.11463, 0.98023, 0.19007, 1, 0.6363, 1, 1, 0.9032, 1, 0.69205, 0.96012, 0.38069, 0.83858, 0.1978, 0.72115, 0.15548, 0.70276, 0.05845, 0.58117, 0, 0.538, 0, 0.48708, 0.14938, 0.3145, 0.2589, 0.10244, 0.32205, 0.02075, 0.77906, 0.44128, 0.74088, 0.70174, 0.42487, 0.3496, 0.32095, 0.57255], "triangles": [20, 1, 2, 21, 15, 20, 15, 16, 20, 20, 16, 0, 16, 17, 0, 20, 0, 1, 10, 21, 9, 9, 21, 19, 10, 11, 21, 11, 12, 21, 15, 21, 12, 15, 12, 14, 14, 12, 13, 7, 5, 6, 8, 19, 7, 7, 19, 5, 8, 9, 19, 21, 20, 19, 19, 18, 5, 19, 20, 18, 18, 4, 5, 20, 2, 18, 18, 3, 4, 18, 2, 3], "vertices": [1, 35, -10.99, -18.52, 1, 2, 37, -41.47, -45.52, 0.16583, 35, -23.21, 11.62, 0.83417, 2, 37, -36.12, -24.06, 0.4791, 35, -24.92, 33.67, 0.5209, 2, 37, -24.82, 1.65, 0.89727, 35, -22.34, 61.64, 0.10273, 1, 37, -13.52, 27.36, 1, 2, 37, 62.42, 35.49, 0.30104, 38, 6.64, 35.14, 0.69896, 1, 38, 68.83, 35.14, 1, 1, 38, 68.83, 18.88, 1, 2, 38, 62.01, -16.59, 0.96047, 36, 52.9, 99.56, 0.03953, 4, 37, 103.52, -66.16, 0.02173, 38, 41.23, -68.9, 0.37478, 36, 58.53, 43.56, 0.6031, 35, 120.86, 37.92, 0.0004, 3, 37, 85.42, -98.09, 0.00182, 38, 21.15, -99.63, 0.02988, 36, 54.85, 7.04, 0.9683, 3, 37, 82.73, -105.39, 9e-05, 38, 18, -106.74, 0.00551, 36, 55.33, -0.72, 0.99441, 2, 36, 44.38, -24.77, 0.96985, 35, 100.41, -28.79, 0.03015, 2, 36, 42.34, -36.88, 0.94435, 35, 97.25, -40.66, 0.05565, 2, 36, 34.61, -40.89, 0.92404, 35, 89.18, -43.93, 0.07596, 2, 36, -3.14, -32.19, 0.29776, 35, 52.41, -31.76, 0.70224, 1, 35, 11.89, -28.34, 1, 1, 35, -5.04, -23.75, 1, 3, 37, 31.49, -3.66, 0.98436, 36, -32.59, 71.7, 0.00139, 35, 32.75, 74.42, 0.01425, 4, 37, 76.34, -7.25, 0.00863, 38, 17.83, -8.39, 0.9602, 36, 9.9, 86.5, 0.02513, 35, 76.44, 85.21, 0.00604, 3, 37, 19.6, -64.04, 0.09266, 38, -42.39, -61.48, 0.00234, 35, 40.58, 13.38, 0.905, 4, 37, 58.75, -79.05, 0.02841, 38, -4.26, -78.94, 0.04493, 36, 22.77, 13.71, 0.90363, 35, 82.47, 11.53, 0.02303], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 4, 6, 6, 8, 6, 36, 36, 38, 2, 40, 40, 42, 42, 20, 38, 16], "width": 168, "height": 171}}, "liusu2": {"liusu2": {"type": "mesh", "uvs": [0.17739, 0, 0.35478, 0, 0.42075, 0.03136, 0.60491, 0.19586, 0.82579, 0.47036, 1, 0.86984, 1, 0.99149, 0.88007, 1, 0.69109, 1, 0.50211, 1, 0.23, 0.97345, 0.18466, 0.949, 0.18483, 0.81759, 0.10946, 0.64621, 0.05473, 0.37369, 0, 0.10117, 0, 0, 0.33073, 0.2547, 0.46462, 0.49361, 0.5701, 0.73703], "triangles": [13, 14, 18, 14, 17, 18, 18, 3, 4, 18, 17, 3, 14, 15, 17, 15, 0, 17, 17, 2, 3, 2, 0, 1, 2, 17, 0, 15, 16, 0, 6, 7, 5, 5, 7, 8, 8, 19, 5, 19, 9, 12, 8, 9, 19, 11, 12, 10, 9, 10, 12, 19, 4, 5, 12, 13, 19, 13, 18, 19, 19, 18, 4], "vertices": [1, 39, -10.8, -3.92, 1, 1, 39, -8.46, 10.08, 1, 1, 39, -3.14, 14.54, 1, 2, 40, -41.36, 24.23, 0.00159, 39, 22.65, 25.18, 0.99841, 2, 40, 0.27, 36.13, 0.57251, 39, 64.54, 36.11, 0.42749, 1, 40, 59.19, 41.79, 1, 1, 40, 76.53, 39.31, 1, 1, 40, 76.38, 29.64, 1, 1, 40, 74.25, 14.68, 1, 1, 40, 72.11, -0.29, 1, 1, 40, 65.24, -21.3, 1, 1, 40, 61.25, -24.39, 1, 1, 40, 42.52, -21.7, 1, 2, 40, 17.23, -24.18, 0.92847, 39, 80.1, -24.58, 0.07153, 2, 40, -22.24, -22.97, 0.01463, 39, 40.67, -22.44, 0.98537, 1, 39, 1.24, -20.31, 1, 1, 39, -13.13, -17.92, 1, 1, 39, 27.4, 2.15, 1, 2, 40, -0.5, 7.05, 0.45397, 39, 63.09, 7.06, 0.54603, 1, 40, 35.39, 10.45, 1], "hull": 17, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 2, 0, 0, 32, 0, 34, 34, 36, 36, 38, 14, 16, 16, 18, 38, 16, 26, 28, 28, 30], "width": 80, "height": 144}}, "liusu3": {"liusu3": {"type": "mesh", "uvs": [0, 0.03514, 0.06666, 0, 0.15377, 0, 0.36147, 0.16076, 0.59493, 0.41082, 0.92334, 0.74013, 1, 0.81618, 1, 0.89536, 0.87575, 0.98493, 0.83317, 1, 0.71525, 1, 0.51123, 0.82175, 0.38252, 0.65468, 0.25382, 0.48761, 0.10247, 0.29115, 0, 0.13645], "triangles": [12, 13, 4, 4, 13, 3, 13, 14, 3, 3, 14, 15, 3, 15, 2, 1, 2, 0, 2, 15, 0, 9, 10, 8, 8, 10, 5, 7, 8, 6, 5, 10, 11, 8, 5, 6, 11, 12, 5, 12, 4, 5], "vertices": [1, 41, -9.46, -10.27, 1, 1, 41, -11.03, -3.42, 1, 1, 41, -7.88, 3, 1, 1, 41, 17.8, 9.36, 1, 2, 42, -10.55, 13.48, 0.02737, 41, 54.52, 12.67, 0.97263, 1, 42, 38.87, 15.75, 1, 1, 42, 50.31, 16.34, 1, 1, 42, 58.92, 11.3, 1, 1, 42, 63.52, -3.19, 1, 1, 42, 63.39, -7.17, 1, 1, 42, 58.51, -15.51, 1, 1, 42, 30.67, -18.6, 1, 2, 42, 7.17, -17.08, 0.79823, 41, 74.43, -16.51, 0.20177, 2, 42, -16.33, -15.55, 0.0094, 41, 50.89, -16.71, 0.9906, 1, 41, 23.2, -16.94, 1, 1, 41, 2, -15.9, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 28, 30, 26, 28, 22, 24, 24, 26, 8, 10], "width": 82, "height": 126}}, "liusu4": {"liusu4": {"type": "mesh", "uvs": [0, 0, 0.1005, 0, 0.23932, 0.14479, 0.44745, 0.45183, 0.49741, 0.50599, 0.70841, 0.6796, 0.84222, 0.75109, 1, 0.76816, 1, 0.98042, 0.87549, 1, 0.65018, 1, 0.45946, 0.87364, 0.21881, 0.63565, 0, 0.28373], "triangles": [4, 12, 3, 3, 13, 2, 3, 12, 13, 13, 1, 2, 13, 0, 1, 7, 8, 9, 9, 10, 6, 10, 5, 6, 10, 11, 5, 9, 6, 7, 11, 12, 4, 11, 4, 5], "vertices": [1, 43, -0.15, -1.04, 1, 1, 43, 5.11, 5.43, 1, 1, 43, 22.04, 6.53, 1, 1, 43, 53.43, 3.3, 1, 2, 44, 0.3, 3.6, 0.50101, 43, 59.66, 3.58, 0.49899, 1, 44, 23.31, 3.13, 1, 1, 44, 35.8, 5.41, 1, 1, 44, 46.86, 12.56, 1, 1, 44, 58.42, -1.57, 1, 1, 44, 51.49, -9.41, 1, 1, 44, 37.02, -21.25, 1, 2, 44, 17.88, -22.87, 0.9923, 43, 82.22, -18.79, 0.0077, 2, 44, -10.54, -19.68, 0.23928, 43, 53.74, -21.4, 0.76072, 1, 43, 18.8, -16.42, 1], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26], "width": 83, "height": 86}}, "liusu5": {"liusu5": {"type": "mesh", "uvs": [1, 0, 1, 0.07246, 0.99556, 0.12296, 0.81167, 0.43386, 0.62778, 0.74477, 0.41554, 0.94149, 0.29575, 1, 0.15259, 1, 0, 0.91516, 0, 0.80291, 0.1642, 0.7069, 0.42401, 0.51447, 0.56467, 0.35617, 0.78719, 0], "triangles": [3, 11, 12, 3, 12, 2, 12, 13, 2, 2, 13, 1, 13, 0, 1, 5, 6, 10, 10, 6, 7, 10, 7, 9, 5, 10, 4, 7, 8, 9, 10, 11, 4, 4, 11, 3], "vertices": [1, 91, -10.3, 0.64, 1, 1, 91, -4.66, 5.19, 1, 1, 91, -0.5, 8.08, 1, 1, 91, 33.39, 15.61, 1, 2, 92, 8.23, 24.03, 0.77952, 91, 67.28, 23.14, 0.22048, 2, 92, 34.76, 25.06, 0.99988, 91, 93.79, 21.64, 0.00012, 1, 92, 45.98, 21.97, 1, 1, 92, 54.4, 13.38, 1, 1, 92, 57.31, -1.71, 1, 1, 92, 49.29, -9.57, 1, 1, 92, 32.78, -6.43, 1, 2, 92, 3.76, -4.31, 0.85083, 91, 60.13, -4.65, 0.14917, 1, 91, 40.39, -5.41, 1, 1, 91, 0.94, -13.26, 1], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 4, 6, 6, 8], "width": 84, "height": 100}}, "maozi": {"maozi": {"type": "mesh", "uvs": [0.38367, 0, 0.58907, 0.08507, 0.815, 0.2253, 0.96911, 0.3406, 1, 0.43973, 1, 0.54488, 0.99787, 0.57243, 0.9503, 0.65348, 0.891, 0.70726, 0.88852, 0.84331, 0.85994, 0.87794, 0.82583, 0.79341, 0.747, 0.74793, 0.6851, 0.81195, 0.67109, 0.73464, 0.35058, 0.89489, 0.0489, 1, 0, 1, 0, 0.9576, 0.00024, 0.95409, 0.12359, 0.80646, 0.12402, 0.68562, 0, 0.48074, 0, 0.23667, 0.03492, 0.13509, 0.10389, 0.05199, 0.19792, 7e-05, 0.21301, 0], "triangles": [25, 22, 23, 7, 3, 4, 4, 5, 7, 3, 8, 2, 6, 7, 5, 27, 21, 25, 25, 23, 24, 21, 22, 25, 7, 8, 3, 14, 1, 2, 12, 14, 2, 8, 12, 2, 11, 12, 8, 13, 14, 12, 9, 11, 8, 10, 11, 9, 0, 21, 27, 15, 0, 1, 15, 1, 14, 27, 25, 26, 0, 15, 21, 16, 17, 18, 16, 19, 20, 16, 18, 19, 20, 21, 15, 16, 20, 15], "vertices": [1, 136, 86.72, 10.82, 1, 1, 136, 61.81, -50.6, 1, 1, 136, 25.68, -116.92, 1, 1, 136, -2.64, -161.63, 1, 1, 136, -22.54, -168.59, 1, 1, 136, -42.22, -165.81, 1, 1, 136, -47.28, -164.42, 1, 1, 136, -60.37, -147.53, 1, 1, 136, -67.84, -127.74, 1, 1, 136, -93.19, -123.37, 1, 1, 136, -98.42, -113.6, 1, 1, 136, -81.11, -105.26, 1, 1, 136, -69.15, -82.03, 1, 1, 136, -78.42, -61.16, 1, 1, 136, -63.34, -58.86, 1, 1, 136, -79.31, 44.71, 1, 1, 136, -85.78, 140.99, 1, 1, 136, -83.64, 156.14, 1, 1, 136, -75.71, 155.02, 1, 1, 136, -75.06, 154.86, 1, 1, 136, -52.83, 112.73, 1, 1, 136, -30.23, 109.4, 1, 1, 136, 13.54, 142.42, 1, 1, 136, 59.21, 135.98, 1, 1, 136, 76.69, 122.47, 1, 1, 136, 89.23, 98.9, 1, 1, 136, 94.83, 68.38, 1, 1, 136, 94.18, 63.71, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 313, "height": 189}}, "meimao": {"meimao": {"type": "mesh", "uvs": [0.87896, 0, 0.91813, 0.08131, 1, 0.33151, 1, 0.47799, 0.88247, 0.40333, 0.79724, 0.38823, 0.50059, 0.90689, 0.34955, 1, 0.02602, 1, 0, 0.90918, 6e-05, 0.51306, 0.26672, 0.71778, 0.32903, 0.57583, 0.68584, 0], "triangles": [7, 12, 6, 11, 12, 7, 8, 11, 7, 6, 12, 13, 6, 13, 5, 4, 2, 3, 5, 13, 0, 4, 5, 0, 1, 4, 0, 4, 1, 2, 11, 8, 10, 8, 9, 10], "vertices": [2, 7, 43.35, 22.62, 0.45, 8, 62.5, -76.5, 0.55, 2, 7, 39.75, 18.66, 0.48, 8, 58.9, -80.46, 0.52, 2, 7, 29.29, 10.79, 0.5, 8, 48.44, -88.33, 0.5, 2, 7, 23.93, 11.55, 0.5, 8, 43.08, -87.57, 0.5, 2, 7, 28.52, 24.31, 0.45, 8, 47.67, -74.81, 0.55, 2, 7, 30.42, 33.77, 0.4, 8, 49.57, -65.35, 0.6, 2, 7, 16.1, 69.65, 0.2, 8, 35.25, -29.48, 0.8, 1, 8, 34.23, -12.09, 1, 2, 7, 20.19, 123.23, 0.65, 8, 39.34, 24.11, 0.35, 2, 7, 23.92, 125.67, 0.7, 8, 43.07, 26.55, 0.3, 2, 7, 38.44, 123.61, 0.65, 8, 57.59, 24.49, 0.35, 2, 7, 26.72, 94.83, 0.1, 8, 45.87, -4.29, 0.9, 2, 7, 30.94, 87.13, 0.1, 8, 50.09, -11.99, 0.9, 2, 7, 46.4, 44.23, 0.3, 8, 65.55, -54.89, 0.7], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 18, 20], "width": 113, "height": 37}}, "pifeng": {"pifeng": {"type": "mesh", "uvs": [0.09963, 0, 0.12712, 0.00713, 0.15954, 0.03202, 0.17008, 0.03771, 0.24078, 0.06719, 0.24829, 0.06622, 0.28468, 0.06621, 0.31653, 0.08647, 0.34696, 0.09416, 0.46233, 0.11499, 0.51335, 0.13512, 0.57055, 0.17881, 0.59468, 0.18842, 0.65576, 0.22056, 0.67693, 0.24818, 0.69194, 0.28796, 0.69649, 0.29372, 0.75183, 0.36613, 0.78582, 0.43762, 0.80766, 0.50282, 0.8295, 0.56802, 0.85431, 0.64209, 0.8808, 0.70986, 0.9073, 0.77762, 0.92979, 0.83515, 0.94789, 0.87742, 0.99304, 0.97822, 1, 0.98998, 1, 1, 0.76482, 1, 0.52963, 1, 0.56381, 0.88185, 0.56599, 0.87261, 0.58165, 0.77721, 0.58396, 0.75865, 0.57987, 0.66432, 0.57992, 0.65702, 0.56822, 0.60916, 0.55624, 0.58433, 0.5291, 0.54773, 0.4975, 0.51819, 0.47663, 0.50314, 0.35995, 0.42842, 0.2916, 0.38172, 0.19365, 0.30886, 0.09569, 0.23601, 0.04404, 0.19524, 0.0123, 0.16178, 0, 0.13049, 0, 0.08834, 0.00723, 0.05314, 0.02301, 0.03416, 0.08231, 0, 0.08879, 0.08231, 0.18781, 0.13398, 0.29374, 0.18325, 0.39276, 0.25414, 0.501, 0.31782, 0.56778, 0.37911, 0.63686, 0.45, 0.69443, 0.5233, 0.73128, 0.58938, 0.73589, 0.66388, 0.7497, 0.76121, 0.75431, 0.83571, 0.75431, 0.90581], "triangles": [30, 31, 65, 30, 65, 29, 34, 62, 63, 33, 34, 63, 33, 63, 64, 64, 32, 33, 65, 32, 64, 31, 32, 65, 36, 37, 61, 36, 61, 62, 35, 36, 62, 34, 35, 62, 40, 41, 59, 40, 59, 60, 39, 40, 60, 38, 39, 60, 38, 60, 61, 37, 38, 61, 58, 42, 57, 41, 42, 58, 41, 58, 59, 44, 55, 56, 43, 44, 56, 43, 56, 57, 42, 43, 57, 48, 53, 54, 47, 48, 54, 46, 47, 54, 45, 46, 54, 45, 54, 55, 44, 45, 55, 48, 49, 53, 53, 2, 3, 50, 51, 53, 49, 50, 53, 2, 51, 1, 1, 52, 0, 1, 51, 52, 53, 51, 2, 4, 53, 3, 54, 53, 4, 7, 5, 6, 55, 7, 8, 55, 8, 9, 7, 4, 5, 7, 54, 4, 55, 54, 7, 10, 56, 55, 10, 55, 9, 56, 10, 11, 12, 57, 56, 12, 56, 11, 57, 12, 13, 57, 13, 14, 57, 14, 15, 16, 57, 15, 16, 58, 57, 58, 16, 17, 59, 58, 17, 59, 17, 18, 60, 59, 18, 60, 18, 19, 60, 19, 20, 61, 60, 20, 61, 20, 21, 62, 61, 21, 62, 21, 22, 63, 62, 22, 63, 22, 23, 64, 63, 23, 64, 23, 24, 64, 24, 25, 65, 64, 25, 26, 29, 65, 26, 65, 25, 26, 27, 28, 29, 26, 28], "vertices": [3, 28, -98.17, 50.89, 0.0194, 53, 40.19, -11.33, 0.9029, 46, 82.34, 1.16, 0.07769, 4, 28, -83.48, 50.2, 0.05829, 53, 38.73, -25.96, 0.70891, 46, 83.89, -13.46, 0.23225, 21, -67.05, 100.58, 0.00055, 5, 28, -60.06, 35.54, 0.22964, 53, 22.85, -48.58, 0.22055, 46, 72.95, -38.84, 0.52849, 45, 78.78, -74.84, 0.00478, 21, -39.45, 101.93, 0.01654, 5, 28, -53.34, 32.78, 0.3052, 53, 19.75, -55.14, 0.13897, 46, 71.25, -45.9, 0.52654, 45, 73.18, -79.47, 0.00691, 21, -32.36, 103.52, 0.02239, 5, 28, -11.45, 21.65, 0.87739, 53, 6.42, -96.39, 0.00213, 46, 66.6, -89, 0.11394, 45, 43.6, -111.16, 0.00183, 21, 8.38, 118.33, 0.00472, 5, 28, -8.52, 23.91, 0.91439, 53, 8.53, -99.44, 0.00117, 46, 69.29, -91.55, 0.08162, 45, 44.22, -114.81, 0.00099, 21, 9.49, 121.86, 0.00183, 2, 28, 7.44, 30.94, 0.99178, 46, 78.66, -106.25, 0.00822, 1, 28, 28.89, 20.05, 1, 1, 28, 45.08, 19.46, 1, 1, 28, 103.36, 24.2, 1, 1, 28, 133.17, 17.12, 1, 4, 29, 0.87, -10.58, 0.61642, 28, 174.39, -8.56, 0.37932, 21, 178.13, 199.78, 0, 22, 43.18, 196.77, 0.00426, 2, 29, 12.71, -2.13, 0.98809, 28, 188.53, -11.99, 0.01191, 1, 29, 49.52, 17.13, 1, 1, 29, 76.74, 19.35, 1, 2, 30, -10.82, 14.89, 0.11728, 29, 113.75, 15.46, 0.88272, 2, 30, -5.16, 15.72, 0.31893, 29, 119.45, 15.98, 0.68107, 1, 30, 65.75, 25.32, 1, 2, 31, -4, 25.1, 0.40284, 30, 133.37, 25.2, 0.59716, 1, 31, 56.7, 22.33, 1, 1, 31, 117.4, 19.56, 1, 1, 32, 42.48, 19.35, 1, 2, 33, -36.5, 23.21, 0.02187, 32, 105.89, 22.58, 0.97813, 1, 33, 26.98, 24.22, 1, 1, 33, 80.87, 25.08, 1, 2, 34, -4.48, 26.6, 0.35494, 33, 120.6, 26.44, 0.64506, 1, 34, 90.55, 27.74, 1, 2, 34, 101.81, 28.67, 1, 27, 71.76, 205.4, 0, 2, 34, 110.79, 26.69, 1, 27, 80.89, 206.55, 0, 3, 34, 86.47, -83.31, 0.55773, 33, 215.05, -80.48, 0.00134, 27, 94.96, 94.78, 0.44093, 1, 27, 109.03, -16.99, 1, 2, 26, 136.44, -14.38, 0.54914, 27, -0.63, -14.3, 0.45086, 2, 26, 127.91, -14.23, 0.89485, 27, -9.17, -14.33, 0.10515, 2, 25, 164.25, -17.57, 0.01632, 26, 40.02, -15.84, 0.98368, 2, 25, 147.23, -16.21, 0.18281, 26, 22.96, -16.5, 0.81719, 1, 25, 60.61, -16.88, 1, 1, 25, 53.91, -16.76, 1, 2, 24, 101.82, -23.82, 0.44293, 25, 9.89, -21.7, 0.55707, 2, 24, 79.03, -18.1, 0.95468, 25, -12.99, -27.1, 0.04532, 1, 24, 43.28, -13.66, 1, 2, 23, 137.99, -15.72, 0.089, 24, 12.22, -14.17, 0.911, 2, 23, 120.95, -15.64, 0.77865, 24, -4.67, -16.44, 0.22135, 1, 23, 32.59, -20.25, 1, 2, 22, 113.88, -21.29, 0.98195, 23, -21.35, -21.36, 0.01805, 1, 22, 32.2, -19.39, 1, 1, 21, 104.46, -21.73, 1, 1, 21, 59.63, -23.35, 1, 2, 45, -34.58, 4.62, 0.00292, 21, 25.49, -20.32, 0.99708, 2, 45, -5.48, 8.29, 0.36249, 21, -2.1, -10.39, 0.63751, 1, 45, 33.09, 5.32, 1, 2, 53, -21.1, 12.8, 0.16183, 46, 17.43, 12.31, 0.83817, 1, 53, -2.13, 11.9, 1, 3, 28, -105.76, 47.55, 0.0106, 53, 37.25, -3.57, 0.9502, 46, 77.89, 8.16, 0.0392, 4, 28, -72.5, -20.37, 0.1411, 46, 15.81, -35.03, 0.51432, 45, 35.35, -37.51, 0.18369, 21, -17.7, 48.95, 0.1609, 5, 28, -9.99, -44.69, 0.51882, 46, 1.25, -100.51, 0.04394, 45, -15.58, -81.16, 0.00525, 21, 47.5, 64.73, 0.43162, 22, -98.7, 73.59, 0.00036, 4, 29, -34.32, -138.48, 0.02496, 28, 54.66, -65.67, 0.56252, 21, 112.53, 84.48, 0.31708, 22, -32.2, 87.62, 0.09544, 6, 30, -75.7, -116.6, 0.01042, 29, 41.85, -112.33, 0.23001, 28, 124.28, -106.15, 0.23977, 21, 192.8, 91.07, 0.0631, 22, 48.33, 87.19, 0.45372, 23, -87.27, 86.89, 0.00298, 6, 30, -6.41, -80.49, 0.21067, 29, 112.99, -80.02, 0.32429, 28, 195.27, -138.79, 0.02775, 21, 269.71, 104.87, 0.00018, 22, 126.15, 94.25, 0.29995, 23, -9.47, 94.22, 0.13716, 7, 31, -79.09, -65.2, 0, 30, 55.93, -63.1, 0.56064, 29, 176.18, -66.03, 0.04677, 28, 247.2, -177.41, 0.00015, 22, 190.42, 86.63, 0.05612, 23, 54.82, 86.83, 0.33597, 24, -84.34, 75.88, 0.00034, 4, 31, -8.38, -47.02, 0.21233, 30, 127.09, -46.78, 0.45158, 23, 126.88, 75.11, 0.22101, 24, -11.35, 74.25, 0.11508, 4, 31, 63.28, -34.69, 0.69835, 30, 199.05, -36.35, 0.00156, 23, 197.47, 57.64, 0.00376, 24, 60.97, 66.73, 0.29633, 5, 32, -14.19, -31.67, 0.13487, 31, 126.34, -30.63, 0.58438, 24, 122.78, 53.58, 0.13131, 25, -7.09, 56.66, 0.14941, 26, -138.9, 37.58, 2e-05, 4, 32, 53.76, -39.73, 0.61561, 31, 193.58, -43.31, 0.00142, 25, 61.32, 57.85, 0.29027, 26, -71.11, 46.86, 0.0927, 3, 33, -1.74, -47.2, 0.00998, 32, 143.09, -46.57, 0.57275, 26, 17.07, 62.7, 0.41727, 5, 34, -61.88, -55.67, 0.01376, 33, 65.88, -57.64, 0.42769, 32, 211.04, -54.64, 0.13947, 26, 84.87, 71.98, 0.38506, 27, -54.05, 70.94, 0.03402, 5, 34, 0.95, -69.56, 0.30261, 33, 129.13, -69.5, 0.23307, 32, 274.66, -64.28, 0.0083, 26, 148.87, 78.65, 0.13976, 27, 9.8, 78.98, 0.31627], "hull": 53, "edges": [0, 104, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 48, 50, 50, 52, 52, 54, 54, 56, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 102, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 56, 58, 58, 60, 130, 58, 46, 48, 42, 44, 44, 46, 40, 42, 36, 38, 38, 40, 86, 88, 88, 90], "width": 479, "height": 918}}, "pifeng2": {"pifeng2": {"type": "mesh", "uvs": [1, 0, 1, 0.00484, 0.93479, 0.06, 0.91425, 0.07855, 0.85929, 0.14004, 0.80434, 0.20154, 0.6968, 0.27798, 0.65285, 0.31411, 0.61478, 0.41111, 0.57672, 0.50811, 0.52667, 0.58963, 0.42777, 0.67825, 0.33115, 0.76482, 0.23453, 0.85138, 0.08995, 0.95975, 0.01934, 1, 0, 1, 0, 0.96869, 0.0008, 0.94492, 0.0867, 0.82186, 0.16429, 0.74314, 0.23569, 0.6707, 0.30709, 0.59827, 0.32377, 0.58159, 0.39172, 0.49144, 0.39111, 0.4761, 0.42595, 0.37578, 0.47242, 0.24201, 0.51507, 0.16613, 0.55772, 0.09025, 0.75342, 0.03375, 0.97527, 0], "triangles": [6, 27, 28, 5, 28, 29, 9, 24, 25, 25, 26, 8, 21, 22, 11, 10, 22, 23, 19, 20, 13, 5, 6, 28, 4, 5, 29, 29, 30, 4, 4, 30, 3, 3, 30, 2, 2, 31, 1, 2, 30, 31, 31, 0, 1, 8, 26, 7, 26, 27, 7, 7, 27, 6, 23, 24, 10, 10, 24, 9, 8, 9, 25, 11, 22, 10, 13, 14, 19, 13, 20, 12, 20, 21, 12, 12, 21, 11, 14, 15, 17, 15, 16, 17, 17, 18, 14, 14, 18, 19], "vertices": [1, 125, -38.32, 29.38, 1, 1, 125, -35.75, 30.31, 1, 1, 125, -0.85, 25.38, 1, 1, 125, 10.76, 24.05, 1, 1, 125, 48.13, 22.78, 1, 1, 125, 85.49, 21.52, 1, 2, 126, -5.21, 10.67, 0.11856, 125, 135.35, 10.58, 0.88144, 1, 126, 17.48, 5.66, 1, 1, 126, 72.7, 11.72, 1, 1, 127, 34.91, 15.34, 1, 1, 127, 82.54, 12.66, 1, 1, 128, 37.96, 6.32, 1, 1, 129, 8.56, 3.7, 1, 1, 129, 63.19, 2.67, 1, 3, 130, 82.92, 9.27, 0.53094, 129, 134.18, -4.04, 0, 115, 43.56, 8.65, 0.46906, 2, 130, 132.04, 10.12, 0.98644, 115, 68.21, -6.48, 0.01356, 1, 130, 137.4, 6.36, 1, 2, 130, 114.42, -4.99, 1, 129, 148.6, -22.47, 0, 3, 130, 96.76, -13.45, 0.58628, 129, 136.42, -28.07, 0, 115, 37.92, -14.82, 0.41372, 1, 114, 74.6, -9.05, 1, 1, 114, 26.13, -5.82, 1, 1, 113, 93.08, -3.81, 1, 1, 113, 48.39, -3.18, 1, 1, 113, 38.07, -2.98, 1, 1, 112, 108.77, -3.53, 1, 1, 112, 100.29, -5.26, 1, 1, 112, 43.04, -6.88, 1, 1, 111, 87.08, -7.88, 1, 1, 111, 43.15, -3.51, 1, 2, 125, 47.91, -58.8, 0.05158, 111, -0.78, 0.85, 0.94842, 2, 125, 0.98, -22.98, 0.93934, 111, -39.67, 45.28, 0.06066, 1, 125, -36.17, 23.47, 1], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 18, 20, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 44, 46, 46, 48, 48, 50, 58, 60, 60, 62, 50, 52, 52, 54, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 38, 40, 40, 42, 42, 44, 54, 56, 56, 58, 6, 8, 8, 10], "width": 254, "height": 564}}, "pifeng3": {"pifeng3": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.8, 0, 0.6, 0, 0.4, 0, 0.2, 0, 0, 0.5, 0, 1, 0, 1, 0.2, 1, 0.4, 1, 0.6, 1, 0.8, 0.5, 0.8, 0.5, 0.6, 0.5, 0.4, 0.5, 0.2], "triangles": [0, 1, 13, 14, 15, 12, 13, 14, 12, 1, 14, 13, 11, 16, 10, 15, 16, 11, 12, 15, 11, 10, 17, 9, 16, 17, 10, 6, 7, 8, 17, 8, 9, 17, 6, 8, 5, 6, 17, 16, 5, 17, 4, 5, 16, 15, 4, 16, 3, 4, 15, 14, 3, 15, 2, 3, 14, 1, 2, 14], "vertices": [1, 124, 87.72, 14.18, 1, 4, 117, 113.45, 66.32, 0.61975, 115, 296.26, 133.95, 0.00016, 123, 188.94, -106.25, 0.01676, 124, 92.93, -102.2, 0.36333, 3, 117, 86.04, -46.91, 0.99966, 123, 188.94, -222.75, 1e-05, 124, 98.14, -218.58, 0.00032, 2, 117, -4.35, -25.03, 0.43125, 116, 96.97, -25.11, 0.56875, 1, 116, 6.18, -4.93, 1, 1, 115, 32.93, -14.61, 1, 2, 114, 55.04, -38.6, 0.98136, 113, 168.37, -35.66, 0.01864, 2, 114, -32.28, -70.59, 0.10788, 113, 82.85, -72.19, 0.89212, 3, 114, -72.35, 38.8, 0.08831, 113, 37.08, 34.94, 0.80151, 122, -155.03, -132.87, 0.11018, 4, 114, -112.43, 148.19, 0.0854, 113, -8.69, 142.08, 0.27073, 121, -37.65, -12.66, 0.3817, 122, -166.48, -16.94, 0.26217, 4, 114, -25.1, 180.18, 0.00417, 113, 76.84, 178.61, 0.00637, 121, 55.1, -5.9, 0.9695, 122, -73.93, -7.8, 0.01996, 1, 122, 18.62, 1.34, 1, 2, 122, 111.17, 10.48, 0.26619, 123, 2.94, 10.25, 0.73381, 2, 123, 95.94, 10.25, 0.83437, 124, -5.19, 10.02, 0.16563, 6, 117, 23.06, 88.2, 0.365, 116, 122.24, 88.62, 0.13414, 115, 203.9, 122.99, 0.07186, 122, 215.17, -96.31, 0.01753, 123, 95.94, -106.25, 0.22217, 124, 0.02, -106.36, 0.1893, 7, 117, -67.33, 110.08, 0.02229, 116, 31.45, 108.79, 0.11119, 115, 111.55, 112.03, 0.3818, 114, 189.62, 134.77, 0.01767, 122, 122.62, -105.45, 0.25324, 123, 2.94, -106.25, 0.20679, 124, -92.88, -110.52, 0.00701, 6, 116, -59.33, 128.97, 0.00033, 115, 19.2, 101.08, 0.32434, 114, 102.3, 102.78, 0.25089, 113, 208.13, 108.01, 0.00216, 122, 30.07, -114.59, 0.41182, 123, -90.06, -106.25, 0.01046, 4, 115, -73.15, 90.12, 0.02136, 114, 14.97, 70.79, 0.54279, 113, 122.6, 71.48, 0.18441, 122, -62.48, -123.73, 0.25144], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 233, "height": 465}}, "pifeng4": {"pifeng4": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.85714, 0, 0.71429, 0, 0.57143, 0, 0.42857, 0, 0.28571, 0, 0.14286, 0, 0, 0.5, 0, 1, 0, 1, 0.14286, 1, 0.28571, 1, 0.42857, 1, 0.57143, 1, 0.71429, 1, 0.85714, 0.5, 0.85714, 0.5, 0.71429, 0.5, 0.57143, 0.5, 0.42857, 0.5, 0.28571, 0.5, 0.14286], "triangles": [23, 10, 11, 13, 22, 12, 22, 23, 12, 12, 23, 11, 21, 22, 13, 20, 21, 14, 14, 21, 13, 16, 19, 15, 15, 20, 14, 19, 20, 15, 17, 18, 16, 18, 19, 16, 7, 8, 23, 23, 8, 10, 8, 9, 10, 6, 7, 22, 22, 7, 23, 20, 5, 21, 5, 6, 21, 21, 6, 22, 3, 4, 19, 19, 4, 20, 4, 5, 20, 0, 1, 17, 1, 18, 17, 1, 2, 18, 2, 3, 18, 18, 3, 19], "vertices": [2, 34, 79.06, -116.85, 0.3588, 27, 99.25, 60.7, 0.6412, 3, 135, 136.23, 87.88, 0.93365, 134, 272.62, 84.24, 0.06383, 30, 585.13, -296.38, 0.00252, 2, 135, 133.31, -70.09, 1, 134, 265.51, -73.6, 0, 2, 135, 34.9, -68.27, 0.89485, 134, 167.18, -69.17, 0.10515, 3, 135, -63.52, -66.45, 0.0259, 134, 68.85, -64.74, 0.97186, 133, 203.15, -65.93, 0.00223, 2, 134, -29.48, -60.31, 0.20551, 133, 104.91, -59.79, 0.79449, 2, 133, 6.67, -53.65, 0.56597, 132, 137.44, -53.22, 0.43403, 2, 132, 39.02, -52.29, 0.95707, 131, 198.82, -51.92, 0.04293, 2, 132, -59.4, -51.37, 0.00293, 131, 100.39, -51.92, 0.99707, 1, 131, 1.97, -51.92, 1, 4, 132, -156.34, 107.56, 5e-05, 131, 1.97, 106.08, 0.59935, 30, -83.3, -129.27, 0.16289, 29, 33.57, -124.57, 0.23771, 1, 30, -44.98, 24.01, 1, 5, 134, -310.25, 268.65, 0.00016, 133, -170.09, 274.01, 0.00117, 132, -56.42, 264.62, 0.00081, 131, 100.39, 264.08, 0.00045, 30, 50.51, 0.14, 0.9974, 2, 31, 9.92, -23.48, 0.5, 30, 146, -23.74, 0.5, 2, 32, -33.51, -47.23, 0.3, 31, 106, -44.84, 0.7, 2, 32, 63.81, -61.98, 0.7863, 25, 74.29, 37.16, 0.2137, 3, 33, 15.23, -77.96, 0.37025, 32, 161.13, -76.72, 0.37025, 26, 42.11, 38.06, 0.2595, 3, 34, -17.05, -95.6, 0.2939, 33, 111.98, -96.1, 0.2939, 26, 140.01, 48.26, 0.4122, 5, 135, 37.82, 89.7, 0.42967, 134, 174.29, 88.67, 0.13986, 133, 311.24, 85.62, 0.00568, 33, 82.86, -251.4, 0.4198, 30, 489.64, -272.51, 0.005, 5, 135, -60.59, 91.52, 0.13095, 134, 75.96, 93.1, 0.46301, 133, 213, 91.76, 0.07171, 33, -13.89, -233.26, 0.313, 30, 394.15, -248.64, 0.02132, 6, 135, -159, 93.35, 0.0161, 134, -22.37, 97.53, 0.18802, 133, 114.77, 97.9, 0.29848, 132, 237.36, 103.84, 0.00228, 32, 40.14, -218.19, 0.4504, 30, 298.66, -224.76, 0.04473, 8, 135, -257.41, 95.17, 0.00095, 134, -120.7, 101.96, 0.02583, 133, 16.53, 104.04, 0.22814, 132, 138.93, 104.77, 0.06266, 131, 297.25, 106.08, 0.00184, 32, -57.18, -203.45, 0.2672, 31, 71.73, -199.07, 0.34683, 30, 203.17, -200.89, 0.06655, 6, 134, -219.03, 106.39, 0.00928, 133, -81.71, 110.18, 0.13959, 132, 40.51, 105.7, 0.31531, 131, 198.82, 106.08, 0.12014, 30, 107.68, -177.02, 0.4122, 29, 221.69, -182.59, 0.00347, 6, 134, -317.36, 110.81, 0.00021, 133, -179.94, 116.32, 0.01945, 132, -57.91, 106.63, 0.08465, 131, 100.39, 106.08, 0.51934, 30, 12.19, -153.15, 0.28418, 29, 127.63, -153.58, 0.09217], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0], "width": 316, "height": 689}}, "shenti": {"shenti": {"type": "mesh", "uvs": [0.9189, 0.03594, 1, 0.08597, 1, 0.38083, 0.94307, 0.50513, 0.81339, 0.64036, 0.79813, 0.70729, 0.86298, 0.81657, 0.55022, 0.86847, 0.25996, 0.97846, 0.20313, 1, 0.10156, 1, 0, 0.88077, 0.08489, 0.62806, 0, 0.44038, 0, 0.22019, 0.03425, 0.10317, 0.20827, 0.03779, 0.38863, 0.00945, 0.44876, 0, 0.746, 0, 0.27561, 0.23928, 0.20749, 0.42971, 0.22068, 0.61228, 0.25583, 0.83577], "triangles": [20, 17, 18, 19, 2, 20, 0, 1, 2, 0, 2, 19, 18, 19, 20, 20, 16, 17, 14, 15, 20, 15, 16, 20, 8, 9, 23, 23, 9, 10, 10, 11, 23, 8, 23, 7, 7, 5, 6, 5, 7, 22, 21, 14, 20, 3, 20, 2, 3, 21, 20, 5, 22, 4, 3, 4, 21, 22, 12, 21, 12, 13, 21, 4, 22, 21, 13, 14, 21, 11, 12, 23, 12, 22, 23, 7, 23, 22], "vertices": [1, 12, 210.57, 70.7, 1, 1, 12, 259.39, 59.96, 1, 5, 3, 168.43, -349.32, 0.25177, 4, 92.87, -336.15, 0.44305, 105, -122.43, 404.82, 0.1368, 103, -441.8, 244.28, 0.00048, 12, 350.61, -109.27, 0.1679, 4, 3, 86.7, -324.94, 0.37383, 4, 7.61, -338.44, 0.37565, 105, -65.16, 341.61, 0.24969, 103, -356.58, 247.85, 0.00083, 5, 2, 146.91, -266.8, 1e-05, 3, -3.08, -266.78, 0.37536, 4, -95.82, -311.14, 0.15977, 105, -18.52, 245.35, 0.46337, 103, -252.76, 222.1, 0.00149, 5, 2, 103.01, -261.46, 2e-05, 3, -46.89, -260.84, 0.30381, 4, -139.3, -319.14, 0.07901, 105, 15.87, 217.56, 0.6151, 103, -209.4, 230.75, 0.00205, 4, 3, -117.3, -293.03, 0.24051, 4, -196.18, -371.66, 0.03866, 105, 92.77, 208.61, 0.71829, 103, -153.32, 284.12, 0.00254, 5, 2, -6.71, -150.06, 0.00025, 3, -155.07, -147.94, 0.10116, 4, -277.27, -245.56, 0.0098, 105, 49.89, 64.95, 0.88609, 103, -70.35, 159.24, 0.0027, 5, 2, -83.89, -17.55, 0.06353, 3, -230.42, -14.38, 1e-05, 4, -390.48, -142.12, 0, 105, 45.12, -88.33, 0.2208, 103, 44.4, 57.51, 0.71566, 5, 2, -99.01, 8.39, 0.01206, 3, -245.18, 11.77, 0, 4, -412.64, -121.86, 0, 105, 44.19, -118.34, 0.06936, 103, 66.86, 37.59, 0.91857, 1, 103, 83.52, -6.82, 1, 2, 2, -25.2, 106.35, 0.06558, 103, 27.38, -78.53, 0.93442, 3, 2, 141.04, 73.46, 0.45591, 3, -4.26, 73.52, 0.52559, 103, -140.81, -99.26, 0.0185, 2, 3, 117, 116.46, 0.89036, 4, -101.1, 90.43, 0.10964, 3, 3, 260.51, 120.34, 0.03987, 4, 34.06, 138.83, 0.33413, 16, 244.98, -14.14, 0.626, 1, 16, 181.79, -59.8, 1, 1, 16, 93, -36.62, 1, 1, 16, 18.27, 6.41, 1, 1, 12, 6.19, -12.86, 1, 1, 12, 128.38, 53.01, 1, 3, 4, 65.73, 13.45, 0.78043, 16, 158.17, 81.7, 0.1, 12, 9.04, -188.55, 0.11957, 1, 3, 126.57, 19.78, 1, 3, 2, 153.91, 10.52, 0.10756, 3, 7.74, 10.41, 0.8919, 103, -172.71, -43.5, 0.00054, 2, 2, 8.98, -11.83, 0.90663, 105, -36.67, -43.96, 0.09337], "hull": 20, "edges": [38, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 36, 38, 40, 42, 42, 44, 44, 46, 14, 16, 16, 18, 46, 16, 34, 36, 40, 34, 32, 34], "width": 467, "height": 652}}, "shenzi": {"shenzi": {"type": "mesh", "uvs": [1, 0.07832, 0.77931, 0.15411, 0.71959, 0.23335, 0.73337, 0.33785, 0.75175, 0.44235, 0.76093, 0.53077, 0.78849, 0.64216, 0.75175, 0.80293, 0.70581, 0.90362, 0.80687, 1, 0.37509, 1, 0.31078, 0.91244, 0.34752, 0.81253, 0.12704, 0.6954, 0, 0.57827, 0, 0.46802, 0, 0.35277, 0.11326, 0.23678, 0.297, 0.13573, 0.46236, 0.11391, 0.81606, 0, 1, 0], "triangles": [3, 16, 17, 17, 2, 3, 17, 18, 2, 18, 19, 2, 2, 19, 1, 1, 19, 0, 19, 20, 0, 20, 21, 0, 5, 15, 4, 3, 4, 16, 16, 4, 15, 13, 14, 6, 14, 5, 6, 15, 5, 14, 8, 12, 7, 12, 13, 7, 7, 13, 6, 10, 8, 9, 10, 11, 8, 11, 12, 8], "vertices": [1, 81, -7.03, 22.34, 1, 1, 81, 17.34, 13.64, 1, 2, 82, -29.82, 22.18, 0.00177, 81, 39.48, 15.85, 0.99823, 3, 83, -39.69, 37.86, 0.00034, 82, -1.17, 25.64, 0.45122, 81, 66.88, 24.9, 0.54844, 3, 83, -11.12, 33.71, 0.16706, 82, 27.45, 29.42, 0.8133, 81, 94.19, 34.26, 0.01964, 2, 83, 12.98, 29.77, 0.75494, 82, 51.71, 32.17, 0.24506, 3, 84, -11.58, 24.14, 0.2372, 83, 43.53, 25.88, 0.76274, 82, 82.17, 36.75, 6e-05, 2, 85, -7.2, 24.91, 0.09794, 84, 32.8, 21.61, 0.90206, 2, 85, 18.36, 13.56, 0.94058, 84, 60.59, 18.44, 0.05942, 1, 85, 45.82, 12.23, 1, 1, 85, 36.89, -16.19, 1, 1, 85, 12.51, -13.18, 1, 1, 84, 35.45, -6.28, 1, 2, 84, 3.12, -21.5, 0.48787, 83, 49.43, -21.7, 0.51213, 2, 83, 16.03, -24.26, 0.98376, 82, 69.35, -18.99, 0.01624, 2, 83, -13.86, -18.57, 0.13639, 82, 39.03, -21.64, 0.86361, 2, 82, 7.34, -24.41, 0.7951, 81, 85.11, -22.48, 0.2049, 2, 82, -25.23, -19.42, 0.01032, 81, 52.2, -24.02, 0.98968, 1, 81, 21.86, -19.72, 1, 1, 81, 12.86, -10.48, 1, 1, 81, -24.18, 4.07, 1, 1, 81, -27.77, 16.24, 1], "hull": 22, "edges": [40, 42, 40, 38, 38, 36, 36, 34, 34, 32, 30, 32, 28, 30, 28, 26, 26, 24, 24, 22, 22, 20, 0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 16, 18], "width": 69, "height": 276}}, "tongkong": {"tongkong": {"type": "mesh", "uvs": [1, 0.6399, 0.81427, 0.75002, 0.24852, 0.72732, 0.19112, 1, 0, 1, 0, 0.41852, 0.16817, 0.27775, 0.729, 0.34586, 0.79787, 0, 1, 0], "triangles": [2, 7, 1, 1, 7, 0, 7, 8, 0, 8, 9, 0, 2, 3, 5, 2, 5, 6, 5, 3, 4, 2, 6, 7], "vertices": [1, 138, -11.69, -7.96, 1, 1, 138, -12.65, 5.68, 1, 1, 137, -2.95, -11.76, 1, 1, 137, -9.39, -6.68, 1, 1, 137, -7.47, 6.95, 1, 2, 137, 7.5, 4.83, 0.99957, 138, 4.08, 62.52, 0.00043, 1, 137, 9.43, -7.67, 1, 1, 138, -1.39, 10.29, 1, 1, 138, 6.82, 4.12, 1, 1, 138, 4.79, -10.29, 1], "hull": 10, "edges": [8, 10, 10, 12, 12, 4, 6, 8, 4, 6, 16, 18, 16, 14, 14, 2, 0, 18, 2, 0, 2, 4, 12, 14], "width": 72, "height": 26}}, "tou": {"tou": {"type": "mesh", "uvs": [0.91951, 0.04864, 1, 0.27443, 1, 0.458, 0.96047, 0.55373, 0.87697, 0.56622, 0.79154, 0.58766, 0.79389, 0.69275, 0.73156, 0.77391, 0.5481, 0.89149, 0.34348, 1, 0.2027, 1, 0.15566, 0.94233, 0.16037, 0.80602, 0.12038, 0.70405, 0.12391, 0.63954, 0.05923, 0.56151, 0.06276, 0.45329, 0, 0.40127, 0, 0.10424, 0.13318, 0.04151, 0.27904, 0.02498, 0.49949, 0, 0.67481, 0, 0.85012, 0, 0.55539, 0.46472, 0.13269, 0.58135, 0.28914, 0.29358, 0.28936, 0.39002, 0.31815, 0.4527, 0.35586, 0.52842, 0.37907, 0.60156, 0.33701, 0.67471, 0.27754, 0.68754, 0.20647, 0.69909, 0.16731, 0.67214, 0.15135, 0.62081, 0.12253, 0.59778, 0.11704, 0.5675, 0.11537, 0.5127, 0.12374, 0.46347, 0.13462, 0.4294, 0.10951, 0.38719, 0.08775, 0.31759, 0.13796, 0.28723, 0.21078, 0.2776, 0.56707, 0.5584, 0.53448, 0.65919, 0.49428, 0.73587, 0.42646, 0.79476, 0.35612, 0.8181, 0.27251, 0.83215, 0.1966, 0.80371, 0.50923, 0.32771, 0.43912, 0.22537, 0.33046, 0.16025, 0.2148, 0.16645, 0.10789, 0.18816, 0.04129, 0.24398, 0.03428, 0.31996, 0.05883, 0.41213, 0.08429, 0.44966, 0.09024, 0.49997, 0.08627, 0.54442, 0.10814, 0.58278, 0.12155, 0.6061, 0.14479, 0.63368, 0.15174, 0.70081, 0.18323, 0.74987, 0.75776, 0.45672, 0.73822, 0.34669, 0.69558, 0.23666, 0.65117, 0.14864, 0.54991, 0.06219, 0.44864, 0.04175, 0.38454, 0.68107, 0.43903, 0.68966, 0.46142, 0.72664, 0.18128, 0.71112, 0.16749, 0.72534, 0.45596, 0.64772, 0.48656, 0.68206, 0.50084, 0.72335, 0.32683, 0.60017, 0.26637, 0.60083, 0.21114, 0.61998, 0.16785, 0.63253, 0.15933, 0.64648, 0.14827, 0.66725], "triangles": [50, 51, 32, 50, 32, 49, 33, 32, 67, 32, 31, 49, 49, 74, 48, 49, 31, 74, 32, 51, 67, 48, 76, 47, 76, 74, 75, 76, 48, 74, 78, 77, 67, 67, 77, 33, 47, 76, 81, 76, 80, 81, 76, 75, 80, 78, 66, 77, 81, 80, 46, 33, 77, 34, 77, 66, 34, 66, 87, 34, 33, 34, 84, 33, 84, 32, 84, 34, 85, 75, 79, 80, 75, 74, 79, 84, 83, 32, 31, 83, 82, 31, 32, 83, 80, 79, 46, 79, 74, 30, 74, 31, 30, 31, 82, 30, 87, 86, 34, 34, 86, 85, 87, 65, 86, 46, 79, 45, 79, 30, 45, 85, 86, 35, 86, 65, 35, 65, 64, 35, 85, 35, 84, 84, 35, 25, 36, 25, 35, 35, 64, 36, 84, 25, 83, 64, 63, 36, 82, 29, 30, 30, 29, 45, 28, 83, 39, 82, 83, 29, 28, 40, 27, 40, 28, 39, 29, 83, 28, 25, 36, 37, 36, 63, 37, 63, 62, 37, 83, 25, 38, 39, 83, 38, 62, 38, 37, 25, 37, 38, 29, 24, 45, 27, 44, 26, 62, 61, 38, 29, 28, 24, 38, 61, 39, 61, 60, 39, 28, 52, 24, 39, 60, 40, 28, 27, 52, 40, 60, 41, 44, 27, 40, 44, 41, 43, 60, 59, 41, 44, 40, 41, 41, 58, 42, 41, 59, 58, 52, 26, 53, 52, 27, 26, 41, 42, 43, 58, 57, 42, 42, 57, 43, 44, 55, 26, 26, 54, 53, 26, 55, 54, 57, 56, 43, 43, 56, 44, 44, 56, 55, 9, 49, 8, 49, 48, 8, 10, 50, 9, 9, 50, 49, 10, 11, 50, 50, 11, 51, 11, 12, 51, 48, 47, 8, 8, 47, 7, 7, 47, 81, 12, 13, 78, 12, 67, 51, 12, 78, 67, 78, 13, 66, 81, 46, 7, 7, 46, 6, 5, 46, 45, 46, 5, 6, 13, 87, 66, 13, 14, 87, 14, 65, 87, 14, 15, 64, 15, 63, 64, 14, 64, 65, 45, 68, 5, 5, 68, 4, 15, 62, 63, 3, 4, 2, 62, 15, 61, 45, 24, 68, 2, 4, 68, 61, 15, 16, 16, 60, 61, 24, 69, 68, 24, 52, 69, 2, 69, 1, 2, 68, 69, 17, 59, 16, 16, 59, 60, 17, 58, 59, 58, 17, 57, 52, 70, 69, 69, 70, 1, 52, 71, 70, 52, 53, 71, 57, 17, 18, 70, 0, 1, 57, 18, 56, 0, 70, 71, 0, 71, 23, 23, 71, 22, 53, 72, 71, 53, 73, 72, 53, 54, 73, 56, 19, 55, 56, 18, 19, 55, 20, 54, 55, 19, 20, 54, 20, 73, 71, 72, 22, 73, 21, 72, 72, 21, 22, 73, 20, 21], "vertices": [2, 7, 79.29, -81.8, 0.85, 8, 98.44, -180.92, 0.15, 2, 7, 22.54, -91.27, 0.85, 8, 41.69, -190.39, 0.15, 2, 7, -21.63, -85.03, 0.85, 8, -2.48, -184.15, 0.15, 2, 7, -43.48, -73.36, 0.85, 8, -24.33, -172.48, 0.15, 2, 7, -43.97, -55.16, 0.8, 8, -24.82, -154.29, 0.2, 2, 7, -46.56, -36.25, 0.7, 8, -27.41, -135.37, 0.3, 2, 7, -71.92, -33.18, 0.7, 8, -52.77, -132.3, 0.3, 2, 7, -89.58, -17.15, 0.7, 8, -70.43, -116.27, 0.3, 2, 7, -112.35, 25.9, 0.7, 8, -93.2, -73.22, 0.3, 2, 7, -132.31, 73.15, 0.7, 8, -113.16, -25.98, 0.3, 2, 7, -128.08, 103.11, 0.7, 8, -108.93, 3.99, 0.3, 2, 7, -112.79, 111.17, 0.7, 8, -93.64, 12.05, 0.3, 2, 7, -80.14, 105.54, 0.7, 8, -60.99, 6.42, 0.3, 2, 7, -54.4, 110.59, 0.7, 8, -35.25, 11.47, 0.3, 2, 7, -38.98, 107.64, 0.7, 8, -19.83, 8.52, 0.3, 2, 7, -18.26, 118.76, 0.7, 8, 0.89, 19.64, 0.3, 2, 7, 7.67, 114.34, 0.7, 8, 26.82, 15.22, 0.3, 2, 7, 22.07, 125.93, 0.7, 8, 41.22, 26.81, 0.3, 2, 7, 93.54, 115.84, 0.8, 8, 112.69, 16.72, 0.2, 2, 7, 104.63, 85.36, 0.8, 8, 123.78, -13.77, 0.2, 2, 7, 104.23, 53.74, 0.8, 8, 123.38, -45.38, 0.2, 2, 7, 103.61, 5.96, 0.85, 8, 122.76, -93.16, 0.15, 2, 7, 98.34, -31.36, 0.85, 8, 117.49, -130.48, 0.15, 2, 7, 93.08, -68.68, 0.85, 8, 112.22, -167.8, 0.15, 2, 7, -9.88, 9.85, 0.5, 8, 9.27, -89.27, 0.5, 1, 8, -6.09, 4.68, 1, 2, 7, 39.3, 60.72, 0.25, 8, 58.45, -38.4, 0.75, 2, 7, 16.08, 63.95, 0.25, 8, 35.23, -35.18, 0.75, 2, 7, 0.14, 59.95, 0.25, 8, 19.29, -39.17, 0.75, 2, 7, -19.21, 54.49, 0.25, 8, -0.06, -44.63, 0.75, 2, 7, -37.51, 52.03, 0.25, 8, -18.36, -47.09, 0.75, 2, 7, -53.85, 63.47, 0.3, 8, -34.7, -35.65, 0.7, 2, 7, -55.15, 76.57, 0.3, 8, -36, -22.55, 0.7, 2, 7, -55.79, 92.09, 0.3, 8, -36.64, -7.03, 0.7, 2, 7, -48.13, 99.51, 0.3, 8, -28.98, 0.39, 0.7, 2, 7, -35.3, 101.17, 0.28263, 8, -16.15, 2.04, 0.71737, 2, 7, -28.89, 106.52, 0.3, 8, -9.74, 7.4, 0.7, 2, 7, -21.44, 106.66, 0.3, 8, -2.29, 7.54, 0.7, 2, 7, -8.2, 105.15, 0.3, 8, 10.95, 6.03, 0.7, 2, 7, 3.39, 101.7, 0.3, 8, 22.54, 2.58, 0.7, 2, 7, 11.26, 98.23, 0.3, 8, 30.41, -0.89, 0.7, 2, 7, 22.17, 102.14, 0.3, 8, 41.32, 3.02, 0.7, 2, 7, 39.57, 104.41, 0.3, 8, 58.72, 5.29, 0.7, 2, 7, 45.37, 92.68, 0.3, 8, 64.52, -6.44, 0.7, 2, 7, 45.5, 76.86, 0.3, 8, 64.65, -22.26, 0.7, 2, 7, -32.78, 10.54, 0.5, 8, -13.63, -88.58, 0.5, 2, 7, -56.05, 20.91, 0.5, 8, -36.9, -78.21, 0.5, 2, 7, -73.29, 32.07, 0.5, 8, -54.14, -67.05, 0.5, 2, 7, -85.42, 48.51, 0.5, 8, -66.27, -50.61, 0.5, 2, 7, -88.92, 64.27, 0.5, 8, -69.77, -34.85, 0.5, 2, 7, -89.79, 82.55, 0.5, 8, -70.64, -16.57, 0.5, 2, 7, -80.67, 97.75, 0.5, 8, -61.52, -1.38, 0.5, 2, 7, 24.47, 15.02, 0.5, 8, 43.62, -84.1, 0.5, 2, 7, 51.2, 26.47, 0.5, 8, 70.35, -72.65, 0.5, 2, 7, 70.14, 47.39, 0.5, 8, 89.29, -51.73, 0.5, 2, 7, 72.12, 72.23, 0.5, 8, 91.27, -26.9, 0.5, 2, 7, 70.11, 95.72, 0.5, 8, 89.26, -3.4, 0.5, 2, 7, 58.68, 111.8, 0.5, 8, 77.83, 12.68, 0.5, 2, 7, 40.61, 115.87, 0.5, 8, 59.76, 16.75, 0.5, 2, 7, 17.69, 113.77, 0.5, 8, 36.84, 14.65, 0.5, 2, 7, 7.9, 109.63, 0.5, 8, 27.05, 10.51, 0.5, 2, 7, -4.39, 110.07, 0.5, 8, 14.76, 10.95, 0.5, 2, 7, -14.96, 112.43, 0.5, 8, 4.19, 13.31, 0.5, 2, 7, -24.85, 109.07, 0.5, 8, -5.7, 9.95, 0.5, 2, 7, -30.86, 107.01, 0.5, 8, -11.71, 7.89, 0.5, 2, 7, -38.2, 103, 0.5, 8, -19.05, 3.88, 0.5, 2, 7, -54.56, 103.8, 0.5, 8, -35.41, 4.68, 0.5, 2, 7, -67.31, 98.76, 0.5, 8, -48.16, -0.36, 0.5, 2, 7, -14.04, -33.51, 0.67894, 8, 5.11, -132.63, 0.32106, 2, 7, 13.02, -33.08, 0.70014, 8, 32.17, -132.2, 0.29986, 2, 7, 40.78, -27.74, 0.73787, 8, 59.93, -126.86, 0.26213, 2, 7, 63.29, -21.28, 0.77958, 8, 82.44, -120.4, 0.22042, 2, 7, 87.14, -2.66, 0.81553, 8, 106.29, -101.78, 0.18447, 2, 7, 95.1, 18.21, 0.80022, 8, 114.25, -80.92, 0.19978, 2, 7, -56.81, 53.57, 0.34643, 8, -37.66, -45.55, 0.65357, 2, 7, -60.51, 42.26, 0.41186, 8, -41.36, -56.86, 0.58814, 2, 7, -70.08, 38.75, 0.46466, 8, -50.93, -60.37, 0.53534, 2, 7, -57.93, 97.86, 0.41788, 8, -38.78, -1.26, 0.58212, 2, 7, -60.94, 101.28, 0.5, 8, -41.79, 2.16, 0.5, 2, 7, -50.93, 37.23, 0.39121, 8, -31.78, -61.89, 0.60879, 2, 7, -60.11, 31.88, 0.46126, 8, -40.96, -67.24, 0.53874, 2, 7, -70.48, 30.25, 0.5, 8, -51.33, -68.87, 0.5, 2, 7, -35.61, 63.11, 0.27011, 8, -16.46, -36.01, 0.72989, 2, 7, -33.95, 76, 0.23045, 8, -14.8, -23.12, 0.76955, 2, 7, -36.9, 88.41, 0.25985, 8, -17.75, -10.71, 0.74015, 2, 7, -38.61, 98.05, 0.28178, 8, -19.46, -1.07, 0.71822, 2, 7, -41.72, 100.34, 0.29131, 8, -22.57, 1.22, 0.70869, 2, 7, -46.38, 103.4, 0.5, 8, -27.23, 4.28, 0.5], "hull": 24, "edges": [34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 18, 20, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 46, 0, 4, 2, 0, 2, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 52, 48, 90, 90, 92, 94, 96, 96, 98, 98, 100, 100, 102, 48, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 134, 102, 10, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 40, 62, 148, 148, 150, 150, 152, 152, 96, 66, 154, 132, 156, 156, 134, 154, 156, 60, 158, 158, 160, 92, 162, 162, 94, 160, 162, 60, 164, 164, 166, 166, 168, 168, 170, 68, 172, 172, 70, 170, 172, 130, 174, 174, 132, 172, 174], "width": 215, "height": 243}}, "tun": {"tun": {"type": "mesh", "uvs": [0.95174, 0.07053, 1, 0.26567, 1, 0.63284, 1, 1, 0.78047, 1, 0.3695, 1, 0, 1, 0, 0.52459, 0.09582, 0.10071, 0.32467, 0.05035, 0.55352, 0, 0.6964, 0, 0.83928, 0, 0.24935, 0.44045, 0.25936, 0.76763, 0.6289, 0.41996, 0.66171, 0.72386], "triangles": [2, 16, 1, 16, 5, 14, 4, 16, 2, 5, 16, 4, 4, 2, 3, 15, 10, 11, 1, 15, 0, 15, 1, 16, 15, 11, 12, 12, 0, 15, 15, 14, 13, 9, 10, 15, 15, 13, 9, 16, 14, 15, 6, 7, 14, 6, 14, 5, 13, 8, 9, 7, 8, 13, 7, 13, 14], "vertices": [2, 105, 66.52, 209.86, 0.99983, 106, -200.07, 141.61, 0.00017, 2, 105, 136.89, 193.83, 0.97188, 106, -129.17, 155.09, 0.02812, 2, 105, 249.83, 129.5, 0.46626, 106, 0.08, 141.36, 0.53374, 2, 105, 362.77, 65.18, 0.00372, 106, 129.33, 127.63, 0.99628, 1, 106, 119.29, 33.1, 1, 4, 103, 225.52, 144.22, 0.00841, 104, 121.11, 113.47, 0.56187, 105, 227.65, -172.05, 0.10001, 106, 100.49, -143.85, 0.32971, 1, 104, 130.45, -46.25, 1, 2, 103, 124.12, -64.68, 0.82599, 104, -37.56, -56.08, 0.17401, 2, 103, -30.94, -78.52, 0.99918, 105, -107.62, -117.47, 0.00082, 2, 103, -82.43, 8, 0.49248, 105, -74.07, -22.54, 0.50752, 2, 103, -133.91, 94.53, 0.00096, 105, -40.52, 72.39, 0.99904, 1, 105, -9.9, 126.15, 1, 1, 105, 20.72, 179.9, 1, 4, 103, 58.32, 25.95, 0.8834, 104, -73.6, 49.97, 0.02051, 105, 29.78, -119.22, 0.0949, 106, -101.98, -174.66, 0.00119, 4, 103, 165.25, 70.68, 0.12473, 104, 41.78, 61.06, 0.69829, 105, 132.57, -172.78, 0.0971, 106, 13.65, -182.58, 0.07988, 1, 105, 104.82, 27.17, 1, 4, 103, 89.57, 228.36, 0.0009, 104, 16.14, 234.07, 0.00426, 105, 205.33, -13.72, 0.0372, 106, 16.65, -7.71, 0.95764], "hull": 13, "edges": [12, 14, 16, 14, 24, 0, 0, 2, 20, 18, 18, 16, 18, 26, 26, 28, 10, 12, 28, 10, 2, 4, 4, 6, 20, 22, 22, 24, 22, 30, 30, 32, 6, 8, 8, 10, 32, 8], "width": 433, "height": 354}}, "yanbai": {"yanbai": {"type": "mesh", "uvs": [1, 0.5, 0.91967, 0.73341, 0.63127, 0.85781, 0.52532, 0.47424, 0.33679, 0.68521, 0.25556, 1, 0, 1, 0, 0.39495, 0.27911, 0.34312, 0.65952, 0, 1, 0], "triangles": [4, 8, 3, 4, 5, 8, 8, 5, 7, 3, 8, 9, 2, 3, 9, 1, 9, 10, 0, 1, 10, 1, 2, 9, 5, 6, 7], "vertices": [2, 7, 6.69, 24.08, 0.5, 8, 25.84, -75.04, 0.5, 2, 7, 0.55, 32.33, 0.4, 8, 19.7, -66.79, 0.6, 2, 7, 0.4, 58.86, 0.2, 8, 19.55, -40.27, 0.8, 2, 7, 13.52, 66.74, 0.2, 8, 32.67, -32.38, 0.8, 2, 7, 9.45, 84.64, 0.1, 8, 28.6, -14.48, 0.9, 2, 7, 0.82, 93.32, 0.3, 8, 19.97, -5.8, 0.7, 2, 7, 4.07, 116.35, 0.7, 8, 23.22, 17.23, 0.3, 2, 7, 22.64, 113.73, 0.6, 8, 41.79, 14.61, 0.4, 2, 7, 20.68, 88.36, 0.3, 8, 39.83, -10.76, 0.7, 2, 7, 26.37, 52.59, 0.3, 8, 45.52, -46.53, 0.7, 2, 7, 22.04, 21.91, 0.5, 8, 41.19, -77.21, 0.5], "hull": 11, "edges": [18, 20, 18, 6, 6, 4, 4, 2, 0, 20, 2, 0, 12, 14, 14, 16, 16, 8, 10, 12, 8, 10, 6, 8, 16, 18], "width": 91, "height": 31}}, "yiling": {"yiling": {"type": "mesh", "uvs": [0.66261, 0.24264, 1, 0.37595, 1, 0.63224, 1, 1, 0.76323, 1, 0.37751, 0.7977, 0, 0.5, 0, 0.38368, 0.17074, 0.17964, 0.13002, 0, 0.3817, 0], "triangles": [0, 6, 7, 0, 1, 2, 8, 0, 7, 5, 0, 2, 5, 6, 0, 8, 10, 0, 8, 9, 10, 4, 5, 2, 4, 2, 3], "vertices": [2, 51, 15.36, 6.52, 0.9998, 52, -10.33, 9.43, 0.0002, 3, 49, -15.55, 80.22, 0.00687, 48, 4.89, 77.6, 0.00474, 52, 35.03, 8.7, 0.98839, 3, 49, 17.01, 43.32, 0.49774, 48, 41.86, 45.13, 0.16928, 52, 62.13, -32.37, 0.33298, 1, 49, 63.72, -9.62, 1, 1, 49, 44.02, -27.01, 1, 6, 49, -13.79, -26.21, 0.22911, 48, 20.14, -27.75, 0.7552, 47, 74.2, -26.58, 0.0118, 96, 34.11, -90.07, 3e-05, 54, -107.71, -28.46, 0, 46, -13.33, -61.13, 0.00385, 5, 48, -50.46, -21.53, 4e-05, 47, 3.37, -24.31, 0.00058, 96, 4.23, -25.8, 0.02917, 54, -56.27, 20.29, 2e-05, 46, 12.39, 4.91, 0.97018, 5, 48, -67.24, -6.79, 0, 47, -14.21, -10.53, 0.00245, 96, 8.61, -3.91, 0.67684, 54, -34.11, 23.1, 0.00022, 46, 31.23, 16.9, 0.32048, 3, 48, -84.17, 33.3, 0, 96, 34.87, 30.79, 0.02235, 54, 7.13, 9.22, 0.97765, 2, 54, 40.78, 18.03, 0.8945, 50, -19.29, -7.08, 0.1055, 1, 50, -0.89, 13.95, 1], "hull": 11, "edges": [20, 0, 0, 2, 12, 10, 6, 8, 10, 8, 18, 20, 18, 16, 12, 14, 16, 14, 2, 4, 4, 6], "width": 111, "height": 192}}, "yiling2": {"yiling2": {"type": "mesh", "uvs": [1, 0.07623, 1, 0.27324, 0.74938, 0.24534, 0.94951, 0.44697, 0.69263, 0.589, 0.42531, 0.73356, 0.18462, 0.86105, 0.04573, 1, 0, 1, 0, 0.84837, 0.07709, 0.74312, 0.22942, 0.55924, 0.41013, 0.36649, 0.64012, 0.19403, 0.81952, 0, 0.90976, 0, 1, 0], "triangles": [4, 2, 3, 4, 5, 12, 6, 10, 5, 6, 7, 9, 9, 10, 6, 7, 8, 9, 5, 11, 12, 10, 11, 5, 2, 13, 14, 2, 12, 13, 4, 12, 2, 14, 15, 0, 15, 16, 0, 2, 14, 0, 2, 0, 1], "vertices": [1, 101, 22.87, -6.39, 1, 2, 100, -2.77, -11.73, 0.99973, 75, 74.7, -79.95, 0.00027, 4, 100, 1.03, 60.72, 0.07672, 75, 30.01, -22.81, 0.79586, 74, 101.91, -21.1, 0.02997, 67, 62.98, 73.39, 0.09745, 1, 67, 83.36, -13.37, 1, 2, 66, 60.22, -22.84, 0.65821, 67, -4.11, -22.82, 0.34179, 2, 64, 110.32, -37.85, 0.02832, 65, 38.13, -30.51, 0.97168, 1, 64, 30.13, -23.8, 1, 2, 71, -41.52, -17.22, 0.47352, 64, -30.91, -32.14, 0.52648, 2, 71, -47.1, -5.34, 0.48879, 64, -40.68, -23.38, 0.51121, 1, 71, -0.71, 16.44, 1, 1, 71, 40.9, 11.53, 1, 1, 72, 46.79, 8.86, 1, 2, 74, 1.74, 12.41, 0.6679, 73, 65.59, 12.09, 0.3321, 1, 75, 19.8, 11.54, 1, 2, 102, 25.87, 10.42, 0.97512, 75, 102.49, 22.23, 0.02488, 2, 101, 46.35, 21.59, 0.42408, 102, 32.48, -14.62, 0.57592, 2, 101, 48.54, -4.21, 0.9971, 102, 39.1, -39.66, 0.0029], "hull": 17, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 12, 14, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 2, 0, 0, 32, 30, 0], "width": 287, "height": 338}}, "yiling3": {"yiling3": {"type": "mesh", "uvs": [1, 0.10162, 0.85736, 0.2477, 0.77392, 0.3905, 0.74737, 0.48734, 0.79288, 0.61536, 0.89908, 0.73354, 1, 0.79099, 1, 0.92722, 0.80163, 1, 0.60325, 1, 0.34915, 0.90424, 0.10263, 0.76801, 0, 0.6367, 0, 0.5, 0.29605, 0.36904, 0.57291, 0.22624, 0.80805, 0, 1, 0], "triangles": [11, 4, 5, 10, 5, 6, 7, 9, 6, 12, 3, 4, 16, 17, 0, 15, 16, 0, 1, 15, 0, 2, 15, 1, 14, 15, 2, 3, 14, 2, 3, 12, 13, 3, 13, 14, 11, 12, 4, 10, 11, 5, 6, 9, 10, 8, 9, 7], "vertices": [2, 79, 56.51, -17.82, 0.02847, 80, 21.1, -14.15, 0.97153, 3, 78, 53.04, -20.24, 0.00523, 79, 18.83, -15.77, 0.96791, 80, -16.32, -19.13, 0.02686, 3, 78, 18.52, -14.18, 0.97086, 79, -16.06, -19.19, 0.01499, 77, 64.98, -18.9, 0.01415, 2, 78, -4.67, -13.16, 0.09862, 77, 43.07, -11.26, 0.90138, 2, 77, 12.31, -9.26, 0.96425, 76, 57.74, -12.74, 0.03575, 1, 76, 28.03, -7.45, 1, 1, 76, 10.94, -9.21, 1, 2, 68, -27.35, -31.43, 0.22348, 76, -16.75, 7.66, 0.77652, 2, 68, -26.11, -4.67, 0.72536, 76, -20.91, 34.12, 0.27464, 2, 68, -12.19, 10.28, 0.96955, 76, -10.28, 51.57, 0.03045, 1, 68, 22.33, 13.92, 1, 2, 68, 63.36, 10.42, 0.05007, 69, 5.96, 13.63, 0.94993, 1, 69, 38.92, 12.27, 1, 3, 69, 69.29, 0.59, 0.84206, 78, -13.36, 63.39, 0.116, 77, 56.75, 64.56, 0.04194, 3, 69, 87.43, -39.06, 0.19274, 78, 19.97, 35.28, 0.76219, 77, 80.6, 28.05, 0.04507, 3, 69, 108.92, -77.87, 0.00012, 78, 55.97, 9.35, 0.13277, 79, 13.71, 13.53, 0.86711, 1, 80, 31.91, 15.16, 1, 1, 80, 41.94, -1.88, 1], "hull": 18, "edges": [32, 34, 32, 30, 30, 28, 28, 26, 24, 26, 24, 22, 22, 20, 20, 18, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 0, 34, 2, 0, 16, 18, 12, 14, 16, 14], "width": 103, "height": 238}}, "yiling4": {"yiling4": {"type": "mesh", "uvs": [1, 0.47132, 0.83719, 0.4834, 0.56152, 0.58697, 0.33897, 0.75439, 0.14945, 1, 0.08018, 1, 0.05721, 0.7216, 0, 0.53173, 0, 0.4368, 0.09577, 0.32806, 0.33123, 0.16235, 0.68013, 0.02772, 0.86678, 0, 1, 0], "triangles": [12, 13, 0, 1, 11, 12, 7, 8, 9, 3, 6, 7, 3, 7, 9, 4, 5, 6, 1, 12, 0, 2, 10, 11, 2, 11, 1, 3, 10, 2, 9, 10, 3, 3, 4, 6], "vertices": [4, 96, 42.42, -23.5, 0.99489, 98, 69.63, -92.17, 0.00047, 99, 49.83, -87.75, 0.00061, 55, -31, 68.97, 0.00403, 5, 95, 48.54, -18.97, 0.24591, 96, 6.86, -18.64, 0.75352, 98, 34.64, -84.22, 0.00044, 99, 14.2, -83.51, 0.0001, 55, 1.94, 83.2, 4e-05, 2, 94, 42.79, -18.69, 0.9063, 95, -14.91, -22.47, 0.0937, 2, 93, 41.46, -19.67, 0.93403, 94, -14.72, -24, 0.06597, 2, 93, -19.49, -26.3, 0.61242, 56, -30.98, -4.79, 0.38758, 2, 93, -31, -16.31, 0.46594, 56, -29.04, 10.32, 0.53406, 5, 93, -1.44, 25.48, 5e-05, 56, 22.13, 8.87, 0.98088, 57, -11.38, 13.62, 0.01906, 61, -21.25, -41.53, 1e-05, 62, -49.26, -43.3, 0, 2, 57, 25.27, 8.92, 0.99998, 61, -12.01, -5.74, 2e-05, 2, 57, 40.72, 0.98, 0.71431, 61, -2.2, 8.59, 0.28569, 2, 61, 26.42, 13.12, 0.72261, 62, -3.56, 13, 0.27739, 2, 63, 24.4, 14.26, 0.40457, 97, -10.25, 13.03, 0.59543, 2, 98, 24.93, 5.52, 0.56539, 99, -4.88, 4.71, 0.43461, 2, 99, 36.43, 2.36, 0.44006, 55, 25.83, -2.22, 0.55994, 2, 99, 65.26, -2.88, 0.0232, 55, -1.74, -12.17, 0.9768], "hull": 14, "edges": [14, 12, 12, 10, 8, 10, 8, 6, 6, 4, 4, 2, 0, 26, 2, 0, 14, 16, 16, 18, 18, 20, 20, 22, 24, 26, 22, 24], "width": 220, "height": 183}}, "yiling5": {"yiling5": {"type": "mesh", "uvs": [1, 0.45465, 0.10502, 1, 0, 1, 0, 0.90751, 0.45129, 0.19126, 0.77865, 0, 1, 0], "triangles": [1, 3, 4, 1, 2, 3, 4, 0, 1, 4, 5, 0, 5, 6, 0], "vertices": [4, 56, 34.92, -30.48, 0.71196, 57, -12.77, -27.73, 0.07926, 61, 19.61, -48.06, 0.10643, 62, -8.19, -48.39, 0.10234, 1, 60, 26.12, 12.36, 1, 1, 60, 33.04, 5.18, 1, 1, 60, 25.52, -2.07, 1, 2, 57, 37.53, 5.01, 0.03755, 58, 0.99, -6.27, 0.96245, 2, 61, 31.27, 6.21, 0.17846, 62, 1.53, 6.27, 0.82154, 3, 56, 85.88, -37, 0.00586, 57, 32.91, -51.23, 0.00383, 62, 19.3, -4.98, 0.99031], "hull": 7, "edges": [0, 12, 2, 4, 0, 2, 4, 6, 6, 8, 10, 12, 8, 10], "width": 95, "height": 113}}, "youshou": {"youshou": {"type": "mesh", "uvs": [1, 0.10927, 1, 0.31252, 0.88, 0.49972, 0.6229, 0.74041, 0.29429, 1, 0.2091, 1, 0, 0.5853, 0, 0.48546, 0.08587, 0.43911, 0.47685, 0.12532, 0.51489, 0.04865, 0.61834, 0, 0.83893, 0], "triangles": [2, 11, 12, 2, 12, 1, 12, 0, 1, 3, 4, 8, 5, 6, 4, 8, 4, 6, 8, 9, 3, 3, 9, 2, 11, 2, 9, 10, 11, 9, 6, 7, 8], "vertices": [2, 14, -44.33, 23.39, 0.29896, 13, 327.55, 50.04, 0.70104, 1, 13, 383.8, 36.22, 1, 2, 14, 50.75, 93.75, 0.41301, 13, 426.05, -15.43, 0.58699, 2, 14, 160.25, 103.2, 0.95563, 13, 472.17, -115.19, 0.04437, 1, 14, 292.61, 103.87, 1, 1, 14, 316.28, 88.09, 1, 1, 14, 308.83, -48.99, 1, 1, 14, 293.05, -72.67, 1, 1, 14, 261.86, -67.75, 1, 2, 14, 103.59, -69.72, 0.99324, 13, 290.29, -120.73, 0.00676, 2, 14, 80.9, -80.86, 0.97019, 13, 272.11, -103.18, 0.02981, 2, 14, 44.46, -73.23, 0.87865, 13, 266.89, -66.32, 0.12135, 1, 13, 284.47, 5.23, 1], "hull": 13, "edges": [12, 10, 8, 10, 8, 6, 6, 4, 4, 2, 2, 0, 0, 24, 22, 24, 22, 20, 20, 18, 18, 16, 12, 14, 16, 14], "width": 334, "height": 285}}, "youshou2": {"youshou2": {"type": "mesh", "uvs": [0.67414, 0.07675, 1, 0.78989, 1, 0.96316, 0.90669, 1, 0.5, 1, 0.41168, 0.97066, 0.28929, 0.85579, 0.21742, 0.74735, 0, 0.33079, 0, 0.10394, 0.28199, 0, 0.56398, 0], "triangles": [8, 9, 10, 8, 10, 0, 0, 10, 11, 4, 5, 6, 6, 1, 4, 1, 3, 4, 2, 3, 1, 0, 7, 8, 1, 7, 0, 1, 6, 7], "vertices": [2, 13, -3.58, 62.43, 0.65607, 12, 356.32, 44.54, 0.34393, 2, 13, 290.16, 65.41, 0.40438, 14, -71.5, -6.54, 0.59562, 2, 13, 357.3, 48.91, 0.30877, 14, -33.15, 50.98, 0.69123, 2, 13, 366.58, 25.11, 0.6561, 14, -7.6, 51.62, 0.3439, 1, 14, 68.2, 1.09, 1, 2, 13, 328.76, -79.78, 0.05517, 14, 78.16, -19.62, 0.94483, 2, 13, 277.7, -95.46, 0.45879, 14, 75.55, -72.97, 0.54121, 2, 13, 231.84, -100.77, 0.77133, 14, 64.94, -117.9, 0.22867, 2, 13, 58.82, -108.41, 0.80106, 12, 271.5, -116.34, 0.19894, 2, 13, -29.08, -86.81, 0.17951, 12, 228.55, -36.67, 0.82049, 2, 13, -54.28, -15.57, 0.00043, 12, 264.47, 29.81, 0.99957, 2, 13, -39.21, 45.77, 0.28394, 12, 320.07, 59.78, 0.71606], "hull": 12, "edges": [22, 0, 0, 2, 20, 22, 18, 20, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 6, 8, 2, 4, 6, 4], "width": 224, "height": 399}}, "youshou3": {"youshou3": {"type": "mesh", "uvs": [0.82857, 0.11669, 1, 0.40434, 1, 0.61121, 0.83168, 0.69987, 0.48327, 1, 0.26551, 1, 0, 0.32356, 0, 0.0832, 0.22818, 0, 0.59215, 0], "triangles": [0, 8, 9, 1, 8, 0, 4, 5, 3, 5, 6, 3, 1, 3, 8, 6, 8, 3, 2, 3, 1, 6, 7, 8], "vertices": [1, 14, 260.08, -3.64, 1, 2, 15, -14.23, 47.69, 0.48416, 14, 270.47, 41.29, 0.51584, 2, 15, 9.89, 67.21, 0.77237, 14, 287.68, 67.11, 0.22763, 2, 15, 30.29, 63.15, 0.8923, 14, 308.36, 69.31, 0.1077, 1, 15, 86.1, 65.75, 1, 1, 15, 99.12, 49.67, 1, 2, 15, 36.13, -33.77, 0.92704, 14, 342.79, -21.49, 0.07296, 2, 15, 8.1, -56.46, 0.57374, 14, 322.79, -51.49, 0.42626, 2, 15, -15.24, -47.46, 0.31949, 14, 297.83, -49.85, 0.68051, 2, 15, -36.99, -20.59, 0.00618, 14, 269.06, -30.67, 0.99382], "hull": 10, "edges": [12, 10, 8, 10, 8, 6, 6, 4, 4, 2, 2, 0, 0, 18, 16, 18, 12, 14, 16, 14], "width": 95, "height": 150}}, "zuoshou": {"zuoshou": {"type": "mesh", "uvs": [1, 0.08713, 1, 0.25511, 0.56858, 0.64957, 0.51279, 0.75059, 0.46223, 0.74337, 0.41167, 0.73615, 0.33451, 0.81481, 0.33372, 0.95457, 0.33347, 1, 0.09273, 1, 0.09413, 0.94869, 0.09704, 0.84158, 0, 0.76496, 0, 0.62763, 0.05826, 0.53119, 0.22632, 0.47439, 0.61989, 0, 0.84651, 0, 0.33863, 0.46792, 0.43056, 0.5136, 0.47494, 0.61078, 0.13444, 0.96887, 0.22607, 0.97281, 0.30955, 0.96428], "triangles": [17, 1, 16, 17, 0, 1, 16, 1, 19, 2, 19, 1, 19, 18, 16, 8, 9, 22, 9, 21, 22, 22, 23, 8, 8, 23, 7, 9, 10, 21, 23, 22, 11, 11, 22, 21, 6, 7, 23, 11, 21, 10, 6, 23, 11, 6, 11, 13, 5, 6, 15, 6, 13, 14, 14, 15, 6, 5, 15, 18, 13, 11, 12, 3, 4, 2, 4, 20, 2, 4, 5, 20, 5, 19, 20, 5, 18, 19, 2, 20, 19, 18, 15, 16], "vertices": [2, 18, -33.73, 64.29, 0.01363, 17, 324.14, 71.05, 0.98637, 2, 18, 19.26, 108.54, 0.31211, 17, 392.69, 79.29, 0.68789, 3, 19, 6.92, 87.56, 0.47857, 20, -13.55, 129.44, 0.10344, 18, 248.23, 87.29, 0.41799, 3, 19, 51.93, 99.48, 0.59384, 20, 27.97, 108.35, 0.25335, 18, 293.61, 97.71, 0.15282, 3, 19, 62.45, 83.25, 0.57783, 20, 25, 89.24, 0.30817, 18, 303.59, 81.14, 0.114, 3, 19, 72.97, 67.02, 0.47704, 20, 22.03, 70.13, 0.47522, 18, 313.56, 64.57, 0.04774, 3, 19, 116.5, 66.79, 0.02981, 20, 54.36, 40.96, 0.97012, 18, 357.07, 62.9, 8e-05, 3, 19, 159.55, 104.83, 0.00731, 20, 111.8, 40.67, 0.99267, 18, 401.35, 99.49, 2e-05, 1, 20, 130.47, 40.57, 1, 2, 19, 234.15, 49.32, 0.00184, 20, 130.47, -50.43, 0.99816, 2, 19, 218.07, 35.66, 0.11442, 20, 109.39, -49.9, 0.88558, 2, 19, 184.5, 7.17, 0.34942, 20, 65.36, -48.8, 0.65058, 2, 19, 185.44, -41.17, 0.91184, 20, 33.87, -85.48, 0.08816, 1, 19, 143.33, -78.76, 1, 1, 19, 99.1, -88.73, 1, 2, 19, 39.38, -56.9, 0.92086, 18, 275.89, -58.17, 0.07914, 1, 18, 30.88, -68.95, 1, 1, 17, 295.51, 9.17, 1, 2, 19, 9.12, -27, 0.68182, 18, 246.64, -27.29, 0.31818, 3, 19, -0.02, 11.43, 0.51566, 20, -69.44, 77.27, 0.00548, 18, 238.77, 11.42, 0.47886, 3, 19, 18.6, 50.54, 0.62403, 20, -29.49, 94.05, 0.09559, 18, 258.68, 49.9, 0.28037, 2, 19, 214.1, 52.56, 0.06982, 20, 117.68, -34.66, 0.93018, 3, 19, 192.25, 79.47, 0.03937, 20, 119.3, -0.03, 0.96062, 18, 433.19, 73.06, 0, 3, 19, 168.61, 100.67, 0.0136, 20, 115.79, 31.53, 0.98639, 18, 410.27, 95.04, 1e-05], "hull": 18, "edges": [6, 4, 4, 2, 32, 30, 30, 28, 28, 26, 24, 26, 24, 22, 30, 36, 36, 38, 38, 40, 10, 8, 8, 6, 40, 8, 32, 34, 2, 0, 34, 0, 10, 12, 16, 18, 18, 20, 20, 22, 20, 42, 42, 44, 44, 46, 12, 14, 14, 16, 46, 14], "width": 378, "height": 411}}, "zuoshou2": {"zuoshou2": {"type": "mesh", "uvs": [0.94711, 0.15297, 1, 0.15967, 1, 0.31933, 0.77759, 0.87007, 0.68449, 0.97403, 0.5, 1, 0.25, 1, 0.13789, 0.98472, 0, 0.93791, 0, 0.84091, 0.19679, 0.1798, 0.31391, 0.06222, 0.69499, 0, 0.86495, 0], "triangles": [6, 9, 3, 7, 9, 6, 9, 7, 8, 4, 5, 3, 3, 5, 6, 3, 9, 2, 2, 9, 10, 2, 10, 11, 2, 11, 0, 0, 1, 2, 0, 11, 12, 0, 12, 13], "vertices": [2, 16, 151.06, 60.87, 0.6494, 17, -7.07, 86.57, 0.3506, 2, 16, 144.98, 70.61, 0.59118, 17, -5.31, 97.91, 0.40882, 2, 16, 194.74, 125.28, 0.18587, 17, 68.09, 106.74, 0.81413, 1, 17, 326.81, 91.02, 1, 2, 17, 376.92, 77.45, 0.96703, 18, 7.04, 98.41, 0.03297, 2, 17, 393.46, 40.6, 0.79389, 18, 40.98, 76.53, 0.20611, 2, 17, 399.7, -11.27, 0.21798, 18, 74.47, 36.42, 0.78202, 2, 17, 395.47, -35.38, 0.02598, 18, 84.06, 13.9, 0.97402, 1, 18, 85.9, -22.11, 1, 2, 17, 332.8, -71.94, 0.09714, 18, 51.42, -50.9, 0.90286, 2, 16, 275.4, -35.5, 0.00879, 17, 23.99, -67.65, 0.99121, 2, 16, 220.65, -59.28, 0.61428, 17, -32.98, -49.84, 0.38572, 1, 16, 142.36, -26.97, 1, 1, 16, 116.09, -3.06, 1], "hull": 14, "edges": [10, 8, 8, 6, 6, 4, 26, 0, 4, 2, 0, 2, 24, 26, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 10, 12, 14, 12], "width": 209, "height": 463}}}}], "animations": {"idle": {"slots": {"biyan": {"rgba": [{"time": 3.8, "color": "ffffff00"}, {"time": 3.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.1333, "color": "ffffffff"}, {"time": 4.2, "color": "ffffff00"}], "attachment": [{"time": 3.8, "name": "biyan"}, {"time": 4.2}]}}, "bones": {"bone": {"translate": [{"x": -0.36, "y": 4.02, "curve": [0.444, -0.36, 0.889, 5.49, 0.444, 4.02, 0.889, -8.6]}, {"time": 1.3333, "x": 5.49, "y": -8.6, "curve": [1.778, 5.49, 2.222, -0.36, 1.778, -8.6, 2.222, 4.02]}, {"time": 2.6667, "x": -0.36, "y": 4.02, "curve": [3.111, -0.36, 3.556, 5.49, 3.111, 4.02, 3.556, -8.6]}, {"time": 4, "x": 5.49, "y": -8.6, "curve": [4.444, 5.49, 4.889, -0.36, 4.444, -8.6, 4.889, 4.02]}, {"time": 5.3333, "x": -0.36, "y": 4.02}]}, "bone2": {"rotate": [{"value": 3.49, "curve": [0.444, 3.49, 0.889, 0.79]}, {"time": 1.3333, "value": 0.79, "curve": [1.778, 0.79, 2.222, 3.49]}, {"time": 2.6667, "value": 3.49, "curve": [3.111, 3.49, 3.556, 0.79]}, {"time": 4, "value": 0.79, "curve": [4.444, 0.79, 4.889, 3.49]}, {"time": 5.3333, "value": 3.49}], "translate": [{"x": 0.02, "y": 3.35}], "scale": [{"curve": [0.444, 1, 0.889, 0.976, 0.444, 1, 0.889, 0.99]}, {"time": 1.3333, "x": 0.976, "y": 0.99, "curve": [1.778, 0.976, 2.222, 1, 1.778, 0.99, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 0.976, 3.111, 1, 3.556, 0.99]}, {"time": 4, "x": 0.976, "y": 0.99, "curve": [4.444, 0.976, 4.889, 1, 4.444, 0.99, 4.889, 1]}, {"time": 5.3333}]}, "bone3": {"rotate": [{"value": -0.74, "curve": [0.444, -0.74, 0.889, -0.74]}, {"time": 1.3333, "value": -0.74, "curve": [1.778, -0.74, 2.222, -0.74]}, {"time": 2.6667, "value": -0.74, "curve": [3.111, -0.74, 3.556, -0.74]}, {"time": 4, "value": -0.74, "curve": [4.444, -0.74, 4.889, -0.74]}, {"time": 5.3333, "value": -0.74}], "translate": [{"x": 7.91, "y": 0.09, "curve": [0.444, 7.91, 0.889, 2.56, 0.444, 0.09, 0.889, -0.06]}, {"time": 1.3333, "x": 2.56, "y": -0.06, "curve": [1.778, 2.56, 2.222, 7.91, 1.778, -0.06, 2.222, 0.09]}, {"time": 2.6667, "x": 7.91, "y": 0.09, "curve": [3.111, 7.91, 3.556, 2.56, 3.111, 0.09, 3.556, -0.06]}, {"time": 4, "x": 2.56, "y": -0.06, "curve": [4.444, 2.56, 4.889, 7.91, 4.444, -0.06, 4.889, 0.09]}, {"time": 5.3333, "x": 7.91, "y": 0.09}], "scale": [{"curve": [0.444, 1, 0.889, 0.962, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 0.962, "curve": [1.778, 0.962, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 0.962, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 0.962, "curve": [4.444, 0.962, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone4": {"rotate": [{"value": -11.83, "curve": [0.444, -11.83, 0.889, -2.73]}, {"time": 1.3333, "value": -2.73, "curve": [1.778, -2.73, 2.222, -11.83]}, {"time": 2.6667, "value": -11.83, "curve": [3.111, -11.83, 3.556, -2.73]}, {"time": 4, "value": -2.73, "curve": [4.444, -2.73, 4.889, -11.83]}, {"time": 5.3333, "value": -11.83}], "translate": [{"x": 4.27, "y": -0.01}], "scale": [{"curve": [0.444, 1, 0.889, 0.98, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 0.98, "curve": [1.778, 0.98, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 0.98, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 0.98, "curve": [4.444, 0.98, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone5": {"rotate": [{"value": 2.2, "curve": [0.444, 2.2, 0.889, -5.22]}, {"time": 1.3333, "value": -5.22, "curve": [1.778, -5.22, 2.222, 2.2]}, {"time": 2.6667, "value": 2.2, "curve": [3.111, 2.2, 3.556, -5.22]}, {"time": 4, "value": -5.22, "curve": [4.444, -5.22, 4.889, 2.2]}, {"time": 5.3333, "value": 2.2}]}, "bone6": {"rotate": [{"value": -0.03}]}, "bone11": {"rotate": [{"value": 7.18, "curve": [0.444, 7.18, 0.889, 5.84]}, {"time": 1.3333, "value": 5.84, "curve": [1.778, 5.84, 2.222, 7.18]}, {"time": 2.6667, "value": 7.18, "curve": [3.111, 7.18, 3.556, 5.84]}, {"time": 4, "value": 5.84, "curve": [4.444, 5.84, 4.889, 7.18]}, {"time": 5.3333, "value": 7.18}]}, "bone12": {"rotate": [{"value": 3.36, "curve": [0.444, 3.36, 0.889, -2.7]}, {"time": 1.3333, "value": -2.7, "curve": [1.778, -2.7, 2.222, 3.36]}, {"time": 2.6667, "value": 3.36, "curve": [3.111, 3.36, 3.556, -2.7]}, {"time": 4, "value": -2.7, "curve": [4.444, -2.7, 4.889, 3.36]}, {"time": 5.3333, "value": 3.36}]}, "bone13": {"rotate": [{"value": -5.07, "curve": [0.444, -5.07, 0.889, -0.13]}, {"time": 1.3333, "value": -0.13, "curve": [1.778, -0.13, 2.222, -5.07]}, {"time": 2.6667, "value": -5.07, "curve": [3.111, -5.07, 3.556, -0.13]}, {"time": 4, "value": -0.13, "curve": [4.444, -0.13, 4.889, -5.07]}, {"time": 5.3333, "value": -5.07}]}, "bone14": {"rotate": [{"value": 9.55, "curve": [0.444, 9.55, 0.889, -5.31]}, {"time": 1.3333, "value": -5.31, "curve": [1.778, -5.31, 2.222, 9.55]}, {"time": 2.6667, "value": 9.55, "curve": [3.111, 9.55, 3.556, -5.31]}, {"time": 4, "value": -5.31, "curve": [4.444, -5.31, 4.889, 9.55]}, {"time": 5.3333, "value": 9.55}]}, "bone15": {"rotate": [{"value": 2.8, "curve": [0.444, 2.8, 0.889, -2.89]}, {"time": 1.3333, "value": -2.89, "curve": [1.778, -2.89, 2.222, 2.8]}, {"time": 2.6667, "value": 2.8, "curve": [3.111, 2.8, 3.556, -2.89]}, {"time": 4, "value": -2.89, "curve": [4.444, -2.89, 4.889, 2.8]}, {"time": 5.3333, "value": 2.8}]}, "bone16": {"rotate": [{"value": 2.99, "curve": [0.444, 2.99, 0.889, 2.57]}, {"time": 1.3333, "value": 2.57, "curve": [1.778, 2.57, 2.222, 2.99]}, {"time": 2.6667, "value": 2.99, "curve": [3.111, 2.99, 3.556, 2.57]}, {"time": 4, "value": 2.57, "curve": [4.444, 2.57, 4.889, 2.99]}, {"time": 5.3333, "value": 2.99}]}, "bone17": {"rotate": [{"value": 8.12, "curve": [0.444, 8.12, 0.889, 5.47]}, {"time": 1.3333, "value": 5.47, "curve": [1.778, 5.47, 2.222, 8.12]}, {"time": 2.6667, "value": 8.12, "curve": [3.111, 8.12, 3.556, 5.47]}, {"time": 4, "value": 5.47, "curve": [4.444, 5.47, 4.889, 8.12]}, {"time": 5.3333, "value": 8.12}]}, "bone18": {"rotate": [{"value": -0.78, "curve": [0.444, -0.78, 0.889, 2.37]}, {"time": 1.3333, "value": 2.37, "curve": [1.778, 2.37, 2.222, -0.78]}, {"time": 2.6667, "value": -0.78, "curve": [3.111, -0.78, 3.556, 2.37]}, {"time": 4, "value": 2.37, "curve": [4.444, 2.37, 4.889, -0.78]}, {"time": 5.3333, "value": -0.78}]}, "bone19": {"rotate": [{"value": -5.29, "curve": [0.444, -5.29, 0.889, 1.42]}, {"time": 1.3333, "value": 1.42, "curve": [1.778, 1.42, 2.222, -5.29]}, {"time": 2.6667, "value": -5.29, "curve": [3.111, -5.29, 3.556, 1.42]}, {"time": 4, "value": 1.42, "curve": [4.444, 1.42, 4.889, -5.29]}, {"time": 5.3333, "value": -5.29}]}, "bone27": {"rotate": [{"value": 2.07}]}, "bone34": {"rotate": [{"value": 0.29, "curve": [0.057, 0.12, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, 6.12]}, {"time": 1.5, "value": 6.12, "curve": [1.89, 6.12, 2.278, 1.4]}, {"time": 2.6667, "value": 0.29, "curve": [2.724, 0.12, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, 6.12]}, {"time": 4.1667, "value": 6.12, "curve": [4.556, 6.12, 4.946, 1.45]}, {"time": 5.3333, "value": 0.29}]}, "bone35": {"rotate": [{"value": 0.98, "curve": [0.114, 0.42, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, 6.12]}, {"time": 1.6667, "value": 6.12, "curve": [2.001, 6.12, 2.333, 2.62]}, {"time": 2.6667, "value": 0.98, "curve": [2.781, 0.42, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, 6.12]}, {"time": 4.3333, "value": 6.12, "curve": [4.668, 6.12, 5.003, 2.69]}, {"time": 5.3333, "value": 0.98}]}, "bone36": {"rotate": [{"value": -3.63, "curve": [0.444, -3.63, 0.889, 2.49]}, {"time": 1.3333, "value": 2.49, "curve": [1.778, 2.49, 2.222, -3.63]}, {"time": 2.6667, "value": -3.63, "curve": [3.111, -3.63, 3.556, 2.49]}, {"time": 4, "value": 2.49, "curve": [4.444, 2.49, 4.889, -3.63]}, {"time": 5.3333, "value": -3.63}]}, "bone37": {"rotate": [{"value": 0.29, "curve": [0.057, 0.12, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, 6.12]}, {"time": 1.5, "value": 6.12, "curve": [1.89, 6.12, 2.278, 1.4]}, {"time": 2.6667, "value": 0.29, "curve": [2.724, 0.12, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, 6.12]}, {"time": 4.1667, "value": 6.12, "curve": [4.556, 6.12, 4.946, 1.45]}, {"time": 5.3333, "value": 0.29}]}, "bone38": {"rotate": [{"value": 0.29, "curve": [0.057, 0.12, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, 6.12]}, {"time": 1.5, "value": 6.12, "curve": [1.89, 6.12, 2.278, 1.4]}, {"time": 2.6667, "value": 0.29, "curve": [2.724, 0.12, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, 6.12]}, {"time": 4.1667, "value": 6.12, "curve": [4.556, 6.12, 4.946, 1.45]}, {"time": 5.3333, "value": 0.29}]}, "bone39": {"rotate": [{"value": 0.98, "curve": [0.114, 0.42, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, 6.12]}, {"time": 1.6667, "value": 6.12, "curve": [2.001, 6.12, 2.333, 2.62]}, {"time": 2.6667, "value": 0.98, "curve": [2.781, 0.42, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, 6.12]}, {"time": 4.3333, "value": 6.12, "curve": [4.668, 6.12, 5.003, 2.69]}, {"time": 5.3333, "value": 0.98}]}, "bone40": {"rotate": [{"curve": [0.444, 0, 0.889, 6.12]}, {"time": 1.3333, "value": 6.12, "curve": [1.778, 6.12, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 6.12]}, {"time": 4, "value": 6.12, "curve": [4.444, 6.12, 4.889, 0]}, {"time": 5.3333}]}, "bone41": {"rotate": [{"value": 0.17, "curve": [0.044, 0.06, 0.089, 0]}, {"time": 0.1333, "curve": [0.578, 0, 1.022, 6.12]}, {"time": 1.4667, "value": 6.12, "curve": [1.867, 6.12, 2.267, 1.16]}, {"time": 2.6667, "value": 0.17, "curve": [2.711, 0.06, 2.756, 0]}, {"time": 2.8, "curve": [3.244, 0, 3.689, 6.12]}, {"time": 4.1333, "value": 6.12, "curve": [4.533, 6.12, 4.933, 1.16]}, {"time": 5.3333, "value": 0.17}]}, "bone42": {"rotate": [{"value": 0.29, "curve": [0.057, 0.12, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, 6.12]}, {"time": 1.5, "value": 6.12, "curve": [1.89, 6.12, 2.278, 1.4]}, {"time": 2.6667, "value": 0.29, "curve": [2.724, 0.12, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, 6.12]}, {"time": 4.1667, "value": 6.12, "curve": [4.556, 6.12, 4.946, 1.45]}, {"time": 5.3333, "value": 0.29}]}, "bone43": {"rotate": [{"value": 0.98, "curve": [0.114, 0.42, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, 6.12]}, {"time": 1.6667, "value": 6.12, "curve": [2.001, 6.12, 2.333, 2.62]}, {"time": 2.6667, "value": 0.98, "curve": [2.781, 0.42, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, 6.12]}, {"time": 4.3333, "value": 6.12, "curve": [4.668, 6.12, 5.003, 2.69]}, {"time": 5.3333, "value": 0.98}]}, "bone44": {"rotate": [{"curve": [0.444, 0, 0.889, -1.92]}, {"time": 1.3333, "value": -1.92, "curve": [1.778, -1.92, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -1.92]}, {"time": 4, "value": -1.92, "curve": [4.444, -1.92, 4.889, 0]}, {"time": 5.3333}]}, "bone46": {"rotate": [{"value": -2.93, "curve": [0.444, -2.93, 0.889, 1.96]}, {"time": 1.3333, "value": 1.96, "curve": [1.778, 1.96, 2.222, -2.93]}, {"time": 2.6667, "value": -2.93, "curve": [3.111, -2.93, 3.556, 1.96]}, {"time": 4, "value": 1.96, "curve": [4.444, 1.96, 4.889, -2.93]}, {"time": 5.3333, "value": -2.93}]}, "bone47": {"rotate": [{"value": 0.23, "curve": [0.057, 0.1, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, 4.89]}, {"time": 1.5, "value": 4.89, "curve": [1.89, 4.89, 2.278, 1.12]}, {"time": 2.6667, "value": 0.23, "curve": [2.724, 0.1, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, 4.89]}, {"time": 4.1667, "value": 4.89, "curve": [4.556, 4.89, 4.946, 1.16]}, {"time": 5.3333, "value": 0.23}]}, "bone48": {"rotate": [{"value": 0.78, "curve": [0.114, 0.33, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, 4.89]}, {"time": 1.6667, "value": 4.89, "curve": [2.001, 4.89, 2.333, 2.09]}, {"time": 2.6667, "value": 0.78, "curve": [2.781, 0.33, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, 4.89]}, {"time": 4.3333, "value": 4.89, "curve": [4.668, 4.89, 5.003, 2.15]}, {"time": 5.3333, "value": 0.78}]}, "bone49": {"rotate": [{"value": 0.08, "curve": [0.057, 0.03, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, 1.66]}, {"time": 1.5, "value": 1.66, "curve": [1.89, 1.66, 2.278, 0.38]}, {"time": 2.6667, "value": 0.08, "curve": [2.724, 0.03, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, 1.66]}, {"time": 4.1667, "value": 1.66, "curve": [4.556, 1.66, 4.946, 0.39]}, {"time": 5.3333, "value": 0.08}]}, "bone50": {"rotate": [{"value": 0.26, "curve": [0.114, 0.11, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, 1.66]}, {"time": 1.6667, "value": 1.66, "curve": [2.001, 1.66, 2.333, 0.71]}, {"time": 2.6667, "value": 0.26, "curve": [2.781, 0.11, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, 1.66]}, {"time": 4.3333, "value": 1.66, "curve": [4.668, 1.66, 5.003, 0.73]}, {"time": 5.3333, "value": 0.26}]}, "bone51": {"rotate": [{"value": 0.53, "curve": [0.168, 0.24, 0.334, 0]}, {"time": 0.5, "curve": [0.944, 0, 1.389, 1.66]}, {"time": 1.8333, "value": 1.66, "curve": [2.112, 1.66, 2.389, 1]}, {"time": 2.6667, "value": 0.53, "curve": [2.835, 0.24, 3.001, 0]}, {"time": 3.1667, "curve": [3.611, 0, 4.056, 1.66]}, {"time": 4.5, "value": 1.66, "curve": [4.779, 1.66, 5.057, 1.01]}, {"time": 5.3333, "value": 0.53}]}, "bone54": {"rotate": [{"value": 10.21}]}, "bone57": {"rotate": [{"value": 1.7, "curve": [0.444, 1.7, 0.889, -1.43]}, {"time": 1.3333, "value": -1.43, "curve": [1.778, -1.43, 2.222, 1.7]}, {"time": 2.6667, "value": 1.7, "curve": [3.111, 1.7, 3.556, -1.43]}, {"time": 4, "value": -1.43, "curve": [4.444, -1.43, 4.889, 1.7]}, {"time": 5.3333, "value": 1.7}]}, "bone58": {"rotate": [{"value": -0.15, "curve": [0.057, -0.06, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -3.13]}, {"time": 1.5, "value": -3.13, "curve": [1.89, -3.13, 2.278, -0.72]}, {"time": 2.6667, "value": -0.15, "curve": [2.724, -0.06, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -3.13]}, {"time": 4.1667, "value": -3.13, "curve": [4.556, -3.13, 4.946, -0.74]}, {"time": 5.3333, "value": -0.15}]}, "bone59": {"rotate": [{"value": -0.5, "curve": [0.114, -0.21, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, -3.13]}, {"time": 1.6667, "value": -3.13, "curve": [2.001, -3.13, 2.333, -1.34]}, {"time": 2.6667, "value": -0.5, "curve": [2.781, -0.21, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, -3.13]}, {"time": 4.3333, "value": -3.13, "curve": [4.668, -3.13, 5.003, -1.38]}, {"time": 5.3333, "value": -0.5}]}, "bone60": {"rotate": [{"value": -0.27}]}, "bone61": {"rotate": [{"value": -0.27}]}, "bone62": {"rotate": [{"value": -0.27, "curve": [0.444, -0.27, 0.889, 2.09]}, {"time": 1.3333, "value": 2.09, "curve": [1.778, 2.09, 2.222, -0.27]}, {"time": 2.6667, "value": -0.27, "curve": [3.111, -0.27, 3.556, 2.09]}, {"time": 4, "value": 2.09, "curve": [4.444, 2.09, 4.889, -0.27]}, {"time": 5.3333, "value": -0.27}]}, "bone63": {"rotate": [{"value": -3.06, "curve": [0.444, -3.06, 0.889, 0.07]}, {"time": 1.3333, "value": 0.07, "curve": [1.778, 0.07, 2.222, -3.06]}, {"time": 2.6667, "value": -3.06, "curve": [3.111, -3.06, 3.556, 0.07]}, {"time": 4, "value": 0.07, "curve": [4.444, 0.07, 4.889, -3.06]}, {"time": 5.3333, "value": -3.06}], "scale": [{"x": 1.014, "y": 1.014}]}, "bone64": {"rotate": [{"value": -2.6, "curve": [0.444, -2.6, 0.889, 0.53]}, {"time": 1.3333, "value": 0.53, "curve": [1.778, 0.53, 2.222, -2.6]}, {"time": 2.6667, "value": -2.6, "curve": [3.111, -2.6, 3.556, 0.53]}, {"time": 4, "value": 0.53, "curve": [4.444, 0.53, 4.889, -2.6]}, {"time": 5.3333, "value": -2.6}], "scale": [{"x": 1.014, "y": 1.014}]}, "bone65": {"rotate": [{"value": -2.6, "curve": [0.444, -2.6, 0.889, 0.53]}, {"time": 1.3333, "value": 0.53, "curve": [1.778, 0.53, 2.222, -2.6]}, {"time": 2.6667, "value": -2.6, "curve": [3.111, -2.6, 3.556, 0.53]}, {"time": 4, "value": 0.53, "curve": [4.444, 0.53, 4.889, -2.6]}, {"time": 5.3333, "value": -2.6}], "scale": [{"x": 1.014, "y": 1.014}]}, "bone66": {"rotate": [{"value": -2.6, "curve": [0.444, -2.6, 0.889, 0.53]}, {"time": 1.3333, "value": 0.53, "curve": [1.778, 0.53, 2.222, -2.6]}, {"time": 2.6667, "value": -2.6, "curve": [3.111, -2.6, 3.556, 0.53]}, {"time": 4, "value": 0.53, "curve": [4.444, 0.53, 4.889, -2.6]}, {"time": 5.3333, "value": -2.6}], "scale": [{"x": 1.014, "y": 1.014}]}, "bone67": {"rotate": [{"value": -1.74}], "scale": [{"curve": [0.444, 1, 0.889, 1.076, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 1.076, "curve": [1.778, 1.076, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 1.076, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 1.076, "curve": [4.444, 1.076, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone68": {"scale": [{"curve": [0.444, 1, 0.889, 1.076, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 1.076, "curve": [1.778, 1.076, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 1.076, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 1.076, "curve": [4.444, 1.076, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone69": {"scale": [{"curve": [0.444, 1, 0.889, 1.076, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 1.076, "curve": [1.778, 1.076, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 1.076, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 1.076, "curve": [4.444, 1.076, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone70": {"rotate": [{"value": -3.4, "curve": [0.444, -3.4, 0.889, -0.53]}, {"time": 1.3333, "value": -0.53, "curve": [1.778, -0.53, 2.222, -3.4]}, {"time": 2.6667, "value": -3.4, "curve": [3.111, -3.4, 3.556, -0.53]}, {"time": 4, "value": -0.53, "curve": [4.444, -0.53, 4.889, -3.4]}, {"time": 5.3333, "value": -3.4}], "scale": [{"x": 1.025}]}, "bone71": {"rotate": [{"value": -3.45, "curve": [0.444, -3.45, 0.889, -0.58]}, {"time": 1.3333, "value": -0.58, "curve": [1.778, -0.58, 2.222, -3.45]}, {"time": 2.6667, "value": -3.45, "curve": [3.111, -3.45, 3.556, -0.58]}, {"time": 4, "value": -0.58, "curve": [4.444, -0.58, 4.889, -3.45]}, {"time": 5.3333, "value": -3.45}], "scale": [{"x": 1.025}]}, "bone72": {"rotate": [{"value": -2.88, "curve": [0.444, -2.88, 0.889, -0.01]}, {"time": 1.3333, "value": -0.01, "curve": [1.778, -0.01, 2.222, -2.88]}, {"time": 2.6667, "value": -2.88, "curve": [3.111, -2.88, 3.556, -0.01]}, {"time": 4, "value": -0.01, "curve": [4.444, -0.01, 4.889, -2.88]}, {"time": 5.3333, "value": -2.88}], "scale": [{"x": 1.025}]}, "bone73": {"rotate": [{"value": -2.88, "curve": [0.444, -2.88, 0.889, -0.01]}, {"time": 1.3333, "value": -0.01, "curve": [1.778, -0.01, 2.222, -2.88]}, {"time": 2.6667, "value": -2.88, "curve": [3.111, -2.88, 3.556, -0.01]}, {"time": 4, "value": -0.01, "curve": [4.444, -0.01, 4.889, -2.88]}, {"time": 5.3333, "value": -2.88}], "scale": [{"x": 1.025}]}, "bone74": {"rotate": [{"value": -2.88, "curve": [0.444, -2.88, 0.889, -0.01]}, {"time": 1.3333, "value": -0.01, "curve": [1.778, -0.01, 2.222, -2.88]}, {"time": 2.6667, "value": -2.88, "curve": [3.111, -2.88, 3.556, -0.01]}, {"time": 4, "value": -0.01, "curve": [4.444, -0.01, 4.889, -2.88]}, {"time": 5.3333, "value": -2.88}], "scale": [{"x": 1.025}]}, "bone75": {"rotate": [{"curve": [0.444, 0, 0.889, -1.01]}, {"time": 1.3333, "value": -1.01, "curve": [1.778, -1.01, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -1.01]}, {"time": 4, "value": -1.01, "curve": [4.444, -1.01, 4.889, 0]}, {"time": 5.3333}], "scale": [{"curve": [0.444, 1, 0.889, 1.034, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 1.034, "curve": [1.778, 1.034, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 1.034, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 1.034, "curve": [4.444, 1.034, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone76": {"rotate": [{"curve": [0.444, 0, 0.889, -1.01]}, {"time": 1.3333, "value": -1.01, "curve": [1.778, -1.01, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -1.01]}, {"time": 4, "value": -1.01, "curve": [4.444, -1.01, 4.889, 0]}, {"time": 5.3333}], "scale": [{"curve": [0.444, 1, 0.889, 1.034, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 1.034, "curve": [1.778, 1.034, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 1.034, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 1.034, "curve": [4.444, 1.034, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone77": {"rotate": [{"curve": [0.444, 0, 0.889, -1.01]}, {"time": 1.3333, "value": -1.01, "curve": [1.778, -1.01, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -1.01]}, {"time": 4, "value": -1.01, "curve": [4.444, -1.01, 4.889, 0]}, {"time": 5.3333}], "scale": [{"curve": [0.444, 1, 0.889, 1.034, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 1.034, "curve": [1.778, 1.034, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 1.034, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 1.034, "curve": [4.444, 1.034, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone78": {"rotate": [{"curve": [0.444, 0, 0.889, 3.47]}, {"time": 1.3333, "value": 3.47, "curve": [1.778, 3.47, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 3.47]}, {"time": 4, "value": 3.47, "curve": [4.444, 3.47, 4.889, 0]}, {"time": 5.3333}], "scale": [{"curve": [0.444, 1, 0.889, 1.034, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 1.034, "curve": [1.778, 1.034, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 1.034, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 1.034, "curve": [4.444, 1.034, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone79": {"rotate": [{"curve": [0.444, 0, 0.889, -2.27]}, {"time": 1.3333, "value": -2.27, "curve": [1.778, -2.27, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -2.27]}, {"time": 4, "value": -2.27, "curve": [4.444, -2.27, 4.889, 0]}, {"time": 5.3333}], "scale": [{"curve": [0.444, 1, 0.889, 1.034, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 1.034, "curve": [1.778, 1.034, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 1.034, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 1.034, "curve": [4.444, 1.034, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone80": {"rotate": [{"value": 0.91}], "translate": [{"curve": [0.444, 0, 0.889, -3.9, 0.444, 0, 0.889, 2.91]}, {"time": 1.3333, "x": -3.9, "y": 2.91, "curve": [1.778, -3.9, 2.222, 0, 1.778, 2.91, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -3.9, 3.111, 0, 3.556, 2.91]}, {"time": 4, "x": -3.9, "y": 2.91, "curve": [4.444, -3.9, 4.889, 0, 4.444, 2.91, 4.889, 0]}, {"time": 5.3333}]}, "bone81": {"rotate": [{"value": 0.91}]}, "bone82": {"rotate": [{"value": 0.91}]}, "bone83": {"rotate": [{"value": 0.91}]}, "bone84": {"rotate": [{"value": 0.91}]}, "bone85": {"rotate": [{"curve": [0.444, 0, 0.889, -1.37]}, {"time": 1.3333, "value": -1.37, "curve": [1.778, -1.37, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -1.37]}, {"time": 4, "value": -1.37, "curve": [4.444, -1.37, 4.889, 0]}, {"time": 5.3333}]}, "bone86": {"rotate": [{"value": -0.19, "curve": [0.057, -0.08, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -4.15]}, {"time": 1.5, "value": -4.15, "curve": [1.89, -4.15, 2.278, -0.95]}, {"time": 2.6667, "value": -0.19, "curve": [2.724, -0.08, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -4.15]}, {"time": 4.1667, "value": -4.15, "curve": [4.556, -4.15, 4.946, -0.98]}, {"time": 5.3333, "value": -0.19}]}, "bone87": {"rotate": [{"value": -0.66, "curve": [0.114, -0.28, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, -4.15]}, {"time": 1.6667, "value": -4.15, "curve": [2.001, -4.15, 2.333, -1.77]}, {"time": 2.6667, "value": -0.66, "curve": [2.781, -0.28, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, -4.15]}, {"time": 4.3333, "value": -4.15, "curve": [4.668, -4.15, 5.003, -1.82]}, {"time": 5.3333, "value": -0.66}]}, "bone88": {"rotate": [{"value": 7.03, "curve": [0.444, 7.03, 0.889, 2.89]}, {"time": 1.3333, "value": 2.89, "curve": [1.778, 2.89, 2.222, 7.03]}, {"time": 2.6667, "value": 7.03, "curve": [3.111, 7.03, 3.556, 2.89]}, {"time": 4, "value": 2.89, "curve": [4.444, 2.89, 4.889, 7.03]}, {"time": 5.3333, "value": 7.03}]}, "bone89": {"rotate": [{"value": -0.19, "curve": [0.057, -0.08, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -4.15]}, {"time": 1.5, "value": -4.15, "curve": [1.89, -4.15, 2.278, -0.95]}, {"time": 2.6667, "value": -0.19, "curve": [2.724, -0.08, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -4.15]}, {"time": 4.1667, "value": -4.15, "curve": [4.556, -4.15, 4.946, -0.98]}, {"time": 5.3333, "value": -0.19}]}, "bone90": {"rotate": [{"value": -0.19, "curve": [0.057, -0.08, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -4.15]}, {"time": 1.5, "value": -4.15, "curve": [1.89, -4.15, 2.278, -0.95]}, {"time": 2.6667, "value": -0.19, "curve": [2.724, -0.08, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -4.15]}, {"time": 4.1667, "value": -4.15, "curve": [4.556, -4.15, 4.946, -0.98]}, {"time": 5.3333, "value": -0.19}]}, "bone91": {"rotate": [{"value": -0.66, "curve": [0.114, -0.28, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, -4.15]}, {"time": 1.6667, "value": -4.15, "curve": [2.001, -4.15, 2.333, -1.77]}, {"time": 2.6667, "value": -0.66, "curve": [2.781, -0.28, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, -4.15]}, {"time": 4.3333, "value": -4.15, "curve": [4.668, -4.15, 5.003, -1.82]}, {"time": 5.3333, "value": -0.66}]}, "bone96": {"rotate": [{"value": -0.27, "curve": [0.444, -0.27, 0.889, 2.09]}, {"time": 1.3333, "value": 2.09, "curve": [1.778, 2.09, 2.222, -0.27]}, {"time": 2.6667, "value": -0.27, "curve": [3.111, -0.27, 3.556, 2.09]}, {"time": 4, "value": 2.09, "curve": [4.444, 2.09, 4.889, -0.27]}, {"time": 5.3333, "value": -0.27}]}, "bone97": {"rotate": [{"value": 6.47, "curve": [0.444, 6.47, 0.889, 2.09]}, {"time": 1.3333, "value": 2.09, "curve": [1.778, 2.09, 2.222, 6.47]}, {"time": 2.6667, "value": 6.47, "curve": [3.111, 6.47, 3.556, 2.09]}, {"time": 4, "value": 2.09, "curve": [4.444, 2.09, 4.889, 6.47]}, {"time": 5.3333, "value": 6.47}]}, "bone98": {"rotate": [{"value": -0.27, "curve": [0.444, -0.27, 0.889, 2.09]}, {"time": 1.3333, "value": 2.09, "curve": [1.778, 2.09, 2.222, -0.27]}, {"time": 2.6667, "value": -0.27, "curve": [3.111, -0.27, 3.556, 2.09]}, {"time": 4, "value": 2.09, "curve": [4.444, 2.09, 4.889, -0.27]}, {"time": 5.3333, "value": -0.27}]}, "bone101": {"rotate": [{"value": -2.88, "curve": [0.444, -2.88, 0.889, -0.01]}, {"time": 1.3333, "value": -0.01, "curve": [1.778, -0.01, 2.222, -2.88]}, {"time": 2.6667, "value": -2.88, "curve": [3.111, -2.88, 3.556, -0.01]}, {"time": 4, "value": -0.01, "curve": [4.444, -0.01, 4.889, -2.88]}, {"time": 5.3333, "value": -2.88}], "scale": [{"x": 1.025}]}, "bone102": {"rotate": [{"value": 1.71}]}, "bone103": {"rotate": [{"value": -1.88, "curve": [0.444, -1.88, 0.889, -6.55]}, {"time": 1.3333, "value": -6.55, "curve": [1.778, -6.55, 2.222, -1.88]}, {"time": 2.6667, "value": -1.88, "curve": [3.111, -1.88, 3.556, -6.55]}, {"time": 4, "value": -6.55, "curve": [4.444, -6.55, 4.889, -1.88]}, {"time": 5.3333, "value": -1.88}]}, "bone104": {"rotate": [{"value": 0.54}]}, "bone105": {"rotate": [{"value": 6.12, "curve": [0.444, 6.12, 0.889, -3.86]}, {"time": 1.3333, "value": -3.86, "curve": [1.778, -3.86, 2.222, 6.12]}, {"time": 2.6667, "value": 6.12, "curve": [3.111, 6.12, 3.556, -3.86]}, {"time": 4, "value": -3.86, "curve": [4.444, -3.86, 4.889, 6.12]}, {"time": 5.3333, "value": 6.12}]}, "bone106": {"rotate": [{"value": 1.12}]}, "bone107": {"rotate": [{"value": 2.11, "curve": [0.444, 2.11, 0.889, 1.62]}, {"time": 1.3333, "value": 1.62, "curve": [1.778, 1.62, 2.222, 2.11]}, {"time": 2.6667, "value": 2.11, "curve": [3.111, 2.11, 3.556, 1.62]}, {"time": 4, "value": 1.62, "curve": [4.444, 1.62, 4.889, 2.11]}, {"time": 5.3333, "value": 2.11}]}, "bone108": {"rotate": [{"value": 3.75, "curve": [0.444, 3.75, 0.889, 3.26]}, {"time": 1.3333, "value": 3.26, "curve": [1.778, 3.26, 2.222, 3.75]}, {"time": 2.6667, "value": 3.75, "curve": [3.111, 3.75, 3.556, 3.26]}, {"time": 4, "value": 3.26, "curve": [4.444, 3.26, 4.889, 3.75]}, {"time": 5.3333, "value": 3.75}]}, "bone109": {"rotate": [{"value": -0.02, "curve": [0.057, -0.01, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -0.49]}, {"time": 1.5, "value": -0.49, "curve": [1.89, -0.49, 2.278, -0.11]}, {"time": 2.6667, "value": -0.02, "curve": [2.724, -0.01, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -0.49]}, {"time": 4.1667, "value": -0.49, "curve": [4.556, -0.49, 4.946, -0.12]}, {"time": 5.3333, "value": -0.02}]}, "bone110": {"rotate": [{"value": -0.41, "curve": [0.114, -0.18, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, -2.59]}, {"time": 1.6667, "value": -2.59, "curve": [2.001, -2.59, 2.333, -1.11]}, {"time": 2.6667, "value": -0.41, "curve": [2.781, -0.18, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, -2.59]}, {"time": 4.3333, "value": -2.59, "curve": [4.668, -2.59, 5.003, -1.14]}, {"time": 5.3333, "value": -0.41}]}, "bone113": {"rotate": [{"value": 1.43, "curve": [0.444, 1.43, 0.889, 2.02]}, {"time": 1.3333, "value": 2.02, "curve": [1.778, 2.02, 2.222, 1.43]}, {"time": 2.6667, "value": 1.43, "curve": [3.111, 1.43, 3.556, 2.02]}, {"time": 4, "value": 2.02, "curve": [4.444, 2.02, 4.889, 1.43]}, {"time": 5.3333, "value": 1.43}]}, "bone114": {"rotate": [{"value": 1.43, "curve": [0.444, 1.43, 0.889, 2.02]}, {"time": 1.3333, "value": 2.02, "curve": [1.778, 2.02, 2.222, 1.43]}, {"time": 2.6667, "value": 1.43, "curve": [3.111, 1.43, 3.556, 2.02]}, {"time": 4, "value": 2.02, "curve": [4.444, 2.02, 4.889, 1.43]}, {"time": 5.3333, "value": 1.43}]}, "bone115": {"rotate": [{"value": 1.43, "curve": [0.444, 1.43, 0.889, 2.02]}, {"time": 1.3333, "value": 2.02, "curve": [1.778, 2.02, 2.222, 1.43]}, {"time": 2.6667, "value": 1.43, "curve": [3.111, 1.43, 3.556, 2.02]}, {"time": 4, "value": 2.02, "curve": [4.444, 2.02, 4.889, 1.43]}, {"time": 5.3333, "value": 1.43}]}, "bone116": {"rotate": [{"value": 1.43, "curve": [0.444, 1.43, 0.889, 2.02]}, {"time": 1.3333, "value": 2.02, "curve": [1.778, 2.02, 2.222, 1.43]}, {"time": 2.6667, "value": 1.43, "curve": [3.111, 1.43, 3.556, 2.02]}, {"time": 4, "value": 2.02, "curve": [4.444, 2.02, 4.889, 1.43]}, {"time": 5.3333, "value": 1.43}]}, "bone117": {"rotate": [{"value": 1.43, "curve": [0.444, 1.43, 0.889, 2.02]}, {"time": 1.3333, "value": 2.02, "curve": [1.778, 2.02, 2.222, 1.43]}, {"time": 2.6667, "value": 1.43, "curve": [3.111, 1.43, 3.556, 2.02]}, {"time": 4, "value": 2.02, "curve": [4.444, 2.02, 4.889, 1.43]}, {"time": 5.3333, "value": 1.43}]}, "bone118": {"rotate": [{"value": 1.43, "curve": [0.444, 1.43, 0.889, 2.02]}, {"time": 1.3333, "value": 2.02, "curve": [1.778, 2.02, 2.222, 1.43]}, {"time": 2.6667, "value": 1.43, "curve": [3.111, 1.43, 3.556, 2.02]}, {"time": 4, "value": 2.02, "curve": [4.444, 2.02, 4.889, 1.43]}, {"time": 5.3333, "value": 1.43}]}, "bone119": {"rotate": [{"value": 1.43, "curve": [0.444, 1.43, 0.889, 2.02]}, {"time": 1.3333, "value": 2.02, "curve": [1.778, 2.02, 2.222, 1.43]}, {"time": 2.6667, "value": 1.43, "curve": [3.111, 1.43, 3.556, 2.02]}, {"time": 4, "value": 2.02, "curve": [4.444, 2.02, 4.889, 1.43]}, {"time": 5.3333, "value": 1.43}]}, "bone120": {"rotate": [{"value": 1.43}]}, "bone121": {"rotate": [{"value": 1.43}]}, "bone122": {"rotate": [{"value": 2.68}]}, "bone123": {"rotate": [{"value": 2.33}]}, "bone124": {"rotate": [{"value": -1.77}]}, "bone125": {"rotate": [{"value": 1.43}]}, "bone126": {"rotate": [{"value": 0.56, "curve": [0.444, 0.56, 0.889, -1.1]}, {"time": 1.3333, "value": -1.1, "curve": [1.778, -1.1, 2.222, 0.56]}, {"time": 2.6667, "value": 0.56, "curve": [3.111, 0.56, 3.556, -1.1]}, {"time": 4, "value": -1.1, "curve": [4.444, -1.1, 4.889, 0.56]}, {"time": 5.3333, "value": 0.56}]}, "bone127": {"rotate": [{"value": 0.56, "curve": [0.444, 0.56, 0.889, -1.1]}, {"time": 1.3333, "value": -1.1, "curve": [1.778, -1.1, 2.222, 0.56]}, {"time": 2.6667, "value": 0.56, "curve": [3.111, 0.56, 3.556, -1.1]}, {"time": 4, "value": -1.1, "curve": [4.444, -1.1, 4.889, 0.56]}, {"time": 5.3333, "value": 0.56}]}, "bone128": {"rotate": [{"value": 0.56, "curve": [0.444, 0.56, 0.889, -1.1]}, {"time": 1.3333, "value": -1.1, "curve": [1.778, -1.1, 2.222, 0.56]}, {"time": 2.6667, "value": 0.56, "curve": [3.111, 0.56, 3.556, -1.1]}, {"time": 4, "value": -1.1, "curve": [4.444, -1.1, 4.889, 0.56]}, {"time": 5.3333, "value": 0.56}]}, "bone129": {"rotate": [{"value": 0.56, "curve": [0.444, 0.56, 0.889, -1.1]}, {"time": 1.3333, "value": -1.1, "curve": [1.778, -1.1, 2.222, 0.56]}, {"time": 2.6667, "value": 0.56, "curve": [3.111, 0.56, 3.556, -1.1]}, {"time": 4, "value": -1.1, "curve": [4.444, -1.1, 4.889, 0.56]}, {"time": 5.3333, "value": 0.56}]}, "bone130": {"rotate": [{"value": 0.56, "curve": [0.444, 0.56, 0.889, -1.1]}, {"time": 1.3333, "value": -1.1, "curve": [1.778, -1.1, 2.222, 0.56]}, {"time": 2.6667, "value": 0.56, "curve": [3.111, 0.56, 3.556, -1.1]}, {"time": 4, "value": -1.1, "curve": [4.444, -1.1, 4.889, 0.56]}, {"time": 5.3333, "value": 0.56}]}, "bone28": {"rotate": [{"value": 2.63}]}, "bone20": {"rotate": [{"value": 0.89, "curve": [0.444, 0.89, 0.889, -0.05]}, {"time": 1.3333, "value": -0.05, "curve": [1.778, -0.05, 2.222, 0.89]}, {"time": 2.6667, "value": 0.89, "curve": [3.111, 0.89, 3.556, -0.05]}, {"time": 4, "value": -0.05, "curve": [4.444, -0.05, 4.889, 0.89]}, {"time": 5.3333, "value": 0.89}]}, "bone8": {"translate": [{"curve": [0.444, 0, 0.889, 0.03, 0.444, 0, 0.889, -7.38]}, {"time": 1.3333, "x": 0.03, "y": -7.38, "curve": [1.778, 0.03, 2.222, 0, 1.778, -7.38, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 0.03, 3.111, 0, 3.556, -7.38]}, {"time": 4, "x": 0.03, "y": -7.38, "curve": [4.444, 0.03, 4.889, 0, 4.444, -7.38, 4.889, 0]}, {"time": 5.3333}]}, "bone29": {"rotate": [{"value": 1.24, "curve": [0.057, 1.21, 0.112, 1.19]}, {"time": 0.1667, "value": 1.19, "curve": [0.611, 1.19, 1.056, 2.21]}, {"time": 1.5, "value": 2.21, "curve": [1.89, 2.21, 2.278, 1.42]}, {"time": 2.6667, "value": 1.24, "curve": [2.724, 1.21, 2.779, 1.19]}, {"time": 2.8333, "value": 1.19, "curve": [3.278, 1.19, 3.722, 2.21]}, {"time": 4.1667, "value": 2.21, "curve": [4.556, 2.21, 4.946, 1.43]}, {"time": 5.3333, "value": 1.24}]}, "bone30": {"rotate": [{"value": 1.35, "curve": [0.114, 1.26, 0.224, 1.19]}, {"time": 0.3333, "value": 1.19, "curve": [0.778, 1.19, 1.222, 2.21]}, {"time": 1.6667, "value": 2.21, "curve": [2.001, 2.21, 2.333, 1.63]}, {"time": 2.6667, "value": 1.35, "curve": [2.781, 1.26, 2.89, 1.19]}, {"time": 3, "value": 1.19, "curve": [3.444, 1.19, 3.889, 2.21]}, {"time": 4.3333, "value": 2.21, "curve": [4.668, 2.21, 5.003, 1.64]}, {"time": 5.3333, "value": 1.35}]}, "bone31": {"rotate": [{"value": 1.97, "curve": [0.168, 1.54, 0.334, 1.19]}, {"time": 0.5, "value": 1.19, "curve": [0.944, 1.19, 1.389, 3.65]}, {"time": 1.8333, "value": 3.65, "curve": [2.112, 3.65, 2.389, 2.68]}, {"time": 2.6667, "value": 1.97, "curve": [2.835, 1.54, 3.001, 1.19]}, {"time": 3.1667, "value": 1.19, "curve": [3.611, 1.19, 4.056, 3.65]}, {"time": 4.5, "value": 3.65, "curve": [4.779, 3.65, 5.057, 2.69]}, {"time": 5.3333, "value": 1.97}]}, "bone32": {"rotate": [{"value": 2.42, "curve": [0.225, 1.81, 0.446, 1.19]}, {"time": 0.6667, "value": 1.19, "curve": [1.111, 1.19, 1.556, 3.65]}, {"time": 2, "value": 3.65, "curve": [2.224, 3.65, 2.444, 3.02]}, {"time": 2.6667, "value": 2.42, "curve": [2.892, 1.81, 3.113, 1.19]}, {"time": 3.3333, "value": 1.19, "curve": [3.778, 1.19, 4.222, 3.65]}, {"time": 4.6667, "value": 3.65, "curve": [4.89, 3.65, 5.114, 3.04]}, {"time": 5.3333, "value": 2.42}], "scale": [{"x": 1.103, "curve": [0.225, 1.052, 0.446, 1, 0.225, 1, 0.446, 1]}, {"time": 0.6667, "curve": [1.111, 1, 1.556, 1.207, 1.111, 1, 1.556, 1]}, {"time": 2, "x": 1.207, "curve": [2.224, 1.207, 2.444, 1.154, 2.224, 1, 2.444, 1]}, {"time": 2.6667, "x": 1.103, "curve": [2.892, 1.052, 3.113, 1, 2.892, 1, 3.113, 1]}, {"time": 3.3333, "curve": [3.778, 1, 4.222, 1.207, 3.778, 1, 4.222, 1]}, {"time": 4.6667, "x": 1.207, "curve": [4.89, 1.207, 5.114, 1.155, 4.89, 1, 5.114, 1]}, {"time": 5.3333, "x": 1.103}]}, "bone33": {"rotate": [{"value": 2.87, "curve": [0.279, 2.15, 0.556, 1.19]}, {"time": 0.8333, "value": 1.19, "curve": [1.278, 1.19, 1.722, 3.65]}, {"time": 2.1667, "value": 3.65, "curve": [2.334, 3.65, 2.5, 3.3]}, {"time": 2.6667, "value": 2.87, "curve": [2.946, 2.15, 3.223, 1.19]}, {"time": 3.5, "value": 1.19, "curve": [3.944, 1.19, 4.389, 3.65]}, {"time": 4.8333, "value": 3.65, "curve": [5.001, 3.65, 5.168, 3.3]}, {"time": 5.3333, "value": 2.87}], "scale": [{"x": 1.141, "curve": [0.279, 1.08, 0.556, 1, 0.279, 1, 0.556, 1]}, {"time": 0.8333, "curve": [1.278, 1, 1.722, 1.207, 1.278, 1, 1.722, 1]}, {"time": 2.1667, "x": 1.207, "curve": [2.334, 1.207, 2.5, 1.177, 2.334, 1, 2.5, 1]}, {"time": 2.6667, "x": 1.141, "curve": [2.946, 1.08, 3.223, 1, 2.946, 1, 3.223, 1]}, {"time": 3.5, "curve": [3.944, 1, 4.389, 1.207, 3.944, 1, 4.389, 1]}, {"time": 4.8333, "x": 1.207, "curve": [5.001, 1.207, 5.168, 1.178, 5.001, 1, 5.168, 1]}, {"time": 5.3333, "x": 1.141}]}, "bone21": {"rotate": [{"curve": [0.444, 0, 0.889, -0.94]}, {"time": 1.3333, "value": -0.94, "curve": [1.778, -0.94, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -0.94]}, {"time": 4, "value": -0.94, "curve": [4.444, -0.94, 4.889, 0]}, {"time": 5.3333}]}, "bone22": {"rotate": [{"curve": [0.444, 0, 0.889, -0.94]}, {"time": 1.3333, "value": -0.94, "curve": [1.778, -0.94, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, -0.94]}, {"time": 4, "value": -0.94, "curve": [4.444, -0.94, 4.889, 0]}, {"time": 5.3333}]}, "bone23": {"rotate": [{"value": -0.04, "curve": [0.057, -0.02, 0.112, 0]}, {"time": 0.1667, "curve": [0.611, 0, 1.056, -0.94]}, {"time": 1.5, "value": -0.94, "curve": [1.89, -0.94, 2.278, -0.21]}, {"time": 2.6667, "value": -0.04, "curve": [2.724, -0.02, 2.779, 0]}, {"time": 2.8333, "curve": [3.278, 0, 3.722, -0.94]}, {"time": 4.1667, "value": -0.94, "curve": [4.556, -0.94, 4.946, -0.22]}, {"time": 5.3333, "value": -0.04}]}, "bone24": {"rotate": [{"value": 1.17, "curve": [0.114, 0.5, 0.224, 0]}, {"time": 0.3333, "curve": [0.778, 0, 1.222, 7.31]}, {"time": 1.6667, "value": 7.31, "curve": [2.001, 7.31, 2.333, 3.13]}, {"time": 2.6667, "value": 1.17, "curve": [2.781, 0.5, 2.89, 0]}, {"time": 3, "curve": [3.444, 0, 3.889, 7.31]}, {"time": 4.3333, "value": 7.31, "curve": [4.668, 7.31, 5.003, 3.21]}, {"time": 5.3333, "value": 1.17}]}, "bone25": {"rotate": [{"value": 2.32, "curve": [0.168, 1.05, 0.334, 0]}, {"time": 0.5, "curve": [0.944, 0, 1.389, 7.31]}, {"time": 1.8333, "value": 7.31, "curve": [2.112, 7.31, 2.389, 4.43]}, {"time": 2.6667, "value": 2.32, "curve": [2.835, 1.05, 3.001, 0]}, {"time": 3.1667, "curve": [3.611, 0, 4.056, 7.31]}, {"time": 4.5, "value": 7.31, "curve": [4.779, 7.31, 5.057, 4.47]}, {"time": 5.3333, "value": 2.32}]}, "bone26": {"rotate": [{"value": 3.65, "curve": [0.225, 1.84, 0.446, 0]}, {"time": 0.6667, "curve": [1.111, 0, 1.556, 7.31]}, {"time": 2, "value": 7.31, "curve": [2.224, 7.31, 2.444, 5.44]}, {"time": 2.6667, "value": 3.65, "curve": [2.892, 1.84, 3.113, 0]}, {"time": 3.3333, "curve": [3.778, 0, 4.222, 7.31]}, {"time": 4.6667, "value": 7.31, "curve": [4.89, 7.31, 5.114, 5.49]}, {"time": 5.3333, "value": 3.65}]}, "bone111": {"rotate": [{"value": -0.82, "curve": [0.168, -0.37, 0.334, 0]}, {"time": 0.5, "curve": [0.944, 0, 1.389, -2.59]}, {"time": 1.8333, "value": -2.59, "curve": [2.112, -2.59, 2.389, -1.57]}, {"time": 2.6667, "value": -0.82, "curve": [2.835, -0.37, 3.001, 0]}, {"time": 3.1667, "curve": [3.611, 0, 4.056, -2.59]}, {"time": 4.5, "value": -2.59, "curve": [4.779, -2.59, 5.057, -1.58]}, {"time": 5.3333, "value": -0.82}], "scale": [{"x": 1.117}]}, "bone112": {"rotate": [{"value": -1.29, "curve": [0.225, -0.65, 0.446, 0]}, {"time": 0.6667, "curve": [1.111, 0, 1.556, -2.59]}, {"time": 2, "value": -2.59, "curve": [2.224, -2.59, 2.444, -1.93]}, {"time": 2.6667, "value": -1.29, "curve": [2.892, -0.65, 3.113, 0]}, {"time": 3.3333, "curve": [3.778, 0, 4.222, -2.59]}, {"time": 4.6667, "value": -2.59, "curve": [4.89, -2.59, 5.114, -1.94]}, {"time": 5.3333, "value": -1.29}], "scale": [{"x": 1.117}]}}, "attachments": {"default": {"biyan": {"biyan": {"deform": [{"time": 3.8, "vertices": [-0.45715, -0.04257, -0.45715, -0.04258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.96216, -0.06629, 1.9621, -0.06639, 10.9823, -1.9299, 10.9823, -1.93004, 17.9068, 2.08247, 17.90686, 2.08223, 18.57635, -0.51227, 18.57605, -0.51245, 17.92181, -0.68215, 17.92163, -0.6825, 10.8385, -0.78446, 10.83838, -0.78463, 0, 0, 0, 0, 0, 0, 13.73322, 1.44862, 13.73315, 1.44849, 13.54425, 1.14433, 13.54395, 1.14434, 12.72552, 0.25428, 12.72522, 0.25425, 11.34528, -0.11189, 11.34528, -0.11202, 8.76575, -0.97158, 8.76569, -0.97175, 4.5083, 0.1679, 4.50781, 0.1677, 3.11682, -0.96933, 3.11682, -0.96944]}, {"time": 3.9667, "offset": 44, "vertices": [-0.26837, 0.01943], "curve": "stepped"}, {"time": 4.1, "offset": 44, "vertices": [-0.26837, 0.01943]}, {"time": 4.2, "vertices": [-0.45715, -0.04257, -0.45715, -0.04258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.96216, -0.06629, 1.9621, -0.06639, 10.9823, -1.9299, 10.9823, -1.93004, 17.9068, 2.08247, 17.90686, 2.08223, 18.57635, -0.51227, 18.57605, -0.51245, 17.92181, -0.68215, 17.92163, -0.6825, 10.8385, -0.78446, 10.83838, -0.78463, 0, 0, 0, 0, 0, 0, 13.73322, 1.44862, 13.73315, 1.44849, 13.54425, 1.14433, 13.54395, 1.14434, 12.72552, 0.25428, 12.72522, 0.25425, 11.34528, -0.11189, 11.34528, -0.11202, 8.76575, -0.97158, 8.76569, -0.97175, 4.5083, 0.1679, 4.50781, 0.1677, 3.11682, -0.96933, 3.11682, -0.96944]}]}}}}}, "xiuxian": {"slots": {"biyan": {"rgba": [{"time": 0.4667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.5333, "color": "ffffffff"}, {"time": 3.6, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "biyan"}, {"time": 0.8667}, {"time": 3.2, "name": "biyan"}, {"time": 3.6}]}}, "bones": {"bone": {"rotate": [{"curve": [0.222, 0, 0.444, -0.25]}, {"time": 0.6667, "curve": [1.065, 0.45, 1, -0.35]}, {"time": 1.3333, "value": -0.76, "curve": [1.578, -1.05, 1.383, 0.24]}, {"time": 1.8333, "curve": [1.857, -0.01, 2.056, -0.01]}, {"time": 2.1667, "value": -0.01, "curve": [2.5, -0.01, 2.833, 0]}, {"time": 3.1667}], "translate": [{"x": -0.36, "y": 4.02, "curve": [0.222, -0.36, 0.445, 5.31, 0.222, 4.02, 0.445, 1.68]}, {"time": 0.6667, "curve": [1.065, -9.55, 1, 6.06, 1.065, -3.02, 1, 4.51]}, {"time": 1.3333, "x": 16.04, "y": 5.07, "curve": [1.578, 23.37, 1.383, -8.71, 1.578, 5.48, 1.383, 3.68]}, {"time": 1.8333, "x": -2.68, "y": 4.02, "curve": [1.857, -2.36, 2.056, -0.09, 1.857, 4.03, 2.056, 4.03]}, {"time": 2.1667, "x": -0.09, "y": 4.03, "curve": [2.5, -0.09, 2.722, 5.49, 2.5, 4.03, 2.722, -8.6]}, {"time": 3.1667, "x": 5.49, "y": -8.6, "curve": [3.611, 5.49, 3.833, -0.36, 3.611, -8.6, 3.833, 4.02]}, {"time": 4.1667, "x": -0.36, "y": 4.02}]}, "bone2": {"rotate": [{"value": 3.49, "curve": [0.222, 3.49, 0.444, 0.14]}, {"time": 0.6667, "value": 0.47, "curve": [1.065, 1.07, 1, 1.4]}, {"time": 1.3333, "value": -1, "curve": [1.578, -2.76, 1.383, 4.94]}, {"time": 1.8333, "value": 3.49, "curve": [1.857, 3.41, 2.056, 3.12]}, {"time": 2.1667, "value": 3.12, "curve": [2.5, 3.12, 2.833, 0.79]}, {"time": 3.1667, "value": 0.79, "curve": [3.5, 0.79, 3.833, 3.49]}, {"time": 4.1667, "value": 3.49}], "translate": [{"x": 0.02, "y": 3.35, "curve": [0.222, 0.02, 0.444, 0, 0.222, 3.35, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, 0.01, 0.741, 0, 1.111, 1.19]}, {"time": 1.3333, "curve": [1.578, -0.01, 1.383, 0.02, 1.578, -1.31, 1.383, 4.43]}, {"time": 1.8333, "x": 0.02, "y": 3.35, "curve": [1.857, 0.02, 2.056, 0.02, 1.857, 3.29, 2.056, 3.29]}, {"time": 2.1667, "x": 0.02, "y": 3.29, "curve": [2.5, 0.02, 2.833, 0.02, 2.5, 3.29, 2.833, 3.35]}, {"time": 3.1667, "x": 0.02, "y": 3.35}], "scale": [{"time": 2.1667, "curve": [2.5, 1, 2.833, 0.976, 2.5, 1, 2.833, 0.99]}, {"time": 3.1667, "x": 0.976, "y": 0.99, "curve": [3.5, 0.976, 3.833, 1, 3.5, 0.99, 3.833, 1]}, {"time": 4.1667}]}, "bone3": {"rotate": [{"value": -0.74, "curve": [0.222, -0.74, 0.444, -0.5]}, {"time": 0.6667, "value": 0.34, "curve": [1.065, 1.84, 1, -1.57]}, {"time": 1.3333, "value": -2.53, "curve": [1.578, -3.23, 1.383, -0.16]}, {"time": 1.8333, "value": -0.74, "curve": [1.857, -0.77, 2.056, -0.87]}, {"time": 2.1667, "value": -0.87, "curve": [2.5, -0.87, 2.833, -0.74]}, {"time": 3.1667, "value": -0.74, "curve": [3.5, -0.74, 3.833, -0.74]}, {"time": 4.1667, "value": -0.74}], "translate": [{"x": 7.91, "y": 0.09, "curve": [0.222, 7.91, 0.444, 0, 0.222, 0.09, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, 2.81, 0.741, 0, 1.111, 0.03]}, {"time": 1.3333, "curve": [1.578, -3.09, 1.383, 10.45, 1.578, -0.03, 1.383, 0.11]}, {"time": 1.8333, "x": 7.91, "y": 0.09, "curve": [1.857, 7.77, 2.056, 7.77, 1.857, 0.09, 2.056, 0.09]}, {"time": 2.1667, "x": 7.77, "y": 0.09, "curve": [2.5, 7.77, 2.833, 2.56, 2.5, 0.09, 2.833, -0.06]}, {"time": 3.1667, "x": 2.56, "y": -0.06, "curve": [3.5, 2.56, 3.833, 7.91, 3.5, -0.06, 3.833, 0.09]}, {"time": 4.1667, "x": 7.91, "y": 0.09}], "scale": [{"curve": [0.222, 1, 0.444, 1.004, 0.222, 1, 0.444, 1.004]}, {"time": 0.6667, "curve": [1.065, 0.992, 1, 1.006, 1.065, 0.992, 1, 1.006]}, {"time": 1.3333, "x": 1.013, "y": 1.013, "curve": [1.578, 1.018, 1.383, 0.996, 1.578, 1.018, 1.383, 0.996]}, {"time": 1.8333, "curve": [1.857, 1, 2.056, 1, 1.857, 1, 2.056, 1]}, {"time": 2.1667, "curve": [2.5, 1, 2.833, 0.962, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 0.962, "curve": [3.5, 0.962, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone4": {"rotate": [{"value": -11.83, "curve": [0.222, -11.83, 0.444, -3.63]}, {"time": 0.6667, "value": -3.07, "curve": [1.065, -2.05, 1.001, -7.1]}, {"time": 1.3333, "value": -1.7, "curve": [1.578, 2.27, 1.383, -15.1]}, {"time": 1.8333, "value": -11.83, "curve": [1.857, -11.66, 2.056, -11.23]}, {"time": 2.1667, "value": -11.23, "curve": [2.5, -11.23, 2.833, -2.73]}, {"time": 3.1667, "value": -2.73, "curve": [3.5, -2.73, 3.833, -11.83]}, {"time": 4.1667, "value": -11.83}], "translate": [{"x": 4.27, "y": -0.01, "curve": [0.222, 4.27, 0.444, 0.15, 0.222, -0.01, 0.444, 0]}, {"time": 0.6667, "curve": [1.065, -0.27, 1, 2.5, 1.065, 0, 1, 0]}, {"time": 1.3333, "x": 0.46, "curve": [1.578, -1.03, 1.383, 5.5, 1.578, 0, 1.383, -0.01]}, {"time": 1.8333, "x": 4.27, "y": -0.01, "curve": [1.857, 4.21, 2.056, 4.21, 1.857, -0.01, 2.056, -0.01]}, {"time": 2.1667, "x": 4.21, "y": -0.01, "curve": [2.5, 4.21, 2.833, 4.27, 2.5, -0.01, 2.833, -0.01]}, {"time": 3.1667, "x": 4.27, "y": -0.01}], "scale": [{"curve": [0.222, 1, 0.444, 1.005, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "curve": [1.065, 0.991, 1, 1.007, 0.741, 1, 1.241, 1]}, {"time": 1.3333, "x": 1.015, "curve": [1.578, 1.021, 1.383, 0.995, 1.434, 1, 1.767, 1]}, {"time": 1.8333, "curve": [1.857, 1, 2.056, 1, 1.944, 1, 2.056, 1]}, {"time": 2.1667, "curve": [2.5, 1, 2.833, 0.98, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 0.98, "curve": [3.5, 0.98, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone5": {"rotate": [{"value": 2.2, "curve": [0.238, 2.2, 0.529, -6.98]}, {"time": 0.7667, "value": -4.63, "curve": [1.165, -0.69, 1.1, -1.72]}, {"time": 1.4333, "value": -6.63, "curve": [1.678, -10.23, 1.483, 5.54]}, {"time": 1.9333, "value": 2.58, "curve": [1.957, 2.42, 2.156, 1.06]}, {"time": 2.2667, "value": 1.06, "curve": [2.6, 1.06, 2.933, -5.22]}, {"time": 3.2667, "value": -5.22, "curve": [3.6, -5.22, 3.867, 2.2]}, {"time": 4.1667, "value": 2.2}]}, "bone6": {"rotate": [{"value": -0.03, "curve": [0.251, -0.03, 0.615, -0.4]}, {"time": 0.8667, "curve": [1.265, 0.64, 1.2, -0.51]}, {"time": 1.5333, "value": -1.07, "curve": [1.778, -1.48, 1.583, 0.31]}, {"time": 2.0333, "value": -0.03, "curve": [2.057, -0.04, 2.256, -0.04]}, {"time": 2.3667, "value": -0.04, "curve": [2.7, -0.04, 3.033, -0.03]}, {"time": 3.3667, "value": -0.03}]}, "bone8": {"translate": [{"curve": [0.222, 0, 0.444, 0.03, 0.222, 0, 0.445, 13.16]}, {"time": 0.6667, "x": 0.12, "y": 10.18, "curve": [1.065, 0.28, 1, -0.11, 1.065, 4.85, 0.999, 11.38]}, {"time": 1.3333, "x": -0.39, "y": 10.76, "curve": [1.578, -0.6, 1.383, 0.5, 1.578, 10.3, 1.383, 12.41]}, {"time": 1.8333, "x": 0.33, "y": 12.04}, {"time": 2.1667, "x": 0.27, "y": 10.12, "curve": "stepped"}, {"time": 3.1667, "x": 0.27, "y": 10.12, "curve": [3.5, 0.27, 3.833, 0, 3.5, 10.12, 3.833, 0]}, {"time": 4.1667}]}, "bone11": {"rotate": [{"value": 7.18, "curve": [0.222, 7.18, 0.444, 0.13]}, {"time": 0.6667, "curve": [1.065, -0.23, 1, 4.01]}, {"time": 1.3333, "value": 0.39, "curve": [1.578, -2.27, 1.383, 9.37]}, {"time": 1.8333, "value": 7.18, "curve": [1.853, 7.09, 2.056, 7.08]}, {"time": 2.1667, "value": 7.08, "curve": [2.5, 7.08, 2.833, 5.84]}, {"time": 3.1667, "value": 5.84, "curve": [3.5, 5.84, 3.833, 7.18]}, {"time": 4.1667, "value": 7.18}]}, "bone12": {"rotate": [{"value": 3.36, "curve": [0.222, 3.36, 0.445, 1.74]}, {"time": 0.6667, "curve": [1.065, -3.12, 1, 4.24]}, {"time": 1.3333, "value": 5.24, "curve": [1.578, 5.97, 1.383, 2.76]}, {"time": 1.8333, "value": 3.36, "curve": [1.944, 3.51, 2.056, 3.68]}, {"time": 2.1667, "value": 3.68, "curve": [2.5, 3.68, 2.833, -2.7]}, {"time": 3.1667, "value": -2.7, "curve": [3.5, -2.7, 3.833, 3.36]}, {"time": 4.1667, "value": 3.36}]}, "bone13": {"rotate": [{"value": -5.07, "curve": [0.222, -5.07, 0.445, -1.6]}, {"time": 0.6667, "curve": [1.065, 2.87, 1, -4.96]}, {"time": 1.3333, "value": -4.83, "curve": [1.578, -4.73, 1.383, -5.15]}, {"time": 1.8333, "value": -5.07, "curve": [1.944, -5.05, 2.056, -5.36]}, {"time": 2.1667, "value": -5.36, "curve": [2.5, -5.36, 2.833, -0.13]}, {"time": 3.1667, "value": -0.13, "curve": [3.5, -0.13, 3.833, -5.07]}, {"time": 4.1667, "value": -5.07}]}, "bone14": {"rotate": [{"value": 9.55, "curve": [0.222, 9.55, 0.444, 0.32]}, {"time": 0.6667, "curve": [1.065, -0.58, 1, 5.54]}, {"time": 1.3333, "value": 0.97, "curve": [1.578, -2.39, 1.383, 12.31]}, {"time": 1.8333, "value": 9.55, "curve": [1.944, 8.86, 2.056, 2.71]}, {"time": 2.1667, "value": 2.71, "curve": [2.5, 2.71, 2.833, -5.31]}, {"time": 3.1667, "value": -5.31, "curve": [3.5, -5.31, 3.833, 9.55]}, {"time": 4.1667, "value": 9.55}]}, "bone15": {"rotate": [{"value": 2.8, "curve": [0.222, 2.8, 0.444, -0.24]}, {"time": 0.6667, "curve": [1.065, 0.43, 1, 1.16]}, {"time": 1.3333, "value": -0.73, "curve": [1.578, -2.1, 1.383, 3.94]}, {"time": 1.8333, "value": 2.8, "curve": [1.857, 2.74, 2.056, 2.74]}, {"time": 2.1667, "value": 2.74, "curve": [2.5, 2.74, 2.833, -2.89]}, {"time": 3.1667, "value": -2.89, "curve": [3.5, -2.89, 3.833, 2.8]}, {"time": 4.1667, "value": 2.8}]}, "bone16": {"rotate": [{"value": 2.99, "curve": [0.222, 2.99, 0.445, -5.56]}, {"time": 0.6667, "curve": [1.065, 10, 1, -6.24]}, {"time": 1.3333, "value": -16.8, "curve": [1.578, -24.55, 1.383, 9.37]}, {"time": 1.8333, "value": 2.99, "curve": [1.857, 2.66, 2.056, 2.66]}, {"time": 2.1667, "value": 2.66, "curve": [2.5, 2.66, 2.833, 2.57]}, {"time": 3.1667, "value": 2.57, "curve": [3.5, 2.57, 3.833, 2.99]}, {"time": 4.1667, "value": 2.99}]}, "bone17": {"rotate": [{"value": 8.12, "curve": [0.222, 8.12, 0.444, -0.5]}, {"time": 0.6667, "curve": [1.065, 0.9, 1, 3.62]}, {"time": 1.3333, "value": -1.52, "curve": [1.578, -5.29, 1.383, 11.23]}, {"time": 1.8333, "value": 8.12, "curve": [1.857, 7.96, 2.056, 7.96]}, {"time": 2.1667, "value": 7.96, "curve": [2.5, 7.96, 2.833, 5.47]}, {"time": 3.1667, "value": 5.47, "curve": [3.5, 5.47, 3.833, 8.12]}, {"time": 4.1667, "value": 8.12}], "translate": [{"curve": [0.222, 0, 0.444, -0.42, 0.222, 0, 0.445, 1.95]}, {"time": 0.6667, "curve": [1.065, 0.76, 1, -0.6, 1.065, -3.49, 1, 2.73]}, {"time": 1.3333, "x": -1.28, "y": 5.86, "curve": [1.578, -1.78, 1.383, 0.41, 1.578, 8.16, 1.383, -1.89]}, {"time": 1.8333, "curve": [1.857, -0.02, 2.056, -0.02, 1.857, 0.1, 2.056, 0.1]}, {"time": 2.1667, "x": -0.02, "y": 0.1, "curve": [2.5, -0.02, 2.833, 0, 2.5, 0.1, 2.833, 0]}, {"time": 3.1667}]}, "bone18": {"rotate": [{"value": -0.78, "curve": [0.222, -0.78, 0.445, -4.28]}, {"time": 0.6667, "curve": [1.065, 7.69, 1, -6.43]}, {"time": 1.3333, "value": -12.92, "curve": [1.578, -17.67, 1.383, 3.13]}, {"time": 1.8333, "value": -0.78, "curve": [1.857, -0.98, 2.056, -0.98]}, {"time": 2.1667, "value": -0.98, "curve": [2.5, -0.98, 2.833, 2.37]}, {"time": 3.1667, "value": 2.37, "curve": [3.5, 2.37, 3.833, -0.78]}, {"time": 4.1667, "value": -0.78}]}, "bone19": {"rotate": [{"value": -5.29, "curve": [0.222, -5.29, 0.451, 14.66]}, {"time": 0.6667, "curve": [1.065, -27.02, 0.999, 18.3]}, {"time": 1.3333, "value": 45.4, "curve": [1.578, 65.24, 1.383, -21.63]}, {"time": 1.8333, "value": -5.29, "curve": [1.837, -5.17, 2.056, -5.41]}, {"time": 2.1667, "value": -5.41, "curve": [2.5, -5.41, 2.833, 1.42]}, {"time": 3.1667, "value": 1.42, "curve": [3.5, 1.42, 3.833, -5.29]}, {"time": 4.1667, "value": -5.29}]}, "bone20": {"rotate": [{"value": 0.89, "curve": [0.222, 0.89, 0.444, 0.2]}, {"time": 0.6667, "curve": [1.065, -0.36, 1, 0.76]}, {"time": 1.3333, "value": 0.61, "curve": [1.578, 0.5, 1.383, 0.98]}, {"time": 1.8333, "value": 0.89, "curve": [1.857, 0.88, 2.056, 0.88]}, {"time": 2.1667, "value": 0.88, "curve": [2.5, 0.88, 2.833, -0.05]}, {"time": 3.1667, "value": -0.05, "curve": [3.5, -0.05, 3.833, 0.89]}, {"time": 4.1667, "value": 0.89}]}, "bone21": {"rotate": [{"curve": [0.222, 0, 0.444, 0.2]}, {"time": 0.6667, "curve": [1.065, -0.36, 1, 0.28]}, {"time": 1.3333, "value": 0.61, "curve": [1.578, 0.84, 1.383, -0.2]}, {"time": 1.8333, "curve": [1.857, 0.01, 2.056, 0.01]}, {"time": 2.1667, "value": 0.01, "curve": [2.5, 0.01, 2.833, -0.94]}, {"time": 3.1667, "value": -0.94, "curve": [3.5, -0.94, 3.833, 0]}, {"time": 4.1667}]}, "bone22": {"rotate": [{"curve": [0.222, 0, 0.444, 0.2]}, {"time": 0.6667, "curve": [1.065, -0.35, 1, 0.27]}, {"time": 1.3333, "value": 0.59, "curve": [1.578, 0.82, 1.383, -0.19]}, {"time": 1.8333, "curve": [1.857, 0.01, 2.056, 0.01]}, {"time": 2.1667, "value": 0.01, "curve": [2.5, 0.01, 2.833, -0.94]}, {"time": 3.1667, "value": -0.94, "curve": [3.5, -0.94, 3.833, 0]}, {"time": 4.1667}]}, "bone23": {"rotate": [{"value": -0.04, "curve": [0.222, -0.04, 0.444, 0.83]}, {"time": 0.6667, "value": 0.65, "curve": [1.065, 0.33, 1, 1.31]}, {"time": 1.3333, "value": 1.62, "curve": [1.578, 1.85, 1.383, -1.4]}, {"time": 1.8333, "value": -1.22, "curve": [1.857, -1.21, 2.056, 0.32]}, {"time": 2.1667, "value": 0.32, "curve": [2.28, 0.32, 2.393, -0.15]}, {"time": 2.5, "value": -0.16, "curve": [2.724, -0.2, 2.947, 2.08]}, {"time": 3.1667, "value": 2.1, "curve": [3.503, 2.14, 3.833, -0.04]}, {"time": 4.1667, "value": -0.04}]}, "bone24": {"rotate": [{"value": 1.17, "curve": [0.242, 1.17, 0.558, 1.64]}, {"time": 0.8, "value": 0.65, "curve": [1.198, -0.98, 1.133, 2.98]}, {"time": 1.4667, "value": 3.82, "curve": [1.711, 4.43, 1.517, -0.51]}, {"time": 1.9667, "curve": [1.99, 0.02, 2.189, 1.55]}, {"time": 2.3, "value": 1.55, "curve": [2.413, 1.55, 2.527, 1.08]}, {"time": 2.6333, "value": 1.06, "curve": [2.857, 1.03, 3.08, 3.3]}, {"time": 3.3, "value": 3.32, "curve": [3.636, 3.36, 3.878, 1.17]}, {"time": 4.1667, "value": 1.17}]}, "bone25": {"rotate": [{"value": 2.32, "curve": [0.259, 2.32, 0.674, 1.66]}, {"time": 0.9333, "value": 0.65, "curve": [1.331, -0.91, 1.267, 3.54]}, {"time": 1.6, "value": 3.7, "curve": [1.844, 3.81, 1.65, 1.06]}, {"time": 2.1, "value": 1.15, "curve": [2.124, 1.16, 2.322, 2.68]}, {"time": 2.4333, "value": 2.68, "curve": [2.547, 2.68, 2.66, 2.22]}, {"time": 2.7667, "value": 2.2, "curve": [2.991, 2.16, 3.214, 4.44]}, {"time": 3.4333, "value": 4.47, "curve": [3.769, 4.51, 3.922, 2.32]}, {"time": 4.1667, "value": 2.32}]}, "bone26": {"rotate": [{"value": 3.65, "curve": [0.274, 3.65, 0.793, 1.69]}, {"time": 1.0667, "value": 0.65, "curve": [1.465, -0.86, 1.4, 4.22]}, {"time": 1.7333, "value": 3.62, "curve": [1.978, 3.19, 1.783, 2.84]}, {"time": 2.2333, "value": 2.48, "curve": [2.257, 2.46, 2.456, 3.99]}, {"time": 2.5667, "value": 3.99, "curve": [2.68, 3.99, 2.793, 3.52]}, {"time": 2.9, "value": 3.51, "curve": [3.124, 3.47, 3.347, 5.75]}, {"time": 3.5667, "value": 5.79, "curve": [3.903, 5.83, 3.967, 3.65]}, {"time": 4.1667, "value": 3.65}]}, "bone27": {"rotate": [{"value": 2.07, "curve": [0.222, 2.07, 0.444, 0.89]}, {"time": 0.6667, "curve": [1.065, -1.6, 1, 2.36]}, {"time": 1.3333, "value": 2.69, "curve": [1.578, 2.93, 1.383, 1.07]}, {"time": 1.8333, "value": 1.27, "curve": [1.944, 1.32, 2.056, 1.67]}, {"time": 2.1667, "value": 1.67, "curve": [2.279, 1.67, 2.391, 1.2]}, {"time": 2.5, "value": 1.26, "curve": [2.724, 1.38, 2.946, 2.3]}, {"time": 3.1667, "value": 2.3, "curve": [3.5, 2.3, 3.833, 2.07]}, {"time": 4.1667, "value": 2.07}]}, "bone28": {"rotate": [{"value": 2.63, "curve": [0.222, 2.63, 0.444, 1.05]}, {"time": 0.6667, "curve": [1.065, -1.88, 1, 2.88]}, {"time": 1.3333, "value": 3.16, "curve": [1.578, 3.37, 1.383, 1.65]}, {"time": 1.8333, "value": 1.83, "curve": [1.944, 1.87, 2.056, 2.19]}, {"time": 2.1667, "value": 2.19, "curve": [2.279, 2.19, 2.391, 1.73]}, {"time": 2.5, "value": 1.79, "curve": [2.724, 1.92, 2.946, 2.85]}, {"time": 3.1667, "value": 2.85, "curve": [3.5, 2.85, 3.833, 2.63]}, {"time": 4.1667, "value": 2.63}]}, "bone29": {"rotate": [{"value": 1.24, "curve": [0.242, 1.24, 0.558, 1.15]}, {"time": 0.8, "curve": [1.198, -1.88, 1.133, 2.14]}, {"time": 1.4667, "value": 3.16, "curve": [1.711, 3.92, 1.517, -0.19]}, {"time": 1.9667, "value": 0.43, "curve": [2.078, 0.58, 2.189, 1.29]}, {"time": 2.3, "value": 1.29, "curve": [2.413, 1.29, 2.527, 0.78]}, {"time": 2.6333, "value": 0.77, "curve": [2.857, 0.77, 3.08, 1.06]}, {"time": 3.3, "value": 1.15, "curve": [3.636, 1.29, 3.878, 1.24]}, {"time": 4.1667, "value": 1.24}]}, "bone30": {"rotate": [{"value": 1.35, "curve": [0.259, 1.35, 0.674, 1.43]}, {"time": 0.9333, "curve": [1.331, -2.2, 1.267, 2.44]}, {"time": 1.6, "value": 3.69, "curve": [1.844, 4.61, 1.65, -0.21]}, {"time": 2.1, "value": 0.55, "curve": [2.211, 0.73, 2.322, 1.55]}, {"time": 2.4333, "value": 1.55, "curve": [2.547, 1.55, 2.66, 1.03]}, {"time": 2.7667, "value": 1.02, "curve": [2.991, 1, 3.214, 1.28]}, {"time": 3.4333, "value": 1.34, "curve": [3.769, 1.44, 3.922, 1.35]}, {"time": 4.1667, "value": 1.35}]}, "bone31": {"rotate": [{"value": 1.97, "curve": [0.274, 1.97, 0.793, 1.51]}, {"time": 1.0667, "curve": [1.465, -2.2, 1.4, 2.77]}, {"time": 1.7333, "value": 3.69, "curve": [1.978, 4.37, 1.783, 0.61]}, {"time": 2.2333, "value": 1.17, "curve": [2.344, 1.3, 2.456, 1.96]}, {"time": 2.5667, "value": 1.96, "curve": [2.68, 1.96, 2.793, 1.44]}, {"time": 2.9, "value": 1.44, "curve": [3.124, 1.44, 3.347, 1.75]}, {"time": 3.5667, "value": 1.85, "curve": [3.903, 2, 3.967, 1.97]}, {"time": 4.1667, "value": 1.97}]}, "bone32": {"rotate": [{"value": 2.42, "curve": [0.283, 2.42, 0.884, 1.14]}, {"time": 1.1667, "curve": [1.565, -1.6, 1.5, 2.55]}, {"time": 1.8333, "value": 2.69, "curve": [2.078, 2.8, 1.883, 1.53]}, {"time": 2.3333, "value": 1.61, "curve": [2.444, 1.64, 2.556, 1.89]}, {"time": 2.6667, "value": 1.89, "curve": [2.78, 1.89, 2.893, 1.39]}, {"time": 3, "value": 1.42, "curve": [3.224, 1.47, 3.447, 1.86]}, {"time": 3.6667, "value": 2.04, "curve": [4.003, 2.32, 4, 2.42]}, {"time": 4.1667, "value": 2.42}], "scale": [{"x": 1.103, "curve": [0.283, 1.103, 0.884, 1.001, 0.283, 1, 0.884, 1]}, {"time": 1.1667, "curve": [1.565, 0.999, 1.501, 1.056, 1.241, 1, 1.702, 1]}, {"time": 1.8333, "x": 1.001, "curve": [2.078, 0.962, 1.883, 1.136, 1.934, 1, 2.267, 1]}, {"time": 2.3333, "x": 1.103, "curve": [2.444, 1.095, 2.556, 1.067, 2.444, 1, 2.556, 1]}, {"time": 2.6667, "x": 1.067, "curve": [3.333, 1.067, 3.667, 1.103, 3.333, 1, 3.667, 1]}, {"time": 4.1667, "x": 1.103}]}, "bone33": {"rotate": [{"value": 2.87, "curve": [0.291, 2.87, 0.976, 1.22]}, {"time": 1.2667, "curve": [1.665, -1.67, 1.6, 2.84]}, {"time": 1.9333, "value": 2.81, "curve": [2.178, 2.78, 1.983, 2.08]}, {"time": 2.4333, "value": 2.06, "curve": [2.544, 2.06, 2.656, 2.22]}, {"time": 2.7667, "value": 2.22, "curve": [2.88, 2.22, 2.993, 1.72]}, {"time": 3.1, "value": 1.76, "curve": [3.324, 1.82, 3.547, 2.23]}, {"time": 3.7667, "value": 2.43, "curve": [4.103, 2.74, 4.033, 2.87]}, {"time": 4.1667, "value": 2.87}], "scale": [{"x": 1.141, "curve": [0.291, 1.141, 0.975, 1.004, 0.291, 1, 0.975, 1]}, {"time": 1.2667, "curve": [1.665, 0.995, 1.601, 1.079, 1.341, 1, 1.802, 1]}, {"time": 1.9333, "x": 1.008, "curve": [2.178, 0.956, 1.983, 1.184, 2.034, 1, 2.367, 1]}, {"time": 2.4333, "x": 1.141, "curve": [2.544, 1.13, 2.656, 1.094, 2.544, 1, 2.656, 1]}, {"time": 2.7667, "x": 1.094, "curve": [3.433, 1.094, 3.7, 1.141, 3.433, 1, 3.7, 1]}, {"time": 4.1667, "x": 1.141}]}, "bone34": {"rotate": [{"value": 0.29, "curve": [0.242, 0.29, 0.558, -5.85]}, {"time": 0.8, "value": -5.85, "curve": [1.022, -5.85, 1.234, 7.85]}, {"time": 1.4667, "value": 7.85, "curve": [1.633, 7.86, 1.8, -1.5]}, {"time": 1.9667, "value": -1.5, "curve": [2.078, -1.5, 2.189, 2.04]}, {"time": 2.3, "value": 2.04, "curve": [2.413, 2.04, 2.532, -0.01]}, {"time": 2.6333, "value": -0.03, "curve": [2.857, -0.08, 3.08, 4.43]}, {"time": 3.3, "value": 4.51, "curve": [3.636, 4.63, 3.878, 0.29]}, {"time": 4.1667, "value": 0.29}]}, "bone35": {"rotate": [{"value": 0.98, "curve": [0.259, 0.98, 0.674, -5.16]}, {"time": 0.9333, "value": -5.16, "curve": [1.156, -5.16, 1.367, 6.38]}, {"time": 1.6, "value": 6.39, "curve": [1.767, 6.39, 1.933, -0.81]}, {"time": 2.1, "value": -0.81, "curve": [2.211, -0.81, 2.322, 2.68]}, {"time": 2.4333, "value": 2.68, "curve": [2.547, 2.68, 2.665, 0.63]}, {"time": 2.7667, "value": 0.61, "curve": [2.991, 0.56, 3.214, 5.09]}, {"time": 3.4333, "value": 5.18, "curve": [3.769, 5.31, 3.922, 0.98]}, {"time": 4.1667, "value": 0.98}]}, "bone36": {"rotate": [{"value": -3.63, "curve": [0.222, -3.63, 0.444, -9.76]}, {"time": 0.6667, "value": -9.76, "curve": [0.889, -9.76, 1.101, 7.75]}, {"time": 1.3333, "value": 7.75, "curve": [1.5, 7.76, 1.667, -5.42]}, {"time": 1.8333, "value": -5.42, "curve": [1.944, -5.42, 2.056, -1.79]}, {"time": 2.1667, "value": -1.79, "curve": [2.279, -1.79, 2.399, -2.59]}, {"time": 2.5, "value": -2.6, "curve": [2.724, -2.62, 2.946, 7.1]}, {"time": 3.1667, "value": 7.1, "curve": [3.5, 7.1, 3.833, -3.63]}, {"time": 4.1667, "value": -3.63}]}, "bone37": {"rotate": [{"value": 0.29, "curve": [0.242, 0.29, 0.558, -5.85]}, {"time": 0.8, "value": -5.85, "curve": [1.022, -5.85, 1.234, 8.87]}, {"time": 1.4667, "value": 8.87, "curve": [1.633, 8.88, 1.8, -1.5]}, {"time": 1.9667, "value": -1.5, "curve": [2.078, -1.5, 2.189, 2.06]}, {"time": 2.3, "value": 2.06, "curve": [2.413, 2.06, 2.532, 0.01]}, {"time": 2.6333, "value": -0.01, "curve": [2.857, -0.06, 3.08, 4.45]}, {"time": 3.3, "value": 4.52, "curve": [3.636, 4.64, 3.878, 0.29]}, {"time": 4.1667, "value": 0.29}]}, "bone38": {"rotate": [{"value": 0.29, "curve": [0.242, 0.29, 0.558, -5.85]}, {"time": 0.8, "value": -5.85, "curve": [1.022, -5.85, 1.234, 9.37]}, {"time": 1.4667, "value": 9.38, "curve": [1.633, 9.38, 1.8, -1.5]}, {"time": 1.9667, "value": -1.5, "curve": [2.078, -1.5, 2.189, 2.07]}, {"time": 2.3, "value": 2.07, "curve": [2.413, 2.07, 2.532, 0.02]}, {"time": 2.6333, "curve": [2.857, -0.05, 3.08, 4.46]}, {"time": 3.3, "value": 4.53, "curve": [3.636, 4.64, 3.878, 0.29]}, {"time": 4.1667, "value": 0.29}]}, "bone39": {"rotate": [{"value": 0.98, "curve": [0.259, 0.98, 0.674, -5.16]}, {"time": 0.9333, "value": -5.16, "curve": [1.156, -5.16, 1.367, 9.33]}, {"time": 1.6, "value": 9.34, "curve": [1.767, 9.34, 1.933, -0.81]}, {"time": 2.1, "value": -0.81, "curve": [2.211, -0.81, 2.322, 2.75]}, {"time": 2.4333, "value": 2.75, "curve": [2.547, 2.75, 2.665, 0.7]}, {"time": 2.7667, "value": 0.68, "curve": [2.991, 0.62, 3.214, 5.14]}, {"time": 3.4333, "value": 5.21, "curve": [3.769, 5.33, 3.922, 0.98]}, {"time": 4.1667, "value": 0.98}]}, "bone40": {"rotate": [{"curve": [0.222, 0, 0.444, -6.13]}, {"time": 0.6667, "value": -6.13, "curve": [0.889, -6.13, 1.101, 8.98]}, {"time": 1.3333, "value": 8.99, "curve": [1.5, 8.99, 1.667, -1.79]}, {"time": 1.8333, "value": -1.79, "curve": [1.944, -1.79, 2.056, 1.78]}, {"time": 2.1667, "value": 1.78, "curve": [2.279, 1.78, 2.399, 0.99]}, {"time": 2.5, "value": 0.98, "curve": [2.724, 0.96, 2.946, 10.73]}, {"time": 3.1667, "value": 10.73, "curve": [3.5, 10.73, 3.833, 0]}, {"time": 4.1667}]}, "bone41": {"rotate": [{"value": 0.17, "curve": [0.242, 0.17, 0.558, -5.96]}, {"time": 0.8, "value": -5.96, "curve": [1.022, -5.96, 1.234, 8.83]}, {"time": 1.4667, "value": 8.84, "curve": [1.633, 8.84, 1.8, -1.62]}, {"time": 1.9667, "value": -1.62, "curve": [2.078, -1.62, 2.189, 1.95]}, {"time": 2.3, "value": 1.95, "curve": [2.413, 1.95, 2.532, -0.1]}, {"time": 2.6333, "value": -0.13, "curve": [2.857, -0.18, 3.08, 4.33]}, {"time": 3.3, "value": 4.41, "curve": [3.636, 4.52, 3.878, 0.17]}, {"time": 4.1667, "value": 0.17}]}, "bone42": {"rotate": [{"value": 0.29, "curve": [0.242, 0.29, 0.558, -5.85]}, {"time": 0.8, "value": -5.85, "curve": [1.022, -5.85, 1.234, 9.37]}, {"time": 1.4667, "value": 9.38, "curve": [1.633, 9.38, 1.8, -1.5]}, {"time": 1.9667, "value": -1.5, "curve": [2.078, -1.5, 2.189, 2.07]}, {"time": 2.3, "value": 2.07, "curve": [2.413, 2.07, 2.532, 0.02]}, {"time": 2.6333, "curve": [2.857, -0.05, 3.08, 4.46]}, {"time": 3.3, "value": 4.53, "curve": [3.636, 4.64, 3.878, 0.29]}, {"time": 4.1667, "value": 0.29}]}, "bone43": {"rotate": [{"value": 0.98, "curve": [0.259, 0.98, 0.674, -5.16]}, {"time": 0.9333, "value": -5.16, "curve": [1.156, -5.16, 1.367, 9.33]}, {"time": 1.6, "value": 9.34, "curve": [1.767, 9.34, 1.933, -0.81]}, {"time": 2.1, "value": -0.81, "curve": [2.211, -0.81, 2.322, 2.75]}, {"time": 2.4333, "value": 2.75, "curve": [2.547, 2.75, 2.665, 0.7]}, {"time": 2.7667, "value": 0.68, "curve": [2.991, 0.62, 3.214, 5.14]}, {"time": 3.4333, "value": 5.21, "curve": [3.769, 5.33, 3.922, 0.98]}, {"time": 4.1667, "value": 0.98}]}, "bone44": {"rotate": [{"time": 2.1667, "curve": [2.5, 0, 2.833, -1.92]}, {"time": 3.1667, "value": -1.92, "curve": [3.5, -1.92, 3.833, 0]}, {"time": 4.1667}]}, "bone46": {"rotate": [{"value": -2.93, "curve": [0.222, -2.93, 0.444, -2.13]}, {"time": 0.6667, "value": -2.46, "curve": [1.065, -3.04, 1, -0.85]}, {"time": 1.3333, "value": 1.23, "curve": [1.578, 2.76, 1.383, -5.72]}, {"time": 1.8333, "value": -4.46, "curve": [1.857, -4.4, 2.056, -2.86]}, {"time": 2.1667, "value": -2.86, "curve": [2.268, -2.86, 2.369, -3.35]}, {"time": 2.4667, "value": -2.73, "curve": [2.702, -1.31, 2.934, 2.87]}, {"time": 3.1667, "value": 2.87, "curve": [3.5, 2.87, 3.833, -2.93]}, {"time": 4.1667, "value": -2.93}]}, "bone47": {"rotate": [{"value": 0.23, "curve": [0.238, 0.23, 0.529, -2.12]}, {"time": 0.7667, "value": -2.46, "curve": [1.165, -3.01, 1.1, 0.81]}, {"time": 1.4333, "value": 1.18, "curve": [1.678, 1.46, 1.483, -1.53]}, {"time": 1.9333, "value": -1.3, "curve": [1.957, -1.29, 2.156, 0.24]}, {"time": 2.2667, "value": 0.24, "curve": [2.369, 0.24, 2.471, -0.67]}, {"time": 2.5667, "value": -0.67, "curve": [2.803, -0.67, 3.038, 0.39]}, {"time": 3.2667, "value": 0.57, "curve": [3.604, 0.83, 3.867, 0.23]}, {"time": 4.1667, "value": 0.23}]}, "bone48": {"rotate": [{"value": 0.78, "curve": [0.251, 0.78, 0.615, -2.11]}, {"time": 0.8667, "value": -2.46, "curve": [1.265, -3, 1.2, 1.09]}, {"time": 1.5333, "value": 1.16, "curve": [1.778, 1.21, 1.583, -0.79]}, {"time": 2.0333, "value": -0.75, "curve": [2.057, -0.75, 2.256, 0.78]}, {"time": 2.3667, "value": 0.78, "curve": [2.469, 0.78, 2.571, -0.13]}, {"time": 2.6667, "value": -0.13, "curve": [2.903, -0.13, 3.138, 0.94]}, {"time": 3.3667, "value": 1.12, "curve": [3.704, 1.38, 3.9, 0.78]}, {"time": 4.1667, "value": 0.78}]}, "bone49": {"rotate": [{"value": 0.08, "curve": [0.238, 0.08, 0.529, -2.13]}, {"time": 0.7667, "value": -2.46, "curve": [1.165, -3.01, 1.1, 0.72]}, {"time": 1.4333, "value": 1.18, "curve": [1.678, 1.51, 1.483, -1.73]}, {"time": 1.9333, "value": -1.46, "curve": [1.957, -1.44, 2.156, 0.09]}, {"time": 2.2667, "value": 0.09, "curve": [2.369, 0.09, 2.471, -0.82]}, {"time": 2.5667, "value": -0.82, "curve": [2.803, -0.82, 3.038, 0.24]}, {"time": 3.2667, "value": 0.42, "curve": [3.604, 0.68, 3.867, 0.08]}, {"time": 4.1667, "value": 0.08}]}, "bone50": {"rotate": [{"value": 0.26, "curve": [0.251, 0.26, 0.615, -3.69]}, {"time": 0.8667, "value": -4.02, "curve": [1.265, -4.55, 1.2, 0.8]}, {"time": 1.5333, "value": 1.13, "curve": [1.778, 1.37, 1.583, -1.47]}, {"time": 2.0333, "value": -1.27, "curve": [2.057, -1.26, 2.256, 0.28]}, {"time": 2.3667, "value": 0.28, "curve": [2.469, 0.28, 2.571, -0.64]}, {"time": 2.6667, "value": -0.64, "curve": [2.903, -0.64, 3.138, 0.43]}, {"time": 3.3667, "value": 0.61, "curve": [3.704, 0.86, 3.9, 0.26]}, {"time": 4.1667, "value": 0.26}]}, "bone51": {"rotate": [{"value": 0.53, "curve": [0.263, 0.53, 0.704, -2.76]}, {"time": 0.9667, "value": -2.46, "curve": [1.365, -1.99, 1.3, 0.17]}, {"time": 1.6333, "value": -0.53, "curve": [1.878, -1.04, 1.683, -0.59]}, {"time": 2.1333, "value": -1.01, "curve": [2.157, -1.03, 2.356, 0.5]}, {"time": 2.4667, "value": 0.5, "curve": [2.569, 0.5, 2.671, -0.41]}, {"time": 2.7667, "value": -0.4, "curve": [3.003, -0.4, 3.238, 0.67]}, {"time": 3.4667, "value": 0.85, "curve": [3.804, 1.12, 3.933, 0.53]}, {"time": 4.1667, "value": 0.53}]}, "bone54": {"rotate": [{"value": 10.21, "curve": [0.222, 10.21, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, 3.63]}, {"time": 1.3333, "curve": [1.578, -4, 1.383, 13.5]}, {"time": 1.8333, "value": 10.21, "curve": [1.857, 10.04, 2.056, 10.04]}, {"time": 2.1667, "value": 10.04, "curve": [2.5, 10.04, 2.833, 10.21]}, {"time": 3.1667, "value": 10.21}]}, "bone57": {"rotate": [{"value": 1.7, "curve": [0.222, 1.7, 0.444, 1.1]}, {"time": 0.6667, "value": 1.8, "curve": [1.065, 3.04, 1, -3.46]}, {"time": 1.3333, "value": -5.49, "curve": [1.578, -6.98, 1.383, 4.65]}, {"time": 1.8333, "value": 3.42, "curve": [1.857, 3.36, 2.056, 0.73]}, {"time": 2.1667, "value": 0.73, "curve": [2.279, 0.73, 2.391, 1.66]}, {"time": 2.5, "value": 1.34, "curve": [2.724, 0.7, 2.946, -4.18]}, {"time": 3.1667, "value": -4.18, "curve": [3.5, -4.18, 3.833, 1.7]}, {"time": 4.1667, "value": 1.7}]}, "bone58": {"rotate": [{"value": -0.15, "curve": [0.238, -0.15, 0.529, 1.09]}, {"time": 0.7667, "value": 1.8, "curve": [1.165, 2.97, 1.1, -4.4]}, {"time": 1.4333, "value": -5.38, "curve": [1.678, -6.09, 1.483, 2.16]}, {"time": 1.9333, "value": 1.57, "curve": [1.957, 1.54, 2.156, -1.08]}, {"time": 2.2667, "value": -1.08, "curve": [2.38, -1.08, 2.493, 0.11]}, {"time": 2.6, "value": 0.16, "curve": [2.824, 0.25, 3.047, -2.53]}, {"time": 3.2667, "value": -2.6, "curve": [3.603, -2.7, 3.867, -0.15]}, {"time": 4.1667, "value": -0.15}]}, "bone59": {"rotate": [{"value": -0.5, "curve": [0.251, -0.5, 0.615, 1.09]}, {"time": 0.8667, "value": 1.8, "curve": [1.265, 2.91, 1.2, -4.54]}, {"time": 1.5333, "value": -5.27, "curve": [1.778, -5.8, 1.583, 1.66]}, {"time": 2.0333, "value": 1.22, "curve": [2.057, 1.2, 2.256, -1.43]}, {"time": 2.3667, "value": -1.43, "curve": [2.48, -1.43, 2.593, -0.23]}, {"time": 2.7, "value": -0.19, "curve": [2.924, -0.1, 3.147, -2.88]}, {"time": 3.3667, "value": -2.95, "curve": [3.703, -3.06, 3.9, -0.5]}, {"time": 4.1667, "value": -0.5}]}, "bone60": {"rotate": [{"value": -0.27, "curve": [0.222, -0.27, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -0.1]}, {"time": 1.3333, "curve": [1.578, 0.11, 1.383, -0.36]}, {"time": 1.8333, "value": -0.27, "curve": [1.857, -0.27, 2.056, -0.27]}, {"time": 2.1667, "value": -0.27, "curve": [2.5, -0.27, 2.833, -0.27]}, {"time": 3.1667, "value": -0.27}]}, "bone61": {"rotate": [{"value": -0.27, "curve": [0.222, -0.27, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -0.1]}, {"time": 1.3333, "curve": [1.578, 0.11, 1.383, -0.36]}, {"time": 1.8333, "value": -0.27, "curve": [1.857, -0.27, 2.056, -0.27]}, {"time": 2.1667, "value": -0.27, "curve": [2.5, -0.27, 2.833, -0.27]}, {"time": 3.1667, "value": -0.27}]}, "bone62": {"rotate": [{"value": -0.27, "curve": [0.222, -0.27, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -0.1]}, {"time": 1.3333, "curve": [1.578, 0.11, 1.383, -0.36]}, {"time": 1.8333, "value": -0.27, "curve": [1.857, -0.27, 2.056, -0.27]}, {"time": 2.1667, "value": -0.27, "curve": [2.5, -0.27, 2.833, 2.09]}, {"time": 3.1667, "value": 2.09, "curve": [3.5, 2.09, 3.833, -0.27]}, {"time": 4.1667, "value": -0.27}]}, "bone63": {"rotate": [{"value": -3.06, "curve": [0.222, -3.06, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -1.09]}, {"time": 1.3333, "curve": [1.578, 1.2, 1.383, -4.04]}, {"time": 1.8333, "value": -3.06, "curve": [1.857, -3, 2.056, -3]}, {"time": 2.1667, "value": -3, "curve": [2.5, -3, 2.833, 0.07]}, {"time": 3.1667, "value": 0.07, "curve": [3.5, 0.07, 3.833, -3.06]}, {"time": 4.1667, "value": -3.06}], "scale": [{"x": 1.014, "y": 1.014, "curve": [0.222, 1.014, 0.444, 1, 0.222, 1.014, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.005, 0.741, 1, 1.111, 1.005]}, {"time": 1.3333, "curve": [1.578, 0.994, 1.383, 1.019, 1.578, 0.994, 1.383, 1.019]}, {"time": 1.8333, "x": 1.014, "y": 1.014, "curve": [1.857, 1.014, 2.056, 1.014, 1.857, 1.014, 2.056, 1.014]}, {"time": 2.1667, "x": 1.014, "y": 1.014, "curve": [2.5, 1.014, 2.833, 1.014, 2.5, 1.014, 2.833, 1.014]}, {"time": 3.1667, "x": 1.014, "y": 1.014}]}, "bone64": {"rotate": [{"value": -2.6, "curve": [0.222, -2.6, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -0.92]}, {"time": 1.3333, "curve": [1.578, 1.02, 1.383, -3.44]}, {"time": 1.8333, "value": -2.6, "curve": [1.857, -2.55, 2.056, -2.55]}, {"time": 2.1667, "value": -2.55, "curve": [2.5, -2.55, 2.833, 0.53]}, {"time": 3.1667, "value": 0.53, "curve": [3.5, 0.53, 3.833, -2.6]}, {"time": 4.1667, "value": -2.6}], "scale": [{"x": 1.014, "y": 1.014, "curve": [0.222, 1.014, 0.444, 1, 0.222, 1.014, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.005, 0.741, 1, 1.111, 1.005]}, {"time": 1.3333, "curve": [1.578, 0.994, 1.383, 1.019, 1.578, 0.994, 1.383, 1.019]}, {"time": 1.8333, "x": 1.014, "y": 1.014, "curve": [1.857, 1.014, 2.056, 1.014, 1.857, 1.014, 2.056, 1.014]}, {"time": 2.1667, "x": 1.014, "y": 1.014, "curve": [2.5, 1.014, 2.833, 1.014, 2.5, 1.014, 2.833, 1.014]}, {"time": 3.1667, "x": 1.014, "y": 1.014}]}, "bone65": {"rotate": [{"value": -2.6, "curve": [0.222, -2.6, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -0.92]}, {"time": 1.3333, "curve": [1.578, 1.02, 1.383, -3.44]}, {"time": 1.8333, "value": -2.6, "curve": [1.857, -2.55, 2.056, -2.55]}, {"time": 2.1667, "value": -2.55, "curve": [2.5, -2.55, 2.833, 0.53]}, {"time": 3.1667, "value": 0.53, "curve": [3.5, 0.53, 3.833, -2.6]}, {"time": 4.1667, "value": -2.6}], "scale": [{"x": 1.014, "y": 1.014, "curve": [0.222, 1.014, 0.444, 1, 0.222, 1.014, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.005, 0.741, 1, 1.111, 1.005]}, {"time": 1.3333, "curve": [1.578, 0.994, 1.383, 1.019, 1.578, 0.994, 1.383, 1.019]}, {"time": 1.8333, "x": 1.014, "y": 1.014, "curve": [1.857, 1.014, 2.056, 1.014, 1.857, 1.014, 2.056, 1.014]}, {"time": 2.1667, "x": 1.014, "y": 1.014, "curve": [2.5, 1.014, 2.833, 1.014, 2.5, 1.014, 2.833, 1.014]}, {"time": 3.1667, "x": 1.014, "y": 1.014}]}, "bone66": {"rotate": [{"value": -2.6, "curve": [0.222, -2.6, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -0.92]}, {"time": 1.3333, "curve": [1.578, 1.02, 1.383, -3.44]}, {"time": 1.8333, "value": -2.6, "curve": [1.857, -2.55, 2.056, -2.55]}, {"time": 2.1667, "value": -2.55, "curve": [2.5, -2.55, 2.833, 0.53]}, {"time": 3.1667, "value": 0.53, "curve": [3.5, 0.53, 3.833, -2.6]}, {"time": 4.1667, "value": -2.6}], "scale": [{"x": 1.014, "y": 1.014, "curve": [0.222, 1.014, 0.444, 1, 0.222, 1.014, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.005, 0.741, 1, 1.111, 1.005]}, {"time": 1.3333, "curve": [1.578, 0.994, 1.383, 1.019, 1.578, 0.994, 1.383, 1.019]}, {"time": 1.8333, "x": 1.014, "y": 1.014, "curve": [1.857, 1.014, 2.056, 1.014, 1.857, 1.014, 2.056, 1.014]}, {"time": 2.1667, "x": 1.014, "y": 1.014, "curve": [2.5, 1.014, 2.833, 1.014, 2.5, 1.014, 2.833, 1.014]}, {"time": 3.1667, "x": 1.014, "y": 1.014}]}, "bone67": {"rotate": [{"value": -1.74, "curve": [0.222, -1.74, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -0.62]}, {"time": 1.3333, "curve": [1.578, 0.68, 1.383, -2.3]}, {"time": 1.8333, "value": -1.74, "curve": [1.857, -1.71, 2.056, -1.71]}, {"time": 2.1667, "value": -1.71, "curve": [2.5, -1.71, 2.833, -1.74]}, {"time": 3.1667, "value": -1.74}], "scale": [{"time": 2.1667, "curve": [2.5, 1, 2.833, 1.076, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.076, "curve": [3.5, 1.076, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone68": {"scale": [{"time": 2.1667, "curve": [2.5, 1, 2.833, 1.076, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.076, "curve": [3.5, 1.076, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone69": {"scale": [{"time": 2.1667, "curve": [2.5, 1, 2.833, 1.076, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.076, "curve": [3.5, 1.076, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone70": {"rotate": [{"value": -3.4, "curve": [0.222, -3.4, 0.444, -0.74]}, {"time": 0.6667, "value": -0.74, "curve": [0.741, -0.74, 1.111, -1.21]}, {"time": 1.3333, "curve": [1.578, 1.33, 1.383, -4.49]}, {"time": 1.8333, "value": -3.4, "curve": [1.857, -3.34, 2.056, -3.34]}, {"time": 2.1667, "value": -3.34, "curve": [2.5, -3.34, 2.833, -0.53]}, {"time": 3.1667, "value": -0.53, "curve": [3.5, -0.53, 3.833, -3.4]}, {"time": 4.1667, "value": -3.4}], "scale": [{"x": 1.025, "curve": [0.222, 1.025, 0.444, 1, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.009, 0.741, 1, 1.306, 1]}, {"time": 1.3333, "curve": [1.578, 0.99, 1.383, 1.033, 1.434, 1, 1.767, 1]}, {"time": 1.8333, "x": 1.025, "curve": [1.857, 1.025, 2.056, 1.025, 1.944, 1, 2.056, 1]}, {"time": 2.1667, "x": 1.025, "curve": [2.5, 1.025, 2.833, 1.025, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.025}]}, "bone71": {"rotate": [{"value": -3.45, "curve": [0.222, -3.45, 0.444, -0.74]}, {"time": 0.6667, "value": -0.74, "curve": [0.741, -0.74, 1.111, -1.23]}, {"time": 1.3333, "curve": [1.578, 1.35, 1.383, -4.57]}, {"time": 1.8333, "value": -3.45, "curve": [1.857, -3.4, 2.056, -3.4]}, {"time": 2.1667, "value": -3.4, "curve": [2.5, -3.4, 2.833, -0.58]}, {"time": 3.1667, "value": -0.58, "curve": [3.5, -0.58, 3.833, -3.45]}, {"time": 4.1667, "value": -3.45}], "scale": [{"x": 1.025, "curve": [0.222, 1.025, 0.444, 1, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.009, 0.741, 1, 1.306, 1]}, {"time": 1.3333, "curve": [1.578, 0.99, 1.383, 1.033, 1.434, 1, 1.767, 1]}, {"time": 1.8333, "x": 1.025, "curve": [1.857, 1.025, 2.056, 1.025, 1.944, 1, 2.056, 1]}, {"time": 2.1667, "x": 1.025, "curve": [2.5, 1.025, 2.833, 1.025, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.025}]}, "bone72": {"rotate": [{"value": -2.88, "curve": [0.222, -2.88, 0.444, -0.74]}, {"time": 0.6667, "value": -0.74, "curve": [0.741, -0.74, 1.111, -1.03]}, {"time": 1.3333, "curve": [1.578, 1.13, 1.383, -3.81]}, {"time": 1.8333, "value": -2.88, "curve": [1.857, -2.83, 2.056, -2.83]}, {"time": 2.1667, "value": -2.83, "curve": [2.5, -2.83, 2.833, -0.01]}, {"time": 3.1667, "value": -0.01, "curve": [3.5, -0.01, 3.833, -2.88]}, {"time": 4.1667, "value": -2.88}], "scale": [{"x": 1.025, "curve": [0.222, 1.025, 0.444, 1, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.009, 0.741, 1, 1.306, 1]}, {"time": 1.3333, "curve": [1.578, 0.99, 1.383, 1.033, 1.434, 1, 1.767, 1]}, {"time": 1.8333, "x": 1.025, "curve": [1.857, 1.025, 2.056, 1.025, 1.944, 1, 2.056, 1]}, {"time": 2.1667, "x": 1.025, "curve": [2.5, 1.025, 2.833, 1.025, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.025}]}, "bone73": {"rotate": [{"value": -2.88, "curve": [0.222, -2.88, 0.444, -0.74]}, {"time": 0.6667, "value": -0.74, "curve": [0.741, -0.74, 1.111, -1.03]}, {"time": 1.3333, "curve": [1.578, 1.13, 1.383, -3.81]}, {"time": 1.8333, "value": -2.88, "curve": [1.857, -2.83, 2.056, -2.83]}, {"time": 2.1667, "value": -2.83, "curve": [2.5, -2.83, 2.833, -0.01]}, {"time": 3.1667, "value": -0.01, "curve": [3.5, -0.01, 3.833, -2.88]}, {"time": 4.1667, "value": -2.88}], "scale": [{"x": 1.025, "curve": [0.222, 1.025, 0.444, 1, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.009, 0.741, 1, 1.306, 1]}, {"time": 1.3333, "curve": [1.578, 0.99, 1.383, 1.033, 1.434, 1, 1.767, 1]}, {"time": 1.8333, "x": 1.025, "curve": [1.857, 1.025, 2.056, 1.025, 1.944, 1, 2.056, 1]}, {"time": 2.1667, "x": 1.025, "curve": [2.5, 1.025, 2.833, 1.025, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.025}]}, "bone74": {"rotate": [{"value": -2.88, "curve": [0.222, -2.88, 0.444, -0.74]}, {"time": 0.6667, "value": -0.74, "curve": [0.741, -0.74, 1.111, -1.03]}, {"time": 1.3333, "curve": [1.578, 1.13, 1.383, -3.81]}, {"time": 1.8333, "value": -2.88, "curve": [1.857, -2.83, 2.056, -2.83]}, {"time": 2.1667, "value": -2.83, "curve": [2.5, -2.83, 2.833, -0.01]}, {"time": 3.1667, "value": -0.01, "curve": [3.5, -0.01, 3.833, -2.88]}, {"time": 4.1667, "value": -2.88}], "scale": [{"x": 1.025, "curve": [0.222, 1.025, 0.444, 1, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.009, 0.741, 1, 1.306, 1]}, {"time": 1.3333, "curve": [1.578, 0.99, 1.383, 1.033, 1.434, 1, 1.767, 1]}, {"time": 1.8333, "x": 1.025, "curve": [1.857, 1.025, 2.056, 1.025, 1.944, 1, 2.056, 1]}, {"time": 2.1667, "x": 1.025, "curve": [2.5, 1.025, 2.833, 1.025, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.025}]}, "bone75": {"rotate": [{"time": 2.1667, "curve": [2.5, 0, 2.833, -1.01]}, {"time": 3.1667, "value": -1.01, "curve": [3.5, -1.01, 3.833, 0]}, {"time": 4.1667}], "scale": [{"time": 2.1667, "curve": [2.5, 1, 2.833, 1.034, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.034, "curve": [3.5, 1.034, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone76": {"rotate": [{"time": 2.1667, "curve": [2.5, 0, 2.833, -1.01]}, {"time": 3.1667, "value": -1.01, "curve": [3.5, -1.01, 3.833, 0]}, {"time": 4.1667}], "scale": [{"time": 2.1667, "curve": [2.5, 1, 2.833, 1.034, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.034, "curve": [3.5, 1.034, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone77": {"rotate": [{"time": 2.1667, "curve": [2.5, 0, 2.833, -1.01]}, {"time": 3.1667, "value": -1.01, "curve": [3.5, -1.01, 3.833, 0]}, {"time": 4.1667}], "scale": [{"time": 2.1667, "curve": [2.5, 1, 2.833, 1.034, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.034, "curve": [3.5, 1.034, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone78": {"rotate": [{"time": 2.1667, "curve": [2.5, 0, 2.833, 3.47]}, {"time": 3.1667, "value": 3.47, "curve": [3.5, 3.47, 3.833, 0]}, {"time": 4.1667}], "scale": [{"time": 2.1667, "curve": [2.5, 1, 2.833, 1.034, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.034, "curve": [3.5, 1.034, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone79": {"rotate": [{"time": 2.1667, "curve": [2.5, 0, 2.833, -2.27]}, {"time": 3.1667, "value": -2.27, "curve": [3.5, -2.27, 3.833, 0]}, {"time": 4.1667}], "scale": [{"time": 2.1667, "curve": [2.5, 1, 2.833, 1.034, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.034, "curve": [3.5, 1.034, 3.833, 1, 3.5, 1, 3.833, 1]}, {"time": 4.1667}]}, "bone80": {"rotate": [{"value": 0.91, "curve": [0.222, 0.91, 0.444, -0.1]}, {"time": 0.6667, "curve": [1.065, 0.18, 1, 0.34]}, {"time": 1.3333, "value": -0.31, "curve": [1.578, -0.79, 1.383, 1.3]}, {"time": 1.8333, "value": 0.91, "curve": [1.857, 0.89, 2.056, 0.89]}, {"time": 2.1667, "value": 0.89, "curve": [2.5, 0.89, 2.833, 0.91]}, {"time": 3.1667, "value": 0.91}], "translate": [{"time": 2.1667, "curve": [2.5, 0, 2.833, -3.9, 2.5, 0, 2.833, 2.91]}, {"time": 3.1667, "x": -3.9, "y": 2.91, "curve": [3.5, -3.9, 3.833, 0, 3.5, 2.91, 3.833, 0]}, {"time": 4.1667}]}, "bone81": {"rotate": [{"value": 0.91, "curve": [0.222, 0.91, 0.444, -0.1]}, {"time": 0.6667, "curve": [1.065, 0.18, 1, 0.34]}, {"time": 1.3333, "value": -0.31, "curve": [1.578, -0.79, 1.383, 1.3]}, {"time": 1.8333, "value": 0.91, "curve": [1.857, 0.89, 2.056, 0.89]}, {"time": 2.1667, "value": 0.89, "curve": [2.5, 0.89, 2.833, 0.91]}, {"time": 3.1667, "value": 0.91}]}, "bone82": {"rotate": [{"value": 0.91, "curve": [0.222, 0.91, 0.444, -0.1]}, {"time": 0.6667, "curve": [1.065, 0.18, 1, 0.34]}, {"time": 1.3333, "value": -0.31, "curve": [1.578, -0.79, 1.383, 1.3]}, {"time": 1.8333, "value": 0.91, "curve": [1.857, 0.89, 2.056, 0.89]}, {"time": 2.1667, "value": 0.89, "curve": [2.5, 0.89, 2.833, 0.91]}, {"time": 3.1667, "value": 0.91}]}, "bone83": {"rotate": [{"value": 0.91, "curve": [0.222, 0.91, 0.444, -0.1]}, {"time": 0.6667, "curve": [1.065, 0.18, 1, 0.34]}, {"time": 1.3333, "value": -0.31, "curve": [1.578, -0.79, 1.383, 1.3]}, {"time": 1.8333, "value": 0.91, "curve": [1.857, 0.89, 2.056, 0.89]}, {"time": 2.1667, "value": 0.89, "curve": [2.5, 0.89, 2.833, 0.91]}, {"time": 3.1667, "value": 0.91}]}, "bone84": {"rotate": [{"value": 0.91, "curve": [0.222, 0.91, 0.444, -0.1]}, {"time": 0.6667, "curve": [1.065, 0.18, 1, 0.34]}, {"time": 1.3333, "value": -0.31, "curve": [1.578, -0.79, 1.383, 1.3]}, {"time": 1.8333, "value": 0.91, "curve": [1.857, 0.89, 2.056, 0.89]}, {"time": 2.1667, "value": 0.89, "curve": [2.5, 0.89, 2.833, 0.91]}, {"time": 3.1667, "value": 0.91}]}, "bone85": {"rotate": [{"time": 2.1667, "curve": [2.5, 0, 2.833, -1.37]}, {"time": 3.1667, "value": -1.37, "curve": [3.5, -1.37, 3.833, 0]}, {"time": 4.1667}]}, "bone86": {"rotate": [{"value": -0.19, "curve": [0.242, -0.19, 0.558, 2.41]}, {"time": 0.8, "value": 4.17, "curve": [1.198, 7.07, 1.133, -5.79]}, {"time": 1.4667, "value": -8.28, "curve": [1.711, -10.11, 1.517, 1.51]}, {"time": 1.9667, "value": 0.01, "curve": [1.99, -0.07, 2.189, -2.92]}, {"time": 2.3, "value": -2.92, "curve": [2.413, -2.92, 2.527, -0.96]}, {"time": 2.6333, "value": -0.83, "curve": [2.857, -0.57, 3.08, -2.45]}, {"time": 3.3, "value": -2.36, "curve": [3.636, -2.24, 3.878, -0.19]}, {"time": 4.1667, "value": -0.19}]}, "bone87": {"rotate": [{"value": -0.66, "curve": [0.259, -0.66, 0.674, 2.4]}, {"time": 0.9333, "value": 4.17, "curve": [1.331, 6.91, 1.267, -5.91]}, {"time": 1.6, "value": -8.01, "curve": [1.844, -9.55, 1.65, 0.8]}, {"time": 2.1, "value": -0.46, "curve": [2.124, -0.53, 2.322, -3.37]}, {"time": 2.4333, "value": -3.37, "curve": [2.547, -3.37, 2.66, -1.41]}, {"time": 2.7667, "value": -1.28, "curve": [2.991, -1.03, 3.214, -2.91]}, {"time": 3.4333, "value": -2.83, "curve": [3.769, -2.7, 3.922, -0.66]}, {"time": 4.1667, "value": -0.66}]}, "bone88": {"rotate": [{"value": 7.03, "curve": [0.222, 7.03, 0.444, 2.84]}, {"time": 0.6667, "value": 4.17, "curve": [1.065, 6.57, 1.001, -1.55]}, {"time": 1.3333, "value": -7.44, "curve": [1.578, -11.77, 1.383, 10.8]}, {"time": 1.8333, "value": 7.23, "curve": [1.857, 7.05, 2.056, 4.2]}, {"time": 2.1667, "value": 4.2, "curve": [2.279, 4.2, 2.391, 5.93]}, {"time": 2.5, "value": 5.74, "curve": [2.724, 5.35, 3.158, 0.87]}, {"time": 3.1667, "value": 0.88, "curve": [3.491, 1.16, 3.833, 7.03]}, {"time": 4.1667, "value": 7.03}]}, "bone89": {"rotate": [{"value": -0.19, "curve": [0.242, -0.19, 0.558, 2.62]}, {"time": 0.8, "value": 4.17, "curve": [1.198, 6.73, 1.133, -5.53]}, {"time": 1.4667, "value": -7.71, "curve": [1.711, -9.32, 1.517, 1.33]}, {"time": 1.9667, "value": 0.01, "curve": [1.99, -0.06, 2.189, -2.91]}, {"time": 2.3, "value": -2.91, "curve": [2.413, -2.91, 2.527, -0.95]}, {"time": 2.6333, "value": -0.82, "curve": [2.857, -0.56, 3.08, -2.44]}, {"time": 3.3, "value": -2.36, "curve": [3.636, -2.23, 3.878, -0.19]}, {"time": 4.1667, "value": -0.19}]}, "bone90": {"rotate": [{"value": -0.19, "curve": [0.242, -0.19, 0.558, 2.41]}, {"time": 0.8, "value": 4.17, "curve": [1.198, 7.07, 1.133, -5.79]}, {"time": 1.4667, "value": -8.28, "curve": [1.711, -10.11, 1.517, 1.51]}, {"time": 1.9667, "value": 0.01, "curve": [1.99, -0.07, 2.189, -2.92]}, {"time": 2.3, "value": -2.92, "curve": [2.413, -2.92, 2.527, -0.96]}, {"time": 2.6333, "value": -0.83, "curve": [2.857, -0.57, 3.08, -2.45]}, {"time": 3.3, "value": -2.36, "curve": [3.636, -2.24, 3.878, -0.19]}, {"time": 4.1667, "value": -0.19}]}, "bone91": {"rotate": [{"value": -0.66, "curve": [0.259, -0.66, 0.674, 2.4]}, {"time": 0.9333, "value": 4.17, "curve": [1.331, 6.91, 1.267, -5.91]}, {"time": 1.6, "value": -8.01, "curve": [1.844, -9.55, 1.65, 0.8]}, {"time": 2.1, "value": -0.46, "curve": [2.124, -0.53, 2.322, -3.37]}, {"time": 2.4333, "value": -3.37, "curve": [2.547, -3.37, 2.66, -1.41]}, {"time": 2.7667, "value": -1.28, "curve": [2.991, -1.03, 3.214, -2.91]}, {"time": 3.4333, "value": -2.83, "curve": [3.769, -2.7, 3.922, -0.66]}, {"time": 4.1667, "value": -0.66}]}, "bone96": {"rotate": [{"value": -0.27, "curve": [0.222, -0.27, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -0.1]}, {"time": 1.3333, "curve": [1.578, 0.11, 1.383, -0.36]}, {"time": 1.8333, "value": -0.27, "curve": [1.857, -0.27, 2.056, -0.27]}, {"time": 2.1667, "value": -0.27, "curve": [2.5, -0.27, 2.833, 2.09]}, {"time": 3.1667, "value": 2.09, "curve": [3.5, 2.09, 3.833, -0.27]}, {"time": 4.1667, "value": -0.27}]}, "bone97": {"rotate": [{"value": 6.47, "curve": [0.222, 6.47, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, 2.3]}, {"time": 1.3333, "curve": [1.578, -2.53, 1.383, 8.56]}, {"time": 1.8333, "value": 6.47, "curve": [1.857, 6.37, 2.056, 6.37]}, {"time": 2.1667, "value": 6.37, "curve": [2.5, 6.37, 2.833, 2.09]}, {"time": 3.1667, "value": 2.09, "curve": [3.5, 2.09, 3.833, 6.47]}, {"time": 4.1667, "value": 6.47}]}, "bone98": {"rotate": [{"value": -0.27, "curve": [0.222, -0.27, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, -0.1]}, {"time": 1.3333, "curve": [1.578, 0.11, 1.383, -0.36]}, {"time": 1.8333, "value": -0.27, "curve": [1.857, -0.27, 2.056, -0.27]}, {"time": 2.1667, "value": -0.27, "curve": [2.5, -0.27, 2.833, 2.09]}, {"time": 3.1667, "value": 2.09, "curve": [3.5, 2.09, 3.833, -0.27]}, {"time": 4.1667, "value": -0.27}]}, "bone101": {"rotate": [{"value": -2.88, "curve": [0.222, -2.88, 0.444, -0.74]}, {"time": 0.6667, "value": -0.74, "curve": [0.741, -0.74, 1.111, -1.03]}, {"time": 1.3333, "curve": [1.578, 1.13, 1.383, -3.81]}, {"time": 1.8333, "value": -2.88, "curve": [1.857, -2.83, 2.056, -2.83]}, {"time": 2.1667, "value": -2.83, "curve": [2.5, -2.83, 2.833, -0.01]}, {"time": 3.1667, "value": -0.01, "curve": [3.5, -0.01, 3.833, -2.88]}, {"time": 4.1667, "value": -2.88}], "scale": [{"x": 1.025, "curve": [0.222, 1.025, 0.444, 1, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "curve": [0.741, 1, 1.111, 1.009, 0.741, 1, 1.306, 1]}, {"time": 1.3333, "curve": [1.578, 0.99, 1.383, 1.033, 1.434, 1, 1.767, 1]}, {"time": 1.8333, "x": 1.025, "curve": [1.857, 1.025, 2.056, 1.025, 1.944, 1, 2.056, 1]}, {"time": 2.1667, "x": 1.025, "curve": [2.5, 1.025, 2.833, 1.025, 2.5, 1, 2.833, 1]}, {"time": 3.1667, "x": 1.025}]}, "bone102": {"rotate": [{"value": 1.71, "curve": [0.222, 1.71, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, 0.61]}, {"time": 1.3333, "curve": [1.578, -0.67, 1.383, 2.26]}, {"time": 1.8333, "value": 1.71, "curve": [1.857, 1.68, 2.056, 1.68]}, {"time": 2.1667, "value": 1.68, "curve": [2.5, 1.68, 2.833, 1.71]}, {"time": 3.1667, "value": 1.71}]}, "bone103": {"rotate": [{"value": -1.88, "curve": [0.222, -1.88, 0.445, -1.71]}, {"time": 0.6667, "curve": [1.065, 3.06, 1, -3.4]}, {"time": 1.3333, "value": -5.15, "curve": [1.578, -6.43, 1.383, -0.82]}, {"time": 1.8333, "value": -1.88, "curve": [1.944, -2.14, 2.056, -3.32]}, {"time": 2.1667, "value": -3.32, "curve": [2.5, -3.32, 2.833, -6.55]}, {"time": 3.1667, "value": -6.55, "curve": [3.5, -6.55, 3.833, -1.88]}, {"time": 4.1667, "value": -1.88}]}, "bone104": {"rotate": [{"value": 0.54, "curve": [0.222, 0.54, 0.444, 0]}, {"time": 0.6667, "curve": [0.741, 0, 1.111, 0.19]}, {"time": 1.3333, "curve": [1.578, -0.21, 1.383, 0.72]}, {"time": 1.8333, "value": 0.54, "curve": [1.857, 0.53, 2.056, 0.53]}, {"time": 2.1667, "value": 0.53, "curve": [2.5, 0.53, 2.833, 0.54]}, {"time": 3.1667, "value": 0.54}]}, "bone105": {"rotate": [{"value": 6.12, "curve": [0.222, 6.12, 0.445, -1.99]}, {"time": 0.6667, "curve": [1.065, 3.56, 1.001, 0.46]}, {"time": 1.3333, "value": -5.99, "curve": [1.578, -10.73, 1.383, 10.03]}, {"time": 1.8333, "value": 6.12, "curve": [1.944, 5.16, 2.056, -1.43]}, {"time": 2.1667, "value": -1.43, "curve": [2.5, -1.43, 2.833, -3.86]}, {"time": 3.1667, "value": -3.86, "curve": [3.5, -3.86, 3.833, 6.12]}, {"time": 4.1667, "value": 6.12}]}, "3": {"rotate": [{"curve": [0.222, 0, 0.5, -41.46]}, {"time": 0.6667, "curve": [1.065, 99.13, 0.976, -71.26]}, {"time": 1.3333, "value": -166.59, "curve": [1.578, -231.78, 1.383, 53.71]}, {"time": 1.8333, "curve": [1.857, -2.81, 2.056, -2.81]}, {"time": 2.1667, "value": -2.81, "curve": [2.5, -2.81, 2.833, 0]}, {"time": 3.1667}], "translate": [{"curve": [0.222, 0, 0.49, -38.13, 0.222, 0, 0.506, 43.34]}, {"time": 0.6667, "curve": [1.065, 86.12, 0.979, -62.7, 1.065, -107.57, 0.974, 76.75]}, {"time": 1.3333, "x": -144.72, "y": 180.79, "curve": [1.578, -201.35, 1.383, 46.66, 1.578, 251.53, 1.383, -58.28]}, {"time": 1.8333, "curve": [1.857, -2.44, 2.056, -2.44, 1.857, 3.05, 2.056, 3.05]}, {"time": 2.1667, "x": -2.44, "y": 3.05, "curve": [2.5, -2.44, 2.833, 0, 2.5, 3.05, 2.833, 0]}, {"time": 3.1667}]}, "bone106": {"rotate": [{"value": 1.12, "curve": [0.222, 1.12, 0.445, -3.18]}, {"time": 0.6667, "curve": [1.065, 5.71, 1, -3.87]}, {"time": 1.3333, "value": -9.59, "curve": [1.578, -13.79, 1.383, 4.58]}, {"time": 1.8333, "value": 1.12, "curve": [1.857, 0.94, 2.056, 0.94]}, {"time": 2.1667, "value": 0.94, "curve": [2.5, 0.94, 2.833, 1.12]}, {"time": 3.1667, "value": 1.12}]}, "bone107": {"rotate": [{"value": 2.11, "curve": [0.222, 2.11, 0.445, -1.93]}, {"time": 0.6667, "curve": [1.065, 3.45, 1, -1.58]}, {"time": 1.3333, "value": -5.81, "curve": [1.578, -8.9, 1.383, 4.66]}, {"time": 1.8333, "value": 2.11, "curve": [1.857, 1.97, 2.056, 1.97]}, {"time": 2.1667, "value": 1.97, "curve": [2.5, 1.97, 2.833, 1.62]}, {"time": 3.1667, "value": 1.62, "curve": [3.5, 1.62, 3.833, 2.11]}, {"time": 4.1667, "value": 2.11}]}, "bone108": {"rotate": [{"value": 3.75, "curve": [0.222, 3.75, 0.444, 0.39]}, {"time": 0.6667, "curve": [1.065, -0.7, 1, 2.55]}, {"time": 1.3333, "value": 1.17, "curve": [1.578, 0.16, 1.383, 4.59]}, {"time": 1.8333, "value": 3.75, "curve": [1.857, 3.71, 2.056, 3.71]}, {"time": 2.1667, "value": 3.71, "curve": [2.5, 3.71, 2.833, 3.26]}, {"time": 3.1667, "value": 3.26, "curve": [3.5, 3.26, 3.833, 3.75]}, {"time": 4.1667, "value": 3.75}]}, "bone109": {"rotate": [{"value": -0.02, "curve": [0.222, -0.02, 0.445, -2.13]}, {"time": 0.6667, "curve": [1.065, 3.81, 1, -3]}, {"time": 1.3333, "value": -6.41, "curve": [1.578, -8.91, 1.383, 2.04]}, {"time": 1.8333, "value": -0.02, "curve": [1.857, -0.13, 2.056, -0.13]}, {"time": 2.1667, "value": -0.13, "curve": [2.833, -0.13, 3.5, -0.02]}, {"time": 4.1667, "value": -0.02}]}, "bone110": {"rotate": [{"value": -0.41, "curve": [0.242, -0.41, 0.558, -4.61]}, {"time": 0.8, "value": -2.64, "curve": [1.198, 0.59, 1.133, -2.76]}, {"time": 1.4667, "value": -5.44, "curve": [1.711, -7.41, 1.517, 2.36]}, {"time": 1.9667, "value": 0.74, "curve": [1.99, 0.66, 2.189, -1.97]}, {"time": 2.3, "value": -1.97, "curve": [2.413, -1.97, 2.527, -0.82]}, {"time": 2.6333, "value": -0.75, "curve": [2.857, -0.6, 3.08, -2.64]}, {"time": 3.3, "value": -2.59, "curve": [3.636, -2.52, 3.878, -0.41]}, {"time": 4.1667, "value": -0.41}]}, "bone111": {"rotate": [{"value": -0.82, "curve": [0.259, -0.82, 0.674, -1.37]}, {"time": 0.9333, "value": -2.64, "curve": [1.331, -4.6, 1.267, 1.1]}, {"time": 1.6, "value": 3.29, "curve": [1.844, 4.91, 1.65, -0.99]}, {"time": 2.1, "value": 0.33, "curve": [2.124, 0.4, 2.322, -2.23]}, {"time": 2.4333, "value": -2.23, "curve": [2.547, -2.23, 2.66, -1.08]}, {"time": 2.7667, "value": -1.02, "curve": [2.991, -0.88, 3.214, -2.94]}, {"time": 3.4333, "value": -2.92, "curve": [3.769, -2.89, 3.922, -0.82]}, {"time": 4.1667, "value": -0.82}], "scale": [{"x": 1.117, "curve": [0.259, 1.117, 0.674, 1.041, 0.259, 1, 0.674, 1]}, {"time": 0.9333, "curve": [1.331, 0.938, 1.266, 1.111, 1.007, 1, 1.468, 1]}, {"time": 1.6, "x": 1.105, "curve": [1.844, 1.1, 1.65, 1.121, 1.701, 1, 2.034, 1]}, {"time": 2.1, "x": 1.117, "curve": [2.124, 1.117, 2.322, 1.117, 2.211, 1, 2.322, 1]}, {"time": 2.4333, "x": 1.117, "curve": [3.1, 1.117, 3.589, 1.117, 3.1, 1, 3.589, 1]}, {"time": 4.1667, "x": 1.117}]}, "bone112": {"rotate": [{"value": -1.29, "curve": [0.274, -1.29, 0.793, -1.41]}, {"time": 1.0667, "value": -2.64, "curve": [1.465, -4.44, 1.4, 0.72]}, {"time": 1.7333, "value": 3.02, "curve": [1.978, 4.71, 1.783, -1.53]}, {"time": 2.2333, "value": -0.14, "curve": [2.257, -0.07, 2.456, -2.7]}, {"time": 2.5667, "value": -2.7, "curve": [2.68, -2.7, 2.793, -1.55]}, {"time": 2.9, "value": -1.48, "curve": [3.124, -1.35, 3.347, -3.41]}, {"time": 3.5667, "value": -3.39, "curve": [3.903, -3.36, 3.967, -1.29]}, {"time": 4.1667, "value": -1.29}], "scale": [{"x": 1.117, "curve": [0.274, 1.117, 0.796, 1.116, 0.274, 1, 0.793, 1]}, {"time": 1.0667, "curve": [1.465, 0.829, 1.396, 1.196, 1.141, 1, 1.602, 1]}, {"time": 1.7333, "x": 1.288, "curve": [1.978, 1.355, 1.783, 1.062, 1.834, 1, 2.167, 1]}, {"time": 2.2333, "x": 1.117, "curve": [2.257, 1.12, 2.456, 1.12, 2.344, 1, 2.456, 1]}, {"time": 2.5667, "x": 1.12, "curve": [3.233, 1.12, 3.633, 1.117, 3.233, 1, 3.633, 1]}, {"time": 4.1667, "x": 1.117}]}, "bone113": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.444, 0.19]}, {"time": 0.6667, "curve": [1.065, -0.33, 1, 1.02]}, {"time": 1.3333, "value": 0.56, "curve": [1.578, 0.22, 1.383, 1.7]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.41, 2.056, 1.41]}, {"time": 2.1667, "value": 1.41, "curve": [2.5, 1.41, 2.833, 2.02]}, {"time": 3.1667, "value": 2.02, "curve": [3.5, 2.02, 3.833, 1.43]}, {"time": 4.1667, "value": 1.43}]}, "bone114": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.444, 0.19]}, {"time": 0.6667, "curve": [1.065, -0.33, 1, 1.02]}, {"time": 1.3333, "value": 0.56, "curve": [1.578, 0.22, 1.383, 1.7]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.41, 2.056, 1.41]}, {"time": 2.1667, "value": 1.41, "curve": [2.5, 1.41, 2.833, 2.02]}, {"time": 3.1667, "value": 2.02, "curve": [3.5, 2.02, 3.833, 1.43]}, {"time": 4.1667, "value": 1.43}]}, "bone115": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.444, 0.19]}, {"time": 0.6667, "curve": [1.065, -0.33, 1, 1.02]}, {"time": 1.3333, "value": 0.56, "curve": [1.578, 0.22, 1.383, 1.7]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.41, 2.056, 1.41]}, {"time": 2.1667, "value": 1.41, "curve": [2.5, 1.41, 2.833, 2.02]}, {"time": 3.1667, "value": 2.02, "curve": [3.5, 2.02, 3.833, 1.43]}, {"time": 4.1667, "value": 1.43}]}, "bone116": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.444, 0.19]}, {"time": 0.6667, "curve": [1.065, -0.33, 1, 1.02]}, {"time": 1.3333, "value": 0.56, "curve": [1.578, 0.22, 1.383, 1.7]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.41, 2.056, 1.41]}, {"time": 2.1667, "value": 1.41, "curve": [2.5, 1.41, 2.833, 2.02]}, {"time": 3.1667, "value": 2.02, "curve": [3.5, 2.02, 3.833, 1.43]}, {"time": 4.1667, "value": 1.43}]}, "bone117": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.444, 0.19]}, {"time": 0.6667, "curve": [1.065, -0.33, 1, 1.02]}, {"time": 1.3333, "value": 0.56, "curve": [1.578, 0.22, 1.383, 1.7]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.41, 2.056, 1.41]}, {"time": 2.1667, "value": 1.41, "curve": [2.5, 1.41, 2.833, 2.02]}, {"time": 3.1667, "value": 2.02, "curve": [3.5, 2.02, 3.833, 1.43]}, {"time": 4.1667, "value": 1.43}]}, "bone118": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.444, 0.19]}, {"time": 0.6667, "curve": [1.065, -0.33, 1, 1.02]}, {"time": 1.3333, "value": 0.56, "curve": [1.578, 0.22, 1.383, 1.7]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.41, 2.056, 1.41]}, {"time": 2.1667, "value": 1.41, "curve": [2.5, 1.41, 2.833, 2.02]}, {"time": 3.1667, "value": 2.02, "curve": [3.5, 2.02, 3.833, 1.43]}, {"time": 4.1667, "value": 1.43}]}, "bone119": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.444, 0.19]}, {"time": 0.6667, "curve": [1.065, -0.33, 1, 1.02]}, {"time": 1.3333, "value": 0.56, "curve": [1.578, 0.22, 1.383, 1.7]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.41, 2.056, 1.41]}, {"time": 2.1667, "value": 1.41, "curve": [2.5, 1.41, 2.833, 2.02]}, {"time": 3.1667, "value": 2.02, "curve": [3.5, 2.02, 3.833, 1.43]}, {"time": 4.1667, "value": 1.43}]}, "bone120": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.445, -1.61]}, {"time": 0.6667, "curve": [1.065, 2.89, 1, -1.5]}, {"time": 1.3333, "value": -4.86, "curve": [1.578, -7.32, 1.383, 3.46]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.33, 2.056, 1.33]}, {"time": 2.1667, "value": 1.33, "curve": [2.5, 1.33, 2.833, 1.43]}, {"time": 3.1667, "value": 1.43}]}, "bone121": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.445, -1.61]}, {"time": 0.6667, "curve": [1.065, 2.89, 1, -1.5]}, {"time": 1.3333, "value": -4.86, "curve": [1.578, -7.32, 1.383, 3.46]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.33, 2.056, 1.33]}, {"time": 2.1667, "value": 1.33, "curve": [2.5, 1.33, 2.833, 1.43]}, {"time": 3.1667, "value": 1.43}]}, "bone122": {"rotate": [{"value": 2.68, "curve": [0.222, 2.68, 0.445, -1.61]}, {"time": 0.6667, "curve": [1.065, 2.89, 1, -0.84]}, {"time": 1.3333, "value": -4.86, "curve": [1.578, -7.81, 1.383, 5.11]}, {"time": 1.8333, "value": 2.68, "curve": [1.857, 2.56, 2.056, 2.56]}, {"time": 2.1667, "value": 2.56, "curve": [2.5, 2.56, 2.833, 2.68]}, {"time": 3.1667, "value": 2.68}]}, "bone123": {"rotate": [{"value": 2.33, "curve": [0.222, 2.33, 0.445, -1.61]}, {"time": 0.6667, "curve": [1.065, 2.89, 1, -1.02]}, {"time": 1.3333, "value": -4.86, "curve": [1.578, -7.67, 1.383, 4.65]}, {"time": 1.8333, "value": 2.33, "curve": [1.857, 2.21, 2.056, 2.21]}, {"time": 2.1667, "value": 2.21, "curve": [2.5, 2.21, 2.833, 2.33]}, {"time": 3.1667, "value": 2.33}]}, "bone124": {"rotate": [{"value": -1.77, "curve": [0.222, -1.77, 0.445, -1.61]}, {"time": 0.6667, "curve": [1.065, 2.89, 1, -3.21]}, {"time": 1.3333, "value": -4.86, "curve": [1.578, -6.07, 1.383, -0.78]}, {"time": 1.8333, "value": -1.77, "curve": [1.857, -1.82, 2.056, -1.82]}, {"time": 2.1667, "value": -1.82, "curve": [2.5, -1.82, 2.833, -1.77]}, {"time": 3.1667, "value": -1.77}]}, "bone125": {"rotate": [{"value": 1.43, "curve": [0.222, 1.43, 0.445, -1.61]}, {"time": 0.6667, "curve": [1.065, 2.89, 1, -1.5]}, {"time": 1.3333, "value": -4.86, "curve": [1.578, -7.32, 1.383, 3.46]}, {"time": 1.8333, "value": 1.43, "curve": [1.857, 1.33, 2.056, 1.33]}, {"time": 2.1667, "value": 1.33, "curve": [2.5, 1.33, 2.833, 1.43]}, {"time": 3.1667, "value": 1.43}]}, "bone126": {"rotate": [{"value": 0.56, "curve": [0.222, 0.56, 0.444, 0.17]}, {"time": 0.6667, "curve": [1.065, -0.3, 1, 0.53]}, {"time": 1.3333, "value": 0.5, "curve": [1.578, 0.47, 1.383, 0.58]}, {"time": 1.8333, "value": 0.56, "curve": [1.857, 0.56, 2.056, 0.56]}, {"time": 2.1667, "value": 0.56, "curve": [2.5, 0.56, 2.833, -1.1]}, {"time": 3.1667, "value": -1.1, "curve": [3.5, -1.1, 3.833, 0.56]}, {"time": 4.1667, "value": 0.56}]}, "bone127": {"rotate": [{"value": 0.56, "curve": [0.222, 0.56, 0.444, 0.17]}, {"time": 0.6667, "curve": [1.065, -0.3, 1, 0.53]}, {"time": 1.3333, "value": 0.5, "curve": [1.578, 0.47, 1.383, 0.58]}, {"time": 1.8333, "value": 0.56, "curve": [1.857, 0.56, 2.056, 0.56]}, {"time": 2.1667, "value": 0.56, "curve": [2.5, 0.56, 2.833, -1.1]}, {"time": 3.1667, "value": -1.1, "curve": [3.5, -1.1, 3.833, 0.56]}, {"time": 4.1667, "value": 0.56}]}, "bone128": {"rotate": [{"value": 0.56, "curve": [0.222, 0.56, 0.444, 0.17]}, {"time": 0.6667, "curve": [1.065, -0.3, 1, 0.53]}, {"time": 1.3333, "value": 0.5, "curve": [1.578, 0.47, 1.383, 0.58]}, {"time": 1.8333, "value": 0.56, "curve": [1.857, 0.56, 2.056, 0.56]}, {"time": 2.1667, "value": 0.56, "curve": [2.5, 0.56, 2.833, -1.1]}, {"time": 3.1667, "value": -1.1, "curve": [3.5, -1.1, 3.833, 0.56]}, {"time": 4.1667, "value": 0.56}]}, "bone129": {"rotate": [{"value": 0.56, "curve": [0.222, 0.56, 0.444, 0.17]}, {"time": 0.6667, "curve": [1.065, -0.3, 1, 0.53]}, {"time": 1.3333, "value": 0.5, "curve": [1.578, 0.47, 1.383, 0.58]}, {"time": 1.8333, "value": 0.56, "curve": [1.857, 0.56, 2.056, 0.56]}, {"time": 2.1667, "value": 0.56, "curve": [2.5, 0.56, 2.833, -1.1]}, {"time": 3.1667, "value": -1.1, "curve": [3.5, -1.1, 3.833, 0.56]}, {"time": 4.1667, "value": 0.56}]}, "bone130": {"rotate": [{"value": 0.56, "curve": [0.222, 0.56, 0.444, 0.17]}, {"time": 0.6667, "curve": [1.065, -0.3, 1, 0.53]}, {"time": 1.3333, "value": 0.5, "curve": [1.578, 0.47, 1.383, 0.58]}, {"time": 1.8333, "value": 0.56, "curve": [1.857, 0.56, 2.056, 0.56]}, {"time": 2.1667, "value": 0.56, "curve": [2.5, 0.56, 2.833, -1.1]}, {"time": 3.1667, "value": -1.1, "curve": [3.5, -1.1, 3.833, 0.56]}, {"time": 4.1667, "value": 0.56}]}, "bone133": {"translate": [{"curve": [0.222, 0, 0.444, -2.5, 0.222, 0, 0.445, 19.89]}, {"time": 0.6667, "x": -2.38, "y": 15.04, "curve": [1.065, -2.17, 1, -0.05, 1.065, 6.32, 0.998, 14.84]}, {"time": 1.3333, "x": -0.42, "y": 15.61, "curve": [1.578, -0.69, 1.383, 0.6, 1.578, 16.17, 1.383, 13.71]}, {"time": 1.8333, "x": 0.38, "y": 14.17}, {"time": 2.1667, "x": 0.35, "y": 13.21, "curve": [2.5, 0.35, 2.833, -1.68, 2.5, 13.21, 2.833, 13.65]}, {"time": 3.1667, "x": -1.68, "y": 13.65}, {"time": 4.1667}]}}, "attachments": {"default": {"biyan": {"biyan": {"deform": [{"time": 0.4667, "vertices": [-0.45715, -0.04257, -0.45715, -0.04258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.96216, -0.06629, 1.9621, -0.06639, 10.9823, -1.9299, 10.9823, -1.93004, 17.9068, 2.08247, 17.90686, 2.08223, 18.57635, -0.51227, 18.57605, -0.51245, 17.92181, -0.68215, 17.92163, -0.6825, 10.8385, -0.78446, 10.83838, -0.78463, 0, 0, 0, 0, 0, 0, 13.73322, 1.44862, 13.73315, 1.44849, 13.54425, 1.14433, 13.54395, 1.14434, 12.72552, 0.25428, 12.72522, 0.25425, 11.34528, -0.11189, 11.34528, -0.11202, 8.76575, -0.97158, 8.76569, -0.97175, 4.5083, 0.1679, 4.50781, 0.1677, 3.11682, -0.96933, 3.11682, -0.96944]}, {"time": 0.6333, "offset": 44, "vertices": [-0.26837, 0.01943], "curve": "stepped"}, {"time": 0.7667, "offset": 44, "vertices": [-0.26837, 0.01943]}, {"time": 0.8667, "vertices": [-0.45715, -0.04257, -0.45715, -0.04258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.96216, -0.06629, 1.9621, -0.06639, 10.9823, -1.9299, 10.9823, -1.93004, 17.9068, 2.08247, 17.90686, 2.08223, 18.57635, -0.51227, 18.57605, -0.51245, 17.92181, -0.68215, 17.92163, -0.6825, 10.8385, -0.78446, 10.83838, -0.78463, 0, 0, 0, 0, 0, 0, 13.73322, 1.44862, 13.73315, 1.44849, 13.54425, 1.14433, 13.54395, 1.14434, 12.72552, 0.25428, 12.72522, 0.25425, 11.34528, -0.11189, 11.34528, -0.11202, 8.76575, -0.97158, 8.76569, -0.97175, 4.5083, 0.1679, 4.50781, 0.1677, 3.11682, -0.96933, 3.11682, -0.96944], "curve": "stepped"}, {"time": 3.2, "vertices": [-0.45715, -0.04257, -0.45715, -0.04258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.96216, -0.06629, 1.9621, -0.06639, 10.9823, -1.9299, 10.9823, -1.93004, 17.9068, 2.08247, 17.90686, 2.08223, 18.57635, -0.51227, 18.57605, -0.51245, 17.92181, -0.68215, 17.92163, -0.6825, 10.8385, -0.78446, 10.83838, -0.78463, 0, 0, 0, 0, 0, 0, 13.73322, 1.44862, 13.73315, 1.44849, 13.54425, 1.14433, 13.54395, 1.14434, 12.72552, 0.25428, 12.72522, 0.25425, 11.34528, -0.11189, 11.34528, -0.11202, 8.76575, -0.97158, 8.76569, -0.97175, 4.5083, 0.1679, 4.50781, 0.1677, 3.11682, -0.96933, 3.11682, -0.96944]}, {"time": 3.3667, "offset": 44, "vertices": [-0.26837, 0.01943], "curve": "stepped"}, {"time": 3.5, "offset": 44, "vertices": [-0.26837, 0.01943]}, {"time": 3.6, "vertices": [-0.45715, -0.04257, -0.45715, -0.04258, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.96216, -0.06629, 1.9621, -0.06639, 10.9823, -1.9299, 10.9823, -1.93004, 17.9068, 2.08247, 17.90686, 2.08223, 18.57635, -0.51227, 18.57605, -0.51245, 17.92181, -0.68215, 17.92163, -0.6825, 10.8385, -0.78446, 10.83838, -0.78463, 0, 0, 0, 0, 0, 0, 13.73322, 1.44862, 13.73315, 1.44849, 13.54425, 1.14433, 13.54395, 1.14434, 12.72552, 0.25428, 12.72522, 0.25425, 11.34528, -0.11189, 11.34528, -0.11202, 8.76575, -0.97158, 8.76569, -0.97175, 4.5083, 0.1679, 4.50781, 0.1677, 3.11682, -0.96933, 3.11682, -0.96944]}]}}, "pifeng3": {"pifeng3": {"deform": [{"offset": 38, "vertices": [-56.33752, -13.10529, -56.82376, -10.80402, -52.7099, 23.8175, -53.08203, 22.97736]}]}}, "tou": {"tou": {"deform": [{"time": 1, "offset": 126, "vertices": [0.10461, 0.00537, 0.10474]}, {"time": 1.3333, "offset": 118, "vertices": [-2.7301, -0.16526, -2.73083, -0.1653, 0, 0, 0, 0, 1.02881, 0.06225, 1.02869, 0.06222, -0.41455, -0.46747, -0.41437, -0.46748, -1.5896, -0.29712, -1.5896, -0.29715, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.02075, 0.89504, -5.02148, 0.89498, -2.51782, -0.83751, -2.51868, -0.83757, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.45581, -0.28902, -1.45581, -0.28905, -1.25989, -0.3044, -1.2597, -0.30446, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.36499, -0.08262, -1.36523, -0.08264, -5.69177, 0.68309, -5.69397, 0.68294, -3.90344, -0.57883, -3.90466, -0.5789, -2.07166, -0.40973, -2.07166, -0.40978, -2.5708, -0.15559, -2.5708, -0.1556, -4.94849, -0.29952, -4.94904, -0.29955, -4.28638, 0.08308, -4.2876, 0.08302, -2.34741, -0.82718, -2.34814, -0.82724, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.39368, -0.47213, -1.39294, -0.47213, -1.23157, -0.18752, -1.23236, -0.18758], "curve": "stepped"}, {"time": 3.3, "offset": 118, "vertices": [-2.7301, -0.16526, -2.73083, -0.1653, 0, 0, 0, 0, 1.02881, 0.06225, 1.02869, 0.06222, -0.41455, -0.46747, -0.41437, -0.46748, -1.5896, -0.29712, -1.5896, -0.29715, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.02075, 0.89504, -5.02148, 0.89498, -2.51782, -0.83751, -2.51868, -0.83757, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.45581, -0.28902, -1.45581, -0.28905, -1.25989, -0.3044, -1.2597, -0.30446, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.36499, -0.08262, -1.36523, -0.08264, -5.69177, 0.68309, -5.69397, 0.68294, -3.90344, -0.57883, -3.90466, -0.5789, -2.07166, -0.40973, -2.07166, -0.40978, -2.5708, -0.15559, -2.5708, -0.1556, -4.94849, -0.29952, -4.94904, -0.29955, -4.28638, 0.08308, -4.2876, 0.08302, -2.34741, -0.82718, -2.34814, -0.82724, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.39368, -0.47213, -1.39294, -0.47213, -1.23157, -0.18752, -1.23236, -0.18758]}, {"time": 3.6667, "offset": 126, "vertices": [0.10461, 0.00537, 0.10474]}]}}, "zuoshou": {"zuoshou": {"deform": [{"time": 1, "offset": 44, "vertices": [1.07156, -0.09445]}, {"time": 1.1667, "offset": 44, "vertices": [119.11781, 6.47928, 19.52131, 98.25685, 103.02419, 7.13091]}, {"time": 1.3333, "offset": 44, "vertices": [167.3162, 11.09482, 33.01137, 142.40454, 155.1088, 10.99899]}, {"time": 1.4667, "offset": 44, "vertices": [154.24683, 13.43543, 24.07312, 135.45424, 138.88814, 8.07202]}, {"time": 1.6667, "offset": 44, "vertices": [1.07156, -0.09445]}]}}}}}}}