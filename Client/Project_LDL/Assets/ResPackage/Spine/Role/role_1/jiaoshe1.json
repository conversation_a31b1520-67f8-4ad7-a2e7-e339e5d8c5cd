{"skeleton": {"hash": "+KgZfhzIla8", "spine": "4.2.40", "x": -387.67, "y": 1.95, "width": 666, "height": 1090, "images": "./images1/", "audio": "F:/工作/新项目/人物spine/角色1/images"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -58.4, "y": 267.48}, {"name": "bone2", "parent": "bone", "length": 88.54, "rotation": -124.45, "x": -24.14, "y": -7.2}, {"name": "bone3", "parent": "bone2", "length": 108.94, "rotation": 21.39, "x": 88.54}, {"name": "bone4", "parent": "bone3", "length": 94.31, "rotation": -0.47, "x": 108.94}, {"name": "bone5", "parent": "bone", "length": 96.17, "rotation": -47.86, "x": 75.19, "y": -10.59}, {"name": "bone6", "parent": "bone5", "length": 95.39, "rotation": -21.28, "x": 96.17}, {"name": "bone7", "parent": "bone6", "length": 136.54, "rotation": 0.29, "x": 95.39}, {"name": "bone8", "parent": "bone", "length": 48.52, "rotation": -27.7, "x": 20.89, "y": -4.72}, {"name": "bone9", "parent": "bone8", "length": 46.33, "rotation": -18.24, "x": 48.52}, {"name": "bone10", "parent": "bone9", "length": 47.43, "rotation": -13.43, "x": 46.33}, {"name": "bone11", "parent": "bone10", "length": 53.34, "rotation": -13.65, "x": 47.43}, {"name": "bone12", "parent": "bone", "length": 50.36, "rotation": -158.09, "x": -34.96, "y": -1.5}, {"name": "bone13", "parent": "bone12", "length": 47.21, "rotation": 11.96, "x": 50.36}, {"name": "bone14", "parent": "bone13", "length": 45.03, "rotation": -3.02, "x": 47.21}, {"name": "bone15", "parent": "bone14", "length": 65.56, "rotation": -4.07, "x": 45.03}, {"name": "bone30", "parent": "bone", "length": 84.01, "rotation": 87.43, "x": 2.79, "y": 27.66}, {"name": "bone31", "parent": "bone30", "length": 77.79, "rotation": 8.84, "x": 84.01}, {"name": "bone32", "parent": "bone31", "length": 77.76, "rotation": -20.3, "x": 77.79, "inherit": "noScale"}, {"name": "bone28", "parent": "bone32", "rotation": -75.96, "x": 48.57, "y": -103.29}, {"name": "bone29", "parent": "bone32", "rotation": 19.8, "x": 4.51, "y": 114.9}, {"name": "bone33", "parent": "bone32", "length": 76.93, "rotation": 2.73, "x": 77.76, "inherit": "noScale"}, {"name": "bone34", "parent": "bone33", "length": 43.68, "rotation": -2.43, "x": 76.93, "inherit": "noScale"}, {"name": "bone35", "parent": "bone34", "length": 108.46, "rotation": 11.14, "x": 43.68}, {"name": "bone36", "parent": "bone35", "length": 79.37, "rotation": -34.44, "x": 108.46}, {"name": "bone37", "parent": "bone36", "x": 53.44, "y": -54.88, "color": "abe323ff"}, {"name": "bone38", "parent": "bone36", "x": 70.52, "y": 15.9}, {"name": "bone39", "parent": "bone38", "x": 44.57, "y": -83.54}, {"name": "bone40", "parent": "bone36", "length": 33.81, "rotation": -11.45, "x": 183.4, "y": 14.27}, {"name": "bone41", "parent": "bone40", "length": 34.43, "rotation": -21.24, "x": 33.81}, {"name": "bone42", "parent": "bone41", "length": 38.52, "rotation": -31.16, "x": 34.43}, {"name": "bone43", "parent": "bone42", "length": 30.83, "rotation": -22.95, "x": 38.52}, {"name": "bone44", "parent": "bone43", "length": 37.1, "rotation": -39.29, "x": 30.83}, {"name": "bone45", "parent": "bone44", "length": 36.1, "rotation": -33.26, "x": 37.1}, {"name": "bone46", "parent": "bone45", "length": 34.44, "rotation": 2.71, "x": 36.1}, {"name": "bone47", "parent": "bone46", "length": 35.25, "rotation": 10.85, "x": 34.73, "y": 0.07}, {"name": "bone48", "parent": "bone47", "length": 30.53, "rotation": 25.25, "x": 33.95, "y": -0.38}, {"name": "bone49", "parent": "bone48", "length": 15.13, "rotation": 15.56, "x": 31.18, "y": 0.05}, {"name": "bone50", "parent": "bone49", "length": 20.68, "rotation": 54.45, "x": 15.13}, {"name": "bone51", "parent": "bone47", "length": 25.13, "rotation": 50.65, "x": 33.31, "y": 0.76}, {"name": "bone52", "parent": "bone51", "length": 22.7, "rotation": 41.45, "x": 25.13}, {"name": "bone53", "parent": "bone52", "length": 18.57, "rotation": 58.54, "x": 23.28, "y": 0.3}, {"name": "bone54", "parent": "bone44", "length": 24.76, "rotation": 0.48, "x": 41.08, "y": 12.39}, {"name": "bone55", "parent": "bone54", "length": 20.68, "rotation": -18.52, "x": 24.76}, {"name": "bone56", "parent": "bone55", "length": 14.69, "rotation": -11.27, "x": 20.68}, {"name": "bone57", "parent": "bone56", "length": 12.28, "rotation": -6.62, "x": 14.69}, {"name": "bone58", "parent": "bone36", "length": 41.14, "rotation": 10.94, "x": 188.64, "y": 22.61}, {"name": "bone59", "parent": "bone58", "length": 36.04, "rotation": -21.87, "x": 41.14}, {"name": "bone60", "parent": "bone59", "length": 39.63, "rotation": -30.51, "x": 36.04}, {"name": "bone61", "parent": "bone60", "length": 39.72, "rotation": -56.91, "x": 39.63}, {"name": "bone62", "parent": "bone61", "length": 36.11, "rotation": -15.91, "x": 39.99, "y": -0.26}, {"name": "bone63", "parent": "bone62", "length": 37.2, "rotation": -9.17, "x": 36.11}, {"name": "bone64", "parent": "bone63", "length": 25.54, "rotation": -28.03, "x": 37.2}, {"name": "bone65", "parent": "bone64", "length": 24.98, "rotation": 3.29, "x": 25.54}, {"name": "bone66", "parent": "bone65", "length": 21.13, "rotation": 20.72, "x": 24.98}, {"name": "bone67", "parent": "bone66", "length": 18.96, "rotation": 57.12, "x": 21.23, "y": 0.36}, {"name": "bone68", "parent": "bone67", "length": 19.14, "rotation": 49.48, "x": 19.45, "y": 0.94}, {"name": "bone69", "parent": "bone68", "length": 15.91, "rotation": 26.45, "x": 19.66, "y": 0.12}, {"name": "bone70", "parent": "bone69", "length": 15.62, "rotation": 39.76, "x": 15.91}, {"name": "bone71", "parent": "bone36", "length": 37.36, "rotation": 35.56, "x": 193.14, "y": 29.81}, {"name": "bone72", "parent": "bone71", "length": 34.29, "rotation": -13.21, "x": 37.36}, {"name": "bone73", "parent": "bone72", "length": 37.15, "rotation": -19, "x": 34.29}, {"name": "bone74", "parent": "bone73", "length": 36.41, "rotation": -34.51, "x": 37.15}, {"name": "bone75", "parent": "bone74", "length": 32.74, "rotation": -60.81, "x": 36.41}, {"name": "bone76", "parent": "bone75", "length": 36.84, "rotation": -14.52, "x": 32.74}, {"name": "bone77", "parent": "bone76", "length": 34.86, "rotation": -11.32, "x": 36.32, "y": 0.38}, {"name": "bone78", "parent": "bone77", "length": 31.47, "rotation": -11.54, "x": 34.72, "y": -0.29}, {"name": "bone79", "parent": "bone78", "length": 24.88, "rotation": 7.64, "x": 31.47}, {"name": "bone80", "parent": "bone79", "length": 17.31, "rotation": 50.31, "x": 24.88}, {"name": "bone81", "parent": "bone80", "length": 16.62, "rotation": 72.9, "x": 17.31}, {"name": "bone82", "parent": "bone81", "length": 16.15, "rotation": 30.96, "x": 16.62}, {"name": "bone83", "parent": "bone82", "length": 14.26, "rotation": 23.01, "x": 16.15}, {"name": "bone84", "parent": "bone36", "length": 37.12, "rotation": 86.13, "x": 187.37, "y": 37.28}, {"name": "bone85", "parent": "bone84", "length": 44.1, "rotation": -9.59, "x": 37.12}, {"name": "bone86", "parent": "bone85", "length": 51.63, "rotation": -32.42, "x": 44.1}, {"name": "bone87", "parent": "bone86", "length": 45.88, "rotation": -36.35, "x": 51.63}, {"name": "bone88", "parent": "bone36", "length": 38.12, "rotation": 39.53, "x": 193.71, "y": 35.34}, {"name": "bone89", "parent": "bone88", "length": 44.78, "rotation": -20.25, "x": 38.12}, {"name": "bone90", "parent": "bone89", "length": 35.23, "rotation": -11.81, "x": 44.78}, {"name": "bone91", "parent": "bone90", "length": 38.36, "rotation": -78.66, "x": 35.23}, {"name": "bone92", "parent": "bone36", "length": 40.78, "rotation": -177.44, "x": 87.93, "y": 99}, {"name": "bone93", "parent": "bone92", "length": 29.31, "rotation": 23.32, "x": 40.78}, {"name": "bone94", "parent": "bone93", "length": 21.37, "rotation": 20.97, "x": 29.31}, {"name": "bone95", "parent": "bone94", "length": 18.98, "rotation": 29.99, "x": 21.37}, {"name": "bone96", "parent": "bone95", "length": 13.92, "rotation": 41.83, "x": 18.98}, {"name": "bone97", "parent": "bone36", "length": 36.32, "rotation": 148.84, "x": 111.67, "y": 170.58}, {"name": "bone98", "parent": "bone97", "length": 21.69, "rotation": -0.7, "x": 36.32}, {"name": "bone99", "parent": "bone98", "length": 22.37, "rotation": -20.2, "x": 21.69}, {"name": "bone100", "parent": "bone99", "length": 15.07, "rotation": -47.82, "x": 22.37}, {"name": "bone101", "parent": "bone100", "length": 16.33, "rotation": -41.85, "x": 15.07}, {"name": "bone102", "parent": "bone101", "length": 18.1, "rotation": -29.32, "x": 16.33}, {"name": "bone103", "parent": "bone36", "length": 57.1, "rotation": -169.77, "x": 172.65, "y": 159.89}, {"name": "bone104", "parent": "bone103", "length": 59.65, "rotation": 6.38, "x": 58.58, "y": -0.48}, {"name": "bone105", "parent": "bone104", "length": 50.73, "rotation": 9.85, "x": 59.65}, {"name": "bone106", "parent": "bone105", "length": 54.28, "rotation": 12.32, "x": 50.73}, {"name": "bone107", "parent": "bone106", "length": 42.08, "rotation": -9.97, "x": 54.28}, {"name": "bone108", "parent": "bone36", "length": 64.01, "rotation": -140.01, "x": 243.32, "y": 12.26}, {"name": "bone109", "parent": "bone108", "length": 44.21, "rotation": -13.26, "x": 64.01}, {"name": "bone110", "parent": "bone109", "length": 69.82, "rotation": -7.28, "x": 44.21}, {"name": "bone111", "parent": "bone110", "length": 45.08, "rotation": 2.32, "x": 69.82}, {"name": "bone112", "parent": "bone111", "length": 30.66, "rotation": -10.21, "x": 45.08}, {"name": "bone113", "parent": "bone112", "length": 24.17, "rotation": 36.47, "x": 30.37, "y": 0.59}, {"name": "bone114", "parent": "bone110", "length": 25.61, "rotation": 0.54, "x": 85.24, "y": 24.7}, {"name": "bone115", "parent": "bone114", "length": 17.78, "rotation": 18.31, "x": 26.49, "y": -0.15}, {"name": "bone116", "parent": "bone115", "length": 12.6, "rotation": 39.91, "x": 17.78}, {"name": "bone117", "parent": "bone110", "length": 26.54, "rotation": 2.92, "x": 106.63, "y": -23.22}, {"name": "bone118", "parent": "bone117", "length": 24.64, "rotation": 2.63, "x": 26.54}, {"name": "bone119", "parent": "bone118", "length": 26.86, "rotation": 12.03, "x": 24.64}, {"name": "bone120", "parent": "bone105", "length": 32.38, "rotation": 10.58, "x": 61.58, "y": -38.66}, {"name": "bone121", "parent": "bone120", "length": 25.7, "x": 32.38}, {"name": "bone122", "parent": "bone121", "length": 27.11, "rotation": -52.39, "x": 25.7}, {"name": "bone123", "parent": "bone106", "length": 26.98, "rotation": -9.23, "x": 44.22, "y": -18.11}, {"name": "bone124", "parent": "bone123", "length": 20.65, "rotation": -22.13, "x": 26.98}, {"name": "bone125", "parent": "bone106", "length": 27.95, "rotation": -7.52, "x": 53.74, "y": 28.79}, {"name": "bone126", "parent": "bone125", "length": 22.93, "rotation": -12.1, "x": 27.95}, {"name": "bone127", "parent": "bone105", "length": 24.67, "rotation": -60.69, "x": 65.52, "y": -31.05}, {"name": "bone128", "parent": "bone127", "length": 18.63, "rotation": -16.29, "x": 24.67}, {"name": "bone129", "parent": "bone128", "length": 16.08, "rotation": -54.44, "x": 18.63}, {"name": "bone130", "parent": "bone129", "length": 10.57, "rotation": -50.99, "x": 16.08}, {"name": "bone131", "parent": "bone34", "length": 83.77, "rotation": -79.88, "x": 33.28, "y": -60.35}, {"name": "bone132", "parent": "bone131", "length": 241.42, "rotation": -65.9, "x": 83.77}, {"name": "bone133", "parent": "bone132", "length": 338.82, "rotation": 168.78, "x": 241.42}, {"name": "bone134", "parent": "bone133", "length": 52.82, "rotation": 2.15, "x": 341.36, "y": -2.67}, {"name": "bone135", "parent": "bone134", "length": 55.68, "rotation": -39.43, "x": 14.23, "y": -11.41}, {"name": "bone136", "parent": "bone135", "length": 44.42, "rotation": 40.25, "x": 55.68}, {"name": "bone137", "parent": "bone136", "length": 27.85, "rotation": 43.48, "x": 44.42}, {"name": "bone138", "parent": "bone137", "length": 31.62, "rotation": 0.07, "x": 27.85}, {"name": "bone139", "parent": "bone34", "length": 107.57, "rotation": 114.52, "x": 6.29, "y": 42.25}, {"name": "bone140", "parent": "bone139", "length": 265.08, "rotation": 41.64, "x": 107.57}, {"name": "bone141", "parent": "bone140", "length": 338.85, "rotation": -163.99, "x": 265.46, "y": -0.5}, {"name": "bone142", "parent": "bone141", "length": 66.75, "rotation": -0.5, "x": 338.02, "y": 2.18}, {"name": "bone143", "parent": "bone142", "length": 22.63, "rotation": -3.08, "x": 66.75}, {"name": "bone144", "parent": "bone143", "length": 37.26, "rotation": -61.64, "x": 22.63}, {"name": "bone145", "parent": "bone144", "length": 29.93, "rotation": -39.13, "x": 37.26}, {"name": "bone146", "parent": "bone145", "length": 27.62, "rotation": -51.49, "x": 29.93}, {"name": "bone147", "parent": "bone142", "length": 54.84, "rotation": -48.43, "x": 90.88, "y": 11.84}, {"name": "bone148", "parent": "bone147", "length": 28.19, "rotation": -66.05, "x": 54.84}, {"name": "bone149", "parent": "bone148", "length": 28.32, "rotation": -4.11, "x": 28.19}, {"name": "bone151", "parent": "bone142", "length": 54.56, "rotation": -28.27, "x": 89.84, "y": 4.84}, {"name": "bone152", "parent": "bone151", "length": 43.46, "rotation": -41.55, "x": 54.56}, {"name": "bone150", "parent": "bone142", "length": 50.51, "rotation": -45.59, "x": 24.81, "y": -26.16}, {"name": "bone153", "parent": "bone150", "length": 30.37, "rotation": 26.08, "x": 50.51}, {"name": "bone154", "parent": "bone153", "length": 25.03, "rotation": 37.18, "x": 30.37}, {"name": "bone155", "parent": "bone134", "length": 32.02, "rotation": 9.05, "x": 14.42, "y": 6.88}, {"name": "bone156", "parent": "bone155", "length": 13.24, "rotation": 4.54, "x": 32.02}, {"name": "bone157", "parent": "bone156", "length": 36.37, "rotation": -33.24, "x": 13.24}, {"name": "bone158", "parent": "bone136", "length": 35.07, "rotation": 42.82, "x": 33.6, "y": 12.47}, {"name": "bone16", "parent": "bone32", "length": 52.48, "rotation": -173.8, "x": -38.62, "y": 99.81, "inherit": "noScale"}, {"name": "bone17", "parent": "bone16", "length": 39.29, "rotation": -2.66, "x": 52.48}, {"name": "bone18", "parent": "bone17", "length": 39.91, "rotation": -4.04, "x": 39.29}, {"name": "bone159", "parent": "bone34", "length": 25.94, "rotation": -178.07, "x": 42.77, "y": 6.37}, {"name": "bone160", "parent": "bone159", "length": 20.97, "rotation": 5.58, "x": 25.94}, {"name": "bone161", "parent": "bone160", "length": 36.96, "rotation": -9.24, "x": 20.97}], "slots": [{"name": "toufa6", "bone": "root", "attachment": "toufa6"}, {"name": "toufa7", "bone": "root", "attachment": "toufa7"}, {"name": "zuoshou3", "bone": "root", "attachment": "zuoshou3"}, {"name": "shenti", "bone": "root", "attachment": "shenti"}, {"name": "xiangliang", "bone": "root", "attachment": "xiangliang"}, {"name": "yaodai2", "bone": "root", "attachment": "yaodai2"}, {"name": "ya<PERSON>i", "bone": "root", "attachment": "ya<PERSON>i"}, {"name": "youshou4", "bone": "root", "attachment": "youshou4"}, {"name": "youshou3", "bone": "root", "attachment": "youshou3"}, {"name": "yan<PERSON>u", "bone": "root", "attachment": "yan<PERSON>u"}, {"name": "tongkong2", "bone": "root", "attachment": "tongkong2", "visible": false}, {"name": "tongkong", "bone": "root", "attachment": "tongkong", "visible": false}, {"name": "tongkong3", "bone": "root", "attachment": "tongkong3"}, {"name": "lian", "bone": "root", "attachment": "lian"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "biyan", "bone": "root"}, {"name": "toufa5", "bone": "root", "attachment": "toufa5"}, {"name": "toufa4", "bone": "root", "attachment": "toufa4"}, {"name": "toufa3", "bone": "root", "attachment": "toufa3"}, {"name": "toufa2", "bone": "root", "attachment": "toufa2"}, {"name": "toufa1", "bone": "root", "attachment": "toufa1"}, {"name": "youshou2", "bone": "root", "attachment": "youshou2"}, {"name": "you<PERSON>ou", "bone": "root", "attachment": "you<PERSON>ou"}, {"name": "zuoshou2 (2)", "bone": "root", "attachment": "zuoshou2 (2)"}, {"name": "shouzhi4", "bone": "root", "attachment": "shouzhi4"}, {"name": "shouzhi3", "bone": "root", "attachment": "shouzhi3"}, {"name": "shouzhi2", "bone": "root", "attachment": "shouzhi2"}, {"name": "shouzhi1", "bone": "root", "attachment": "shouzhi1"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON>"}], "skins": [{"name": "default", "attachments": {"biyan": {"biyan": {"type": "mesh", "uvs": [0.4529, 0.16371, 0.47513, 0.34774, 0.7636, 0.46044, 0.83223, 0.33657, 0.92862, 0.29385, 1, 0.36647, 1, 0.48607, 1, 0.58431, 1, 0.70391, 1, 0.84487, 0.97111, 0.92603, 0.91882, 1, 0.78158, 1, 0.72929, 0.92175, 0.69498, 0.82778, 0.69498, 0.72527, 0.72439, 0.60994, 0.47513, 0.52208, 0.47143, 0.67221, 0.41771, 0.79812, 0.33249, 0.81749, 0.25284, 0.78843, 0.17689, 0.72548, 0.1102, 0.59956, 0.06203, 0.48818, 0, 0.27509, 0.06389, 0.24603, 0.09723, 0.15886, 0.16577, 0, 0.24728, 0, 0.32323, 0, 0.40659, 0, 0.9613, 0.33657], "triangles": [17, 23, 1, 19, 20, 18, 17, 1, 2, 7, 16, 2, 11, 12, 13, 16, 17, 2, 0, 1, 26, 18, 22, 17, 18, 20, 21, 5, 32, 4, 32, 5, 6, 24, 25, 26, 3, 32, 6, 27, 0, 26, 26, 1, 24, 1, 23, 24, 30, 31, 0, 29, 30, 0, 27, 28, 29, 29, 0, 27, 3, 4, 32, 17, 22, 23, 7, 3, 6, 7, 2, 3, 21, 22, 18, 8, 14, 15, 8, 16, 7, 8, 15, 16, 9, 14, 8, 13, 14, 9, 10, 13, 9, 10, 11, 13], "vertices": [2, 24, 95.28, 13.92, 0.31903, 25, 41.83, 68.79, 0.68097, 2, 24, 87.45, 2.91, 0.33378, 25, 34, 57.79, 0.66622, 2, 24, 112.95, -43.98, 0.37162, 25, 59.5, 10.9, 0.62838, 2, 24, 127.43, -48.78, 0.53658, 25, 73.99, 6.1, 0.46342, 2, 24, 140.45, -61.06, 0.75336, 25, 87, -6.18, 0.24664, 2, 24, 144.26, -74.55, 0.8, 25, 90.81, -19.67, 0.2, 2, 24, 137.57, -79.59, 0.8, 25, 84.13, -24.71, 0.2, 2, 24, 132.08, -83.73, 0.8, 25, 78.64, -28.86, 0.2, 2, 24, 125.4, -88.78, 0.81825, 25, 71.96, -33.9, 0.18175, 2, 24, 117.53, -94.72, 0.8, 25, 64.08, -39.84, 0.2, 2, 24, 109.81, -93.92, 0.78226, 25, 56.36, -39.05, 0.21774, 2, 24, 99.91, -89.4, 0.64423, 25, 46.46, -34.53, 0.35577, 2, 24, 84.78, -69.35, 0.31621, 25, 31.34, -14.48, 0.68379, 2, 24, 83.39, -58.42, 0.32486, 25, 29.94, -3.54, 0.67514, 2, 24, 84.86, -49.44, 0.41224, 25, 31.41, 5.43, 0.58776, 2, 24, 90.58, -45.12, 0.39952, 25, 37.14, 9.75, 0.60048, 2, 24, 100.27, -44.55, 0.32348, 25, 46.83, 10.32, 0.67652, 2, 24, 77.7, -4.44, 0.36595, 25, 24.26, 50.44, 0.63405, 2, 24, 68.91, -10.23, 0.41465, 25, 15.46, 44.65, 0.58535, 2, 24, 55.95, -7.69, 0.32886, 25, 2.5, 47.19, 0.67114, 2, 24, 45.47, 3.94, 0.42388, 25, -7.97, 58.82, 0.57612, 2, 24, 38.32, 16.8, 0.62658, 25, -15.13, 71.68, 0.37342, 2, 24, 33.46, 30.55, 0.67723, 25, -19.98, 85.43, 0.32277, 2, 24, 33.15, 45.6, 0.60215, 25, -20.3, 100.48, 0.39785, 2, 24, 34.06, 57.33, 0.66019, 25, -19.38, 112.21, 0.33981, 2, 24, 39.13, 75.38, 0.82307, 25, -14.32, 130.26, 0.17693, 2, 24, 47.79, 67.27, 0.6797, 25, -5.65, 122.15, 0.3203, 2, 24, 56.34, 66.08, 0.62382, 25, 2.9, 120.95, 0.37618, 2, 24, 72.77, 62.76, 0.67714, 25, 19.33, 117.64, 0.32286, 2, 24, 81.76, 50.86, 0.80894, 25, 28.31, 105.73, 0.19106, 2, 24, 90.13, 39.76, 0.71549, 25, 36.69, 94.64, 0.28451, 2, 24, 99.32, 27.59, 0.49287, 25, 45.88, 82.46, 0.50713, 2, 24, 141.66, -67.63, 0.78864, 25, 88.22, -12.76, 0.21136], "hull": 32, "edges": [56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 2, 2, 0, 0, 62, 60, 62, 56, 58, 58, 60, 32, 4, 4, 6, 6, 8, 8, 64, 64, 10, 32, 30, 30, 28, 28, 26, 26, 24, 22, 24, 22, 20, 20, 18, 16, 18, 14, 16, 10, 12, 12, 14, 2, 4, 32, 34, 8, 10], "width": 183, "height": 70}}, "jiemao": {"jiemao": {"type": "mesh", "uvs": [0.02025, 0.08332, 0.03604, 0.05502, 0.0986, 0.07977, 0.16387, 0.09745, 0.22969, 0.13104, 0.28789, 0.15579, 0.91811, 0.50095, 0.96855, 0.54223, 1, 0.58716, 1, 0.66973, 1, 0.77052, 1, 0.8798, 0.95883, 0.92594, 0.92371, 0.92594, 0.90279, 0.84094, 0.88653, 0.77537, 0.86038, 0.7268, 0.8204, 0.74016, 0.78117, 0.78509, 0.74455, 0.86523, 0.71616, 0.9393, 0.69972, 1, 0.67125, 1, 0.382, 0.73387, 0.38145, 0.68084, 0.37873, 0.56416, 0.35752, 0.46516, 0.3189, 0.39798, 0.26178, 0.35025, 0.20466, 0.34319, 0.16279, 0.38207, 0.13505, 0.42804, 0.10837, 0.47385, 0.06742, 0.40442, 0.03894, 0.23953, 0.41084, 0.73388, 0.41627, 0.65609, 0.41192, 0.55002, 0.3994, 0.42273, 0.38854, 0.2919, 0.34446, 0.21766, 0.87039, 0.53495, 0.82256, 0.5568, 0.77997, 0.5993, 0.74934, 0.65273, 0.7258, 0.73044, 0.70338, 0.81545, 0.68395, 0.90773], "triangles": [6, 40, 5, 2, 0, 1, 34, 0, 2, 6, 39, 40, 4, 34, 3, 29, 4, 5, 28, 29, 5, 40, 28, 5, 39, 28, 40, 29, 34, 4, 3, 34, 2, 30, 34, 29, 27, 28, 39, 33, 34, 30, 31, 33, 30, 32, 33, 31, 39, 6, 42, 42, 6, 41, 8, 41, 7, 16, 42, 41, 43, 42, 16, 41, 6, 7, 8, 16, 41, 9, 16, 8, 15, 16, 9, 10, 15, 9, 14, 15, 10, 14, 10, 11, 12, 14, 11, 13, 14, 12, 38, 39, 42, 42, 43, 38, 44, 43, 16, 16, 17, 44, 27, 39, 38, 26, 27, 38, 38, 43, 37, 26, 38, 37, 25, 26, 37, 25, 37, 36, 24, 25, 36, 35, 23, 24, 36, 37, 44, 36, 35, 24, 17, 45, 44, 18, 45, 17, 46, 45, 18, 19, 46, 18, 47, 46, 19, 20, 47, 19, 22, 47, 20, 21, 22, 20, 45, 36, 44, 46, 35, 45, 47, 35, 46, 22, 35, 47, 44, 37, 43, 35, 36, 45, 23, 35, 22], "vertices": [2, 25, 6.76, 131.52, 0.30179, 24, 60.21, 76.65, 0.69821, 2, 25, 9.76, 130.18, 0.10647, 24, 63.21, 75.31, 0.89353, 2, 25, 15.51, 120.26, 0.1914, 24, 68.96, 65.39, 0.8086, 2, 25, 21.88, 110.18, 0.38372, 24, 75.32, 55.31, 0.61628, 2, 25, 27.59, 99.49, 0.27425, 24, 81.04, 44.61, 0.72575, 2, 25, 32.87, 90.2, 0.34107, 24, 86.31, 35.32, 0.65893, 2, 25, 86.53, -13, 0.21262, 24, 139.97, -67.88, 0.78738, 2, 25, 90.21, -21.72, 0.15042, 24, 143.66, -76.6, 0.84958, 2, 25, 91.65, -27.81, 0.09538, 24, 145.1, -82.68, 0.90462, 2, 25, 87.96, -30.59, 0.11183, 24, 141.41, -85.47, 0.88817, 2, 25, 83.46, -33.99, 0.12656, 24, 136.9, -88.87, 0.87344, 2, 25, 78.57, -37.68, 0.13664, 24, 132.02, -92.55, 0.86336, 2, 25, 72, -33.25, 0.23282, 24, 125.44, -88.13, 0.76718, 2, 25, 68.15, -28.15, 0.29223, 24, 121.59, -83.03, 0.70777, 2, 25, 69.65, -22.24, 0.31694, 24, 123.1, -77.12, 0.68306, 2, 25, 70.8, -17.67, 0.33324, 24, 124.25, -72.55, 0.66676, 2, 25, 70.1, -12.23, 0.36252, 24, 123.55, -67.11, 0.63748, 2, 25, 65.12, -6.88, 0.42945, 24, 118.57, -61.75, 0.57055, 2, 25, 58.82, -2.69, 0.50951, 24, 112.26, -57.57, 0.49049, 2, 25, 51.22, -0.08, 0.50166, 24, 104.66, -54.95, 0.49834, 2, 25, 44.79, 1.55, 0.54572, 24, 98.24, -53.32, 0.45428, 2, 25, 40.28, 1.89, 0.56521, 24, 93.72, -52.98, 0.43479, 2, 25, 37.16, 6.03, 0.62497, 24, 90.6, -48.85, 0.37503, 2, 25, 17.34, 57.03, 0.66941, 24, 70.79, 2.15, 0.33059, 2, 25, 19.65, 58.89, 0.65982, 24, 73.1, 4.02, 0.34018, 2, 25, 24.57, 63.22, 0.62922, 24, 78.02, 8.35, 0.37078, 2, 25, 26.67, 69.65, 0.55815, 24, 80.12, 14.77, 0.44185, 2, 25, 25.44, 77.52, 0.47219, 24, 78.88, 22.65, 0.52781, 2, 25, 21.31, 87.43, 0.38704, 24, 74.76, 32.55, 0.61296, 2, 25, 15.37, 95.97, 0.30673, 24, 68.81, 41.09, 0.69327, 2, 25, 9.04, 100.74, 0.35241, 24, 62.48, 45.86, 0.64759, 2, 25, 3.94, 103.22, 0.31701, 24, 57.39, 48.34, 0.68299, 2, 25, -1.03, 105.55, 0.2803, 24, 52.41, 50.67, 0.7197, 2, 25, -2.42, 113.84, 0.20774, 24, 51.03, 58.96, 0.79226, 2, 25, 1.83, 123.54, 0.3389, 24, 55.28, 68.66, 0.6611, 2, 25, 20.5, 52.84, 0.73362, 24, 73.95, -2.04, 0.26638, 2, 25, 24.58, 54.67, 0.73116, 24, 78.02, -0.2, 0.26884, 2, 25, 28.84, 58.88, 0.69552, 24, 82.29, 4.01, 0.30448, 2, 25, 33.16, 64.99, 0.62451, 24, 86.6, 10.12, 0.37549, 2, 25, 37.82, 70.98, 0.50559, 24, 91.26, 16.11, 0.49441, 2, 25, 36.3, 79.89, 0.40555, 24, 89.75, 25.02, 0.59445, 2, 25, 79.78, -7.22, 0.28836, 24, 133.22, -62.09, 0.71164, 2, 25, 73.56, -1, 0.35668, 24, 127, -55.88, 0.64332, 2, 25, 66.99, 3.75, 0.43604, 24, 120.43, -51.13, 0.56396, 2, 25, 61.24, 6.4, 0.52089, 24, 114.69, -48.48, 0.47911, 2, 25, 55.19, 7.2, 0.62422, 24, 108.63, -47.68, 0.37578, 2, 25, 48.93, 7.59, 0.58277, 24, 102.37, -47.29, 0.41723, 2, 25, 42.67, 7.29, 0.61311, 24, 96.12, -47.58, 0.38689], "hull": 35, "edges": [0, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 42, 44, 94, 44, 10, 12, 44, 46], "width": 182, "height": 56}}, "lian": {"lian": {"type": "mesh", "uvs": [0.83839, 0.09165, 0.91638, 0.15154, 0.97367, 0.23424, 1, 0.31123, 1, 0.3811, 1, 0.46477, 1, 0.49734, 1, 0.53652, 0.96571, 0.60924, 0.93866, 0.68339, 0.92911, 0.75896, 0.88295, 0.84023, 0.81929, 0.91152, 0.73494, 0.96998, 0.66014, 1, 0.54714, 1, 0.45323, 0.95287, 0.37684, 0.89441, 0.31795, 0.82882, 0.26702, 0.75753, 0.2527, 0.68624, 0.22087, 0.70763, 0.16994, 0.70335, 0.1397, 0.65772, 0.09673, 0.64631, 0, 0.61067, 0, 0.50515, 0, 0.41247, 0.02511, 0.29127, 0.0649, 0.21, 0.12856, 0.133, 0.20814, 0.07882, 0.29408, 0.04032, 0.41026, 0, 0.57101, 0, 0.69674, 0, 0.75881, 0.04032, 0.25588, 0.55363, 0.28135, 0.45382, 0.36092, 0.33405, 0.44369, 0.2599, 0.56305, 0.21713, 0.67128, 0.21, 0.78428, 0.26418, 0.85271, 0.26133, 0.9307, 0.26418, 0.97049, 0.30838, 0.6917, 0.51338, 0.6826, 0.58016, 0.64405, 0.63888, 0.60549, 0.69645, 0.59264, 0.74711, 0.63762, 0.7782, 0.76871, 0.77129, 0.77128, 0.74021, 0.75971, 0.6953, 0.76228, 0.64003, 0.77642, 0.58822, 0.79827, 0.54562, 0.85276, 0.53598, 0.91239, 0.52607, 0.96149, 0.50647, 0.65051, 0.49106, 0.57979, 0.44775, 0.49405, 0.41213, 0.39254, 0.40422, 0.4131, 0.50607, 0.44453, 0.48647, 0.49148, 0.4829, 0.5265, 0.48575, 0.56271, 0.49716, 0.59016, 0.51534, 0.60727, 0.5378, 0.61045, 0.57701, 0.38116, 0.4866, 0.41665, 0.4607, 0.46685, 0.45071, 0.53023, 0.45071, 0.58448, 0.4657, 0.62809, 0.49568, 0.64026, 0.53248, 0.63874, 0.5611, 0.62961, 0.60289, 0.60922, 0.60093, 0.57669, 0.60656, 0.52715, 0.6102, 0.4776, 0.59994, 0.43693, 0.58304, 0.41179, 0.55025, 0.39479, 0.52011, 0.78389, 0.62427, 0.79689, 0.60523, 0.81464, 0.58753, 0.83739, 0.5723, 0.86589, 0.55797, 0.90164, 0.5526, 0.92418, 0.56312, 0.93693, 0.58194, 0.93443, 0.60948, 0.92243, 0.63546, 0.90443, 0.65249, 0.87643, 0.66548, 0.83196, 0.65862, 0.80046, 0.64384, 0.77771, 0.63219, 0.36546, 0.4042, 0.40125, 0.37824, 0.44811, 0.36374, 0.5052, 0.35228, 0.57593, 0.36679, 0.62109, 0.39504, 0.68159, 0.42175, 0.73101, 0.44695, 0.7472, 0.48435, 0.72505, 0.50955, 0.82547, 0.53983, 0.82643, 0.51611, 0.8443, 0.49182, 0.90654, 0.47416, 0.96261, 0.4598, 0.98541, 0.50452, 0.40016, 0.62733, 0.39867, 0.70072, 0.44634, 0.80213, 0.79635, 0.83683, 0.83657, 0.79279, 0.85444, 0.74475, 0.54522, 0.36049, 0.58336, 0.30441, 0.68762, 0.29106, 0.77401, 0.32442, 0.81125, 0.35645, 0.87231, 0.34978, 0.9304, 0.36178, 0.95125, 0.40582, 0.52871, 0.3571, 0.90964, 0.61287, 0.88953, 0.64475, 0.77696, 0.51931, 0.60504, 0.62912, 0.55946, 0.63406, 0.48564, 0.62583, 0.43927, 0.61131, 0.40647, 0.58556, 0.38126, 0.56279, 0.36557, 0.52811, 0.78461, 0.57225, 0.78578, 0.65973, 0.81286, 0.67563, 0.84792, 0.68267, 0.87136, 0.68376, 0.89883, 0.68322, 0.92743, 0.67703, 0.94396, 0.65355, 0.95202, 0.62425, 0.95553, 0.60715, 0.94963, 0.56934, 0.93508, 0.54657, 0.52328, 0.83252, 0.5488, 0.8379, 0.56487, 0.85189, 0.58849, 0.86874, 0.62041, 0.88367, 0.65127, 0.89064, 0.6818, 0.89506, 0.70992, 0.88992, 0.72445, 0.87684, 0.74518, 0.87203, 0.75283, 0.85927, 0.7382, 0.84782, 0.72938, 0.83158, 0.71909, 0.81853, 0.70257, 0.81632, 0.68634, 0.82017, 0.6785, 0.81094, 0.6645, 0.80427, 0.6364, 0.80431, 0.6003, 0.81392, 0.56608, 0.81366, 0.53228, 0.80829, 0.51778, 0.81819, 0.66411, 0.78191, 0.69204, 0.79427, 0.72614, 0.78926], "triangles": [109, 127, 128, 109, 128, 110, 111, 110, 129, 111, 129, 130, 112, 111, 130, 112, 130, 131, 63, 109, 110, 127, 109, 63, 63, 64, 127, 77, 64, 63, 62, 78, 63, 77, 63, 78, 134, 118, 132, 118, 134, 119, 68, 76, 77, 117, 113, 112, 69, 68, 77, 70, 69, 77, 67, 76, 68, 62, 110, 111, 62, 111, 112, 112, 47, 62, 62, 63, 110, 117, 132, 118, 117, 112, 131, 79, 78, 62, 78, 70, 77, 61, 118, 119, 113, 114, 112, 114, 47, 112, 71, 78, 79, 70, 78, 71, 117, 138, 113, 116, 138, 117, 114, 113, 138, 60, 118, 61, 117, 118, 60, 80, 79, 62, 80, 62, 47, 72, 71, 79, 59, 117, 60, 116, 117, 59, 80, 72, 79, 116, 58, 138, 115, 116, 59, 115, 58, 116, 157, 60, 61, 95, 59, 60, 95, 60, 157, 94, 59, 95, 81, 72, 80, 96, 95, 157, 146, 138, 58, 93, 115, 59, 93, 59, 94, 73, 72, 81, 48, 80, 47, 81, 80, 48, 87, 88, 67, 143, 88, 87, 93, 92, 58, 93, 58, 115, 146, 58, 92, 146, 57, 114, 146, 114, 138, 68, 87, 67, 86, 87, 68, 73, 84, 72, 82, 73, 81, 82, 81, 48, 83, 73, 82, 91, 146, 92, 57, 146, 91, 71, 85, 70, 84, 71, 72, 83, 84, 73, 85, 69, 70, 84, 85, 71, 86, 68, 69, 85, 86, 69, 142, 87, 86, 143, 87, 142, 136, 95, 96, 94, 95, 136, 90, 57, 91, 141, 86, 85, 142, 86, 141, 121, 143, 142, 139, 84, 83, 139, 83, 82, 104, 57, 90, 140, 85, 84, 140, 84, 139, 141, 85, 140, 49, 82, 48, 139, 82, 49, 48, 47, 114, 57, 48, 114, 56, 48, 57, 56, 57, 104, 49, 48, 56, 102, 103, 91, 90, 91, 103, 104, 90, 103, 136, 93, 94, 137, 93, 136, 102, 92, 93, 102, 93, 137, 102, 91, 92, 147, 104, 103, 56, 104, 147, 101, 102, 137, 148, 103, 102, 147, 103, 148, 149, 102, 101, 148, 102, 149, 55, 49, 56, 55, 56, 147, 50, 139, 49, 140, 139, 50, 148, 54, 55, 148, 55, 147, 51, 140, 50, 181, 49, 55, 126, 54, 148, 126, 148, 149, 50, 181, 52, 49, 181, 50, 55, 54, 181, 51, 50, 52, 54, 183, 181, 53, 183, 54, 125, 54, 126, 53, 54, 125, 182, 181, 183, 142, 122, 121, 142, 141, 122, 123, 122, 141, 175, 181, 182, 51, 177, 178, 176, 52, 181, 176, 181, 175, 51, 179, 141, 51, 141, 140, 178, 179, 51, 123, 141, 179, 174, 175, 182, 177, 51, 52, 176, 177, 52, 172, 182, 183, 173, 174, 182, 180, 123, 179, 171, 172, 183, 172, 173, 182, 170, 183, 53, 171, 183, 170, 158, 180, 179, 124, 53, 125, 170, 53, 124, 159, 179, 178, 158, 179, 159, 169, 170, 124, 160, 159, 178, 160, 178, 177, 168, 169, 124, 161, 160, 177, 167, 169, 168, 170, 172, 171, 166, 170, 169, 166, 169, 167, 162, 177, 176, 161, 177, 162, 176, 163, 162, 163, 176, 175, 173, 175, 174, 172, 166, 173, 170, 166, 172, 165, 173, 166, 175, 173, 163, 173, 164, 163, 165, 164, 173, 61, 119, 120, 35, 0, 36, 42, 34, 35, 42, 35, 36, 42, 36, 0, 41, 33, 34, 41, 34, 42, 40, 33, 41, 32, 33, 40, 44, 0, 1, 45, 44, 1, 43, 42, 0, 44, 43, 0, 2, 45, 1, 129, 42, 43, 128, 41, 42, 128, 42, 129, 40, 41, 128, 46, 45, 2, 46, 2, 3, 130, 129, 43, 40, 31, 32, 39, 31, 40, 30, 31, 39, 29, 30, 39, 132, 44, 45, 133, 132, 45, 108, 40, 128, 107, 39, 40, 44, 130, 43, 131, 44, 132, 131, 130, 44, 135, 108, 128, 127, 135, 128, 46, 133, 45, 108, 107, 40, 106, 39, 107, 46, 3, 4, 133, 46, 4, 110, 128, 129, 105, 39, 106, 65, 105, 106, 134, 133, 4, 64, 107, 108, 64, 108, 135, 65, 106, 107, 64, 65, 107, 64, 135, 127, 76, 65, 64, 76, 64, 77, 38, 29, 39, 38, 39, 105, 28, 29, 38, 27, 28, 38, 5, 119, 134, 75, 65, 76, 5, 134, 4, 134, 132, 133, 67, 75, 76, 75, 74, 105, 75, 105, 65, 38, 105, 74, 117, 131, 132, 120, 119, 5, 6, 120, 5, 26, 27, 38, 66, 74, 75, 66, 75, 67, 89, 74, 66, 145, 38, 74, 145, 74, 89, 120, 6, 7, 88, 89, 66, 88, 66, 67, 37, 26, 38, 37, 38, 145, 144, 145, 89, 144, 89, 88, 7, 156, 157, 96, 157, 156, 61, 120, 7, 7, 157, 61, 97, 96, 156, 143, 144, 88, 8, 155, 156, 97, 156, 155, 7, 8, 156, 97, 136, 96, 98, 97, 155, 24, 25, 26, 98, 136, 97, 154, 98, 155, 154, 155, 8, 121, 144, 143, 99, 136, 98, 99, 98, 154, 137, 136, 99, 37, 24, 26, 100, 137, 99, 153, 99, 154, 23, 24, 37, 101, 137, 100, 153, 100, 99, 152, 100, 153, 151, 101, 100, 151, 100, 152, 9, 152, 153, 8, 9, 153, 8, 153, 154, 150, 149, 101, 150, 101, 151, 20, 23, 37, 37, 145, 144, 37, 144, 121, 21, 22, 23, 121, 20, 37, 20, 21, 23, 126, 149, 150, 121, 19, 20, 122, 19, 121, 9, 151, 152, 10, 151, 9, 126, 150, 151, 10, 126, 151, 18, 19, 122, 18, 122, 123, 10, 125, 126, 11, 125, 10, 124, 125, 11, 17, 18, 123, 12, 124, 11, 168, 124, 12, 167, 168, 12, 158, 16, 123, 158, 123, 180, 17, 123, 16, 167, 165, 166, 13, 167, 12, 13, 165, 167, 158, 160, 16, 160, 158, 159, 15, 160, 161, 15, 161, 162, 15, 16, 160, 14, 163, 164, 164, 165, 13, 14, 164, 13, 162, 163, 14, 15, 162, 14], "vertices": [2, 24, 234.26, 39.57, 0.9, 25, 180.81, 94.44, 0.1, 2, 24, 232.16, 10.77, 0.9, 25, 178.71, 65.65, 0.1, 2, 24, 220.71, -17.63, 0.9, 25, 167.27, 37.24, 0.1, 2, 24, 205.62, -38.17, 0.85, 25, 152.17, 16.7, 0.15, 2, 24, 187.9, -51.47, 0.8, 25, 134.46, 3.4, 0.2, 2, 24, 166.75, -67.47, 0.72, 25, 113.3, -12.6, 0.28, 2, 24, 158.51, -73.7, 0.74, 25, 105.07, -18.82, 0.26, 2, 24, 148.6, -81.18, 0.8, 25, 95.16, -26.31, 0.2, 2, 24, 124.33, -87.29, 0.8, 25, 70.89, -32.42, 0.2, 2, 24, 100.94, -95.31, 0.8, 25, 47.5, -40.43, 0.2, 2, 24, 80.26, -107.66, 0.8, 25, 26.82, -52.79, 0.2, 2, 24, 51.94, -112.88, 0.8, 25, -1.5, -58.01, 0.2, 2, 24, 23.23, -112.32, 0.8, 25, -30.21, -57.44, 0.2, 2, 24, -5.72, -104.66, 0.8, 25, -59.16, -49.79, 0.2, 2, 24, -25.89, -93.69, 0.78, 25, -79.33, -38.81, 0.22, 2, 24, -45.06, -68.21, 0.78, 25, -98.51, -13.33, 0.22, 2, 24, -49.12, -37.97, 0.8, 25, -102.57, 16.91, 0.2, 2, 24, -47.34, -9.52, 0.8, 25, -100.78, 45.35, 0.2, 2, 24, -40.78, 16.33, 0.8, 25, -94.22, 71.2, 0.2, 2, 24, -31.42, 41.47, 0.8, 25, -84.86, 96.34, 0.2, 2, 24, -15.82, 58.32, 0.9, 25, -69.27, 113.2, 0.1, 2, 24, -26.68, 61.46, 0.9, 25, -80.12, 116.33, 0.1, 2, 24, -34.31, 73.83, 0.9, 25, -87.75, 128.7, 0.1, 2, 24, -27.94, 89.4, 0.9, 25, -81.38, 144.27, 0.1, 2, 24, -32.4, 101.32, 0.9, 25, -85.85, 156.2, 0.1, 2, 24, -39.93, 130.06, 0.9, 25, -93.38, 184.94, 0.1, 2, 24, -13.23, 150.21, 0.9, 25, -66.68, 205.09, 0.1, 2, 24, 10.22, 167.91, 0.9, 25, -43.23, 222.78, 0.1, 2, 24, 45.18, 185.36, 0.9, 25, -8.26, 240.24, 0.1, 2, 24, 72.55, 191.86, 0.9, 25, 19.11, 246.74, 0.1, 2, 24, 102.93, 192.12, 0.9, 25, 49.48, 246.99, 0.1, 2, 24, 130.25, 184.41, 0.9, 25, 76.81, 239.29, 0.1, 2, 24, 154.7, 172.27, 0.9, 25, 101.26, 227.15, 0.1, 2, 24, 184.78, 153.58, 0.9, 25, 131.34, 208.46, 0.1, 2, 24, 212.28, 117.09, 0.9, 25, 158.84, 171.97, 0.1, 2, 24, 233.68, 88.67, 0.9, 25, 180.23, 143.55, 0.1, 2, 24, 233.91, 67.1, 0.9, 25, 180.46, 121.98, 0.1, 2, 24, 18.28, 82.92, 0.75, 25, -35.17, 137.8, 0.25, 2, 24, 47.89, 96.21, 0.75, 25, -5.56, 151.09, 0.25, 2, 24, 91.79, 101.07, 0.75, 25, 38.34, 155.94, 0.25, 2, 24, 124.65, 96.53, 0.75, 25, 71.21, 151.4, 0.25, 2, 24, 155.66, 77.87, 0.75, 25, 102.22, 132.75, 0.25, 2, 24, 175.74, 54.92, 0.75, 25, 122.3, 109.8, 0.25, 2, 24, 181, 19.33, 0.75, 25, 127.56, 74.21, 0.25, 2, 24, 193.29, 4.5, 0.75, 25, 139.85, 59.38, 0.25, 2, 24, 205.76, -13.57, 0.75, 25, 152.31, 41.31, 0.25, 2, 24, 201.3, -30.94, 0.75, 25, 147.85, 23.93, 0.25, 2, 24, 102.04, -7.15, 0.3, 25, 48.6, 47.72, 0.7, 2, 24, 83.6, -17.85, 0.3, 25, 30.15, 37.02, 0.7, 2, 24, 62.26, -20.45, 0.3, 25, 8.82, 34.42, 0.7, 2, 24, 41.24, -22.86, 0.3, 25, -12.21, 32.02, 0.7, 2, 24, 26.29, -29.7, 0.3, 25, -27.15, 25.17, 0.7, 2, 24, 26.03, -45.76, 0.3, 25, -27.41, 9.12, 0.7, 2, 24, 49.9, -73.84, 0.3, 25, -3.55, -18.96, 0.7, 2, 24, 58.16, -68.43, 0.3, 25, 4.71, -13.55, 0.7, 2, 24, 67.51, -57.19, 0.3, 25, 14.07, -2.32, 0.7, 2, 24, 81.89, -47.16, 0.3, 25, 28.44, 7.71, 0.7, 2, 24, 97.37, -40.42, 0.3, 25, 43.93, 14.45, 0.7, 2, 24, 111.84, -37.19, 0.3, 25, 58.4, 17.68, 0.7, 2, 24, 123.56, -47.67, 0.46, 25, 70.11, 7.21, 0.54, 2, 24, 136.26, -59.31, 0.48, 25, 82.81, -4.43, 0.52, 2, 24, 149.62, -66.71, 0.5, 25, 96.17, -11.83, 0.5, 2, 24, 100.77, 6.31, 0.4, 25, 47.32, 61.19, 0.6, 2, 24, 99.86, 30.39, 0.47, 25, 46.41, 85.26, 0.53, 2, 24, 94.46, 56.35, 0.5, 25, 41.02, 111.23, 0.5, 2, 24, 79.37, 80.57, 0.65, 25, 25.93, 135.44, 0.35, 1, 24, 57.01, 56.58, 1, 2, 24, 67.27, 53.28, 0.5, 25, 13.82, 108.16, 0.5, 2, 24, 76.08, 43.46, 0.48, 25, 22.63, 98.34, 0.52, 2, 24, 81.25, 35.09, 0.46, 25, 27.8, 89.97, 0.54, 2, 24, 84.45, 24.81, 0.45, 25, 31, 79.69, 0.55, 2, 24, 84.46, 15.2, 0.45, 25, 31.01, 70.08, 0.55, 2, 24, 81.65, 7.09, 0.4, 25, 28.2, 61.96, 0.6, 2, 24, 72.26, -1.11, 0.4, 25, 18.81, 53.77, 0.6, 2, 24, 56.56, 67.44, 0.7, 25, 3.12, 122.31, 0.3, 2, 24, 69.11, 64.43, 0.65, 25, 15.66, 119.3, 0.35, 2, 24, 80.09, 55.1, 0.5, 25, 26.65, 109.97, 0.5, 2, 24, 90.76, 40.92, 0.5, 25, 37.32, 95.8, 0.5, 2, 24, 96.09, 25.92, 0.45, 25, 42.65, 80.8, 0.55, 2, 24, 95.82, 10.45, 0.42, 25, 42.38, 65.33, 0.58, 2, 24, 88.55, 0.72, 0.42, 25, 35.1, 55.59, 0.58, 2, 24, 81.04, -4.4, 0.42, 25, 27.6, 50.48, 0.58, 2, 24, 68.93, -10.34, 0.42, 25, 15.49, 44.54, 0.58, 2, 24, 66, -5.4, 0.4, 25, 12.55, 49.48, 0.6, 2, 24, 59.1, 0.81, 0.4, 25, 5.65, 55.68, 0.6, 2, 24, 49.84, 11.2, 0.45, 25, -3.61, 66.08, 0.55, 2, 24, 44.1, 24.25, 0.46, 25, -9.35, 79.12, 0.54, 2, 24, 41.53, 36.56, 0.48, 25, -11.92, 91.44, 0.52, 2, 24, 45.6, 48.44, 0.5, 25, -7.85, 103.32, 0.5, 1, 24, 50.37, 58, 1, 2, 24, 89.53, -49.01, 0.5, 25, 36.09, 5.87, 0.5, 2, 24, 96.55, -48.29, 0.5, 25, 43.1, 6.58, 0.5, 2, 24, 104.03, -48.91, 0.5, 25, 50.58, 5.97, 0.5, 2, 24, 111.74, -51.12, 0.46, 25, 58.29, 3.76, 0.54, 2, 24, 120.24, -54.85, 0.46, 25, 66.79, 0.03, 0.54, 2, 24, 127.71, -61.93, 0.48, 25, 74.26, -7.06, 0.52, 2, 24, 128.9, -69.06, 0.5, 25, 75.46, -14.18, 0.5, 2, 24, 126.32, -75.55, 0.6, 25, 72.87, -20.67, 0.4, 2, 24, 118.92, -80.24, 0.6, 25, 65.48, -25.36, 0.4, 2, 24, 110.29, -82.48, 0.6, 25, 56.85, -27.6, 0.4, 2, 24, 102.91, -81.64, 0.6, 25, 49.46, -26.77, 0.4, 2, 24, 94.85, -77.79, 0.5, 25, 41.41, -22.91, 0.5, 2, 24, 89.02, -66.44, 0.48, 25, 35.58, -11.57, 0.52, 2, 24, 87.41, -56.5, 0.46, 25, 33.96, -1.62, 0.54, 2, 24, 86.48, -49.14, 0.5, 25, 33.04, 5.74, 0.5, 2, 24, 74.79, 86.66, 0.7, 25, 21.35, 141.54, 0.3, 2, 24, 87.44, 83.56, 0.69, 25, 33.99, 138.43, 0.31, 2, 24, 99.01, 75.83, 0.65, 25, 45.56, 130.71, 0.35, 2, 24, 111.53, 65.23, 0.6, 25, 58.09, 120.11, 0.4, 2, 24, 119.76, 46.64, 0.48, 25, 66.32, 101.51, 0.52, 2, 24, 120.19, 31.16, 0.5, 25, 66.75, 86.03, 0.5, 2, 24, 123.59, 12.53, 0.4, 25, 70.14, 67.41, 0.6, 2, 24, 125.51, -3.32, 0.4, 25, 72.06, 51.56, 0.6, 2, 24, 118.74, -14.05, 0.3, 25, 65.29, 40.82, 0.7, 2, 24, 108.62, -13.89, 0.3, 25, 55.18, 40.99, 0.7, 2, 24, 117.92, -42.22, 0.5, 25, 64.48, 12.66, 0.5, 2, 24, 124.08, -37.9, 0.5, 25, 70.64, 16.98, 0.5, 2, 24, 133.28, -37.32, 0.5, 25, 79.84, 17.56, 0.5, 2, 24, 148.39, -48.06, 0.4, 25, 94.95, 6.81, 0.6, 2, 24, 161.62, -58.05, 0.5, 25, 108.17, -3.17, 0.5, 2, 24, 154.2, -71.76, 0.65, 25, 100.76, -16.89, 0.35, 2, 24, 24.14, 36.33, 0.5, 25, -29.31, 91.2, 0.5, 2, 24, 5.32, 22.64, 0.5, 25, -48.12, 77.51, 0.5, 2, 24, -12.24, -7.49, 0.5, 25, -65.68, 47.38, 0.5, 2, 24, 38.1, -92.71, 0.5, 25, -15.34, -37.83, 0.5, 2, 24, 55.99, -93.27, 0.5, 25, 2.55, -38.39, 0.5, 2, 24, 71.13, -88.06, 0.5, 25, 17.69, -33.19, 0.5, 2, 24, 116.19, 54.71, 0.5, 25, 62.74, 109.59, 0.5, 2, 24, 136.87, 56.81, 0.5, 25, 83.42, 111.68, 0.5, 2, 24, 157.84, 35.95, 0.5, 25, 104.4, 90.82, 0.5, 2, 24, 163.91, 10.27, 0.5, 25, 110.47, 65.14, 0.5, 2, 24, 162.04, -4.14, 0.5, 25, 108.59, 50.74, 0.5, 2, 24, 174.06, -16.6, 0.5, 25, 120.62, 38.28, 0.5, 2, 24, 180.92, -32.03, 0.5, 25, 127.47, 22.85, 0.5, 2, 24, 173.32, -45.15, 0.5, 25, 119.88, 9.72, 0.5, 2, 24, 114.27, 59.05, 0.5, 25, 60.82, 113.93, 0.5, 2, 24, 113.82, -75.26, 0.5, 25, 60.38, -20.38, 0.5, 2, 24, 102.33, -76.8, 0.5, 25, 48.89, -21.92, 0.5, 2, 24, 114.91, -27.39, 0.3, 25, 61.46, 27.48, 0.7, 2, 24, 58.17, -9.86, 0.42, 25, 4.72, 45.02, 0.58, 2, 24, 49.25, -0.6, 0.45, 25, -4.19, 54.28, 0.55, 2, 24, 38.9, 17.5, 0.46, 25, -14.54, 72.37, 0.54, 2, 24, 34.77, 30.64, 0.48, 25, -18.67, 85.52, 0.52, 2, 24, 35.77, 42.89, 0.5, 25, -17.68, 97.77, 0.5, 2, 24, 37.26, 52.91, 0.65, 25, -16.19, 107.79, 0.35, 2, 24, 43.39, 63.05, 0.7, 25, -10.06, 117.93, 0.3, 2, 24, 102.79, -39.21, 0.3, 25, 49.35, 15.66, 0.7, 2, 24, 80.91, -56.24, 0.5, 25, 27.46, -1.36, 0.5, 2, 24, 81.5, -65.4, 0.48, 25, 28.05, -10.52, 0.52, 2, 24, 85.67, -74.66, 0.5, 25, 32.23, -19.79, 0.5, 2, 24, 89.38, -80.16, 0.5, 25, 35.94, -25.28, 0.5, 2, 24, 94.2, -86.27, 0.7, 25, 40.75, -31.39, 0.3, 2, 24, 100.64, -91.55, 0.7, 25, 47.19, -36.68, 0.3, 2, 24, 109.4, -90.82, 0.7, 25, 55.96, -35.94, 0.3, 2, 24, 118.19, -87.05, 0.7, 25, 64.75, -32.18, 0.3, 2, 24, 123.12, -84.58, 0.8, 25, 69.68, -29.71, 0.2, 2, 24, 131.68, -76.02, 0.7, 25, 78.24, -21.15, 0.3, 2, 24, 134.95, -68.37, 0.5, 25, 81.51, -13.5, 0.5, 2, 24, -6.91, -30.6, 0.5, 25, -60.35, 24.28, 0.5, 2, 24, -3.96, -37.36, 0.5, 25, -57.4, 17.52, 0.5, 2, 24, -4.77, -43.66, 0.5, 25, -58.21, 11.21, 0.5, 2, 24, -5.03, -52.21, 0.5, 25, -58.47, 2.67, 0.5, 2, 24, -3.4, -62.24, 0.5, 25, -56.84, -7.37, 0.5, 2, 24, 0.06, -70.51, 0.5, 25, -53.39, -15.64, 0.5, 2, 24, 4.1, -78.22, 0.5, 25, -49.34, -23.34, 0.5, 2, 24, 10.14, -83.53, 0.5, 25, -43.31, -28.66, 0.5, 2, 24, 15.88, -84.26, 0.5, 25, -37.57, -29.39, 0.5, 2, 24, 20.58, -87.98, 0.5, 25, -32.86, -33.11, 0.5, 2, 24, 25.08, -87.24, 0.5, 25, -28.36, -32.37, 0.5, 2, 24, 25.49, -81.75, 0.47, 25, -27.95, -26.87, 0.53, 2, 24, 28.08, -76.63, 0.43, 25, -25.36, -21.75, 0.57, 2, 24, 29.62, -71.8, 0.35, 25, -23.82, -16.92, 0.65, 2, 24, 27.39, -67.66, 0.35, 25, -26.05, -12.79, 0.65, 2, 24, 23.68, -64.77, 0.35, 25, -29.76, -9.89, 0.65, 2, 24, 24.68, -61.23, 0.35, 25, -28.76, -6.35, 0.65, 2, 24, 24, -56.81, 0.35, 25, -29.44, -1.93, 0.65, 2, 24, 19.26, -50.52, 0.47, 25, -34.19, 4.36, 0.53, 2, 24, 10.76, -44.28, 0.45, 25, -42.69, 10.6, 0.55, 2, 24, 5.06, -36.56, 0.47, 25, -48.39, 18.31, 0.53, 2, 24, 0.71, -27.95, 0.47, 25, -52.74, 26.93, 0.53, 2, 24, -4.23, -26.6, 0.5, 25, -57.67, 28.27, 0.5, 2, 24, 29.56, -52.41, 0.3, 25, -23.88, 2.47, 0.7, 2, 24, 31.16, -61.05, 0.3, 25, -22.28, -6.18, 0.7, 2, 24, 38.18, -67.74, 0.3, 25, -15.27, -12.86, 0.7], "hull": 36, "edges": [50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 28, 30, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 6, 4, 4, 2, 2, 0, 0, 72, 72, 70, 68, 70, 66, 68, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 50, 52, 52, 54, 70, 0, 40, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 6, 8, 92, 8, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 106, 108, 108, 110, 110, 112, 112, 114, 118, 120, 120, 122, 94, 124, 124, 126, 126, 128, 128, 130, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 146, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 132, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 180, 130, 210, 210, 212, 212, 214, 214, 216, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 94, 116, 230, 230, 118, 230, 232, 232, 234, 234, 236, 236, 238, 8, 10, 238, 10, 122, 240, 10, 12, 12, 14, 240, 12, 242, 244, 244, 246, 248, 250, 250, 252, 254, 218, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 238, 176, 134, 134, 152, 152, 128, 216, 270, 270, 254, 128, 270, 270, 256, 238, 122, 192, 272, 272, 274, 274, 202, 228, 276, 276, 116, 164, 278, 278, 280, 280, 282, 282, 284, 176, 286, 286, 242, 286, 288, 288, 290, 290, 148, 284, 286, 114, 292, 292, 116, 112, 294, 294, 296, 296, 298, 202, 300, 300, 252, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 122, 314, 314, 192, 312, 314, 314, 120, 316, 318, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 342, 344, 344, 346, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 316, 336, 248, 360, 246, 104, 362, 362, 364, 364, 366, 366, 106], "width": 284, "height": 317}}, "shenti": {"shenti": {"type": "mesh", "uvs": [0.59361, 0.02908, 0.62447, 0.04792, 0.61512, 0.09323, 0.6198, 0.13031, 0.63008, 0.15856, 0.67872, 0.16857, 0.74512, 0.16386, 0.82087, 0.16209, 0.87324, 0.18446, 0.86576, 0.2492, 0.8525, 0.3102, 0.82538, 0.36199, 0.79358, 0.40731, 0.75804, 0.46028, 0.70848, 0.50913, 0.72472, 0.54715, 0.69666, 0.58835, 0.68263, 0.59835, 0.69011, 0.63308, 0.71723, 0.66073, 0.73968, 0.69781, 0.82207, 0.75281, 0.88847, 0.81226, 0.94459, 0.87699, 0.97358, 0.92582, 1, 1, 0.43612, 1, 0, 1, 0, 0.97732, 0.01511, 0.94636, 0.03469, 0.89833, 0.06531, 0.84716, 0.10004, 0.7931, 0.13896, 0.73934, 0.17209, 0.69384, 0.19017, 0.6553, 0.18515, 0.62252, 0.18113, 0.59377, 0.16238, 0.58336, 0.15625, 0.57745, 0.15141, 0.57035, 0.15152, 0.55925, 0.15698, 0.54226, 0.16552, 0.52574, 0.17502, 0.51266, 0.16276, 0.50201, 0.16021, 0.5165, 0.15869, 0.52307, 0.1538, 0.54194, 0.15022, 0.55978, 0.14961, 0.57663, 0.14803, 0.60808, 0.13896, 0.63148, 0.09997, 0.62406, 0.06188, 0.60408, 0.07458, 0.54983, 0.1045, 0.49908, 0.1172, 0.46369, 0.0909, 0.42888, 0.09453, 0.39007, 0.11557, 0.34782, 0.10889, 0.29808, 0.11446, 0.24273, 0.15454, 0.18668, 0.23804, 0.18668, 0.3093, 0.17687, 0.31709, 0.15024, 0.31932, 0.11311, 0.35828, 0.08649, 0.35049, 0.04935, 0.35828, 0, 0.4393, 0, 0.47297, 0, 0.53282, 0.02202, 0.43165, 0.20598, 0.38546, 0.28661, 0.3497, 0.36161, 0.33332, 0.40568, 0.34822, 0.48725, 0.35864, 0.55007, 0.37056, 0.62132, 0.37354, 0.67758, 0.38099, 0.7481, 0.38695, 0.81373, 0.40036, 0.88124, 0.41526, 0.94406, 0.4636, 0.33497, 0.51393, 0.30802, 0.60817, 0.30734, 0.66706, 0.3161, 0.69705, 0.36395, 0.70026, 0.44481, 0.62316, 0.48053, 0.51715, 0.46436, 0.46039, 0.41921, 0.45932, 0.3626, 0.6071, 0.3936, 0.15213, 0.32958, 0.17998, 0.30465, 0.26672, 0.29993, 0.31276, 0.33093, 0.32026, 0.36867, 0.30312, 0.42258, 0.26564, 0.46301, 0.23566, 0.48862, 0.17034, 0.48323, 0.13179, 0.3936, 0.7194, 0.1879, 0.68284, 0.23621, 0.60377, 0.35438, 0.56747, 0.36146, 0.55882, 0.38702, 0.57352, 0.41422, 0.61328, 0.42564, 0.64785, 0.41639, 0.64958, 0.39736, 0.64699, 0.38049, 0.63143, 0.36689, 0.13818, 0.37349, 0.11688, 0.38835, 0.11372, 0.40323, 0.1232, 0.41518, 0.15366, 0.4191, 0.18204, 0.4069, 0.18221, 0.38693, 0.16474, 0.37335, 0.17401, 0.37898, 0.60442, 0.54151, 0.50445, 0.53855, 0.50092, 0.61553, 0.61265, 0.61997, 0.63617, 0.69028], "triangles": [50, 55, 49, 53, 54, 55, 55, 50, 53, 51, 53, 50, 52, 53, 51, 47, 56, 46, 48, 56, 47, 55, 56, 48, 48, 49, 55, 46, 56, 45, 125, 97, 98, 118, 60, 97, 125, 118, 97, 126, 125, 98, 126, 99, 100, 124, 126, 100, 119, 60, 118, 59, 60, 119, 106, 119, 118, 106, 118, 125, 106, 125, 126, 106, 126, 124, 120, 59, 119, 120, 119, 106, 124, 122, 106, 121, 120, 106, 123, 122, 124, 121, 106, 122, 123, 124, 102, 58, 59, 120, 58, 120, 121, 103, 123, 102, 57, 58, 121, 57, 121, 122, 105, 122, 123, 104, 105, 123, 57, 122, 105, 88, 110, 87, 117, 109, 88, 109, 110, 88, 116, 117, 89, 111, 87, 110, 96, 109, 117, 110, 109, 96, 111, 110, 96, 115, 116, 90, 112, 111, 96, 116, 115, 96, 111, 94, 95, 96, 115, 113, 116, 96, 117, 114, 113, 115, 112, 96, 113, 115, 90, 91, 114, 115, 91, 93, 111, 112, 95, 87, 111, 93, 94, 111, 92, 113, 114, 92, 114, 91, 112, 113, 92, 93, 112, 92, 65, 66, 74, 75, 65, 74, 99, 64, 65, 99, 65, 75, 98, 63, 64, 98, 64, 99, 62, 63, 98, 61, 62, 98, 97, 61, 98, 60, 61, 97, 126, 98, 99, 107, 5, 6, 108, 5, 107, 9, 7, 8, 4, 87, 3, 108, 88, 4, 108, 4, 5, 4, 88, 87, 9, 6, 7, 89, 88, 108, 9, 10, 6, 10, 107, 6, 10, 108, 107, 108, 90, 89, 108, 10, 90, 11, 90, 10, 89, 117, 88, 90, 116, 89, 12, 90, 11, 91, 90, 12, 13, 91, 12, 14, 91, 13, 73, 72, 0, 71, 69, 70, 68, 69, 71, 2, 0, 1, 72, 74, 68, 66, 67, 68, 74, 66, 68, 3, 73, 2, 2, 73, 0, 71, 72, 68, 73, 3, 74, 72, 73, 74, 3, 87, 74, 86, 74, 87, 100, 99, 75, 75, 74, 86, 86, 95, 75, 76, 100, 75, 95, 76, 75, 101, 100, 76, 124, 100, 101, 77, 101, 76, 102, 124, 101, 102, 101, 77, 87, 95, 86, 77, 76, 94, 94, 76, 95, 94, 78, 77, 102, 77, 78, 103, 102, 78, 103, 104, 123, 45, 56, 57, 105, 45, 57, 44, 105, 104, 45, 105, 44, 128, 94, 93, 127, 93, 92, 128, 93, 127, 128, 78, 94, 38, 39, 41, 40, 41, 39, 37, 38, 42, 38, 41, 42, 37, 44, 104, 43, 44, 37, 37, 42, 43, 104, 103, 78, 104, 78, 79, 128, 79, 78, 129, 79, 128, 80, 79, 129, 79, 36, 37, 81, 80, 129, 104, 79, 37, 129, 128, 127, 80, 36, 79, 36, 80, 35, 82, 81, 129, 81, 82, 34, 35, 80, 81, 34, 35, 81, 82, 33, 34, 82, 32, 33, 82, 31, 32, 30, 31, 82, 27, 28, 29, 82, 29, 30, 82, 27, 29, 83, 27, 82, 27, 83, 84, 27, 84, 85, 27, 85, 26, 26, 23, 24, 26, 22, 23, 25, 26, 24, 26, 21, 22, 21, 26, 131, 92, 91, 14, 127, 92, 14, 16, 127, 14, 16, 14, 15, 17, 127, 16, 130, 127, 17, 130, 17, 18, 131, 130, 18, 131, 18, 19, 129, 127, 130, 131, 83, 82, 83, 131, 84, 82, 129, 131, 130, 131, 129, 85, 84, 131, 26, 85, 131, 21, 131, 20, 131, 19, 20], "vertices": [2, 23, 117.36, -62.3, 0.93449, 119, 2.63, 139.29, 0.06551, 2, 23, 103.29, -78.2, 0.90766, 119, 18.78, 125.5, 0.09234, 2, 23, 67.54, -75.2, 0.78343, 119, 16.41, 89.71, 0.21657, 2, 23, 38.57, -78.83, 0.5266, 119, 20.55, 60.8, 0.4734, 2, 23, 16.65, -84.92, 0.21685, 119, 27.02, 38.99, 0.78315, 2, 23, 9.89, -109.27, 0.03177, 119, 51.49, 32.67, 0.96823, 2, 23, 15.07, -141.87, 3e-05, 119, 84, 38.42, 0.99997, 1, 119, 121.26, 42.17, 1, 4, 5, -169.68, 386.7, 0.00016, 17, 235.17, -268.8, 0.00038, 18, 240.86, -197.51, 0.00043, 119, 148.19, 26.28, 0.99903, 6, 5, -134.47, 349.86, 0.00674, 16, 306.74, -228.07, 7e-05, 17, 185.05, -259.58, 0.01351, 18, 190.65, -206.25, 0.01956, 21, 102.95, -211.38, 0.00919, 119, 147.7, -24.68, 0.95092, 6, 5, -103.36, 312.87, 0.02612, 16, 258.61, -223.68, 0.0027, 17, 138.16, -247.84, 0.04951, 18, 142.61, -211.51, 0.06953, 21, 54.71, -214.35, 0.03929, 119, 144.18, -72.88, 0.81286, 6, 5, -82.2, 275.66, 0.05475, 16, 217.39, -212.12, 0.01139, 17, 99.21, -230.09, 0.09608, 18, 99.92, -208.37, 0.12316, 21, 12.22, -209.19, 0.06334, 119, 133.37, -114.3, 0.65128, 6, 5, -66.36, 240.15, 0.09597, 16, 181.15, -198.02, 0.02982, 17, 65.56, -210.6, 0.1492, 18, 61.59, -201.76, 0.16536, 21, -25.75, -200.76, 0.06897, 119, 119.94, -150.8, 0.49068, 6, 5, -47.3, 199.23, 0.17017, 16, 138.82, -182.35, 0.07039, 17, 26.15, -188.61, 0.20615, 18, 17, -194.81, 0.1784, 21, -69.96, -191.7, 0.05268, 119, 105.04, -193.4, 0.32221, 6, 5, -35.3, 155.35, 0.31781, 16, 99.42, -159.61, 0.14438, 17, -9.3, -160.08, 0.21824, 18, -26.14, -180.36, 0.13127, 21, -112.37, -175.21, 0.02312, 119, 83.02, -233.21, 0.16517, 6, 5, -7.78, 141.27, 0.5011, 16, 69.96, -168.97, 0.19539, 17, -39.84, -164.8, 0.08, 18, -53.15, -195.38, 0.09937, 21, -140.06, -188.93, 0.01109, 119, 92.91, -262.5, 0.11305, 5, 5, 6.9, 109.3, 0.66636, 16, 37.03, -156.57, 0.19609, 18, -87.89, -189.77, 0.06361, 21, -174.49, -181.68, 0.00441, 119, 81.11, -295.64, 0.06952, 5, 5, 8.07, 98.89, 0.72412, 16, 28.87, -150, 0.17374, 18, -97.19, -184.96, 0.04774, 21, -183.55, -176.43, 0.00258, 119, 74.69, -303.92, 0.05182, 7, 5, 30.77, 83.34, 0.84294, 6, -91.2, 53.92, 0.00123, 16, 1.81, -154.92, 0.07539, 17, -105.03, -140.45, 0.04441, 18, -122.74, -195.15, 0.01702, 21, -209.55, -185.4, 0.00031, 119, 80.1, -330.89, 0.01871, 7, 5, 55.85, 78.71, 0.91277, 6, -66.14, 58.71, 0.03375, 16, -19.28, -169.28, 0.02285, 17, -128.07, -151.4, 0.01725, 18, -140.55, -213.42, 0.00626, 21, -228.22, -202.79, 0, 119, 94.84, -351.72, 0.00711, 6, 5, 84.88, 67.4, 0.76198, 6, -34.99, 58.71, 0.22935, 16, -47.86, -181.66, 0.002, 17, -158.22, -159.25, 0.00384, 18, -166.1, -231.23, 0.00127, 119, 107.74, -380.07, 0.00156, 3, 5, 144.2, 68.62, 0.13643, 6, 19.85, 81.37, 0.82058, 7, -75.14, 81.75, 0.04299, 3, 5, 200.81, 61.64, 0.00109, 6, 75.13, 95.41, 0.65639, 7, -19.79, 95.51, 0.34252, 2, 6, 132.49, 103.23, 0.22886, 7, 37.61, 103.04, 0.77114, 2, 6, 173.4, 102.97, 0.06158, 7, 78.52, 102.58, 0.93842, 1, 7, 137.54, 93.75, 1, 6, 5, 160.17, -202.95, 0.02376, 6, 133.3, -165.88, 0.17029, 7, 37.08, -166.06, 0.26812, 4, 46.33, 145.91, 0.31222, 3, 156.48, 145.52, 0.22143, 2, 181.17, 192.57, 0.00418, 1, 4, 96.75, -63.55, 1, 2, 4, 79.44, -67.72, 0.99617, 3, 187.82, -68.37, 0.00383, 2, 4, 54.07, -66.15, 0.95169, 3, 162.46, -66.59, 0.04831, 2, 4, 15.15, -65.57, 0.68452, 3, 123.55, -65.69, 0.31548, 2, 4, -27.45, -60.26, 0.18836, 3, 81, -60.04, 0.81164, 3, 4, -72.72, -53.52, 0.00385, 3, 35.78, -52.91, 0.98095, 2, 141.16, -36.22, 0.0152, 2, 3, -9.67, -43.72, 0.48011, 2, 95.48, -44.24, 0.51989, 4, 3, -48.16, -35.85, 0.02333, 2, 56.78, -50.94, 0.95685, 16, -57.34, 98.58, 0.01376, 17, -124.53, 119.13, 0.00606, 3, 2, 26.78, -60.7, 0.83473, 16, -26.71, 91.02, 0.12207, 17, -95.43, 106.95, 0.04321, 5, 2, 6.96, -77.3, 0.5987, 16, -1.12, 94.65, 0.27577, 17, -69.58, 106.61, 0.12498, 18, -175.2, 48.86, 0.00018, 127, 143.64, 303.63, 0.00037, 5, 2, -10.53, -91.7, 0.37693, 16, 21.34, 97.65, 0.35761, 17, -46.93, 106.12, 0.25963, 18, -153.79, 56.26, 0.00325, 127, 141.37, 281.08, 0.00258, 5, 2, -12.03, -103.97, 0.29083, 16, 29.09, 107.27, 0.35027, 17, -37.79, 114.43, 0.34726, 18, -148.1, 67.23, 0.00704, 127, 148.94, 271.32, 0.0046, 5, 2, -14.14, -109.08, 0.26057, 16, 33.59, 110.5, 0.34287, 17, -32.85, 116.94, 0.38202, 18, -144.34, 71.29, 0.009, 127, 151.05, 266.2, 0.00554, 5, 2, -17.38, -114.21, 0.22188, 16, 39.05, 113.14, 0.33058, 17, -27.05, 118.71, 0.43042, 18, -139.51, 74.96, 0.01082, 127, 152.35, 260.27, 0.0063, 5, 2, -24.6, -119.1, 0.2061, 16, 47.76, 113.48, 0.32404, 17, -18.39, 117.7, 0.44557, 18, -131.04, 77.03, 0.01558, 127, 150.67, 251.72, 0.00871, 5, 2, -37.13, -124.42, 0.17797, 16, 61.2, 111.38, 0.30721, 17, -5.43, 113.56, 0.47359, 18, -117.45, 77.64, 0.02697, 127, 145.52, 239.13, 0.01427, 5, 2, -50.2, -128.27, 0.14785, 16, 74.34, 107.75, 0.28102, 17, 7, 107.96, 0.50647, 18, -103.85, 76.69, 0.04291, 127, 138.95, 227.18, 0.02175, 5, 2, -61.32, -130.21, 0.12303, 16, 84.81, 103.53, 0.25336, 17, 16.69, 102.18, 0.53538, 18, -92.75, 74.63, 0.05919, 127, 132.42, 217.97, 0.02905, 6, 2, -64.79, -139.93, 0.10777, 16, 92.89, 109.95, 0.21981, 17, 25.66, 107.28, 0.54117, 18, -86.11, 82.53, 0.07953, 127, 136.8, 208.63, 0.03855, 20, -96.22, 0.25, 0.01316, 2, 147, 60.51, 12.35, 0.5, 148, 7.45, 12.7, 0.5, 2, 147, 65.72, 12.3, 0.2, 148, 12.66, 12.9, 0.8, 2, 148, 27.66, 13.23, 0.65, 149, -12.54, 12.37, 0.35, 2, 148, 41.75, 14.04, 0.5, 149, 1.47, 14.17, 0.5, 1, 149, 14.35, 17.2, 1, 1, 149, 38.44, 22.65, 1, 1, 149, 57.34, 22.92, 1, 1, 149, 56.54, 2.81, 1, 1, 149, 46.08, -19.34, 1, 3, 20, -129.19, 47.36, 0.01534, 148, 40.88, -24.13, 0.49233, 149, 3.28, -23.95, 0.49233, 2, 147, 50.72, -16.78, 0.5, 148, -0.98, -16.84, 0.5, 6, 2, -76.86, -175.51, 0.04794, 16, 121.93, 133.79, 0.09656, 17, 58.02, 126.37, 0.4085, 18, -62.38, 111.67, 0.14547, 127, 153.29, 174.86, 0.0756, 20, -64.03, 19.62, 0.22593, 6, 2, -92.05, -201.69, 0.01953, 16, 148.65, 147.99, 0.03749, 17, 86.61, 136.3, 0.29823, 18, -39.02, 130.9, 0.19577, 127, 160.94, 145.59, 0.11738, 20, -35.54, 29.8, 0.33161, 6, 2, -118.18, -217.44, 0.00927, 16, 179.16, 147.57, 0.01598, 17, 116.69, 131.2, 0.21496, 18, -9.03, 136.55, 0.21476, 127, 153.48, 116, 0.15878, 20, -5.41, 24.96, 0.38625, 7, 2, -151.41, -227.63, 0.00306, 16, 212.76, 138.67, 0.00392, 17, 148.52, 117.25, 0.13076, 18, 25.66, 134.51, 0.2304, 21, -45.64, 136.83, 0.00212, 127, 137.06, 85.36, 0.28987, 20, 26.54, 11.28, 0.33986, 7, 2, -181.75, -252.44, 0.00032, 16, 251.62, 143.72, 6e-05, 17, 187.7, 116.27, 0.06128, 18, 62.75, 147.18, 0.18999, 21, -8, 147.73, 0.00543, 127, 132.99, 46.39, 0.69285, 20, 65.73, 10.64, 0.05008, 3, 17, 230.59, 108.79, 0.00498, 18, 105.57, 155.05, 0.01985, 127, 122.16, 4.22, 0.97517, 1, 127, 94.48, -35.3, 1, 2, 23, -14.19, 107.55, 0.00279, 127, 53.96, -27.58, 0.99721, 3, 22, 24.83, 70.42, 0.01283, 23, -4.9, 72.74, 0.13819, 127, 17.94, -28.56, 0.84898, 3, 22, 46.04, 71.65, 0.02507, 23, 16.15, 69.84, 0.418, 127, 10.25, -48.37, 0.55693, 3, 22, 74.62, 77.5, 0.00396, 23, 45.32, 70.06, 0.67341, 127, 3.71, -76.8, 0.32263, 2, 23, 67.07, 51.78, 0.86604, 127, -19.11, -93.73, 0.13396, 2, 23, 96.02, 56.95, 0.97095, 127, -20.78, -123.08, 0.02905, 2, 23, 134.9, 54.86, 0.99866, 127, -31.81, -160.42, 0.00134, 1, 23, 136.71, 14.88, 1, 2, 23, 137.47, -1.73, 0.99983, 119, -58.28, 158.32, 0.00017, 2, 23, 121.54, -32.05, 0.97719, 119, -27.68, 142.94, 0.02281, 3, 22, 16.98, 6.28, 0.91757, 23, -24.99, 11.32, 0.0045, 127, -37.16, 5.2, 0.07793, 5, 18, 104.62, 16.82, 0.01723, 21, 27.63, 15.52, 0.70049, 127, -2.9, 63.11, 0.08781, 19, -102.93, 83.51, 0.1, 20, 60.95, -126.2, 0.09446, 7, 2, -207.92, -126.13, 1e-05, 17, 125.14, 3.46, 0.00304, 18, 43.21, 19.67, 0.5787, 21, -33.57, 21.29, 0.00971, 127, 25.46, 117.64, 0.03312, 19, -120.59, 24.63, 0.17542, 20, 4.15, -102.71, 0.2, 7, 2, -174.81, -113.24, 0.00071, 16, 172.22, 29.18, 0.00018, 17, 91.64, 15.28, 0.13328, 18, 7.69, 19.14, 0.47184, 127, 39.89, 150.11, 0.01856, 19, -128.69, -9.97, 0.17542, 20, -29.46, -91.18, 0.2, 6, 2, -126.17, -70.94, 0.00507, 16, 108.58, 18.95, 0.01579, 17, 27.19, 14.95, 0.73347, 127, 44.64, 214.39, 0.0018, 19, -121.33, -74, 0.09387, 20, -93.9, -92.07, 0.15, 6, 2, -88.43, -38.8, 0.01635, 16, 59.55, 11.59, 0.867, 17, -22.39, 15.21, 0.05738, 127, 48.81, 263.79, 0.00017, 19, -116.17, -123.31, 0.0291, 20, -143.48, -92.24, 0.03, 3, 2, -45.63, -2.3, 0.00421, 16, 3.93, 3.19, 0.99448, 17, -78.64, 15.46, 0.00131, 5, 5, -48.25, -56.06, 0.14501, 6, -114.22, -104.66, 0.00053, 3, -83.09, 58.21, 0.00358, 2, -10.05, 23.9, 0.64805, 16, -40.11, -0.26, 0.20284, 7, 5, -4.73, -90.48, 0.27608, 6, -61.18, -120.93, 0.03072, 7, -157.17, -120.15, 0.00202, 4, -139.55, 73.15, 0.00078, 3, -29.99, 74.3, 0.11985, 2, 33.52, 58.25, 0.56767, 16, -95.25, -6.42, 0.00287, 6, 5, 35.45, -122.86, 0.21591, 6, -11.99, -136.52, 0.1101, 7, -108.06, -135.98, 0.02412, 4, -90.15, 88.07, 0.02543, 3, 19.53, 88.82, 0.36368, 2, 74.34, 89.82, 0.26075, 6, 5, 79.19, -153.5, 0.11135, 6, 39.89, -149.2, 0.17768, 7, -56.25, -148.92, 0.09568, 4, -40.18, 106.92, 0.14553, 3, 69.65, 107.24, 0.39339, 2, 114.29, 125.26, 0.07637, 6, 5, 120.69, -181.13, 0.04292, 6, 88.59, -159.87, 0.17483, 7, -7.6, -159.84, 0.19663, 4, 6.04, 125.61, 0.29217, 3, 116.03, 125.56, 0.27651, 2, 150.79, 159.23, 0.01695, 6, 5, -217.83, 157.37, 0.00496, 17, 139.8, -54.75, 0.02171, 18, 77.15, -29.84, 0.37196, 21, -2.03, -29.78, 0.32604, 119, -46.33, -104.4, 0.07208, 19, -64.32, 45.54, 0.20324, 8, 5, -216.84, 190, 0.00637, 16, 252.81, -56.52, 5e-05, 17, 158.12, -81.78, 0.02021, 18, 103.71, -48.83, 0.12056, 21, 23.59, -50.01, 0.41064, 22, -51.17, -52.22, 0.00441, 119, -22.85, -81.71, 0.19777, 19, -39.46, 66.7, 0.23999, 8, 5, -186, 224.88, 0.01646, 16, 255.43, -103, 0.00129, 17, 153.56, -128.11, 0.04164, 18, 115.51, -93.86, 0.11214, 21, 33.24, -95.55, 0.20754, 22, -39.61, -97.32, 0.00089, 119, 23.58, -78.25, 0.46677, 19, 7.09, 67.23, 0.15327, 7, 5, -161.38, 241.84, 0.02521, 16, 249.87, -132.37, 0.00328, 17, 143.55, -156.28, 0.05559, 18, 115.9, -123.76, 0.10774, 21, 32.21, -125.43, 0.12436, 119, 53.05, -83.28, 0.54569, 19, 36.19, 60.35, 0.13813, 7, 5, -123.6, 227.62, 0.04894, 16, 213.01, -148.86, 0.01236, 17, 104.6, -166.91, 0.0935, 18, 83.05, -147.24, 0.1328, 21, -1.72, -147.32, 0.08099, 119, 70.2, -119.83, 0.39799, 19, 51, 22.79, 0.23342, 7, 5, -75.46, 186.21, 0.14136, 16, 149.67, -153.29, 0.06067, 17, 41.33, -161.56, 0.18624, 18, 21.85, -164.17, 0.16389, 21, -63.66, -161.33, 0.04885, 119, 75.79, -183.08, 0.26857, 19, 52.59, -40.69, 0.13042, 7, 5, -80.22, 139.16, 0.14967, 16, 119.95, -116.5, 0.08993, 17, 17.61, -120.64, 0.18344, 18, -14.59, -134.02, 0.11113, 21, -98.62, -129.48, 0.01965, 119, 39.54, -213.47, 0.11084, 19, 14.5, -68.72, 0.33534, 7, 5, -124.77, 108.84, 0.07862, 16, 130.28, -63.61, 0.07548, 17, 35.95, -69.97, 0.34213, 18, -14.97, -80.14, 0.15124, 21, -96.44, -75.63, 0.01348, 119, -13.53, -204.1, 0.06521, 19, -37.87, -56.03, 0.27384, 7, 5, -169.87, 111.83, 0.02172, 16, 164.43, -34.01, 0.00538, 17, 74.24, -45.96, 0.29444, 18, 12.61, -44.34, 0.38058, 21, -67.18, -41.19, 0.01575, 119, -43.74, -170.49, 0.04364, 19, -65.91, -20.58, 0.23848, 7, 5, -203.17, 141.25, 0.00804, 16, 208.8, -31.49, 0.00013, 17, 118.47, -50.28, 0.05058, 18, 55.6, -33.05, 0.54071, 21, -23.71, -31.96, 0.10314, 119, -47.07, -126.18, 0.0558, 19, -66.44, 23.85, 0.24159, 1, 19, 6.56, -0.48, 1, 7, 2, -173.44, -220.84, 0.00116, 16, 227.88, 121.27, 0.00098, 17, 160.79, 97.73, 0.08072, 18, 43.94, 120.46, 0.20203, 21, -28.06, 121.93, 0.00661, 127, 116.64, 74.68, 0.333, 20, 38.98, -8.13, 0.37551, 6, 2, -197.36, -220.57, 0.00025, 17, 178.74, 81.92, 0.05017, 18, 66.26, 111.86, 0.20232, 21, -6.17, 112.28, 0.02275, 127, 99.46, 58.02, 0.51214, 20, 57.07, -23.78, 0.21236, 5, 17, 177.75, 38.93, 0.01775, 18, 80.25, 71.19, 0.27069, 21, 5.87, 70.99, 0.14701, 127, 56.68, 62.4, 0.4484, 20, 56.45, -66.79, 0.11615, 6, 2, -217.46, -154.81, 1e-05, 17, 151.08, 18.97, 0.01208, 18, 62.16, 43.22, 0.48224, 21, -13.53, 43.91, 0.13273, 127, 38.88, 90.56, 0.17295, 20, 29.95, -86.97, 0.2, 6, 2, -195.12, -134.99, 0.00031, 17, 121.23, 18.52, 0.03439, 18, 34.31, 32.44, 0.68224, 21, -41.85, 34.47, 0.01336, 127, 40.79, 120.36, 0.07776, 20, 0.1, -87.68, 0.19194, 6, 2, -155.44, -118.03, 0.00441, 16, 158.3, 43.48, 0.00526, 17, 80.08, 31.55, 0.42958, 18, -8.79, 30.39, 0.31961, 127, 57.02, 160.35, 0.03676, 20, -41.15, -75.01, 0.20439, 6, 2, -118.79, -115.34, 0.02265, 16, 125.76, 60.55, 0.06032, 17, 50.55, 53.42, 0.55586, 18, -44.08, 40.65, 0.09737, 127, 81.15, 188.06, 0.03035, 20, -70.87, -53.39, 0.23346, 6, 2, -93.83, -116.19, 0.05304, 16, 105.01, 74.45, 0.14754, 17, 32.19, 70.34, 0.52604, 18, -67.17, 50.15, 0.06435, 127, 99.46, 205.04, 0.02747, 20, -89.38, -36.64, 0.18156, 6, 2, -79.07, -145.19, 0.05652, 16, 107.79, 106.87, 0.12075, 17, 39.92, 101.95, 0.39913, 18, -70.89, 82.48, 0.08515, 127, 130.37, 194.84, 0.04021, 20, -81.93, -4.96, 0.29824, 1, 20, -10.01, 6.92, 1, 2, 23, -4.36, -130.04, 0.00144, 119, 72.51, 18.79, 0.99856, 5, 5, -202.66, 289.69, 0.00295, 17, 205.04, -170.87, 0.00659, 18, 178.63, -116.11, 0.01393, 21, 95.23, -120.78, 0.02804, 119, 56.88, -20.2, 0.94848, 7, 5, -160.08, 198.49, 0.01577, 16, 218.44, -102.49, 0.00326, 17, 117.09, -121.92, 0.04153, 18, 79.16, -100.71, 0.08942, 21, -3.39, -100.67, 0.07135, 119, 23.74, -115.24, 0.14387, 19, 4.92, 30.3, 0.6348, 7, 5, -167.99, 181.47, 0.01492, 16, 212.09, -84.82, 0.003, 17, 113.53, -103.49, 0.04679, 18, 69.42, -84.66, 0.12019, 21, -12.35, -84.17, 0.08105, 119, 6.19, -121.91, 0.11408, 19, -13.02, 24.75, 0.61997, 7, 5, -155.98, 164.84, 0.01999, 16, 191.85, -81.45, 0.0059, 17, 94.05, -97.05, 0.06907, 18, 48.92, -85.39, 0.13537, 21, -32.87, -83.92, 0.05105, 119, 3.19, -142.21, 0.08752, 19, -17.29, 4.68, 0.6311, 7, 5, -135.28, 155.89, 0.03336, 16, 170.85, -89.66, 0.0148, 17, 72.03, -101.94, 0.09972, 18, 29.97, -97.61, 0.12196, 21, -52.38, -95.22, 0.03303, 119, 11.78, -163.06, 0.08437, 19, -10.03, -16.67, 0.61276, 7, 5, -115.45, 164.44, 0.04806, 16, 162.77, -109.69, 0.02261, 17, 60.98, -120.49, 0.10098, 18, 26.03, -118.84, 0.10259, 21, -57.32, -116.24, 0.02993, 119, 31.95, -170.77, 0.10287, 19, 9.62, -25.64, 0.59296, 7, 5, -109.37, 181.98, 0.04916, 16, 170.79, -126.43, 0.02014, 17, 66.33, -138.26, 0.09024, 18, 37.22, -133.65, 0.09837, 21, -46.86, -131.57, 0.03494, 119, 48.54, -162.45, 0.13512, 19, 26.7, -18.38, 0.57203, 7, 5, -119.88, 192.64, 0.03401, 16, 185.76, -126.61, 0.01199, 17, 81.09, -140.74, 0.06765, 18, 51.92, -130.85, 0.08623, 21, -32.03, -129.47, 0.03785, 119, 48.45, -147.48, 0.13515, 19, 27.55, -3.43, 0.62712, 7, 5, -130.56, 200.57, 0.02667, 16, 198.93, -124.73, 0.00806, 17, 94.39, -140.91, 0.05662, 18, 64.45, -126.4, 0.08287, 21, -19.31, -125.62, 0.04449, 119, 46.34, -134.35, 0.14656, 19, 26.27, 9.81, 0.63472, 7, 5, -143.63, 202.03, 0.0199, 16, 209.25, -116.58, 0.00514, 17, 105.84, -134.43, 0.04617, 18, 72.95, -116.35, 0.07913, 21, -10.35, -115.99, 0.05124, 119, 38, -124.18, 0.14119, 19, 18.58, 20.48, 0.65723, 7, 2, -141.11, -207.02, 0.00274, 16, 193.13, 126.61, 0.00421, 17, 127.27, 108.35, 0.08458, 18, 8.82, 118.78, 0.1132, 21, -63.21, 121.93, 0.0004, 127, 129.86, 107.25, 0.09708, 20, 5.37, 2.19, 0.69779, 7, 2, -125.54, -209.1, 0.00457, 16, 181.01, 136.6, 0.00776, 17, 116.83, 120.08, 0.11091, 18, -5.04, 126.16, 0.11668, 21, -76.71, 129.96, 3e-05, 127, 142.38, 116.74, 0.08833, 20, -5.17, 13.84, 0.67172, 6, 2, -115.03, -203.78, 0.00643, 16, 169.27, 137.63, 0.0116, 17, 105.39, 122.9, 0.13006, 18, -16.75, 124.84, 0.11387, 127, 146.09, 127.92, 0.07718, 20, -16.64, 16.56, 0.66085, 6, 2, -109.94, -194.61, 0.00791, 16, 160.11, 132.54, 0.01483, 17, 95.56, 119.27, 0.13904, 18, -24.72, 118.03, 0.1054, 127, 143.25, 138.01, 0.06577, 20, -26.44, 12.84, 0.66705, 6, 2, -115.92, -180.46, 0.00743, 16, 157.71, 117.36, 0.01417, 17, 90.85, 104.65, 0.13035, 18, -24.05, 102.68, 0.09468, 127, 129.04, 143.85, 0.05339, 20, -31.01, -1.82, 0.69998, 7, 2, -131.75, -174.32, 0.00515, 16, 167.91, 103.79, 0.00927, 17, 98.84, 89.67, 0.12065, 18, -11.36, 91.4, 0.11176, 21, -84.68, 95.54, 7e-05, 127, 113.48, 137.06, 0.05895, 20, -22.9, -16.73, 0.69415, 7, 2, -144.72, -183.12, 0.00313, 16, 183.57, 104.41, 0.00495, 17, 114.42, 87.87, 0.09551, 18, 3.87, 95.12, 0.12567, 21, -69.29, 98.53, 0.00067, 127, 110.46, 121.68, 0.07728, 20, -7.31, -18.39, 0.69279, 7, 2, -148.63, -196.27, 0.00245, 16, 193.83, 113.51, 0.00361, 17, 125.95, 95.29, 0.08249, 18, 12.11, 106.08, 0.12296, 21, -60.53, 109.09, 0.00107, 127, 116.95, 109.6, 0.09683, 20, 4.16, -10.87, 0.69058, 7, 2, -147.58, -189.99, 0.00271, 16, 189.63, 108.73, 0.00411, 17, 121.06, 91.22, 0.08757, 18, 8.94, 100.57, 0.12559, 21, -63.96, 103.73, 0.00104, 127, 113.27, 114.79, 0.08834, 20, -0.69, -14.99, 0.69064, 11, 5, -50.93, 100.17, 0.2887, 6, -173.44, 39.94, 0.02781, 7, -268.63, 41.28, 0.01498, 4, -323.05, 142.5, 0.02278, 3, -212.91, 145.17, 0.06157, 2, -162.65, 57.52, 0.01195, 16, 71.71, -109.4, 0.12039, 17, -28.96, -106.21, 0.1878, 18, -63.27, -136.65, 0.12155, 21, -147.38, -129.79, 0.02054, 119, 33.32, -261.83, 0.12193, 11, 5, -85.79, 65.11, 0.12722, 6, -193.19, -5.38, 0.00903, 7, -288.61, -3.94, 0.00486, 4, -313.75, 93.94, 0.0074, 3, -204.02, 96.54, 0.01999, 2, -136.63, 15.48, 0.00542, 16, 71.81, -59.96, 0.4353, 17, -21.26, -57.38, 0.20714, 18, -73, -88.18, 0.10387, 21, -154.78, -80.91, 0.01241, 119, -16.11, -262.62, 0.06736, 11, 5, -42.15, 23.28, 0.31281, 6, -137.34, -28.52, 0.0145, 7, -232.88, -27.36, 0.00301, 4, -254.59, 106.39, 0.00385, 3, -144.76, 108.5, 0.04822, 2, -85.82, 48.23, 0.18499, 16, 11.37, -60.93, 0.2271, 17, -81.14, -49.05, 0.10927, 18, -132.04, -101.14, 0.05378, 21, -214.37, -91.05, 0.00609, 119, -14.05, -323.03, 0.03636, 11, 5, -2.54, 61.87, 0.62128, 6, -114.44, 21.82, 0.00749, 7, -209.72, 22.87, 0.00192, 4, -264.12, 160.87, 0.00266, 3, -153.84, 163.05, 0.02079, 2, -114.17, 95.71, 0.06722, 16, 10.37, -116.23, 0.13248, 17, -90.62, -103.54, 0.07575, 18, -122.03, -155.53, 0.03608, 21, -206.97, -145.85, 0.00352, 119, 41.26, -323.03, 0.03082, 12, 5, 46.19, 33.46, 0.58503, 6, -58.72, 13.03, 0.11268, 7, -154.05, 13.8, 0.02559, 4, -213.18, 185.09, 0.03793, 3, -102.7, 186.84, 0.04442, 2, -75.23, 136.52, 0.03349, 16, -44.25, -130.32, 0.06256, 17, -146.76, -109.07, 0.0368, 18, -172.76, -180.19, 0.01732, 21, -258.81, -168.07, 0.00164, 119, 56.34, -377.39, 0.01497, 19, 20.93, -233.38, 0.02756], "hull": 73, "edges": [140, 138, 138, 136, 136, 134, 134, 132, 132, 130, 130, 128, 128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 54, 56, 58, 56, 140, 142, 142, 144, 144, 146, 146, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 50, 52, 52, 54, 170, 52, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 172, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 204, 206, 206, 208, 208, 210, 210, 114, 194, 120, 214, 216, 216, 178, 144, 0, 94, 96, 96, 98, 98, 100, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 92, 94, 90, 92, 80, 78, 74, 76, 78, 76, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 232, 232, 234, 234, 218, 194, 236, 236, 212, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 202, 248, 248, 212, 246, 248, 250, 236, 200, 252, 252, 212, 248, 252, 252, 250], "width": 494, "height": 785}}, "shouzhi1": {"shouzhi1": {"type": "mesh", "uvs": [0.94509, 0.35607, 1, 0.59352, 1, 0.75676, 0.98474, 0.82911, 0.9263, 0.86806, 0.87065, 1, 0.72735, 1, 0.6703, 0.84951, 0.655, 0.57682, 0.51587, 0.51375, 0.10544, 0.64175, 0, 0.58053, 0, 0, 0.34405, 0, 0.6877, 0], "triangles": [10, 11, 13, 11, 12, 13, 9, 10, 13, 9, 13, 14, 9, 14, 0, 5, 6, 4, 6, 7, 4, 3, 4, 2, 2, 4, 7, 2, 7, 8, 8, 1, 2, 8, 0, 1, 8, 9, 0], "vertices": [2, 134, -2.32, 7.15, 0.58158, 133, 34.08, 6.27, 0.41842, 1, 134, 14.27, 11.45, 1, 1, 134, 25.53, 10.94, 1, 1, 134, 30.45, 9.31, 1, 1, 134, 32.89, 3.82, 1, 1, 134, 41.75, -1.71, 1, 2, 134, 41.15, -14.88, 0.99957, 133, 43.91, -41.47, 0.00043, 3, 134, 30.54, -19.65, 0.97413, 133, 33.57, -36.14, 0.02299, 132, 40.5, -49.22, 0.00288, 4, 134, 11.68, -20.21, 0.51739, 133, 21.4, -21.72, 0.348, 132, 40.15, -30.35, 0.12302, 131, 14.99, -49.75, 0.01159, 4, 134, 6.75, -32.8, 0.08587, 133, 8.48, -25.71, 0.28388, 132, 27.62, -25.29, 0.50714, 131, 13.49, -36.31, 0.12311, 2, 132, -10.58, -31.99, 0.01252, 131, -10.55, -5.89, 0.98748, 1, 131, -10.85, 4.69, 1, 2, 132, -17.78, 12.76, 0.24631, 131, 25.41, 21.71, 0.75369, 1, 132, 13.82, 10.99, 1, 2, 133, 0.49, 12.28, 0.82435, 132, 45.39, 9.22, 0.17565], "hull": 15, "edges": [22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 10, 12, 10, 8, 8, 6, 2, 0, 0, 28, 24, 26, 26, 28, 2, 4, 6, 4], "width": 92, "height": 69}}, "shouzhi2": {"shouzhi2": {"type": "mesh", "uvs": [0.82521, 0.37644, 1, 0.71991, 1, 1, 0.82023, 1, 0.65826, 0.73758, 0.52976, 0.52939, 0.1281, 0.74137, 0, 0.59916, 0, 0.29862, 0.52644, 0, 0.66421, 0], "triangles": [8, 9, 5, 7, 8, 5, 6, 7, 5, 0, 5, 9, 0, 9, 10, 4, 5, 0, 1, 4, 0, 3, 4, 1, 3, 1, 2], "vertices": [2, 137, -2.3, 9.62, 0.30533, 136, 26.58, 9.76, 0.69467, 1, 137, 24.39, 9.67, 1, 1, 137, 37.38, -0.99, 1, 2, 137, 26.33, -14.47, 0.998, 136, 53.41, -16.32, 0.002, 3, 137, 4.19, -16.64, 0.55537, 136, 31.18, -16.9, 0.42919, 135, 52.05, -35.35, 0.01545, 3, 137, -13.37, -18.36, 0.0112, 136, 13.54, -17.36, 0.56476, 135, 44.47, -19.42, 0.42404, 1, 135, 3.5, -18.4, 1, 1, 135, -5.36, -6.21, 1, 1, 135, 0.66, 10.79, 1, 2, 136, -9.74, 4.25, 0.29035, 135, 54.77, 10.63, 0.70965, 2, 136, -0.55, 13.95, 0.96595, 135, 67.37, 6.17, 0.03405], "hull": 11, "edges": [14, 12, 12, 10, 4, 6, 4, 2, 2, 0, 0, 20, 18, 20, 14, 16, 18, 16, 6, 8, 8, 10], "width": 97, "height": 60}}, "shouzhi3": {"shouzhi3": {"type": "mesh", "uvs": [1, 0.31408, 0.90355, 0.45495, 0.55342, 0.4722, 0.34396, 1, 0, 1, 0, 0.50671, 0.39867, 0, 1, 0], "triangles": [3, 4, 5, 5, 6, 2, 2, 3, 5, 6, 7, 0, 0, 2, 6, 1, 2, 0], "vertices": [1, 139, 55.44, -7.91, 1, 1, 139, 45.77, -16.12, 1, 3, 139, 9.76, -18.28, 0.48675, 138, 49.74, -20.15, 0.49303, 131, 59.23, -33.34, 0.02022, 2, 138, 14.27, -29.14, 0.39328, 131, 23.3, -26.37, 0.60672, 2, 138, -13, -6.53, 0.07816, 131, 8.25, 5.7, 0.92184, 1, 138, 4.63, 14.74, 1, 2, 139, -7.04, 7.63, 0.03434, 138, 54.35, 10.38, 0.96566, 1, 139, 54.86, 9.67, 1], "hull": 8, "edges": [6, 8, 6, 4, 4, 2, 0, 14, 2, 0, 12, 14, 8, 10, 12, 10], "width": 103, "height": 56}}, "shouzhi4": {"shouzhi4": {"type": "mesh", "uvs": [0.86192, 0.49574, 0.58477, 0.87673, 0.22449, 1, 0, 1, 0, 0.57054, 0.18292, 0.5121, 0.32495, 0.29707, 0.56052, 0, 0.87924, 0], "triangles": [0, 7, 8, 6, 7, 0, 1, 6, 0, 5, 6, 1, 1, 2, 5, 2, 3, 4, 5, 2, 4], "vertices": [2, 141, 35.73, -13.4, 0.49804, 142, -3.83, -13.91, 0.50196, 2, 140, 62.12, -19.68, 0.31592, 141, 1.77, -22.78, 0.68408, 2, 140, 39.57, -21.47, 0.96474, 141, -19.27, -14.48, 0.03526, 1, 140, 27.94, -16.7, 1, 3, 140, 41.49, 16.27, 0.50366, 141, -0.95, 18.58, 0.49626, 142, -13.73, 33.74, 8e-05, 3, 140, 52.8, 16.87, 0.13463, 141, 9.47, 14.14, 0.83001, 142, -8.1, 23.9, 0.03536, 2, 141, 28.1, 20.04, 0.36074, 142, 10.3, 17.34, 0.63926, 1, 142, 35.9, 6.07, 1, 1, 142, 37.27, -11.72, 1], "hull": 9, "edges": [4, 6, 4, 2, 2, 0, 0, 16, 14, 16, 14, 12, 12, 10, 6, 8, 10, 8], "width": 56, "height": 83}}, "tongkong": {"tongkong": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 26, -3.96, -16.29, 1, 1, 26, -17.21, 1.27, 1, 1, 26, 4.34, 17.54, 1, 1, 26, 17.59, -0.03, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 22, "height": 27}}, "tongkong2": {"tongkong2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 27, -5.29, -16.62, 1, 1, 27, -17.33, -0.66, 1, 1, 27, 3.42, 15, 1, 1, 27, 15.47, -0.96, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 20, "height": 26}}, "tongkong3": {"tongkong3": {"type": "mesh", "uvs": [0.74603, 0.30902, 1, 0.29927, 1, 1, 0.73966, 1, 0.30455, 0.69282, 0, 0.70908, 0, 0, 0.3122, 0], "triangles": [4, 6, 7, 4, 7, 0, 5, 6, 4, 3, 4, 0, 0, 1, 2, 3, 0, 2], "vertices": [1, 27, 0.41, 24.74, 1, 1, 27, 20.7, -1.31, 1, 1, 27, -7.83, -22.84, 1, 1, 27, -28.22, 4.18, 1, 1, 26, -5.21, -24.77, 1, 1, 26, -29.72, 6.33, 1, 1, 26, -0.85, 28.12, 1, 1, 26, 23.6, -4.28, 1], "hull": 8, "edges": [10, 12, 10, 8, 12, 14, 8, 14, 4, 2, 2, 0, 4, 6, 0, 6, 6, 8, 14, 0], "width": 130, "height": 51}}, "toufa1": {"toufa1": {"type": "mesh", "uvs": [0.58582, 0.01395, 0.63784, 0.03477, 0.71013, 0.07901, 0.76656, 0.13432, 0.81642, 0.17739, 0.85345, 0.23204, 0.8702, 0.27954, 0.89665, 0.32118, 0.9324, 0.41298, 0.94029, 0.45629, 0.93926, 0.49923, 0.93617, 0.53722, 0.92588, 0.57757, 0.91078, 0.61581, 0.89842, 0.64316, 0.86685, 0.67989, 0.85449, 0.67406, 0.86548, 0.63962, 0.87948, 0.61118, 0.89424, 0.5737, 0.90625, 0.52963, 0.91189, 0.50603, 0.91168, 0.45876, 0.90675, 0.49773, 0.89689, 0.52683, 0.87788, 0.56996, 0.84831, 0.61257, 0.81359, 0.65127, 0.81536, 0.68336, 0.81448, 0.71677, 0.82513, 0.76066, 0.8553, 0.80454, 0.90323, 0.83074, 0.95205, 0.82943, 0.9769, 0.80913, 0.97956, 0.76786, 1, 0.76459, 1, 0.85039, 0.9769, 0.8845, 0.93075, 0.90935, 0.87838, 0.92703, 0.82088, 0.94222, 0.8695, 0.95927, 0.92098, 0.93373, 0.94672, 0.92718, 0.96004, 0.94552, 0.92808, 0.97696, 0.88459, 1, 0.82601, 1, 0.77259, 1, 0.6665, 0.96475, 0.61199, 0.90386, 0.589, 0.85647, 0.56926, 0.81578, 0.56032, 0.76958, 0.55158, 0.72445, 0.55584, 0.68121, 0.56042, 0.63468, 0.57497, 0.58531, 0.58989, 0.53465, 0.59915, 0.48924, 0.61052, 0.43352, 0.61557, 0.38082, 0.62083, 0.32588, 0.59889, 0.27451, 0.5781, 0.22584, 0.5118, 0.20083, 0.42487, 0.22149, 0.35709, 0.23645, 0.28195, 0.25303, 0.22086, 0.2643, 0.15819, 0.27586, 0.11808, 0.27829, 0.06832, 0.2813, 0, 0.26735, 0, 0.22671, 0.02361, 0.18051, 0.06241, 0.13887, 0.11531, 0.10114, 0.16115, 0.07055, 0.21758, 0.03802, 0.28547, 0.01525, 0.35159, 0, 0.42917, 0, 0.51176, 0, 0.91422, 0.36848, 0.74782, 0.88597, 0.71161, 0.82324], "triangles": [71, 77, 78, 74, 75, 73, 70, 71, 78, 72, 76, 77, 76, 72, 75, 72, 77, 71, 72, 73, 75, 81, 82, 68, 69, 80, 81, 68, 69, 81, 79, 80, 69, 70, 78, 79, 69, 70, 79, 66, 84, 0, 66, 0, 1, 67, 82, 83, 83, 84, 66, 67, 83, 66, 68, 82, 67, 65, 66, 1, 65, 1, 2, 65, 2, 3, 64, 65, 3, 4, 64, 3, 4, 63, 64, 63, 4, 5, 63, 5, 6, 7, 62, 63, 63, 6, 7, 22, 61, 62, 24, 25, 22, 25, 61, 22, 60, 61, 25, 26, 59, 60, 25, 26, 60, 27, 58, 59, 26, 27, 59, 27, 57, 58, 57, 27, 56, 56, 28, 55, 54, 55, 28, 27, 28, 56, 28, 87, 53, 53, 54, 28, 52, 53, 87, 31, 86, 87, 51, 52, 87, 51, 87, 86, 41, 86, 31, 50, 51, 86, 50, 86, 49, 49, 86, 41, 49, 41, 48, 45, 46, 43, 45, 43, 44, 42, 43, 46, 48, 41, 42, 47, 42, 46, 48, 42, 47, 29, 87, 28, 30, 87, 29, 31, 87, 30, 39, 32, 33, 40, 31, 32, 40, 32, 39, 40, 41, 31, 34, 35, 36, 37, 34, 36, 33, 34, 37, 38, 33, 37, 39, 33, 38, 8, 85, 7, 22, 85, 8, 22, 8, 9, 7, 85, 62, 85, 22, 62, 10, 22, 9, 10, 21, 22, 23, 24, 22, 11, 21, 10, 20, 21, 11, 12, 20, 11, 19, 20, 12, 13, 19, 12, 18, 19, 13, 14, 18, 13, 17, 18, 14, 15, 17, 14, 16, 17, 15], "vertices": [2, 31, -13.83, 20.4, 0.17896, 30, 33.74, 24.18, 0.82104, 2, 31, -3.88, 21.34, 0.46465, 30, 43.28, 21.16, 0.53535, 2, 31, 11.91, 19.73, 0.93084, 30, 57.19, 13.52, 0.06916, 2, 32, -12.25, 8.74, 0.11482, 31, 26.89, 14.52, 0.88518, 3, 42, -41.43, 1.9, 0.00386, 32, -0.36, 13.94, 0.77133, 31, 39.38, 11.02, 0.22481, 3, 42, -27.62, 4.14, 0.06073, 32, 13.43, 16.3, 0.93918, 31, 51.55, 4.11, 9e-05, 2, 42, -16.39, 3.59, 0.26152, 32, 24.66, 15.85, 0.73848, 2, 42, -5.96, 5.02, 0.74909, 32, 35.08, 17.36, 0.25091, 1, 42, 15.91, 4.51, 1, 2, 43, 0.06, 3, 0.54797, 42, 25.77, 2.83, 0.45203, 1, 43, 9.9, 3.03, 1, 2, 44, -2.56, 2.23, 0.15513, 43, 18.61, 2.68, 0.84487, 1, 44, 6.84, 2.52, 1, 2, 45, 1.02, 2.04, 0.67073, 44, 15.94, 1.91, 0.32927, 2, 45, 7.62, 2.12, 1, 43, 42.99, -3.2, 0, 2, 45, 17.31, -0.18, 1, 43, 51.51, -8.36, 0, 2, 45, 16.73, -2.59, 1, 43, 50.22, -10.48, 0, 1, 45, 8.67, -3.41, 1, 2, 45, 1.74, -3.3, 0.74806, 44, 16.05, -3.48, 0.25194, 1, 44, 7.13, -2.89, 1, 2, 44, -3.17, -3.09, 0.04838, 43, 16.97, -2.41, 0.95162, 1, 43, 11.55, -1.56, 1, 4, 43, 0.73, -1.82, 0.4865, 42, 24.87, -1.96, 0.4625, 34, -16.61, 25.55, 0.0147, 33, 18.3, 24.74, 0.0363, 4, 43, 9.67, -2.47, 0.30936, 42, 33.14, -5.42, 0.26023, 34, -7.74, 26.85, 0.23413, 33, 27.1, 26.46, 0.19629, 4, 43, 16.36, -4, 0.22855, 42, 39, -8.99, 0.1455, 34, -0.87, 26.81, 0.44222, 33, 33.96, 26.74, 0.18372, 6, 43, 26.31, -7.01, 0.12775, 42, 47.47, -15.01, 0.04801, 39, -10.94, 59.84, 0.00046, 35, -19.9, 30.24, 0.0031, 34, 9.49, 26.02, 0.74068, 33, 44.35, 26.44, 0.08, 7, 43, 36.16, -11.81, 0.05699, 42, 55.3, -22.69, 0.00934, 40, 7.71, 58.91, 5e-05, 39, -8.09, 49.25, 0.01122, 35, -9.91, 25.73, 0.06397, 34, 20.15, 23.48, 0.85043, 33, 55.12, 24.4, 0.008, 6, 43, 45.14, -17.5, 0.01591, 42, 62, -30.93, 0.00038, 40, 1.95, 49.97, 0.00304, 39, -6.48, 38.75, 0.06387, 35, -0.77, 20.31, 0.31039, 34, 30.15, 19.87, 0.60642, 5, 43, 52.48, -17.05, 0.00479, 40, 2.35, 42.63, 0.01462, 39, -1.33, 33.5, 0.17102, 35, 6.56, 20.97, 0.53229, 34, 37.22, 21.9, 0.27728, 5, 43, 60.14, -17.04, 0.00095, 40, 2.29, 34.98, 0.04342, 39, 3.7, 27.73, 0.33252, 35, 14.21, 21.2, 0.54482, 34, 44.69, 23.57, 0.07829, 5, 43, 70.15, -15.04, 2e-05, 40, 4.22, 24.95, 0.14923, 39, 11.78, 21.5, 0.55683, 35, 24.15, 23.5, 0.28906, 34, 54.03, 27.69, 0.00487, 3, 40, 9.45, 14.97, 0.52051, 39, 22.31, 17.47, 0.42039, 35, 33.94, 29.09, 0.05909, 4, 41, 4.53, 9.4, 0.04649, 40, 17.63, 9.07, 0.87443, 39, 32.34, 18.47, 0.07349, 35, 39.53, 37.48, 0.0056, 4, 41, 9.18, 2.58, 0.87051, 40, 25.87, 9.48, 0.12564, 39, 38.25, 24.23, 0.00368, 35, 38.82, 45.7, 0.00016, 1, 41, 15.36, 1.5, 1, 3, 41, 23.59, 6.15, 0.99998, 40, 30.34, 23.63, 2e-05, 39, 32.23, 37.8, 0, 3, 41, 26.07, 3.63, 0.99997, 40, 33.79, 24.43, 3e-05, 39, 34.29, 40.67, 0, 1, 41, 9.44, -6.84, 1, 2, 41, 0.75, -7.7, 0.99993, 37, 16.28, 35.56, 7e-05, 4, 41, -8.22, -4.13, 0.01397, 40, 22.51, -8.87, 0.95431, 38, 21.56, 14.39, 0.00428, 37, 15.97, 25.91, 0.02743, 4, 40, 13.71, -13.03, 0.79926, 38, 12.55, 10.72, 0.03873, 37, 13.71, 16.44, 0.15795, 36, 39.98, 19.57, 0.00406, 4, 40, 4.04, -16.63, 0.22211, 39, 39.17, -9.79, 0.00336, 38, 2.7, 7.65, 0.26755, 37, 10.48, 6.64, 0.50698, 3, 40, 12.31, -20.43, 0.00441, 38, 10.74, 3.4, 0.9833, 37, 18.61, 10.72, 0.01229, 3, 40, 20.93, -14.47, 1e-05, 38, 19.68, 8.88, 0.99998, 37, 19.35, 21.18, 1e-05, 3, 40, 25.26, -12.92, 0, 38, 24.09, 10.2, 0.99998, 37, 20.85, 25.53, 1e-05, 3, 40, 27.56, -17.09, 0, 38, 26.16, 5.91, 0.99999, 37, 25.54, 24.72, 1e-05, 1, 38, 20.46, -1.06, 1, 1, 38, 12.89, -6.02, 1, 2, 38, 3, -5.6, 0.90678, 37, 21.44, -0.82, 0.09322, 3, 38, -6.02, -5.22, 0.17389, 37, 15.89, -7.93, 0.77551, 36, 48.61, -3.33, 0.0506, 2, 37, -1.51, -17.1, 0.1747, 36, 34.32, -16.83, 0.8253, 2, 36, 17.91, -20.03, 0.99734, 35, 58.69, -10.85, 0.00266, 2, 36, 6.4, -19.49, 0.97381, 35, 48.05, -15.27, 0.02619, 2, 36, -3.49, -19.02, 0.87161, 35, 38.91, -19.06, 0.12839, 2, 36, -13.84, -16.38, 0.56709, 35, 28.41, -21.1, 0.43291, 3, 36, -23.96, -13.81, 0.28748, 35, 18.16, -23.08, 0.68792, 34, 56.91, -19.19, 0.02461, 3, 36, -32.84, -9.37, 0.12136, 35, 8.24, -22.86, 0.72572, 34, 47.12, -20.83, 0.15292, 4, 36, -42.4, -4.59, 0.03367, 35, -2.44, -22.61, 0.47853, 34, 36.58, -22.6, 0.48781, 33, 73.7, -20.85, 0, 4, 36, -51.91, 1.99, 0.00358, 35, -13.86, -20.72, 0.1413, 34, 25.01, -22.88, 0.83185, 33, 62.16, -21.68, 0.02327, 4, 35, -25.57, -18.77, 0.00957, 34, 13.15, -23.18, 0.82715, 33, 50.32, -22.53, 0.16153, 32, 66.82, -46.44, 0.00176, 3, 34, 2.67, -24.12, 0.54194, 33, 39.9, -23.96, 0.43864, 32, 57.32, -41.92, 0.01942, 4, 34, -10.18, -25.27, 0.17194, 33, 27.12, -25.72, 0.71689, 32, 45.67, -36.38, 0.10716, 31, 43.14, -57.08, 0.00401, 4, 34, -22.11, -27.3, 0.02528, 33, 15.3, -28.31, 0.65982, 32, 34.37, -32.06, 0.28767, 31, 37.13, -46.58, 0.02722, 4, 34, -34.54, -29.41, 0.00013, 33, 2.98, -31.01, 0.35392, 32, 22.59, -27.56, 0.51724, 31, 30.86, -35.63, 0.12871, 4, 33, -7.26, -37.88, 0.11833, 32, 10.25, -27.69, 0.481, 31, 21.23, -27.93, 0.39306, 30, 47.18, -33.99, 0.00762, 5, 33, -16.96, -44.4, 0.02371, 32, -1.43, -27.82, 0.18606, 31, 12.1, -20.63, 0.65692, 30, 41.62, -23.71, 0.13031, 29, 57.78, -41.83, 0.003, 6, 33, -19.29, -56.76, 0.00096, 32, -10.16, -36.88, 0.0167, 31, -0.39, -22.11, 0.26622, 30, 29.54, -20.21, 0.65625, 29, 49.26, -32.58, 0.05971, 28, 67.92, -48.21, 0.00016, 4, 31, -9.96, -34.22, 0.02195, 30, 16.01, -27.63, 0.63021, 29, 33.84, -31.92, 0.32827, 28, 53.78, -42.01, 0.01958, 4, 31, -17.57, -43.44, 0.0005, 30, 5.41, -33.15, 0.35467, 29, 21.91, -31.17, 0.54289, 28, 42.94, -36.98, 0.10195, 3, 30, -6.35, -39.28, 0.12426, 29, 8.68, -30.33, 0.53101, 28, 30.91, -31.41, 0.34473, 3, 30, -16, -43.76, 0.03721, 29, -1.9, -29.17, 0.3225, 28, 21.47, -26.5, 0.64029, 3, 30, -25.9, -48.36, 0.0062, 29, -12.75, -27.99, 0.10711, 28, 11.78, -21.47, 0.88669, 3, 30, -32.45, -50.19, 0.00083, 29, -19.3, -26.16, 0.03532, 28, 6.34, -17.39, 0.96385, 2, 29, -27.43, -23.89, 0.00274, 28, -0.41, -12.33, 0.99726, 1, 28, -6.94, -2.29, 1, 1, 28, -0.78, 4.68, 1, 1, 28, 9.22, 9.96, 1, 1, 28, 20.45, 12.75, 1, 2, 29, -5.69, 12.06, 0.31352, 28, 32.88, 13.3, 0.68648, 2, 29, 4.01, 15.94, 0.85572, 28, 43.32, 13.41, 0.14428, 3, 30, -26.33, 7.02, 0.00357, 29, 15.53, 19.63, 0.99575, 28, 55.4, 12.67, 0.00068, 2, 30, -16.05, 14.3, 0.14786, 29, 28.1, 20.54, 0.85214, 2, 30, -5.73, 19.84, 0.54349, 29, 39.79, 19.95, 0.45651, 2, 30, 7.14, 22.32, 0.92201, 29, 52.09, 15.41, 0.07799, 2, 31, -26.01, 16.09, 0.01816, 30, 20.85, 24.96, 0.98184, 2, 42, 5.27, 4.62, 0.99994, 32, 46.31, 17.06, 6e-05, 3, 40, -8.47, -3.91, 0.04203, 39, 21.37, -8.54, 0.13117, 36, 22.88, 2.75, 0.8268, 2, 39, 7.19, -2.01, 0.57604, 36, 7.26, 2.57, 0.42396], "hull": 85, "edges": [148, 146, 134, 132, 132, 130, 102, 100, 100, 98, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 170, 170, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 168, 166, 168, 164, 166, 164, 162, 162, 160, 160, 158, 158, 156, 156, 154, 154, 152, 148, 150, 152, 150, 14, 16, 138, 140, 140, 142, 134, 136, 136, 138, 142, 144, 144, 146, 122, 124, 124, 126, 126, 128, 128, 130, 118, 120, 120, 122, 114, 116, 116, 118, 106, 108, 108, 110, 110, 112, 112, 114, 102, 104, 104, 106], "width": 169, "height": 229}}, "toufa2": {"toufa2": {"type": "mesh", "uvs": [0.48645, 0.06829, 0.63606, 0.162, 0.6975, 0.21083, 0.75118, 0.27002, 0.80346, 0.34018, 0.84686, 0.42303, 0.86057, 0.49997, 0.85143, 0.58576, 0.83202, 0.67256, 0.81032, 0.73569, 0.80118, 0.79191, 0.83544, 0.82249, 0.90397, 0.82052, 0.9428, 0.78698, 0.95308, 0.72287, 0.9725, 0.71596, 1, 0.7564, 1, 0.80079, 1, 0.85504, 0.9645, 0.92211, 0.91082, 0.9734, 0.8423, 1, 0.75435, 1, 0.68126, 0.98228, 0.639, 0.93099, 0.63329, 0.86194, 0.63557, 0.78698, 0.63557, 0.70708, 0.63083, 0.62602, 0.61141, 0.55895, 0.57829, 0.4978, 0.53146, 0.44749, 0.45723, 0.4179, 0.41269, 0.41539, 0.35215, 0.41198, 0.30007, 0.41812, 0.25165, 0.42382, 0.1637, 0.45242, 0.09289, 0.48004, 0.03036, 0.52237, 0, 0.50802, 0, 0.37308, 0.03646, 0.24189, 0.07414, 0.16496, 0.12554, 0.09986, 0.19635, 0.02687, 0.27173, 0, 0.37452, 0, 0.55497, 0.11564, 0.42363, 0.0318, 0.01932, 0.31587], "triangles": [14, 15, 16, 13, 14, 16, 13, 16, 17, 13, 17, 18, 12, 13, 18, 19, 12, 18, 20, 12, 19, 11, 12, 20, 22, 23, 24, 11, 22, 10, 21, 22, 11, 20, 21, 11, 10, 25, 26, 24, 25, 10, 10, 22, 24, 9, 27, 8, 26, 27, 9, 10, 26, 9, 28, 29, 7, 8, 28, 7, 27, 28, 8, 30, 4, 5, 29, 30, 5, 6, 29, 5, 7, 29, 6, 31, 2, 3, 30, 31, 3, 4, 30, 3, 48, 0, 1, 48, 33, 0, 32, 33, 48, 1, 32, 48, 31, 32, 1, 2, 31, 1, 0, 49, 47, 47, 49, 34, 47, 34, 46, 34, 49, 0, 33, 34, 0, 46, 34, 45, 35, 44, 45, 35, 45, 34, 35, 43, 44, 35, 42, 43, 42, 35, 36, 50, 41, 42, 42, 36, 50, 50, 37, 41, 36, 37, 50, 38, 41, 37, 40, 41, 38, 39, 40, 38], "vertices": [3, 48, 48.4, 15.24, 0.58549, 49, -7.98, 15.67, 0.41451, 50, -50.5, 2.18, 0, 2, 49, 26.66, 21.43, 0.94823, 50, -18.77, 17.21, 0.05177, 2, 49, 42.5, 22.19, 0.51185, 50, -3.74, 22.29, 0.48815, 3, 49, 58.94, 20.31, 0.07027, 50, 12.58, 24.98, 0.91567, 51, -27.2, 20.91, 0.01406, 2, 50, 30.89, 26.28, 0.72193, 51, -9.34, 25.11, 0.27807, 2, 50, 50.84, 24.75, 0.17232, 51, 10.6, 26.79, 0.82768, 3, 50, 66.94, 18.91, 0.00707, 51, 27.42, 23.58, 0.96347, 52, -19.71, 16.22, 0.02946, 2, 51, 44.63, 15.63, 0.4073, 52, -0.79, 17.29, 0.5927, 2, 52, 18.64, 16.46, 0.83394, 53, -5.94, 16.83, 0.16606, 5, 52, 32.99, 14.43, 0.16132, 53, 8.27, 13.98, 0.80661, 54, -10.69, 18.99, 0.02593, 56, 13.61, 39.44, 0.00519, 57, 12.1, 37.9, 0.00095, 6, 52, 45.48, 14.54, 0.00074, 53, 20.74, 13.37, 0.50981, 54, 0.76, 14.01, 0.36222, 55, 0.35, 24.6, 0.01255, 56, 5.57, 29.89, 0.09246, 57, 0.64, 32.93, 0.02221, 6, 53, 26.85, 20.46, 0.08185, 54, 8.99, 18.48, 0.34336, 55, 8.57, 20.12, 0.05959, 56, 7.5, 20.73, 0.39119, 57, -1.71, 23.87, 0.12384, 58, 1.73, 29.62, 0.00018, 5, 53, 25.24, 33.39, 0.00194, 54, 12.05, 31.14, 0.04864, 56, 18.76, 14.18, 0.30368, 57, 5.45, 12.99, 0.56946, 58, 0.27, 16.67, 0.07628, 4, 54, 6.92, 40.22, 0.00261, 56, 28.93, 16.5, 0.0081, 57, 15.6, 10.54, 0.29789, 58, 6.5, 8.3, 0.69141, 3, 54, -6.15, 45.88, 0, 56, 38.09, 27.41, 0, 58, 20.18, 4.33, 1, 4, 54, -6.63, 49.84, 0, 56, 42.02, 26.73, 0, 57, 31.87, 13.87, 0, 58, 21.14, 0.46, 1, 2, 56, 41.71, 16.42, 0, 58, 11.58, -3.43, 1, 3, 56, 36.52, 8.15, 1e-05, 57, 18.67, -0.32, 0.07649, 58, 1.92, -2.01, 0.9235, 2, 56, 30.17, -1.96, 0.07884, 57, 8.49, -6.54, 0.92116, 2, 55, 38.51, 6.51, 0.0525, 56, 16.61, -10.87, 0.9475, 2, 55, 32.14, -7.3, 0.64971, 56, 1.98, -15, 0.35029, 1, 55, 21.46, -16.77, 1, 2, 54, 42.49, -6.82, 0.11895, 55, 5.51, -21.75, 0.88105, 2, 54, 35.02, -19.16, 0.46839, 55, -8.91, -22.17, 0.53161, 3, 53, 54, -14.55, 0.0015, 54, 22, -23.87, 0.78622, 55, -19.94, -13.8, 0.21228, 3, 53, 38.97, -17, 0.12398, 54, 7.07, -20.85, 0.8623, 55, -25.5, 0.38, 0.01372, 3, 52, 49.05, -16.74, 0.00332, 53, 22.51, -18.06, 0.72227, 54, -8.71, -16.02, 0.27441, 4, 51, 56.07, -31.95, 0.01408, 52, 31.66, -19.33, 0.27693, 53, 5, -19.65, 0.70803, 54, -25.64, -11.31, 0.00096, 4, 50, 70.29, -32.7, 0.00095, 51, 38.96, -26.83, 0.26079, 52, 14.16, -22.86, 0.64564, 53, -12.67, -22.17, 0.09261, 6, 47, 30.84, -107.51, 0.00039, 48, 50.1, -95.27, 7e-05, 50, 55.58, -28.85, 0.05502, 51, 23.82, -25.37, 0.67755, 52, 0.11, -28.69, 0.26632, 53, -27.04, -27.18, 0.00064, 7, 46, 39.02, -99.69, 0.00012, 47, 35.17, -93.31, 0.00637, 48, 46.62, -80.83, 0.00404, 49, 71.54, -38.27, 0.00933, 50, 40.76, -27.9, 0.33504, 51, 9.04, -26.81, 0.59917, 52, -12.27, -36.9, 0.04593, 7, 46, 45.04, -86.84, 0.00337, 47, 35.97, -79.13, 0.03534, 48, 40.12, -68.21, 0.02967, 49, 57.41, -36.83, 0.08586, 50, 26.78, -30.39, 0.63687, 51, -4.37, -31.49, 0.20653, 52, -21.9, -47.33, 0.00236, 6, 46, 44.69, -71.3, 0.02421, 47, 29.86, -64.85, 0.12768, 48, 27.6, -59.01, 0.10772, 49, 42.87, -42.3, 0.2353, 50, 14.29, -39.64, 0.47744, 51, -15.22, -42.61, 0.02765, 6, 46, 41.46, -63.46, 0.05356, 47, 23.94, -58.77, 0.20823, 48, 19.42, -56.78, 0.15223, 49, 36.53, -47.94, 0.24783, 50, 9.74, -46.79, 0.3321, 51, -18.57, -50.4, 0.00605, 6, 46, 37.07, -52.8, 0.12936, 47, 15.9, -50.52, 0.32308, 48, 8.3, -53.75, 0.16829, 49, 27.92, -55.6, 0.19199, 50, 3.56, -56.52, 0.18717, 51, -23.12, -60.98, 0.00011, 5, 46, 31.51, -44.51, 0.25176, 47, 7.65, -44.89, 0.38794, 48, -1.67, -53.09, 0.13186, 49, 21.93, -63.59, 0.12268, 50, -0.01, -65.85, 0.10576, 5, 46, 26.34, -36.8, 0.434, 47, -0.03, -39.66, 0.36119, 48, -10.94, -52.48, 0.07899, 49, 16.36, -71.02, 0.06884, 50, -3.33, -74.52, 0.05699, 5, 46, 13.33, -24.56, 0.83754, 47, -16.65, -33.15, 0.12416, 48, -28.57, -55.31, 0.01282, 49, 9.11, -87.33, 0.01378, 50, -5.83, -92.2, 0.0117, 5, 46, 1.96, -15.15, 0.98491, 47, -30.71, -28.66, 0.01202, 48, -42.96, -58.58, 0.0005, 49, 3.98, -101.18, 0.00132, 50, -6.97, -106.92, 0.00125, 2, 46, -11.63, -8.58, 1, 48, -56.46, -65.33, 0, 2, 46, -11.33, -2.01, 1, 48, -61.49, -61.08, 0, 1, 46, 15.33, 11.05, 1, 2, 46, 44.3, 17.52, 0.35706, 47, -3.6, 17.44, 0.64294, 3, 46, 62.65, 18.54, 0.00662, 47, 13.05, 25.22, 0.98719, 48, -32.6, 10.06, 0.00619, 2, 47, 29.89, 29.32, 0.81076, 48, -20.18, 22.14, 0.18924, 3, 47, 50.64, 32.24, 0.35635, 48, -3.78, 35.19, 0.64365, 50, -84.97, -41.79, 0, 3, 47, 65.23, 27.04, 0.14065, 48, 11.43, 38.12, 0.85935, 50, -83.28, -26.39, 0, 4, 47, 79.74, 13.96, 0.0158, 48, 30.57, 34.21, 0.98321, 49, -33.61, 11.09, 0.001, 50, -73.9, -9.26, 0, 2, 48, 59.08, 2.44, 0.02777, 49, 8.58, 17.62, 0.97223, 4, 47, 81.99, 2.52, 0.00089, 48, 38.31, 25.5, 0.94882, 49, -22.08, 12.82, 0.05029, 50, -63.28, -4.44, 0, 2, 46, 28.25, 13.29, 0.95729, 47, -16.91, 7.53, 0.04271], "hull": 48, "edges": [80, 78, 78, 76, 76, 74, 74, 72, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 42, 44, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 96, 96, 0, 0, 98, 98, 94, 92, 94, 92, 90, 90, 88, 88, 86, 86, 84, 84, 100, 80, 82, 100, 82, 72, 70, 70, 68, 90, 70, 94, 68, 68, 66, 66, 64, 66, 0, 94, 0, 0, 2, 82, 84], "width": 190, "height": 220}}, "toufa3": {"toufa3": {"type": "mesh", "uvs": [0.48764, 0.02574, 0.56493, 0.06075, 0.65682, 0.11069, 0.7224, 0.18924, 0.7818, 0.27442, 0.8185, 0.3709, 0.8359, 0.45353, 0.84917, 0.54061, 0.85168, 0.63708, 0.85792, 0.72758, 0.87198, 0.77027, 0.91258, 0.77369, 0.936, 0.73868, 0.93756, 0.70453, 0.94146, 0.63793, 0.96185, 0.6328, 1, 0.70807, 1, 0.73645, 1, 0.80802, 1, 0.88576, 0.97539, 0.95486, 0.93815, 1, 0.9043, 1, 0.85587, 1, 0.80283, 0.98077, 0.74529, 0.92648, 0.69678, 0.83393, 0.66502, 0.74853, 0.64809, 0.64611, 0.62666, 0.54863, 0.57588, 0.48694, 0.53977, 0.43264, 0.50329, 0.35984, 0.44123, 0.31172, 0.36789, 0.26483, 0.3363, 0.28087, 0.28214, 0.34133, 0.23249, 0.41413, 0.18397, 0.51408, 0.15576, 0.60169, 0.12868, 0.69917, 0.1174, 0.78184, 0.11514, 0.82379, 0.07791, 0.82873, 0.04519, 0.78184, 0.02149, 0.70287, 0, 0.60169, 0, 0.53905, 0, 0.48199, 0.01214, 0.40856, 0.02697, 0.32233, 0.04181, 0.24549, 0.07708, 0.15467, 0.13017, 0.08551, 0.20122, 0.0283, 0.26993, 0, 0.34956, 0, 0.41269, 0], "triangles": [13, 14, 15, 16, 13, 15, 13, 16, 17, 12, 13, 17, 12, 17, 18, 11, 12, 18, 11, 18, 19, 20, 11, 19, 20, 10, 11, 10, 23, 24, 10, 22, 23, 20, 22, 10, 21, 22, 20, 26, 27, 9, 26, 9, 10, 25, 26, 10, 24, 25, 10, 28, 29, 7, 8, 28, 7, 27, 28, 8, 27, 8, 9, 32, 3, 4, 31, 4, 5, 30, 31, 5, 5, 29, 30, 6, 29, 5, 29, 6, 7, 32, 33, 2, 3, 32, 2, 31, 32, 4, 34, 57, 0, 1, 33, 34, 1, 34, 0, 33, 1, 2, 34, 56, 57, 55, 34, 54, 34, 55, 56, 35, 54, 34, 53, 54, 35, 52, 53, 35, 36, 51, 52, 35, 36, 52, 50, 51, 36, 37, 50, 36, 49, 50, 37, 38, 49, 37, 48, 49, 38, 47, 48, 38, 39, 46, 47, 38, 39, 47, 40, 46, 39, 45, 46, 40, 44, 45, 40, 41, 44, 40, 41, 43, 44, 42, 43, 41], "vertices": [2, 62, 52.51, 2.63, 0.03749, 63, 5.56, 15.33, 0.96251, 3, 63, 20.55, 19.78, 0.88956, 64, -16.75, 16.09, 0.10218, 65, -55.12, 4.98, 0.00826, 3, 63, 39.28, 23.96, 0.28612, 64, 0.33, 24.83, 0.59236, 65, -40.09, 16.91, 0.12152, 3, 63, 57.27, 21.24, 0.01808, 64, 18.42, 26.71, 0.49544, 65, -22.72, 22.3, 0.48648, 2, 64, 36.74, 26.99, 0.05242, 65, -4.82, 26.17, 0.94758, 2, 65, 13.04, 25.37, 0.99791, 66, -26.38, 20.8, 0.00209, 2, 65, 27.21, 22.31, 0.8769, 66, -11.88, 20.64, 0.1231, 2, 65, 41.74, 18.23, 0.32847, 66, 3.17, 19.55, 0.67153, 5, 65, 56.87, 11.64, 0.00259, 66, 19.32, 16.12, 0.96136, 67, -9.9, 17.6, 0.03576, 69, 28.68, 36, 5e-05, 70, 28.87, 24.67, 0.00023, 5, 66, 34.63, 13.62, 0.30669, 67, 4.94, 13.08, 0.63135, 68, -2.66, 23.69, 0.0134, 69, 16.77, 26.06, 0.02313, 70, 13.53, 22.27, 0.02543, 5, 66, 42.35, 14.45, 0.02941, 67, 12.7, 12.88, 0.61707, 68, 2.14, 17.6, 0.10528, 69, 12.36, 19.68, 0.12339, 70, 6.47, 19.07, 0.12485, 5, 67, 15.99, 19.74, 0.18494, 68, 9.52, 19.44, 0.08865, 69, 16.29, 13.16, 0.23077, 70, 6.49, 11.45, 0.48696, 71, -4.42, 14.32, 0.00867, 5, 67, 12, 26, 0.02679, 68, 11.79, 26.51, 0.01124, 69, 23.71, 13.07, 0.03381, 70, 12.81, 7.56, 0.69942, 71, -0.12, 8.27, 0.22873, 5, 67, 6.67, 28.39, 0.00262, 68, 10.22, 32.14, 0.00058, 69, 28.63, 16.23, 0.00068, 70, 18.65, 7.74, 0.17686, 71, 5.33, 6.15, 0.81926, 3, 67, -3.68, 33.19, 0, 70, 30.06, 7.92, 7e-05, 71, 15.9, 1.85, 0.99993, 1, 71, 15.53, -2.04, 1, 2, 70, 18.98, -3.95, 0.28422, 71, 1.06, -4.74, 0.71578, 2, 70, 14.14, -4.34, 0.85772, 71, -3.55, -3.21, 0.14228, 2, 69, 21.02, -3.56, 0.19979, 70, 1.94, -5.31, 0.80021, 3, 68, 31.09, 6.43, 0.00617, 69, 10.2, -11.28, 0.98724, 70, -11.31, -6.37, 0.00659, 2, 68, 30.46, -6.23, 0.40435, 69, -2.09, -14.4, 0.59565, 2, 68, 26.3, -15.76, 0.7358, 69, -12.42, -13.23, 0.2642, 2, 68, 20.29, -17.76, 0.86251, 69, -16.1, -8.07, 0.13749, 3, 67, 48.22, -4.16, 0.01374, 68, 11.7, -20.62, 0.97101, 69, -21.36, -0.71, 0.01525, 2, 67, 41.56, -12.22, 0.14456, 68, 1.25, -20.64, 0.85544, 2, 67, 29.01, -18.88, 0.62225, 68, -11.89, -15.24, 0.37775, 3, 66, 45.22, -19.95, 0.12217, 67, 10.97, -21.6, 0.86981, 68, -25.5, -3.09, 0.00802, 3, 65, 59.29, -28.05, 0.00082, 66, 29.63, -22.28, 0.61671, 67, -4.79, -21.84, 0.38247, 4, 64, 72.99, -30.9, 0.00288, 65, 42.1, -23.47, 0.13587, 66, 11.86, -21.24, 0.84171, 67, -22.26, -18.44, 0.01954, 3, 64, 57.2, -24.22, 0.11771, 65, 25.3, -20.02, 0.6566, 66, -5.28, -21.21, 0.22568, 3, 64, 43.07, -25.58, 0.52066, 65, 11.72, -24.13, 0.46847, 66, -17.77, -27.96, 0.01087, 4, 62, 35.72, -65.6, 0.00057, 63, 56.93, -32.6, 0.00432, 64, 31.59, -25.49, 0.84078, 65, 0.45, -26.3, 0.15433, 5, 61, 35.99, -61.71, 0.0012, 62, 34.01, -51.51, 0.01868, 63, 43.79, -27.22, 0.09702, 64, 17.53, -23.58, 0.87812, 65, -13.72, -27.19, 0.00498, 4, 61, 36.4, -47.49, 0.02119, 62, 26.29, -39.56, 0.12094, 63, 29.6, -28.13, 0.38087, 64, 4.01, -28.02, 0.477, 5, 60, 57.52, -41.45, 0.00395, 61, 35.47, -31.63, 0.1803, 62, 16.53, -27.02, 0.40598, 63, 13.89, -30.53, 0.32139, 64, -10.59, -34.28, 0.08838, 5, 60, 53.37, -36.43, 0.02275, 61, 29.91, -28.24, 0.39542, 62, 10.03, -27.37, 0.39516, 63, 11.03, -36.38, 0.15923, 64, -11.9, -40.67, 0.02744, 5, 60, 40.8, -29.26, 0.18581, 61, 15.69, -25.54, 0.65546, 62, -3.22, -33.21, 0.12909, 63, 9.67, -50.79, 0.02852, 64, -9.6, -54.96, 0.00112, 5, 59, 57.72, -28.85, 0.00205, 60, 26.41, -23.44, 0.67063, 61, 0.18, -24.72, 0.31293, 62, -16.46, -41.32, 0.01266, 63, 10.29, -66.31, 0.00173, 3, 59, 40.4, -20.22, 0.23387, 60, 7.57, -18.99, 0.76101, 61, -19.07, -26.66, 0.00513, 2, 59, 25.29, -15.33, 0.89424, 60, -8.26, -17.69, 0.10576, 1, 59, 8.49, -10.7, 1, 1, 59, -5.7, -8.96, 1, 1, 59, -12.88, -8.72, 1, 1, 59, -13.9, -1.78, 1, 1, 59, -6.04, 4.54, 1, 1, 59, 7.34, 9.32, 1, 2, 59, 24.53, 13.78, 0.98576, 60, -15.64, 10.49, 0.01424, 2, 59, 35.24, 14.06, 0.68783, 60, -5.28, 13.2, 0.31217, 2, 59, 44.99, 14.31, 0.20649, 60, 4.16, 15.68, 0.79351, 3, 59, 57.6, 12.37, 0.00221, 60, 16.88, 16.67, 0.99605, 61, -21.88, 10.09, 0.00174, 2, 60, 31.85, 17.72, 0.69826, 61, -8.08, 15.96, 0.30174, 2, 60, 45.26, 18.37, 0.15658, 61, 4.4, 20.94, 0.84342, 3, 60, 61.96, 15.93, 2e-05, 61, 20.98, 24.07, 0.99128, 62, -26.96, 10.67, 0.0087, 2, 61, 36.32, 22.37, 0.75976, 62, -13.35, 17.96, 0.24024, 2, 61, 51.83, 16.74, 0.20507, 62, 2.61, 22.11, 0.79493, 2, 61, 62.99, 8.73, 0.00815, 62, 16.34, 21.83, 0.99185, 2, 62, 30.17, 16.3, 0.99483, 63, -17.28, 2.5, 0.00517, 2, 62, 41.13, 11.92, 0.70347, 63, -8.11, 9.93, 0.29653], "hull": 58, "edges": [92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 44, 46, 42, 44, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 114, 112, 114, 110, 112, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 92, 94, 94, 96], "width": 187, "height": 171}}, "toufa4": {"toufa4": {"type": "mesh", "uvs": [0.67424, 0.03405, 0.75732, 0.07887, 0.83618, 0.13685, 0.90684, 0.19679, 0.95907, 0.26951, 1, 0.34026, 1, 0.38693, 0.90786, 0.41981, 0.80078, 0.46913, 0.72797, 0.56228, 0.66943, 0.66503, 0.63517, 0.77051, 0.61375, 0.91436, 0.61385, 1, 0.58395, 1, 0.50919, 0.9826, 0.43034, 0.95017, 0.34943, 0.90202, 0.28492, 0.85191, 0.22757, 0.79884, 0.17329, 0.73595, 0.12516, 0.67208, 0.07822, 0.60609, 0.02701, 0.53042, 0, 0.44002, 0, 0.3673, 0, 0.29348, 0.0178, 0.18831, 0.069, 0.14116, 0.14376, 0.08712, 0.23388, 0.04879, 0.32707, 0.04192, 0.40081, 0, 0.48581, 0, 0.5831, 0, 0.38275, 0.56265, 0.43622, 0.38228, 0.54318, 0.25167, 0.65337, 0.17237, 0.75384, 0.2299, 0.8381, 0.29987, 0.8851, 0.37917, 0.40815, 0.47699, 0.49245, 0.31361, 0.59036, 0.21771, 0.38275, 0.67771, 0.40543, 0.79278, 0.42488, 0.86741, 0.46863, 0.93583, 0.26445, 0.14593, 0.21584, 0.24234, 0.19801, 0.33874, 0.18505, 0.45536, 0.18991, 0.56265, 0.21098, 0.67305, 0.25311, 0.77256], "triangles": [14, 12, 13, 14, 15, 12, 12, 47, 11, 46, 45, 11, 11, 45, 10, 45, 35, 10, 35, 42, 10, 10, 42, 9, 42, 36, 9, 36, 43, 9, 9, 43, 8, 8, 43, 37, 8, 37, 44, 44, 33, 34, 44, 37, 33, 44, 34, 38, 38, 34, 0, 39, 44, 38, 44, 39, 8, 8, 41, 7, 8, 40, 41, 8, 39, 40, 7, 41, 6, 41, 5, 6, 41, 4, 5, 41, 40, 4, 40, 39, 3, 40, 3, 4, 3, 39, 2, 39, 1, 2, 39, 38, 1, 38, 0, 1, 51, 50, 36, 36, 50, 43, 43, 50, 49, 43, 49, 37, 37, 49, 31, 37, 31, 32, 51, 26, 50, 50, 26, 27, 50, 27, 28, 37, 32, 33, 28, 29, 50, 50, 29, 49, 29, 30, 49, 49, 30, 31, 21, 22, 53, 22, 23, 53, 42, 35, 52, 23, 52, 53, 35, 53, 52, 23, 24, 52, 36, 42, 51, 42, 52, 51, 51, 52, 25, 52, 24, 25, 25, 26, 51, 19, 55, 18, 18, 55, 46, 19, 20, 55, 55, 45, 46, 20, 54, 55, 55, 54, 45, 20, 21, 54, 54, 35, 45, 21, 53, 54, 54, 53, 35, 16, 48, 15, 15, 48, 12, 17, 47, 16, 16, 47, 48, 48, 47, 12, 47, 17, 46, 17, 18, 46, 47, 46, 11], "vertices": [3, 75, 83.84, -54.65, 0.2374, 79, -20.87, 36.01, 0.41127, 78, 66.43, 27.54, 0.35133, 3, 75, 83.83, -70.47, 0.10665, 79, -5.34, 32.96, 0.68515, 78, 66.5, 11.72, 0.2082, 3, 75, 81.48, -86.79, 0.02384, 79, 10.23, 27.53, 0.9448, 78, 64.23, -4.62, 0.03135, 2, 75, 78.17, -102.09, 0.00083, 79, 24.62, 21.36, 0.99917, 1, 79, 36.79, 12.12, 1, 1, 79, 47.07, 2.62, 1, 2, 79, 49.6, -5.04, 0.99986, 78, 40.03, -49.62, 0.00014, 3, 79, 36.85, -15.23, 0.9215, 78, 27.53, -39.13, 0.06967, 77, 63.72, -43.94, 0.00882, 3, 79, 22.64, -28.9, 0.39259, 78, 11.34, -27.88, 0.41888, 77, 50.17, -29.61, 0.18853, 4, 79, 16.21, -47.99, 0.04046, 78, -8.64, -25.32, 0.15832, 77, 31.13, -23.02, 0.80099, 76, 59.36, -32.37, 0.00024, 4, 79, 12.54, -67.91, 0.0001, 78, -28.9, -25.65, 0.00115, 77, 11.24, -19.19, 0.82512, 76, 42.03, -21.89, 0.17363, 2, 77, -7.87, -19.34, 0.125, 76, 24.04, -15.42, 0.875, 2, 72, -9.8, -13.38, 0.03129, 76, -0.66, -10.79, 0.96871, 2, 72, -19.52, -2.2, 0.34905, 76, -15.47, -10.16, 0.65095, 2, 72, -15.77, 1.05, 0.40652, 76, -15.25, -5.2, 0.59348, 2, 72, -4.42, 6.91, 0.95402, 76, -11.7, 7.07, 0.04598, 1, 72, 9.15, 11.24, 1, 2, 72, 24.75, 13.74, 0.97165, 73, -14.48, 11.49, 0.02835, 2, 72, 38.52, 14.2, 0.47706, 73, -0.98, 14.24, 0.52294, 2, 72, 51.73, 13.5, 0.02929, 73, 12.16, 15.75, 0.97071, 1, 73, 26.28, 15.78, 1, 2, 73, 39.89, 14.92, 0.80886, 74, -11.55, 10.33, 0.19114, 2, 73, 53.66, 13.67, 0.13575, 74, 0.74, 16.66, 0.86425, 1, 74, 14.77, 23.48, 1, 2, 74, 30.85, 26.01, 0.99995, 75, -32.16, 8.63, 5e-05, 2, 74, 43.33, 24.46, 0.93923, 75, -21.18, 14.78, 0.06077, 2, 74, 56.01, 22.89, 0.6226, 75, -10.04, 21.03, 0.3774, 2, 74, 73.7, 17.71, 0.11515, 75, 7.27, 27.35, 0.88485, 2, 74, 80.75, 8.28, 0.01468, 75, 18.54, 23.92, 0.98532, 1, 75, 32.77, 17.67, 1, 1, 75, 45.87, 7.87, 1, 5, 74, 92.51, -36.35, 3e-05, 75, 54.47, -5.05, 0.90851, 79, -75.18, 16.68, 0.01383, 78, 36.8, 76.99, 0.07751, 77, 96.56, 67.82, 0.00012, 3, 75, 66.78, -12.17, 0.74011, 79, -65.82, 27.4, 0.05137, 78, 49.15, 69.92, 0.20851, 3, 75, 73.68, -24.48, 0.57585, 79, -52.42, 31.82, 0.11179, 78, 56.12, 57.65, 0.31236, 3, 75, 81.58, -38.57, 0.39316, 79, -37.08, 36.87, 0.22887, 78, 64.09, 43.61, 0.37796, 6, 73, 27.31, -30.12, 0.31638, 74, 1.97, -34.43, 0.20053, 75, -19.59, -57.17, 0.01726, 78, -36.98, 24.48, 0.01127, 77, 13.59, 31.54, 0.44836, 76, 61.79, 24.88, 0.00619, 5, 73, 45.74, -56.82, 0.02659, 74, 31.84, -47.08, 0.18886, 75, 11.97, -49.65, 0.18795, 78, -5.46, 32.17, 0.28006, 77, 46.01, 32.6, 0.31653, 6, 73, 51.89, -84.89, 9e-05, 74, 52.08, -67.48, 0.02922, 75, 40.37, -54.08, 0.23852, 79, -29.75, -6.55, 0.02427, 78, 22.95, 27.88, 0.67241, 77, 72.95, 22.59, 0.03549, 4, 74, 63.44, -87.33, 0.00143, 75, 61.28, -63.33, 0.14921, 79, -16.67, 12.2, 0.34159, 78, 43.91, 18.74, 0.50777, 3, 75, 60.75, -82.74, 0.01247, 79, 2.29, 7.97, 0.92148, 78, 43.49, -0.68, 0.06605, 1, 79, 19.36, 0.85, 1, 3, 79, 31.06, -9.74, 0.93295, 78, 31.78, -32.37, 0.06038, 77, 69.26, -38.19, 0.00667, 5, 73, 36.06, -42.8, 0.11555, 74, 16.16, -40.44, 0.25204, 75, -4.6, -53.6, 0.07094, 78, -22.01, 28.13, 0.07975, 77, 28.99, 32.04, 0.48173, 6, 73, 48.97, -71.58, 0.00407, 74, 42.48, -57.81, 0.08138, 75, 26.9, -51.98, 0.23317, 79, -34.39, -19.37, 0.00058, 78, 9.48, 29.91, 0.5382, 77, 60.18, 27.34, 0.14259, 5, 74, 56.95, -75.98, 0.01062, 75, 49.32, -58.04, 0.19841, 79, -24.15, 1.48, 0.09363, 78, 31.93, 23.97, 0.69036, 77, 80.93, 16.92, 0.00697, 6, 72, 45.99, -19.21, 0.04315, 73, 11.95, -17.46, 0.58698, 74, -17.79, -31.98, 0.03032, 75, -36.95, -66.9, 0.00027, 77, -5.37, 25.46, 0.23046, 76, 41.9, 25.75, 0.10881, 4, 72, 30.1, -6.63, 0.78502, 73, -5.81, -7.71, 0.07196, 77, -23.18, 15.8, 0.00937, 76, 21.85, 22.85, 0.13365, 1, 72, 19.21, 1.01, 1, 1, 72, 5.97, 5.2, 1, 5, 74, 75.93, -23.82, 0.00372, 75, 33.69, -4.78, 0.97711, 79, -79.42, -3.66, 0.00023, 78, 16.02, 77.15, 0.0165, 77, 76.25, 72.23, 0.00244, 4, 74, 60.37, -13.76, 0.0463, 75, 15.2, -5.9, 0.93391, 78, -2.47, 75.93, 0.01277, 77, 57.91, 74.83, 0.00701, 5, 73, 76.7, -31.09, 0.00029, 74, 44.19, -8.77, 0.68206, 75, -0.8, -11.47, 0.2865, 78, -18.43, 70.27, 0.01379, 77, 41.12, 72.56, 0.01736, 5, 73, 62.5, -16.6, 0.00456, 74, 24.43, -4.15, 0.97204, 75, -19.45, -19.46, 0.00809, 78, -37.04, 62.19, 0.00283, 77, 21.25, 68.45, 0.01248, 5, 73, 47.67, -5.42, 0.06167, 74, 5.91, -2.66, 0.93062, 75, -35.24, -29.25, 0.00033, 78, -52.78, 52.32, 0.00023, 77, 3.82, 62.02, 0.00715, 1, 73, 30.7, 4.03, 1, 2, 72, 51.5, 7.29, 0.01252, 73, 12.97, 9.58, 0.98748], "hull": 35, "edges": [26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 68, 66, 68, 64, 66, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 50, 52, 48, 50, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 26, 28, 30, 28, 76, 78, 78, 80, 80, 82, 70, 84, 84, 72, 72, 86, 86, 74, 74, 88, 88, 76, 70, 90, 90, 92, 92, 94, 94, 96, 62, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110], "width": 166, "height": 173}}, "toufa5": {"toufa5": {"type": "mesh", "uvs": [0.85479, 0.03249, 0.77687, 0.09934, 0.69896, 0.16476, 0.63218, 0.24156, 0.55983, 0.31978, 0.50696, 0.39373, 0.48748, 0.46058, 0.45966, 0.53738, 0.43344, 0.61177, 0.46405, 0.69283, 0.47239, 0.75968, 0.53361, 0.83221, 0.61987, 0.89479, 0.70613, 0.92608, 0.80074, 0.9403, 0.89813, 0.94314, 1, 0.92323, 1, 0.97301, 0.88978, 1, 0.79796, 1, 0.66439, 1, 0.5197, 0.97728, 0.37779, 0.93888, 0.25257, 0.8763, 0.14683, 0.79666, 0.0717, 0.72127, 0, 0.62741, 0, 0.53639, 0, 0.42981, 0, 0.33025, 0.09118, 0.25203, 0.23309, 0.18518, 0.40005, 0.11692, 0.58926, 0.05719, 0.72283, 0, 0.87705, 0], "triangles": [15, 16, 17, 20, 12, 13, 19, 13, 14, 20, 13, 19, 18, 14, 15, 18, 15, 17, 19, 14, 18, 22, 23, 10, 22, 10, 11, 21, 22, 11, 21, 11, 12, 21, 12, 20, 8, 27, 7, 26, 27, 8, 25, 26, 8, 24, 25, 8, 24, 8, 9, 23, 24, 9, 10, 23, 9, 5, 32, 4, 31, 32, 5, 6, 28, 29, 6, 31, 5, 30, 31, 6, 6, 29, 30, 7, 27, 28, 6, 7, 28, 0, 34, 35, 1, 34, 0, 2, 33, 34, 2, 34, 1, 3, 33, 2, 32, 33, 3, 4, 32, 3], "vertices": [1, 80, 14.32, 7.14, 1, 1, 80, 21.31, 7.59, 1, 2, 80, 28.19, 7.97, 0.9899, 81, -8.4, 12.3, 0.0101, 2, 80, 35.63, 9.35, 0.74022, 81, -1.02, 10.63, 0.25978, 2, 80, 43.32, 10.59, 0.14458, 81, 6.53, 8.72, 0.85542, 2, 80, 50.18, 12.36, 0.0014, 81, 13.53, 7.62, 0.9986, 2, 81, 19.6, 7.91, 0.98875, 82, -6.23, 10.86, 0.01125, 2, 81, 26.63, 7.99, 0.6676, 82, 0.36, 8.42, 0.3324, 2, 81, 33.43, 8.1, 0.06509, 82, 6.75, 6.09, 0.93491, 2, 82, 14.18, 6.23, 0.98784, 83, -3.11, 8.99, 0.01216, 2, 82, 20.17, 5.58, 0.51458, 83, 1.75, 5.44, 0.48542, 2, 82, 27.09, 7.25, 0.00014, 83, 8.57, 3.42, 0.99986, 2, 83, 15.44, 2.86, 0.93248, 84, -0.73, 4.5, 0.06752, 2, 83, 20.14, 4.11, 0.06443, 84, 3.61, 2.29, 0.93557, 1, 84, 8.1, 1.65, 1, 1, 84, 12.57, 2.05, 1, 1, 84, 16.94, 4.51, 1, 1, 84, 17.59, 0.07, 1, 1, 84, 12.93, -3.07, 1, 1, 84, 8.75, -3.68, 1, 2, 83, 24.02, -1.63, 0.02913, 84, 2.67, -4.58, 0.97087, 2, 83, 18.19, -5.43, 0.88423, 84, -4.21, -3.52, 0.11577, 1, 83, 11.36, -8.23, 1, 2, 82, 28.79, -6.17, 0.07287, 83, 3.34, -9.05, 0.92713, 2, 82, 20.9, -9.74, 0.76571, 83, -5.28, -8.2, 0.23429, 3, 81, 46.32, -6.32, 0.00132, 82, 13.63, -11.99, 0.99372, 83, -12.7, -6.51, 0.00496, 2, 81, 38.67, -11.19, 0.13172, 82, 4.74, -13.8, 0.86828, 2, 81, 30.64, -12.77, 0.58145, 82, -3.33, -12.4, 0.41855, 2, 81, 21.22, -14.63, 0.97008, 82, -12.79, -10.76, 0.02992, 1, 81, 12.43, -16.36, 1, 2, 80, 50.5, -10.63, 0.01811, 81, 4.71, -13.61, 0.98189, 2, 80, 41.84, -8.65, 0.40419, 81, -2.45, -8.37, 0.59581, 1, 80, 32.43, -5.8, 1, 1, 80, 23.07, -1.67, 1, 1, 80, 15.35, 0.48, 1, 1, 80, 11.33, 6.33, 1], "hull": 36, "edges": [68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 56, 58, 54, 56, 52, 54, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 38, 40, 36, 38, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 68, 70, 0, 70, 58, 12, 52, 16], "width": 46, "height": 90}}, "toufa6": {"toufa6": {"type": "mesh", "uvs": [0.16445, 0.1212, 0.17234, 0.26457, 0.22168, 0.3987, 0.31442, 0.48195, 0.43085, 0.52126, 0.57293, 0.49813, 0.70909, 0.43338, 0.83144, 0.35476, 0.91608, 0.29444, 1, 0.22507, 1, 0.56202, 0.95863, 0.6314, 0.89548, 0.71002, 0.80273, 0.78402, 0.58172, 0.93896, 0.48503, 1, 0.41794, 1, 0.31335, 1, 0.20284, 1, 0.1022, 0.91584, 0.043, 0.8164, 0, 0.69152, 0, 0.58746, 0, 0.48802, 0, 0.3724, 0, 0.24984, 0.06274, 0.0764, 0.12194, 0, 0.24536, 0, 0.69617, 0.85802], "triangles": [25, 0, 1, 25, 26, 0, 26, 27, 0, 0, 27, 28, 22, 23, 2, 23, 24, 2, 24, 1, 2, 24, 25, 1, 19, 20, 3, 20, 21, 3, 3, 22, 2, 3, 21, 22, 15, 16, 14, 14, 16, 4, 4, 16, 17, 4, 17, 18, 18, 19, 4, 19, 3, 4, 14, 29, 13, 14, 4, 29, 29, 4, 5, 29, 5, 13, 5, 6, 13, 13, 6, 12, 12, 6, 11, 11, 6, 10, 10, 6, 7, 7, 8, 10, 8, 9, 10], "vertices": [2, 89, 24.73, -7.79, 0.00175, 90, 11.14, -2.68, 0.99825, 4, 87, 24.4, -26.76, 0.00479, 88, 21.19, -16.46, 0.03152, 89, 15.55, -8.18, 0.46657, 90, 3.32, -7.52, 0.49712, 4, 87, 20.84, -18.12, 0.12622, 88, 12.4, -13.3, 0.33918, 89, 6.88, -11.69, 0.5115, 90, -2.51, -14.82, 0.0231, 4, 86, 30.42, -16.72, 0.00232, 87, 13.97, -12.68, 0.59676, 88, 3.76, -14.74, 0.30285, 89, 1.41, -18.53, 0.09807, 4, 86, 23.18, -11.23, 0.20024, 87, 5.27, -10.03, 0.75921, 88, -4.05, -19.4, 0.03513, 89, -1.3, -27.21, 0.00542, 3, 85, 48.92, -8.93, 0.00141, 86, 12.7, -8.78, 0.91951, 87, -5.4, -11.34, 0.07908, 2, 85, 37.9, -8.98, 0.44482, 86, 1.68, -8.96, 0.55518, 2, 85, 27.51, -10.25, 0.9925, 86, -8.69, -10.36, 0.0075, 1, 85, 20.18, -11.48, 1, 1, 85, 12.69, -13.26, 1, 1, 85, 20.7, 6.76, 1, 2, 85, 25.23, 9.73, 0.97647, 86, -11.21, 9.6, 0.02353, 2, 85, 31.49, 12.65, 0.75383, 86, -4.98, 12.59, 0.24617, 3, 85, 39.71, 14.46, 0.27282, 86, 3.21, 14.5, 0.72619, 87, -22.35, 7.23, 0.00098, 2, 86, 22.25, 17.78, 0.52275, 87, -5.62, 16.88, 0.47725, 2, 86, 30.42, 18.82, 0.22459, 87, 1.7, 20.67, 0.77541, 2, 86, 35.11, 17, 0.11513, 87, 6.73, 20.59, 0.88487, 3, 86, 42.43, 14.18, 0.01695, 87, 14.57, 20.47, 0.96755, 88, -20.4, 7.97, 0.01551, 2, 87, 22.86, 20.34, 0.88582, 88, -14.74, 14.02, 0.11418, 2, 87, 30.32, 14.83, 0.60243, 88, -5.65, 15.85, 0.39757, 2, 87, 34.66, 8.4, 0.24189, 88, 2.03, 14.75, 0.75811, 2, 87, 37.76, 0.36, 0.01101, 88, 10.07, 11.64, 0.98899, 2, 88, 14.93, 7.09, 0.92007, 89, -4.83, 5.19, 0.07993, 2, 88, 19.58, 2.74, 0.1289, 89, 1.53, 5.05, 0.8711, 1, 89, 8.93, 4.89, 1, 2, 89, 16.77, 4.72, 0.65673, 90, -1.93, 4.33, 0.34327, 1, 90, 10.08, 5.4, 1, 1, 90, 16.48, 3.79, 1, 1, 90, 20.84, -4.38, 1, 3, 85, 48.89, 15.89, 0.02675, 86, 12.37, 16.04, 0.8839, 87, -14.28, 11.84, 0.08935], "hull": 29, "edges": [56, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 54, 56, 54, 52, 52, 50, 48, 50, 46, 48, 44, 46, 42, 44, 42, 40, 40, 38, 38, 36, 34, 36, 36, 8, 32, 34, 30, 32, 30, 28, 28, 58, 58, 26, 26, 24, 24, 22, 18, 20, 22, 20, 26, 28], "width": 75, "height": 64}}, "toufa7": {"toufa7": {"type": "mesh", "uvs": [0.69038, 0.03177, 0.77206, 0.07777, 0.84605, 0.12877, 0.89702, 0.1788, 0.93642, 0.2188, 0.96813, 0.2618, 1, 0.3428, 1, 0.40089, 1, 0.47389, 1, 0.55289, 1, 0.58893, 0.97582, 0.66293, 0.94123, 0.72293, 0.89126, 0.77193, 0.83936, 0.80393, 0.79612, 0.83993, 0.7894, 0.87093, 0.8163, 0.89893, 0.79324, 0.92993, 0.76345, 0.92493, 0.73078, 0.88693, 0.7154, 0.91293, 0.72598, 0.95093, 0.7058, 0.97293, 0.66544, 0.98293, 0.59352, 1, 0.57814, 0.9761, 0.57045, 0.9271, 0.56565, 0.8971, 0.52625, 0.8901, 0.48974, 0.8791, 0.47532, 0.8431, 0.47052, 0.8991, 0.4513, 0.9441, 0.41286, 0.9671, 0.3946, 0.9311, 0.36962, 0.9251, 0.37538, 0.8971, 0.35424, 0.8731, 0.34463, 0.8431, 0.32733, 0.8721, 0.28683, 0.8691, 0.23494, 0.8701, 0.18497, 0.8561, 0.14365, 0.8461, 0.12443, 0.8201, 0.12828, 0.7741, 0.09753, 0.7931, 0.05992, 0.7911, 0.02244, 0.7721, 0.05607, 0.7561, 0.08874, 0.7271, 0.09931, 0.6931, 0.08682, 0.6661, 0.08298, 0.6461, 0.04742, 0.6521, 0.0186, 0.6461, 0, 0.6121, 0, 0.53851, 0.0173, 0.53251, 0.02883, 0.56251, 0.05862, 0.58951, 0.05189, 0.56051, 0.04228, 0.51451, 0.04324, 0.46451, 0.06246, 0.39051, 0.09033, 0.32651, 0.12492, 0.28251, 0.14933, 0.22877, 0.17431, 0.16577, 0.21083, 0.11177, 0.25407, 0.07477, 0.31365, 0.03577, 0.37707, 0, 0.44058, 0, 0.53187, 0, 0.60005, 0, 0.65294, 0.95893, 0.64333, 0.91893, 0.63003, 0.9551, 0.52567, 0.29823, 0.44619, 0.3455, 0.38375, 0.40901, 0.34543, 0.47399, 0.31136, 0.5567, 0.31278, 0.6276, 0.34117, 0.71031, 0.40929, 0.78858, 0.46748, 0.81074, 0.55122, 0.8388, 0.62218, 0.84028, 0.70024, 0.8004, 0.75276, 0.75314, 0.77688, 0.69258, 0.79959, 0.62612, 0.80669, 0.55079, 0.81804, 0.46661, 0.80527, 0.40162, 0.75559, 0.34697, 0.68037, 0.32482, 0.61793, 0.30414, 0.64638, 0.58766], "triangles": [60, 58, 59, 60, 57, 58, 57, 60, 61, 56, 57, 61, 55, 56, 61, 54, 55, 61, 52, 53, 54, 85, 54, 84, 62, 63, 84, 84, 63, 64, 54, 61, 84, 61, 62, 84, 46, 52, 85, 85, 52, 54, 51, 46, 47, 49, 50, 48, 47, 48, 50, 47, 50, 51, 51, 52, 46, 45, 46, 43, 43, 44, 45, 43, 46, 86, 81, 101, 82, 81, 100, 101, 81, 80, 100, 82, 68, 81, 81, 69, 70, 81, 70, 71, 81, 71, 72, 81, 72, 80, 74, 80, 72, 74, 72, 73, 100, 80, 76, 80, 75, 76, 80, 74, 75, 65, 66, 83, 66, 67, 83, 83, 67, 82, 67, 68, 82, 81, 68, 69, 84, 83, 101, 65, 83, 84, 84, 64, 65, 42, 43, 86, 39, 86, 87, 87, 86, 101, 46, 85, 86, 101, 86, 85, 101, 85, 84, 34, 35, 33, 33, 35, 32, 36, 37, 35, 35, 37, 32, 32, 37, 31, 31, 37, 87, 37, 38, 87, 38, 39, 87, 40, 41, 39, 41, 42, 86, 39, 41, 86, 24, 25, 79, 25, 26, 79, 26, 27, 79, 79, 27, 78, 27, 28, 78, 28, 90, 78, 29, 89, 28, 28, 89, 90, 31, 87, 88, 29, 30, 89, 30, 31, 89, 31, 88, 89, 89, 88, 101, 101, 90, 89, 88, 87, 101, 101, 99, 98, 101, 100, 99, 7, 97, 6, 97, 4, 5, 97, 98, 4, 97, 5, 6, 98, 3, 4, 3, 98, 2, 98, 99, 2, 2, 99, 1, 99, 100, 1, 100, 0, 1, 100, 76, 0, 96, 101, 98, 95, 96, 9, 96, 8, 9, 97, 96, 98, 96, 7, 8, 96, 97, 7, 82, 101, 83, 92, 101, 93, 93, 94, 12, 12, 94, 11, 93, 101, 94, 10, 11, 95, 11, 94, 95, 94, 101, 95, 95, 9, 10, 95, 101, 96, 90, 91, 20, 15, 20, 91, 91, 90, 101, 91, 92, 15, 91, 101, 92, 24, 79, 77, 24, 77, 23, 22, 23, 21, 79, 78, 77, 23, 77, 21, 77, 78, 21, 21, 78, 20, 20, 78, 90, 15, 92, 14, 13, 14, 93, 14, 92, 93, 13, 93, 12, 17, 18, 16, 18, 19, 16, 19, 20, 16, 16, 20, 15], "vertices": [2, 96, -73.44, -34.54, 0.65686, 91, -89.94, 91.39, 0.34314, 2, 96, -54.88, -4.09, 0.85872, 91, -88.93, 127.04, 0.14128, 3, 104, -183.34, 208.02, 0, 96, -34.62, 23.32, 0.98127, 91, -84.95, 160.89, 0.01873, 3, 104, -156.56, 210.6, 0, 97, -86.68, 22.64, 0.00412, 96, -15.18, 41.92, 0.99588, 4, 104, -135.49, 212.26, 0, 102, -208.43, 2.04, 0.0001, 97, -74.87, 40.17, 0.03725, 96, 0.34, 56.26, 0.96264, 5, 104, -115.53, 210.98, 0, 102, -196.83, 18.33, 0.002, 98, -111.76, 41.17, 0.00014, 97, -61.44, 54.99, 0.10778, 96, 16.82, 67.61, 0.89008, 5, 104, -84.98, 200.51, 0, 102, -171.84, 38.79, 0.01539, 98, -86.96, 61.86, 0.01585, 97, -34.22, 72.37, 0.29843, 96, 47.3, 78.29, 0.67033, 5, 104, -68.84, 186.39, 0, 102, -151.34, 45.07, 0.03803, 98, -66.53, 68.33, 0.05789, 97, -13.13, 76.21, 0.43723, 96, 68.7, 77.19, 0.46685, 4, 102, -125.59, 52.96, 0.09789, 98, -40.85, 76.47, 0.17486, 97, 13.37, 81.03, 0.52413, 96, 95.6, 75.8, 0.20311, 4, 102, -97.72, 61.5, 0.20259, 98, -13.06, 85.27, 0.32793, 97, 42.05, 86.24, 0.42006, 96, 124.72, 74.3, 0.04942, 4, 102, -85, 65.4, 0.25545, 98, -0.38, 89.29, 0.37318, 97, 55.14, 88.62, 0.34855, 96, 138, 73.62, 0.02282, 4, 102, -56.17, 64.52, 0.39104, 98, 28.46, 88.69, 0.41129, 97, 83.67, 84.37, 0.19633, 96, 164.79, 62.94, 0.00135, 3, 102, -31.11, 58.31, 0.54172, 98, 53.58, 82.71, 0.36232, 97, 107.82, 75.26, 0.09597, 3, 102, -8.2, 45.26, 0.72988, 98, 76.61, 69.88, 0.2351, 97, 129.05, 59.62, 0.03502, 5, 104, 2.46, 42.03, 0.0051, 103, -7.3, 33.82, 0.03993, 102, 8.93, 29.67, 0.87342, 98, 93.88, 54.45, 0.07407, 97, 144.23, 42.12, 0.00748, 5, 104, 1.52, 20.79, 0.14796, 103, 5.61, 16.92, 0.37722, 102, 26.49, 17.68, 0.47323, 98, 111.56, 42.63, 0.00143, 97, 160.27, 28.16, 0.00016, 3, 104, 8.43, 11.31, 0.71601, 103, 16.99, 14.09, 0.21838, 102, 38.19, 18.57, 0.06561, 2, 104, 23.01, 12.28, 0.99661, 103, 27.55, 24.19, 0.00339, 1, 104, 25.79, -1.91, 1, 1, 104, 16.87, -9.31, 1, 4, 104, -1.95, -9.51, 0.31229, 103, 22.39, -8.55, 0.33889, 101, 4.08, 27.04, 0.13903, 100, 17.58, 24.77, 0.20979, 4, 104, 1.39, -20.28, 0.05894, 103, 31.85, -14.66, 0.13542, 101, 12.37, 19.42, 0.61045, 100, 28.78, 23.56, 0.19518, 4, 104, 14.61, -26.45, 0.01155, 103, 45.96, -10.92, 0.03719, 101, 26.91, 20.73, 0.9192, 100, 39.69, 33.25, 0.03206, 4, 104, 15.62, -37.63, 0.00359, 103, 53.9, -18.84, 0.01479, 101, 33.4, 11.57, 0.97671, 100, 50.35, 29.75, 0.00491, 2, 101, 34.06, -4.35, 0.8249, 107, 27.58, 22.95, 0.1751, 1, 107, 33.88, -4.67, 1, 1, 107, 25.06, -10.57, 1, 5, 105, 61.33, -10.19, 0.0091, 106, 34.28, -11.77, 0.21102, 107, 6.98, -13.52, 0.77278, 114, 17.92, 116.62, 0.00469, 113, 69.91, 110.28, 0.00241, 6, 104, -40.88, -59.71, 0, 105, 51.08, -14.77, 0.08421, 106, 23.84, -15.88, 0.64932, 107, -4.09, -15.37, 0.22284, 114, 7.95, 111.47, 0.02767, 113, 59.08, 107.32, 0.01595, 7, 104, -52.78, -69.39, 0, 105, 52.41, -30.06, 0.21055, 106, 24.47, -31.22, 0.63052, 107, -6.67, -30.5, 0.01845, 114, 10.14, 96.27, 0.08984, 113, 58.04, 92.01, 0.05056, 93, 147.23, 136.13, 9e-05, 9, 104, -65.07, -77.27, 0, 98, 160.87, -65.16, 0.00063, 105, 52.03, -44.66, 0.24549, 106, 23.42, -45.78, 0.53654, 107, -10.73, -44.52, 0.00063, 114, 10.58, 81.68, 0.14174, 113, 55.41, 77.65, 0.07447, 93, 145.81, 121.61, 0.0004, 91, 223.87, 163.65, 0.00011, 8, 104, -78.72, -72.69, 0, 98, 149.88, -74.45, 0.00314, 105, 40.58, -53.37, 0.24893, 106, 11.58, -53.96, 0.37646, 114, -0.36, 72.34, 0.25804, 113, 42.75, 70.81, 0.11157, 93, 133.77, 113.72, 0.00131, 91, 214.51, 152.72, 0.00055, 7, 104, -64.38, -87.68, 0, 98, 170.14, -69.97, 0.00013, 105, 61.04, -49.93, 0.17088, 106, 32.18, -51.46, 0.30572, 114, 19.87, 76.93, 0.4674, 113, 63.5, 71.05, 0.05585, 93, 154.42, 115.71, 2e-05, 5, 104, -56.74, -104.17, 0, 105, 78.97, -52.87, 0.15193, 106, 49.96, -55.21, 0.29454, 114, 37.94, 75, 0.52425, 113, 80.76, 65.38, 0.02929, 5, 104, -60.07, -120.87, 0, 105, 90.92, -65, 0.14708, 106, 61.33, -67.88, 0.29064, 114, 50.55, 63.56, 0.53935, 113, 90.69, 51.56, 0.02294, 5, 104, -74.69, -117.4, 0, 105, 79.84, -75.14, 0.14536, 106, 49.8, -77.51, 0.28051, 114, 40.06, 52.81, 0.54333, 113, 78.18, 43.24, 0.0308, 5, 104, -82.67, -123.16, 0, 105, 80.13, -84.98, 0.14272, 106, 49.64, -87.35, 0.27151, 114, 40.9, 43, 0.55033, 113, 76.95, 33.47, 0.03544, 5, 104, -88.99, -114.69, 0, 105, 69.57, -85.46, 0.13978, 106, 39.07, -87.34, 0.25527, 114, 30.38, 41.93, 0.55505, 113, 66.44, 34.63, 0.0499, 6, 104, -101, -114.97, 0, 98, 174.48, -115.43, 8e-05, 105, 63.06, -95.55, 0.12206, 106, 32.1, -97.12, 0.20747, 114, 24.45, 31.49, 0.59731, 113, 58.45, 25.67, 0.07308, 7, 104, -111.76, -110.46, 0, 98, 165.04, -122.29, 0.00032, 105, 53.28, -101.92, 0.07879, 106, 22.04, -103.04, 0.12145, 114, 15.04, 24.58, 0.71741, 113, 47.81, 20.88, 0.08203, 91, 237.14, 107.93, 1e-05, 5, 104, -108.08, -122.5, 0, 105, 65.31, -105.64, 0.02993, 106, 33.89, -107.31, 0.04847, 114, 27.27, 21.54, 0.91526, 113, 59.13, 15.35, 0.00633, 5, 104, -119.16, -133.48, 0, 105, 68.18, -120.97, 0.00452, 106, 36.05, -122.75, 0.00795, 114, 30.99, 6.4, 0.96903, 95, 54.03, 30.57, 0.01849, 2, 114, 37.46, -12.45, 0.42498, 95, 57.25, 10.9, 0.57502, 4, 104, -148.52, -159.76, 0, 114, 38.43, -32.3, 0.00741, 95, 54.89, -8.83, 0.69689, 112, 28.49, 22.34, 0.29569, 3, 104, -161.75, -169.27, 0, 95, 53.51, -25.06, 0.18748, 112, 33.13, 6.72, 0.81252, 2, 112, 28.44, -4.44, 0.99881, 110, 18.34, 26.75, 0.00119, 3, 112, 12.95, -11.54, 0.56066, 110, 6.81, 14.2, 0.42905, 109, 41.11, 3.27, 0.01029, 2, 112, 24.88, -18.34, 0.07456, 110, 20.44, 12.55, 0.92544, 2, 110, 31.43, 3.15, 1, 109, 47.38, -22.98, 0, 3, 104, -212.95, -186.31, 0, 110, 38.55, -11.18, 1, 109, 40.37, -37.37, 0, 2, 104, -208.89, -172.71, 0, 110, 24.72, -7.98, 1, 3, 104, -208.68, -156.22, 0, 110, 8.25, -8.8, 0.77698, 109, 23.76, -11.91, 0.22302, 4, 104, -215.45, -144.9, 0, 110, -2.62, -16.26, 0.07562, 109, 11.22, -7.85, 0.89019, 108, 43.6, -7.85, 0.03419, 4, 104, -226.11, -141.95, 0, 110, -4.9, -27.08, 0.00537, 109, 1.25, -12.64, 0.43747, 108, 33.64, -12.64, 0.55716, 3, 104, -232.63, -138.2, 0, 109, -6.13, -14.12, 0.1172, 108, 26.26, -14.12, 0.8828, 2, 116, 11.81, 14.79, 0.75, 117, -16, 3.05, 0.25, 2, 116, 22.78, 12.11, 0.5, 117, -7.44, 10.42, 0.5, 2, 117, 6.85, 8.32, 0.71, 118, -12.28, -1.94, 0.29, 1, 118, 14.18, 4.17, 1, 1, 118, 17.83, -1.81, 1, 2, 117, 14.45, -11.67, 0.6, 118, 8.04, -8.61, 0.4, 5, 93, 71.17, -60.75, 0.00079, 92, 140.16, -47.68, 0.00062, 108, 5.37, -23.47, 0.18635, 116, 6.53, -8.1, 0.55224, 117, -0.44, -14.55, 0.26, 3, 93, 61.12, -65.26, 0.05421, 92, 131.04, -53.84, 0.02392, 108, -5.33, -26.06, 0.92186, 3, 93, 45.12, -72, 0.16199, 92, 116.42, -63.22, 0.07649, 108, -22.3, -29.75, 0.76152, 3, 93, 26.91, -75.03, 0.24814, 92, 99, -69.31, 0.16466, 108, -40.75, -29.38, 0.5872, 3, 93, -1.28, -72.79, 0.26812, 92, 70.84, -71.93, 0.39026, 108, -68.06, -22, 0.34162, 4, 93, -26.46, -66.6, 0.17391, 92, 44.97, -70.15, 0.64194, 91, 111.06, -65.2, 0.0003, 108, -91.67, -11.3, 0.18386, 4, 93, -44.86, -56.53, 0.07696, 92, 25.12, -63.37, 0.80314, 91, 90.58, -60.67, 0.02923, 108, -107.91, 1.99, 0.09067, 4, 93, -66.08, -50.96, 0.01436, 92, 3.27, -61.51, 0.76749, 91, 68.65, -61.25, 0.18721, 108, -127.74, 11.36, 0.03095, 5, 104, -342.93, 4.9, 0, 93, -90.69, -45.79, 4e-05, 92, -21.87, -60.64, 0.48649, 91, 43.58, -63.17, 0.5078, 108, -150.98, 20.95, 0.00568, 4, 104, -348.69, 28.58, 0, 92, -45.43, -54.45, 0.22572, 91, 19.47, -59.65, 0.77401, 108, -170.91, 34.97, 0.00028, 3, 104, -348.03, 50.07, 0, 92, -64.03, -43.66, 0.08012, 91, -0.2, -50.98, 0.91988, 4, 104, -343.79, 76.76, 0, 96, -79.41, -179.09, 0.00252, 92, -85.5, -27.24, 0.00713, 91, -23.37, -37.06, 0.99035, 3, 96, -91.34, -154.09, 0.03451, 92, -106.37, -9.03, 0, 91, -46.13, -21.28, 0.96549, 2, 96, -90.09, -129.73, 0.10984, 91, -57.13, 0.49, 0.89016, 2, 96, -88.28, -94.72, 0.29957, 91, -72.94, 31.78, 0.70043, 2, 96, -86.94, -68.57, 0.44701, 91, -84.76, 55.15, 0.55299, 2, 101, 24.45, -7.37, 0.73831, 107, 18.73, 18.15, 0.26169, 3, 101, 9.26, -8.17, 0.6921, 106, 25.5, 14.97, 0.09636, 107, 3.97, 14.46, 0.21154, 4, 104, -8.49, -55.19, 0, 103, 46.68, -47.78, 0, 101, 21.38, -15.73, 0.34151, 107, 17.31, 9.36, 0.65848, 8, 98, -47.62, -116.74, 0.05271, 97, -17.82, -109.77, 0.05623, 96, 21.5, -102.76, 0.2508, 105, -158.82, -85.54, 0.02809, 113, -159.23, 69.79, 0.03484, 93, -67.42, 95.81, 0.02081, 92, -23.17, 82.86, 0.00636, 91, 26.35, 79.29, 0.55015, 10, 104, -224.27, 39.81, 0, 98, -21.78, -140.57, 0.05775, 97, 4.8, -136.68, 0.03933, 96, 37.34, -134.14, 0.09802, 105, -134.23, -110.65, 0.03798, 113, -138.8, 41.18, 0.05485, 94, -78.48, 87.77, 0.0012, 93, -44.67, 69.01, 0.04974, 92, 3.83, 60.35, 0.06804, 91, 55.68, 59.92, 0.5931, 10, 104, -222.42, 6.33, 0, 98, 7.8, -156.35, 0.05086, 97, 32.15, -156.08, 0.02265, 96, 59.51, -159.29, 0.03253, 105, -105.49, -127.92, 0.0438, 113, -113.07, 19.68, 0.08239, 94, -55.79, 63.1, 0.01085, 93, -17.23, 49.74, 0.1493, 92, 34.16, 46.06, 0.27411, 91, 87.41, 49.09, 0.33352, 11, 104, -214.06, -20.54, 0, 98, 35.11, -163.14, 0.04009, 97, 58.37, -166.27, 0.01222, 96, 82.7, -175.22, 0.01095, 105, -78.57, -136.09, 0.04554, 106, -111.23, -131.13, 6e-05, 113, -87.73, 7.46, 0.12322, 94, -32.26, 47.66, 0.0511, 93, 9.04, 39.68, 0.39806, 92, 61.77, 40.64, 0.19633, 91, 115.45, 46.77, 0.12242, 11, 104, -199.71, -50.48, 0, 98, 68.15, -166.39, 0.02258, 97, 90.74, -173.68, 0.00436, 96, 112.51, -189.86, 0.00198, 105, -45.73, -141.02, 0.03629, 106, -78.65, -137.56, 0.00059, 113, -56.05, -2.48, 0.20189, 94, -2.15, 33.66, 0.26994, 93, 41.45, 32.43, 0.41583, 92, 94.94, 39.04, 0.01688, 91, 148.59, 48.86, 0.02967, 11, 104, -179.66, -67.3, 0, 98, 92.93, -157.97, 0.0158, 97, 116.38, -168.46, 0.00211, 96, 138.66, -190.66, 0.00045, 105, -20.56, -133.88, 0.0354, 106, -53.18, -131.58, 0.00285, 113, -30.07, 0.7, 0.43236, 94, 24.01, 33.42, 0.37795, 93, 67.06, 37.77, 0.1193, 92, 119.26, 48.69, 0.00151, 91, 171.69, 61.15, 0.01226, 12, 104, -149.52, -79.19, 0, 98, 118.73, -138.36, 0.01032, 97, 144.46, -152.28, 0.00067, 96, 169.7, -181.35, 1e-05, 105, 6.21, -115.61, 0.05878, 106, -25.61, -114.56, 0.02757, 114, -31.18, 8.27, 0.02155, 113, -0.81, 14.62, 0.85158, 94, 54.85, 43.39, 0.00819, 93, 95.06, 54.09, 0.01712, 92, 144.05, 69.55, 2e-05, 91, 194.01, 84.64, 0.00418, 9, 104, -110.55, -78.52, 0, 98, 138.36, -104.7, 0.00919, 97, 168.2, -121.37, 0.00012, 105, 27.54, -82.99, 0.18946, 106, -2.81, -82.95, 0.18696, 114, -11.72, 42.03, 0.29925, 113, 25.29, 43.56, 0.3082, 93, 118.65, 85.11, 0.0048, 91, 207.99, 121.02, 0.00202, 9, 104, -89.68, -67.09, 0, 98, 139.41, -80.92, 0.00845, 97, 172.24, -97.93, 4e-05, 105, 29.79, -59.3, 0.27802, 106, 0.53, -59.39, 0.31097, 114, -10.8, 65.81, 0.22952, 113, 31.18, 66.61, 0.16777, 93, 122.58, 108.57, 0.00362, 91, 205.21, 144.65, 0.00162, 9, 104, -60.72, -49.71, 0, 98, 139.57, -47.14, 0.00196, 105, 31.67, -25.58, 0.3363, 106, 3.96, -25.78, 0.54383, 107, -25.6, -20.91, 0.0015, 114, -10.82, 99.59, 0.06434, 113, 38.24, 99.64, 0.0508, 93, 126.86, 142.08, 0.00089, 91, 199.95, 178.02, 0.00038, 5, 101, -20.78, -10.6, 0.00019, 100, 19.96, -20.28, 0.01691, 99, 61.13, -23.5, 2e-05, 105, 25.3, 0.92, 0.5922, 106, -1.19, 0.98, 0.39068, 5, 104, -33.7, 2.69, 0, 103, -9.79, -19.56, 0.00674, 102, 23.33, -21.79, 0.02549, 100, -6.21, 0.45, 0.01995, 99, 39.05, 1.55, 0.94783, 3, 102, 0.75, -7.62, 0.68721, 99, 16.92, 16.41, 0.31246, 98, 86.06, 17.08, 0.00033, 4, 102, -23.33, -5.31, 0.36588, 99, -7.08, 19.47, 0.07645, 98, 61.96, 19.17, 0.55449, 97, 108.09, 11.17, 0.00318, 3, 102, -49.33, -4.16, 0.16228, 98, 35.95, 20.07, 0.81481, 97, 82.41, 15.36, 0.02291, 3, 102, -76.7, -9.7, 0.04334, 98, 8.63, 14.28, 0.79326, 97, 54.57, 13.07, 0.16339, 3, 102, -107.68, -14.63, 0.01059, 98, -22.3, 9.05, 0.01853, 97, 23.23, 11.8, 0.97089, 4, 104, -117.88, 129.94, 0, 102, -129.17, -26.35, 0.00046, 97, 0.51, 2.69, 0.57264, 96, 65.12, 2.5, 0.4269, 7, 98, -57.15, -27.14, 0.00698, 97, -15.92, -19.69, 0.0779, 96, 44, -15.52, 0.879, 105, -163.76, 4.43, 0.00337, 113, -150.23, 159.44, 0.00347, 93, -65.95, 185.9, 0.00163, 91, 2.57, 166.2, 0.02765, 8, 98, -56.21, -57.15, 0.03861, 97, -18.79, -49.57, 0.11996, 96, 34.35, -43.94, 0.63976, 105, -164.36, -25.58, 0.01569, 113, -155.45, 129.88, 0.01642, 93, -68.68, 156.01, 0.00801, 92, -34.71, 141.95, 0.00017, 91, 8.31, 136.74, 0.16139, 8, 98, -56.24, -82.31, 0.05072, 97, -22.01, -74.53, 0.08857, 96, 25.5, -67.5, 0.47829, 105, -165.68, -50.71, 0.02294, 113, -160.63, 105.25, 0.02555, 93, -71.78, 131.03, 0.01314, 92, -33.49, 116.82, 0.001, 91, 12.31, 111.89, 0.31979, 13, 104, -106.4, 38.81, 0, 99, -31.24, -39.06, 0.03078, 98, 40.19, -40.3, 0.59465, 97, 78.97, -45.06, 0.04344, 96, 130.54, -61.97, 0.0099, 105, -67.23, -13.67, 0.18767, 106, -94.3, -9.36, 0.00042, 114, -110.24, 105.91, 1e-05, 113, -57.64, 126.66, 0.07025, 94, 13.16, 161.9, 8e-05, 93, 29.05, 160.98, 0.02248, 92, 60.73, 163.58, 0.00231, 91, 100.76, 168.83, 0.038], "hull": 77, "edges": [116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 152, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 146, 148, 148, 150, 150, 152, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 154, 154, 156, 156, 158, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 114, 116, 112, 114, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 160, 48, 50], "width": 384, "height": 369}}, "xiangliang": {"xiangliang": {"type": "mesh", "uvs": [1, 0.08177, 0.89138, 0.26151, 0.7762, 0.36724, 0.63799, 0.48988, 0.4681, 0.60195, 0.5041, 0.72249, 0.51443, 0.80362, 0.32871, 1, 0.25528, 1, 0.17322, 0.92415, 0.15163, 0.79093, 0.20489, 0.64291, 0.25241, 0.56256, 0.14443, 0.44414, 0.05516, 0.35533, 0, 0.20308, 0, 0.07782, 0.02941, 0.07782, 0.12443, 0.27236, 0.20793, 0.39712, 0.32599, 0.51342, 0.41669, 0.52188, 0.58082, 0.41615, 0.72293, 0.27843, 0.82947, 0.15578, 0.94897, 0, 1, 0, 0.41941, 0.90089], "triangles": [15, 16, 17, 25, 26, 0, 4, 11, 12, 27, 5, 6, 27, 9, 10, 4, 12, 20, 11, 4, 5, 27, 11, 5, 27, 10, 11, 7, 8, 9, 27, 7, 9, 7, 27, 6, 3, 22, 2, 4, 22, 3, 22, 23, 2, 14, 18, 19, 4, 21, 22, 21, 4, 20, 13, 14, 19, 13, 19, 12, 12, 19, 20, 2, 23, 1, 14, 15, 18, 15, 17, 18, 24, 25, 0, 1, 24, 0, 23, 24, 1], "vertices": [2, 150, -15.05, 80.81, 0.5, 22, 60.53, -73.89, 0.5, 2, 150, 4.98, 69.35, 0.77, 151, -14.13, 71.06, 0.23, 2, 150, 18.23, 55.53, 0.6, 151, -2.27, 56.01, 0.4, 2, 150, 33.75, 38.86, 0.14, 151, 11.54, 37.92, 0.86, 2, 151, 24.84, 15.27, 0.51122, 152, 1.37, 15.69, 0.48878, 2, 151, 35.79, 21.57, 0.06076, 152, 11.17, 23.67, 0.93924, 2, 151, 43.37, 23.86, 0.01689, 152, 18.28, 27.15, 0.98311, 2, 151, 64.95, -0.12, 0, 152, 43.44, 6.94, 1, 2, 151, 66.08, -10.42, 0, 152, 46.2, -3.04, 1, 1, 152, 42.26, -16.13, 1, 1, 152, 30.75, -22.48, 1, 2, 151, 32.77, -21.2, 0.00266, 152, 15.05, -19.03, 0.99734, 3, 150, 51.69, -12.93, 0.00629, 151, 24.37, -15.37, 0.49, 152, 5.83, -14.63, 0.50371, 1, 151, 14.72, -31.74, 1, 2, 150, 37.91, -44.22, 0.51, 151, 7.61, -45.18, 0.49, 3, 150, 25.19, -54.83, 0.48, 151, -6.07, -54.5, 0.02, 22, 15.74, 60.32, 0.5, 2, 150, 13.42, -57.29, 0.5, 22, 27.42, 63.17, 0.5, 2, 150, 12.58, -53.23, 0.5, 22, 28.4, 59.14, 0.5, 3, 150, 28.12, -36.29, 0.468, 151, -1.36, -36.33, 0.432, 22, 13.44, 41.69, 0.1, 1, 151, 9.27, -23.33, 1, 3, 150, 44.96, -3.74, 0.03112, 151, 18.56, -5.57, 0.28601, 152, -1.48, -5.89, 0.68287, 3, 150, 43.13, 8.94, 0.02348, 151, 17.98, 7.23, 0.70561, 152, -4.11, 6.66, 0.2709, 2, 150, 28.47, 29.52, 0.1, 151, 5.38, 29.14, 0.9, 2, 150, 11.42, 46.43, 0.6, 151, -9.94, 47.62, 0.4, 2, 150, -3.17, 58.73, 0.8, 22, 47.92, -52.22, 0.2, 2, 150, -21.26, 72.16, 0.46, 22, 66.45, -65.03, 0.54, 2, 150, -22.73, 79.2, 0.46, 22, 68.16, -72.02, 0.54, 1, 152, 30.86, 16.73, 1], "hull": 27, "edges": [32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 48, 50, 0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 54, 54, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 28, 30, 12, 14], "width": 141, "height": 96}}, "yanqiu": {"yanqiu": {"type": "mesh", "uvs": [0.96607, 0.3391, 1, 0.38196, 1, 0.69481, 0.98372, 0.83624, 0.95813, 0.92195, 0.91842, 1, 0.87078, 1, 0.8196, 1, 0.24431, 0.80739, 0.12254, 0.75596, 0.04137, 0.60168, 0, 0.43561, 0, 0.25307, 0, 0.1224, 0.03542, 0.05573, 0.08702, 0, 0.13666, 0, 0.20019, 0, 0.25843, 0, 0.31843, 0, 0.35372, 0.76454, 0.38901, 0.64025, 0.40313, 0.45596, 0.39254, 0.28453, 0.37137, 0.14739, 0.18914, 0.1784, 0.13863, 0.2184, 0.11338, 0.3304, 0.14083, 0.4584, 0.19573, 0.52507, 0.25063, 0.55973, 0.30334, 0.55973, 0.32859, 0.46107, 0.31102, 0.34373, 0.27808, 0.23173, 0.23745, 0.17573, 0.6899, 0.71195, 0.71372, 0.57695, 0.75254, 0.47409, 0.80196, 0.38838, 0.85754, 0.33267, 0.91578, 0.31338, 0.75607, 0.96052, 0.70048, 0.92195, 0.66254, 0.84267, 0.65813, 0.77624, 0.89107, 0.51695, 0.85843, 0.55767, 0.8099, 0.64552, 0.79048, 0.70766, 0.80284, 0.78909, 0.84343, 0.84052, 0.8946, 0.85766, 0.92813, 0.77838, 0.95107, 0.67767, 0.95637, 0.58981, 0.93254, 0.51052], "triangles": [43, 20, 44, 45, 36, 50, 43, 44, 50, 48, 37, 47, 37, 38, 47, 46, 38, 39, 41, 40, 24, 21, 20, 30, 21, 31, 22, 37, 22, 38, 32, 28, 33, 38, 22, 23, 8, 42, 7, 31, 30, 32, 32, 30, 29, 32, 33, 22, 33, 34, 23, 50, 36, 49, 49, 36, 48, 48, 36, 37, 8, 43, 42, 39, 38, 23, 40, 39, 24, 34, 24, 23, 23, 24, 39, 34, 35, 24, 24, 35, 19, 20, 8, 30, 8, 9, 30, 22, 31, 32, 33, 23, 22, 35, 18, 19, 41, 24, 19, 43, 8, 20, 21, 30, 31, 9, 29, 30, 34, 33, 27, 28, 32, 29, 25, 35, 34, 35, 17, 18, 25, 34, 26, 27, 33, 28, 25, 17, 35, 42, 43, 51, 43, 50, 51, 7, 42, 51, 50, 44, 45, 36, 21, 37, 4, 52, 3, 3, 53, 2, 5, 6, 4, 20, 45, 44, 6, 7, 52, 7, 51, 52, 52, 53, 3, 52, 51, 53, 51, 50, 53, 50, 49, 53, 53, 49, 54, 53, 54, 2, 54, 49, 48, 20, 21, 45, 45, 21, 36, 9, 10, 29, 54, 55, 2, 55, 1, 2, 48, 47, 54, 54, 47, 55, 55, 47, 46, 55, 46, 56, 21, 22, 37, 10, 28, 29, 10, 11, 28, 55, 56, 1, 40, 56, 46, 56, 40, 0, 56, 0, 1, 0, 40, 41, 11, 27, 28, 26, 34, 27, 11, 12, 27, 27, 12, 26, 19, 0, 41, 26, 13, 14, 26, 14, 25, 16, 25, 14, 26, 12, 13, 16, 14, 15, 25, 16, 17, 47, 38, 46, 46, 39, 40, 4, 6, 52], "vertices": [2, 24, 136.15, -66.64, 0.69, 25, 82.71, -11.77, 0.31, 2, 24, 137.12, -72.41, 0.71, 25, 83.68, -17.54, 0.29, 2, 24, 121.39, -84.29, 0.76, 25, 67.94, -29.41, 0.24, 2, 24, 112.78, -87.67, 0.78, 25, 59.33, -32.79, 0.22, 2, 24, 106.11, -87.79, 0.76, 25, 52.66, -32.92, 0.24, 2, 24, 98.52, -85.91, 0.82683, 25, 45.08, -31.03, 0.17317, 2, 24, 94.13, -80.09, 0.66601, 25, 40.69, -25.21, 0.33399, 2, 24, 89.42, -73.84, 0.5192, 25, 35.97, -18.96, 0.4808, 2, 24, 46.08, 3.73, 0.33557, 25, -7.36, 58.61, 0.66443, 2, 24, 37.45, 20.55, 0.44276, 25, -16, 75.43, 0.55724, 2, 24, 37.72, 36.32, 0.67369, 25, -15.72, 91.2, 0.32631, 2, 24, 42.26, 47.68, 0.76386, 25, -11.18, 102.55, 0.23614, 2, 24, 51.44, 54.6, 0.79379, 25, -2, 109.48, 0.20621, 2, 24, 58.01, 59.56, 0.80745, 25, 4.57, 114.44, 0.19255, 2, 24, 64.63, 57.77, 0.80136, 25, 11.18, 112.64, 0.19864, 2, 24, 72.19, 53.58, 0.76569, 25, 18.74, 108.46, 0.23431, 2, 24, 76.76, 47.52, 0.69699, 25, 23.32, 102.39, 0.30301, 2, 24, 82.62, 39.76, 0.54808, 25, 29.17, 94.63, 0.45192, 2, 24, 87.98, 32.65, 0.4168, 25, 34.54, 87.52, 0.5832, 2, 24, 93.51, 25.32, 0.33075, 25, 40.07, 80.19, 0.66925, 2, 24, 58.32, -8, 0.54411, 25, 4.88, 46.87, 0.45589, 2, 24, 67.82, -7.6, 0.57996, 25, 14.38, 47.28, 0.42004, 2, 24, 78.39, -2.33, 0.5239, 25, 24.95, 52.55, 0.4761, 2, 24, 86.04, 5.47, 0.40958, 25, 32.59, 60.35, 0.59042, 2, 24, 90.98, 13.26, 0.33252, 25, 37.54, 68.14, 0.66748, 2, 24, 72.63, 34.34, 0.49709, 25, 19.18, 89.21, 0.50291, 2, 24, 65.96, 38.99, 0.60875, 25, 12.52, 93.86, 0.39125, 2, 24, 58, 37.82, 0.63318, 25, 4.56, 92.7, 0.36682, 2, 24, 54.09, 29.61, 0.52179, 25, 0.65, 84.49, 0.47821, 2, 24, 55.8, 20.38, 0.39741, 25, 2.36, 75.25, 0.60259, 2, 24, 59.12, 12.36, 0.33772, 25, 5.67, 67.23, 0.66228, 2, 24, 63.98, 5.92, 0.35498, 25, 10.53, 60.8, 0.64502, 2, 24, 71.26, 6.58, 0.35533, 25, 17.82, 61.46, 0.64467, 2, 24, 75.55, 13.18, 0.31985, 25, 22.1, 68.05, 0.68015, 2, 24, 78.14, 21.45, 0.33399, 25, 24.7, 76.33, 0.66601, 2, 24, 77.21, 28.54, 0.39449, 25, 23.77, 83.41, 0.60551, 2, 24, 91.95, -47.07, 0.4608, 25, 38.5, 7.81, 0.5392, 2, 24, 100.93, -44.85, 0.44052, 25, 47.49, 10.02, 0.55948, 2, 24, 109.68, -45.69, 0.4079, 25, 56.24, 9.19, 0.5921, 2, 24, 118.55, -48.47, 0.41727, 25, 65.1, 6.4, 0.58273, 2, 24, 126.47, -53.15, 0.48558, 25, 73.03, 1.73, 0.51442, 2, 24, 132.81, -59.53, 0.63219, 25, 79.36, -4.65, 0.36781, 2, 24, 85.55, -64.58, 0.4158, 25, 32.1, -9.7, 0.5842, 2, 24, 82.36, -56.33, 0.42545, 25, 28.92, -1.45, 0.57455, 2, 24, 82.85, -48.69, 0.50399, 25, 29.41, 6.19, 0.49601, 2, 24, 85.79, -45.63, 0.52808, 25, 32.34, 9.25, 0.47192, 2, 24, 120.29, -64.23, 0.61283, 25, 66.85, -9.36, 0.38717, 2, 24, 115.24, -61.79, 0.53109, 25, 61.79, -6.92, 0.46891, 2, 24, 106.35, -59.2, 0.44213, 25, 52.9, -4.32, 0.55787, 2, 24, 101.43, -59.19, 0.42286, 25, 47.99, -4.31, 0.57714, 2, 24, 98.48, -63.79, 0.45445, 25, 45.03, -8.91, 0.54555, 2, 24, 99.63, -70.69, 0.5562, 25, 46.19, -15.82, 0.4438, 2, 24, 103.49, -77.6, 0.71913, 25, 50.04, -22.72, 0.28087, 2, 24, 110.56, -78.68, 0.75, 25, 57.12, -23.81, 0.25, 2, 24, 117.74, -77.66, 0.74, 25, 64.3, -22.79, 0.26, 2, 24, 122.65, -74.97, 0.75, 25, 69.2, -20.1, 0.25, 2, 24, 124.44, -69.06, 0.67, 25, 70.99, -14.18, 0.33], "hull": 20, "edges": [22, 20, 20, 18, 18, 16, 16, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 38, 36, 38, 34, 36, 32, 34, 30, 32, 30, 28, 28, 26, 22, 24, 24, 26, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 50, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 84, 84, 86, 86, 88, 88, 90, 90, 72, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 92, 14, 16, 38, 0], "width": 153, "height": 63}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [1, 0.15912, 1, 0.29506, 0.86734, 0.38569, 0.86295, 0.55787, 0.88931, 0.80256, 0.84537, 1, 0.48507, 1, 0.27855, 1, 0.18628, 1, 0.08522, 0.90678, 0, 0.73006, 0, 0.51709, 0, 0.36303, 0, 0.20444, 0, 0, 0.49386, 0, 1, 0, 0.3884, 0.19537, 0.3884, 0.38116, 0.37961, 0.58053, 0.43234, 0.80709], "triangles": [15, 16, 0, 17, 14, 15, 13, 14, 17, 2, 15, 0, 2, 17, 15, 12, 13, 17, 2, 18, 17, 12, 17, 18, 2, 0, 1, 11, 12, 18, 3, 18, 2, 19, 11, 18, 19, 18, 3, 10, 11, 19, 20, 19, 3, 20, 3, 4, 9, 10, 19, 20, 9, 19, 7, 8, 9, 20, 7, 9, 6, 20, 4, 7, 20, 6, 5, 6, 4], "vertices": [1, 1, 32.72, 16.29, 1, 1, 1, 32.72, 7.59, 1, 1, 1, 23.97, 1.79, 1, 1, 1, 23.68, -9.23, 1, 1, 1, 25.42, -24.89, 1, 1, 1, 22.52, -37.53, 1, 1, 1, -1.26, -37.53, 1, 1, 1, -14.89, -37.53, 1, 1, 1, -20.98, -37.53, 1, 1, 1, -27.65, -31.56, 1, 1, 1, -33.28, -20.25, 1, 1, 1, -33.28, -6.62, 1, 1, 1, -33.28, 3.24, 1, 1, 1, -33.28, 13.39, 1, 1, 1, -33.28, 26.47, 1, 1, 1, -0.68, 26.47, 1, 1, 1, 32.72, 26.47, 1, 1, 1, -7.64, 13.97, 1, 1, 1, -7.64, 2.08, 1, 1, 1, -8.22, -10.68, 1, 1, 1, -4.74, -25.18, 1], "hull": 17, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 2, 0, 0, 32, 28, 30, 30, 32, 30, 34, 34, 36, 36, 38, 38, 40, 40, 12], "width": 66, "height": 64}}, "yaodai2": {"yaodai2": {"type": "mesh", "uvs": [0.70623, 0.01873, 0.73371, 0.04646, 0.76162, 0.07705, 0.7856, 0.12294, 0.81308, 0.15258, 0.84317, 0.19369, 0.87108, 0.2434, 0.89745, 0.31203, 0.92274, 0.38947, 0.95719, 0.53574, 0.98568, 0.68574, 1, 0.78325, 1, 0.85496, 1, 0.93049, 1, 1, 0.80923, 1, 0.79527, 0.94025, 0.78873, 0.85707, 0.78088, 0.78346, 0.7739, 0.69359, 0.76823, 0.61615, 0.76111, 0.53932, 0.7441, 0.49247, 0.72448, 0.44754, 0.70332, 0.39122, 0.67846, 0.33194, 0.65535, 0.26884, 0.63878, 0.20766, 0.62919, 0.14743, 0.58553, 0.15708, 0.55313, 0.16206, 0.53735, 0.23182, 0.50189, 0.29713, 0.44798, 0.33756, 0.40968, 0.47906, 0.35932, 0.55836, 0.29643, 0.64845, 0.22195, 0.71376, 0.15741, 0.76041, 0.09216, 0.78062, 0.04463, 0.70443, 0, 0.64379, 0, 0.59526, 0.02603, 0.53889, 0.05987, 0.47164, 0.09415, 0.41131, 0.16682, 0.29776, 0.21058, 0.23051, 0.24758, 0.18007, 0.2937, 0.12547, 0.3907, 0.02954, 0.42904, 0, 0.4728, 0, 0.50892, 0, 0.61174, 0, 0.64213, 0, 0.65957, 0, 0.67789, 0, 0.54817, 0.10173, 0.53644, 0.04141, 0.34197, 0.07899, 0.1307, 0.3579, 0.60651, 0.05087, 0.60651, 0.09389, 0.60956, 0.13309, 0.96998, 0.60734, 0.94018, 0.46595], "triangles": [61, 45, 46, 45, 61, 35, 40, 41, 42, 35, 44, 45, 36, 44, 35, 43, 44, 36, 43, 40, 42, 43, 37, 40, 36, 37, 43, 38, 40, 37, 39, 40, 38, 47, 48, 33, 33, 46, 47, 61, 46, 34, 50, 60, 49, 32, 49, 60, 48, 49, 33, 34, 46, 33, 35, 61, 34, 59, 53, 54, 58, 59, 62, 30, 58, 29, 59, 58, 52, 31, 58, 30, 51, 52, 58, 59, 52, 53, 31, 32, 58, 51, 32, 50, 58, 32, 51, 32, 60, 50, 33, 49, 32, 10, 65, 9, 19, 20, 65, 19, 65, 10, 18, 19, 10, 18, 10, 11, 17, 18, 11, 12, 17, 11, 16, 17, 12, 13, 16, 12, 15, 16, 13, 15, 13, 14, 8, 23, 7, 8, 9, 66, 66, 22, 8, 21, 22, 66, 21, 66, 9, 20, 21, 9, 20, 9, 65, 26, 4, 5, 25, 5, 6, 25, 6, 7, 24, 25, 7, 23, 24, 7, 22, 23, 8, 62, 59, 54, 55, 63, 62, 58, 62, 63, 55, 64, 63, 58, 63, 64, 62, 54, 55, 0, 28, 64, 29, 58, 64, 29, 64, 28, 28, 1, 27, 56, 57, 0, 64, 55, 56, 64, 56, 0, 4, 26, 27, 0, 1, 28, 2, 27, 1, 3, 27, 2, 4, 27, 3, 25, 26, 5], "vertices": [1, 8, 18.05, 25.04, 1, 2, 8, 30.1, 25.67, 0.97267, 9, -25.53, 18.61, 0.02733, 2, 8, 42.55, 25.91, 0.80997, 9, -13.78, 22.74, 0.19003, 2, 8, 54.91, 22.97, 0.415, 9, -1.12, 23.81, 0.585, 3, 8, 67.12, 23.29, 0.10382, 9, 10.38, 27.94, 0.89327, 10, -41.46, 18.82, 0.0029, 3, 8, 81.23, 22.24, 0.0072, 9, 24.1, 31.36, 0.93427, 10, -28.91, 25.34, 0.05853, 2, 9, 38.35, 33.07, 0.74927, 10, -15.45, 30.31, 0.25073, 2, 9, 54.64, 31.95, 0.36923, 10, 0.66, 33, 0.63077, 3, 9, 71.79, 29.4, 0.07655, 10, 17.93, 34.51, 0.90949, 11, -36.81, 26.57, 0.01396, 2, 10, 47.84, 32.77, 0.57894, 11, -7.34, 31.94, 0.42106, 2, 10, 77.12, 28.64, 0.04361, 11, 22.09, 34.84, 0.95639, 1, 11, 40.73, 35.13, 1, 1, 11, 53.22, 31.32, 1, 1, 11, 66.36, 27.3, 1, 1, 11, 78.46, 23.61, 1, 2, 10, 90.47, -61.08, 0.02016, 11, 56.24, -49.19, 0.97984, 2, 10, 78.27, -60.33, 0.04216, 11, 44.21, -51.34, 0.95784, 2, 10, 63.92, -54.86, 0.12493, 11, 28.97, -49.42, 0.87507, 3, 9, 83.95, -61.14, 0.00301, 10, 50.79, -50.73, 0.27218, 11, 15.24, -48.5, 0.72481, 3, 9, 70.26, -51.77, 0.03299, 10, 35.3, -44.79, 0.51097, 11, -1.21, -46.39, 0.45603, 3, 9, 58.56, -43.59, 0.13331, 10, 22.02, -39.56, 0.64884, 11, -15.35, -44.44, 0.21785, 4, 8, 81.48, -48.67, 0.00272, 9, 46.54, -35.91, 0.3962, 10, 8.54, -34.88, 0.54253, 11, -29.56, -43.07, 0.05855, 4, 8, 71.51, -44.28, 0.02652, 9, 35.69, -34.86, 0.67528, 10, -2.25, -36.37, 0.28647, 11, -39.69, -47.08, 0.01173, 4, 8, 60.77, -40.67, 0.09994, 9, 24.37, -34.8, 0.79597, 10, -13.28, -38.95, 0.10333, 11, -49.8, -52.18, 0.00076, 3, 8, 48.53, -35.52, 0.30479, 9, 11.13, -33.73, 0.67895, 10, -26.4, -40.99, 0.01626, 2, 8, 34.74, -30.58, 0.65697, 9, -3.52, -33.36, 0.34303, 2, 8, 21.24, -24.7, 0.91087, 9, -18.18, -32, 0.08913, 2, 8, 10.2, -17.91, 0.98889, 9, -30.78, -29.01, 0.01111, 2, 8, 1.72, -9.99, 0.97239, 12, -44.12, 31.62, 0.02761, 3, 8, -12.89, -19.64, 0.62483, 9, -52.17, -37.87, 0, 12, -27.3, 26.74, 0.37517, 3, 8, -23.91, -26.45, 0.26327, 9, -60.51, -47.79, 0, 12, -14.97, 22.76, 0.73673, 4, 8, -23.58, -40.62, 0.06914, 9, -55.77, -61.15, 0, 12, -4.39, 32.19, 0.92896, 13, -46.89, 42.83, 0.0019, 4, 8, -30.59, -57.72, 0.00811, 9, -57.07, -79.58, 0, 12, 13.18, 37.93, 0.95072, 13, -28.52, 44.81, 0.04117, 4, 9, -66.74, -100.15, 0, 12, 35.88, 36.73, 0.66838, 13, -6.56, 38.94, 0.32052, 14, -55.75, 36.05, 0.0111, 3, 12, 59.66, 54.92, 0.1715, 13, 20.48, 51.8, 0.66564, 14, -29.43, 50.32, 0.16286, 4, 12, 83.69, 60.81, 0.03587, 13, 45.21, 52.59, 0.4991, 14, -4.78, 52.41, 0.45582, 15, -53.41, 48.74, 0.0092, 4, 12, 113.09, 66.66, 0.00023, 13, 75.18, 52.22, 0.14451, 14, 25.18, 53.62, 0.70233, 15, -23.61, 52.07, 0.15294, 3, 13, 106.48, 45.52, 0.00822, 14, 56.78, 48.59, 0.37651, 15, 8.27, 49.3, 0.61527, 2, 14, 83.25, 42.67, 0.06984, 15, 35.09, 45.28, 0.93016, 2, 14, 107.49, 32.48, 0.00105, 15, 59.99, 36.83, 0.99895, 1, 15, 70.67, 15.91, 1, 1, 15, 81.6, -1.97, 1, 2, 9, -157.33, -261.22, 0, 15, 77.62, -9.85, 1, 2, 9, -157.48, -246.62, 0, 15, 63.72, -14.33, 1, 2, 9, -156.89, -228.4, 0, 15, 46.16, -19.18, 1, 3, 9, -155.27, -210.94, 0, 14, 72.34, -24.82, 0.01374, 15, 29, -22.82, 0.98626, 3, 13, 82.55, -29.6, 0.00209, 14, 36.85, -27.69, 0.73963, 15, -6.2, -28.2, 0.25828, 3, 13, 61.23, -30.03, 0.14946, 14, 15.58, -29.25, 0.84107, 15, -27.3, -31.27, 0.00947, 2, 13, 43.86, -29.43, 0.5783, 14, -1.8, -29.56, 0.4217, 3, 12, 78.58, -22.05, 0.00856, 13, 23.04, -27.42, 0.95337, 14, -22.69, -28.66, 0.03806, 2, 12, 36.16, -23.81, 0.93639, 13, -18.82, -20.35, 0.06361, 3, 9, -116.14, -62.86, 0, 12, 19.96, -23.08, 0.99999, 13, -34.52, -16.28, 1e-05, 2, 12, 3.76, -16.57, 1, 13, -49.02, -6.55, 0, 4, 8, -53.24, -8.54, 0.05862, 9, -93.97, -39.96, 0, 12, -9.61, -11.19, 0.94138, 13, -60.99, 1.48, 0, 3, 8, -16.91, 10.53, 0.85505, 9, -65.44, -10.48, 0, 12, -47.67, 4.12, 0.14495, 3, 8, -6.18, 16.17, 0.98015, 9, -57.01, -1.76, 0, 12, -58.92, 8.65, 0.01985, 3, 8, -0.02, 19.4, 0.99811, 9, -52.17, 3.24, 0, 12, -65.38, 11.25, 0.00189, 1, 8, 6.45, 22.8, 1, 3, 8, -30.77, -17.65, 0.2942, 9, -69.78, -41.58, 0, 12, -17.23, 11.83, 0.7058, 3, 8, -40.01, -10.11, 0.2197, 9, -80.92, -37.31, 0, 12, -16.99, -0.1, 0.7803, 3, 12, 57.55, -22.71, 0.31784, 13, 2.33, -23.71, 0.68216, 14, -43.57, -26.04, 0, 2, 14, 54.84, -25.69, 0.22987, 15, 11.6, -24.93, 0.77013, 3, 8, -14.46, 1.37, 0.82515, 9, -60.24, -18.42, 0, 12, -42.28, 11.93, 0.17485, 3, 8, -10.82, -5.57, 0.82772, 9, -54.62, -23.86, 0, 12, -39.36, 19.2, 0.17228, 3, 8, -6.43, -11.32, 0.84412, 9, -48.64, -27.95, 0, 12, -37.83, 26.27, 0.15588, 2, 10, 61.65, 30.52, 0.24896, 11, 6.62, 33.02, 0.75104, 3, 9, 86.63, 24.72, 0.00551, 10, 33.45, 33.4, 0.85967, 11, -21.47, 29.16, 0.13482], "hull": 58, "edges": [82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 116, 116, 118, 118, 106, 104, 106, 102, 104, 102, 100, 100, 120, 120, 98, 98, 96, 96, 94, 94, 92, 92, 122, 122, 90, 90, 88, 88, 86, 82, 84, 86, 84, 106, 108, 108, 124, 124, 126, 126, 128, 128, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 28, 30, 32, 30, 28, 26, 26, 24, 24, 22, 22, 20, 20, 130, 130, 18, 18, 132, 132, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 114, 108, 110, 110, 112, 112, 114, 98, 100, 90, 92, 16, 18, 18, 20, 60, 58, 58, 56], "width": 399, "height": 182}}, "youshou": {"youshou": {"type": "mesh", "uvs": [0.62657, 0.03483, 0.64551, 0.09511, 0.66919, 0.17443, 0.71419, 0.21779, 0.74971, 0.37853, 0.82312, 0.45256, 0.88706, 0.54351, 0.86338, 0.59321, 0.92624, 0.64567, 1, 0.71864, 1, 0.78738, 1, 0.855, 0.7447, 0.96605, 0.58367, 1, 0.47947, 1, 0.42264, 1, 0.30424, 0.96076, 0.20478, 0.83808, 0.09111, 0.75877, 0.07095, 0.62879, 0.05322, 0.51448, 0.09374, 0.43881, 0, 0.33834, 0, 0.2474, 0, 0.16279, 0, 0.0581, 0.09374, 0.01897, 0.23109, 0, 0.53184, 0, 0.85837, 0.91528, 0.49518, 0.52798, 0.5443, 0.68928, 0.41138, 0.33571, 0.35937, 0.18989, 0.2958, 0.0933, 0.57898, 0.82727], "triangles": [34, 27, 28, 24, 25, 26, 33, 34, 28, 34, 23, 24, 32, 33, 2, 28, 1, 33, 0, 1, 28, 2, 33, 1, 21, 22, 23, 21, 23, 33, 26, 27, 34, 26, 34, 24, 33, 23, 34, 32, 4, 30, 3, 32, 2, 4, 32, 3, 7, 5, 6, 19, 20, 21, 21, 33, 32, 5, 31, 30, 5, 30, 4, 31, 5, 7, 30, 18, 19, 8, 9, 10, 35, 31, 7, 19, 21, 30, 30, 21, 32, 17, 30, 31, 16, 17, 31, 17, 18, 30, 29, 8, 10, 29, 35, 7, 29, 7, 8, 11, 29, 10, 35, 16, 31, 12, 35, 29, 12, 29, 11, 15, 16, 35, 14, 15, 35, 13, 14, 35, 12, 13, 35], "vertices": [1, 121, 321.65, -61.82, 1, 1, 121, 299.44, -61.32, 1, 1, 121, 270.25, -60.47, 1, 1, 121, 253.45, -65.16, 1, 1, 121, 194.61, -61.43, 1, 1, 121, 166.01, -68.89, 1, 1, 121, 131.57, -73.83, 1, 1, 121, 114.29, -67.1, 1, 1, 121, 93.74, -74.13, 1, 1, 121, 65.52, -81.7, 1, 1, 121, 40.76, -77.66, 1, 1, 121, 16.39, -73.69, 1, 1, 121, -16.91, -26.09, 1, 1, 121, -24.91, 1.81, 1, 1, 121, -22.18, 18.57, 1, 1, 121, -20.68, 27.72, 1, 1, 121, -3.44, 44.46, 1, 1, 121, 43.36, 53.25, 1, 1, 121, 74.92, 66.87, 1, 1, 121, 122.27, 62.48, 1, 1, 121, 163.91, 58.61, 1, 1, 121, 190.11, 47.64, 1, 1, 121, 228.76, 56.82, 1, 1, 121, 261.52, 51.47, 1, 1, 121, 292, 46.5, 1, 1, 121, 329.72, 40.35, 1, 1, 121, 341.35, 22.97, 1, 1, 121, 344.58, -0.24, 1, 1, 121, 336.69, -48.63, 1, 1, 121, -1.6, -47.36, 1, 1, 121, 147.45, -11.7, 1, 1, 121, 88.06, -10.12, 1, 1, 121, 218.91, -9.52, 1, 1, 121, 272.81, -9.72, 1, 1, 121, 309.27, -5.17, 1, 1, 121, 37.43, -7.59, 1], "hull": 29, "edges": [26, 24, 24, 58, 58, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 36, 38, 38, 40], "width": 163, "height": 365}}, "youshou2": {"youshou2": {"type": "mesh", "uvs": [0.64825, 0.08864, 0.72708, 0.16274, 0.8532, 0.21004, 0.92572, 0.3535, 1, 0.47647, 1, 0.58998, 0.93518, 0.70822, 0.85951, 0.83277, 0.83113, 0.93209, 0.60411, 1, 0.34241, 1, 0.13418, 1, 0.06646, 0.91174, 0, 0.83798, 0, 0.73158, 0, 0.65286, 0.07372, 0.57426, 0.18255, 0.5259, 0.30346, 0.49688, 0.44373, 0.47995, 0.46549, 0.41586, 0.40262, 0.24778, 0.19222, 0.14863, 0.14385, 0.04948, 0.25994, 0, 0.39916, 0], "triangles": [8, 9, 7, 10, 12, 17, 17, 18, 10, 19, 7, 9, 10, 18, 19, 10, 11, 12, 19, 9, 10, 7, 19, 20, 16, 17, 14, 14, 17, 12, 12, 13, 14, 14, 15, 16, 21, 22, 25, 22, 24, 25, 21, 25, 0, 22, 23, 24, 1, 21, 0, 1, 20, 21, 6, 20, 3, 3, 20, 1, 3, 1, 2, 3, 4, 5, 20, 6, 7, 6, 3, 5], "vertices": [2, 125, 22.08, -23.23, 0.63631, 126, -5.8, -23.22, 0.36369, 3, 124, 60.59, -6.14, 0.01694, 125, 7.51, -15.58, 0.9499, 126, -20.36, -15.56, 0.03315, 2, 124, 48.91, -16.25, 0.49797, 125, -7.92, -14.89, 0.50203, 1, 124, 19.93, -17.24, 1, 2, 123, 64.17, -18.03, 0.49676, 124, -5.17, -19.24, 0.50324, 2, 123, 44.53, -28.48, 0.9868, 124, -26.91, -14.53, 0.0132, 2, 123, 21.08, -33.75, 0.96923, 122, 9.08, -50.87, 0.03077, 2, 123, -3.95, -38.67, 0.61457, 122, -13.38, -38.77, 0.38543, 2, 123, -22.44, -45.35, 0.33853, 122, -31.91, -32.19, 0.66147, 3, 123, -44.64, -31.96, 0.11562, 143, -56.58, -5.79, 0.01507, 122, -40.55, -7.75, 0.86931, 3, 123, -56.69, -9.32, 0.00509, 143, -47.61, 18.23, 0.18889, 122, -35.47, 17.39, 0.80602, 2, 143, -40.47, 37.35, 0.35054, 122, -31.43, 37.4, 0.64946, 2, 143, -21.95, 37.52, 0.53396, 122, -13.16, 40.47, 0.46604, 2, 143, -6.13, 38.56, 0.77941, 122, 2.3, 44, 0.22059, 2, 143, 13.41, 31.27, 0.98159, 122, 22.74, 39.87, 0.01841, 1, 143, 27.87, 25.87, 1, 2, 143, 39.77, 13.72, 0.99986, 122, 51.54, 26.68, 0.00014, 2, 143, 44.92, 0.41, 0.72571, 122, 58.72, 14.35, 0.27429, 5, 123, 28.58, 40.36, 0.01302, 124, 5.39, 48.31, 0.01015, 125, 4.92, 61.92, 0.00035, 143, 46.11, -12.68, 0.08948, 122, 61.94, 1.61, 0.887, 4, 123, 37.97, 29.78, 0.22199, 124, 5.72, 34.18, 0.18221, 125, -4.57, 51.43, 0.01218, 122, 62.48, -12.52, 0.58362, 4, 123, 50.06, 33.8, 0.15765, 124, 17.54, 29.43, 0.53832, 125, 0.75, 39.85, 0.08945, 122, 74.37, -17.1, 0.21458, 5, 123, 76.25, 54.71, 0.0002, 124, 51.04, 28.47, 0.1276, 125, 24.4, 16.1, 0.61615, 126, -3.44, 16.11, 0.25042, 122, 107.88, -17.59, 0.00563, 1, 126, 24.54, 11.63, 1, 1, 126, 39.39, -1.77, 1, 1, 126, 35.44, -16.19, 1, 2, 125, 52.03, -23.83, 0.02193, 126, 24.15, -23.86, 0.97807], "hull": 26, "edges": [50, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 48, 50, 46, 48], "width": 98, "height": 196}}, "youshou3": {"youshou3": {"type": "mesh", "uvs": [0.76583, 0.17901, 0.77563, 0.24014, 0.74932, 0.30125, 0.6424, 0.31677, 0.47241, 0.261, 0.41378, 0.25155, 0.44059, 0.3428, 0.43871, 0.45317, 0.4086, 0.575, 0.60934, 0.52637, 0.64446, 0.47396, 0.70478, 0.40134, 0.95644, 0.49555, 1, 0.55681, 1, 0.68586, 0.96112, 0.88392, 0.74592, 0.95578, 0.53249, 1, 0.12783, 1, 0, 0.98252, 0, 0.75456, 0.08584, 0.56154, 0.06292, 0.48324, 0.13689, 0.24398, 0.21934, 0.20235, 0.33813, 0.20908, 0.34552, 0.19205, 0.3122, 0.17394, 0.28794, 0.13067, 0.26699, 0.0401, 0.36893, 0, 0.46281, 0], "triangles": [25, 26, 5, 8, 21, 7, 23, 7, 22, 7, 23, 24, 6, 7, 25, 24, 25, 7, 7, 21, 22, 6, 25, 5, 18, 8, 17, 8, 18, 20, 8, 20, 21, 18, 19, 20, 17, 9, 16, 17, 8, 9, 16, 9, 14, 14, 9, 10, 15, 16, 14, 10, 12, 14, 11, 12, 10, 12, 13, 14, 1, 2, 3, 0, 1, 3, 4, 0, 3, 0, 4, 31, 31, 4, 26, 31, 26, 30, 30, 26, 28, 26, 27, 28, 28, 29, 30, 4, 5, 26], "vertices": [1, 146, -2.68, -7.58, 1, 1, 146, -7.78, -1.35, 1, 1, 146, -10.44, 6.49, 1, 2, 146, -4.36, 12.98, 0.9949, 145, 32.77, -33.4, 0.0051, 1, 146, 11.15, 14.53, 1, 1, 145, 39.02, -13.98, 1, 1, 145, 27.42, -17.58, 1, 5, 146, -0.53, 37, 3e-05, 123, 36.48, 35.18, 0.00358, 143, 47.46, -22.03, 0.00336, 144, 13.65, -23.18, 0.18345, 145, 13.05, -19.17, 0.80958, 4, 123, 21.13, 30.18, 0.18707, 143, 33.55, -13.85, 0.35715, 144, 0.42, -13.92, 0.32715, 145, -3.09, -18.67, 0.12863, 4, 123, 34.13, 18.52, 0.86316, 143, 33.43, -31.31, 0.11898, 144, -1.08, -31.32, 0.01778, 145, 5.19, -34.05, 8e-05, 3, 123, 41.54, 19.06, 0.96472, 143, 38.75, -36.5, 0.03198, 144, 3.82, -36.92, 0.00331, 2, 123, 52.23, 18.98, 0.99597, 143, 45.78, -44.56, 0.00403, 1, 123, 50.37, -4.78, 1, 1, 123, 44.79, -11.54, 1, 1, 123, 29.69, -19.14, 1, 1, 123, 5.1, -27.99, 1, 2, 123, -11.15, -16.65, 0.93182, 143, -22.91, -20.72, 0.06818, 2, 123, -24.09, -3.81, 0.42081, 143, -21.88, -2.52, 0.57919, 2, 143, -9.72, 27.92, 1, 144, -39.4, 31.14, 0, 2, 143, -3.75, 36.69, 0.99835, 144, -32.76, 39.41, 0.00165, 2, 143, 23.98, 25.61, 0.82162, 144, -5.99, 26.17, 0.17838, 3, 143, 44.88, 9.78, 0.03697, 144, 13.59, 8.73, 0.67319, 145, -4.49, 7.49, 0.28985, 2, 144, 23.61, 5.85, 0.02383, 145, 5.47, 10.57, 0.97617, 1, 145, 37.31, 8.4, 1, 1, 145, 43.52, 2.43, 1, 1, 145, 43.81, -7.23, 1, 1, 146, 24.71, 12.7, 1, 1, 146, 28.27, 12.23, 1, 2, 146, 33.05, 8.6, 0.95201, 145, 53.51, -1.96, 0.04799, 1, 146, 41.04, -0.34, 1, 1, 146, 37.07, -9.29, 1, 1, 146, 30.74, -13.5, 1], "hull": 32, "edges": [34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 34, 36, 38, 36, 60, 58, 58, 56, 56, 54, 8, 6, 6, 4, 4, 2, 2, 0, 60, 62, 0, 62, 10, 8, 54, 52, 52, 8, 50, 52, 38, 40], "width": 81, "height": 131}}, "youshou4": {"youshou4": {"type": "mesh", "uvs": [0.54936, 0.02906, 0.66972, 0.07616, 0.76163, 0.16917, 0.78351, 0.27788, 0.81415, 0.39746, 0.88198, 0.49047, 0.94326, 0.57623, 1, 0.66199, 1, 0.82929, 0.89476, 0.90176, 0.76784, 0.95974, 0.62778, 1, 0.52931, 1, 0.48554, 1, 0.32361, 0.97423, 0.20106, 0.93075, 0.10259, 0.88727, 0.05007, 0.81117, 0, 0.71333, 0, 0.59979, 0, 0.46692, 0, 0.35009, 0, 0.25467, 0, 0.15683, 0, 0.09643, 0, 0.06141, 0.07488, 0, 0.40056, 0, 0.28701, 0.11747, 0.38813, 0.25372, 0.43274, 0.38504, 0.47438, 0.53278, 0.53089, 0.65098, 0.59334, 0.77245], "triangles": [23, 24, 26, 24, 25, 26, 10, 11, 33, 33, 11, 12, 12, 13, 33, 13, 14, 33, 33, 14, 32, 10, 33, 9, 32, 14, 15, 8, 9, 7, 31, 32, 15, 15, 16, 17, 6, 7, 9, 9, 33, 6, 15, 17, 31, 31, 17, 19, 33, 5, 6, 33, 32, 5, 17, 18, 19, 32, 4, 5, 32, 31, 4, 31, 20, 30, 31, 19, 20, 31, 30, 4, 30, 21, 29, 30, 20, 21, 30, 3, 4, 30, 2, 3, 2, 29, 1, 1, 29, 0, 29, 2, 30, 29, 22, 28, 29, 21, 22, 22, 23, 28, 29, 27, 0, 29, 28, 27, 23, 26, 28, 28, 26, 27], "vertices": [2, 120, 8.05, 78.3, 0.98438, 119, 158.53, 24.62, 0.01562, 2, 120, 26.85, 91.06, 0.99904, 119, 177.86, 12.67, 0.00096, 1, 120, 56.11, 95.24, 1, 1, 120, 85.7, 87.79, 1, 1, 120, 118.61, 80.53, 1, 1, 120, 146.57, 81.24, 1, 1, 120, 172.29, 81.71, 1, 1, 120, 197.76, 81.52, 1, 1, 120, 241.49, 65.19, 1, 1, 120, 254.76, 42.93, 1, 1, 120, 263.07, 18.96, 1, 1, 120, 266.05, -5.18, 1, 1, 120, 260.74, -19.38, 1, 1, 120, 258.38, -25.7, 1, 1, 120, 242.92, -46.54, 1, 1, 120, 224.95, -59.98, 1, 1, 120, 208.28, -69.94, 1, 1, 120, 185.56, -70.09, 1, 1, 120, 157.29, -67.76, 1, 1, 120, 127.62, -56.67, 1, 1, 120, 92.89, -43.7, 1, 1, 120, 62.35, -32.29, 1, 1, 120, 37.41, -22.98, 1, 2, 120, 11.84, -13.43, 0.86303, 119, 76.34, -16.29, 0.13697, 1, 119, 75.28, 0.52, 1, 2, 120, -13.1, -4.11, 0.00334, 119, 74.67, 10.28, 0.99666, 2, 120, -25.11, 12.69, 0.28229, 119, 85.09, 28.1, 0.71771, 2, 120, -7.56, 59.67, 0.92335, 119, 135.15, 31.26, 0.07665, 2, 120, 17.02, 31.82, 0.96736, 119, 119.77, -2.55, 0.03264, 1, 120, 58.08, 33.11, 1, 1, 120, 94.81, 26.72, 1, 1, 120, 135.67, 18.3, 1, 1, 120, 169.6, 14.92, 1, 1, 120, 204.72, 12.07, 1], "hull": 28, "edges": [54, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 52, 54, 50, 52], "width": 154, "height": 279}}, "zuoshou": {"zuoshou": {"type": "mesh", "uvs": [0.82545, 0.01661, 0.90696, 0.04946, 1, 0.091, 1, 0.16732, 1, 0.21323, 0.94691, 0.27602, 0.91495, 0.34558, 0.87979, 0.4143, 0.82865, 0.4858, 0.80468, 0.56502, 0.73276, 0.61236, 0.71838, 0.67612, 0.696, 0.73988, 0.64486, 0.84229, 0.53619, 0.89929, 0.28369, 1, 0.18584, 1, 0.08516, 0.95929, 0.03722, 0.90229, 0, 0.84626, 0, 0.75783, 0.038, 0.69406, 0.0428, 0.63416, 0.11471, 0.56171, 0.21656, 0.50449, 0.28848, 0.43783, 0.29647, 0.35571, 0.32524, 0.29871, 0.40674, 0.20693, 0.48825, 0.21176, 0.53299, 0.15959, 0.56336, 0.09873, 0.6097, 0.04463, 0.67523, 0, 0.77911, 0, 0.39555, 0.95532, 0.96769, 0.14124, 0.21158, 0.82757, 0.37723, 0.67436, 0.46596, 0.54562, 0.56062, 0.42641, 0.66908, 0.29409, 0.76527, 0.17617, 0.82443, 0.09749, 0.64695, 0.04981], "triangles": [44, 32, 33, 43, 34, 0, 43, 0, 1, 44, 33, 34, 43, 44, 34, 36, 1, 2, 36, 2, 3, 42, 44, 43, 36, 3, 4, 36, 43, 1, 5, 36, 4, 36, 42, 43, 5, 42, 36, 42, 31, 44, 6, 42, 5, 41, 42, 6, 7, 41, 6, 30, 41, 29, 40, 29, 41, 31, 32, 44, 41, 31, 42, 41, 30, 31, 8, 41, 7, 40, 41, 8, 27, 29, 40, 29, 27, 28, 40, 26, 27, 40, 25, 26, 39, 25, 40, 8, 10, 40, 9, 10, 8, 39, 40, 10, 38, 25, 39, 24, 25, 38, 11, 39, 10, 12, 39, 11, 38, 39, 12, 38, 37, 23, 38, 23, 24, 21, 22, 23, 37, 21, 23, 20, 21, 37, 13, 38, 12, 19, 20, 37, 14, 38, 13, 35, 37, 38, 18, 19, 37, 14, 35, 38, 17, 18, 37, 16, 17, 37, 15, 16, 37, 35, 15, 37, 15, 35, 14], "vertices": [1, 129, 349.14, 5.9, 1, 1, 129, 344.74, -14.6, 1, 1, 129, 338.38, -38.52, 1, 1, 129, 313.26, -48.45, 1, 1, 129, 298.14, -54.42, 1, 1, 129, 273.29, -52.03, 1, 1, 129, 247.88, -54.72, 1, 1, 129, 222.49, -56.66, 1, 1, 129, 194.93, -55.79, 1, 1, 129, 166.96, -61.32, 1, 1, 129, 145.72, -53.17, 1, 1, 129, 123.6, -58.6, 1, 1, 129, 100.85, -62.45, 1, 1, 129, 63.11, -65.59, 1, 1, 129, 35.8, -51.38, 1, 1, 129, -17.22, -14.23, 1, 1, 129, -24.92, 5.24, 1, 1, 129, -19.43, 30.58, 1, 1, 129, -4.44, 47.53, 1, 1, 129, 11.08, 62.23, 1, 1, 129, 40.19, 73.74, 1, 1, 129, 64.18, 74.47, 1, 1, 129, 84.27, 81.31, 1, 1, 129, 113.78, 76.42, 1, 1, 129, 140.63, 63.6, 1, 1, 129, 168.23, 57.96, 1, 1, 129, 195.9, 67.05, 1, 1, 129, 216.93, 68.75, 1, 1, 129, 253.55, 64.47, 1, 1, 129, 258.37, 47.62, 1, 1, 129, 279.07, 45.5, 1, 1, 129, 301.49, 47.37, 1, 1, 129, 322.95, 45.19, 1, 1, 129, 342.8, 37.96, 1, 1, 129, 350.97, 17.28, 1, 1, 129, 6.29, -30.68, 1, 1, 129, 319.3, -38.63, 1, 1, 129, 33.87, 22.55, 1, 1, 129, 97.34, 9.52, 1, 1, 129, 146.71, 8.61, 1, 1, 129, 193.4, 5.28, 1, 1, 129, 245.49, 0.92, 1, 1, 129, 291.88, -2.89, 1, 1, 129, 322.44, -4.42, 1, 1, 129, 324.17, 37.1, 1], "hull": 35, "edges": [30, 70, 70, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 72, 6, 4, 72, 4, 4, 2, 2, 0, 0, 68, 66, 68, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 38, 40, 38, 36, 36, 34, 30, 32, 34, 32, 88, 86, 28, 30], "width": 214, "height": 354}}, "zuoshou2 (2)": {"zuoshou2 (2)": {"type": "mesh", "uvs": [0.60455, 0.09788, 0.73455, 0.1761, 0.81388, 0.28668, 0.82554, 0.41395, 0.96554, 0.49482, 1, 0.56907, 1, 0.64331, 1, 0.73876, 0.87388, 0.83952, 0.68388, 0.92702, 0.50888, 0.96016, 0.44221, 1, 0.21721, 1, 0.09054, 0.92569, 0, 0.85145, 0, 0.67591, 0.06955, 0.56455, 0.16122, 0.41739, 0.27455, 0.23576, 0.35122, 0.09523, 0.45622, 0.06076, 0.68249, 0.4794, 0.57209, 0.69267, 0.49323, 0.8636], "triangles": [21, 0, 1, 21, 1, 2, 21, 2, 3, 19, 0, 21, 0, 19, 20, 21, 18, 19, 21, 17, 18, 16, 17, 22, 15, 13, 14, 15, 16, 13, 16, 23, 13, 12, 13, 23, 11, 12, 23, 4, 5, 6, 22, 17, 21, 4, 21, 3, 21, 4, 6, 7, 8, 6, 8, 21, 6, 22, 21, 8, 22, 23, 16, 9, 23, 22, 8, 9, 22, 10, 23, 9, 10, 11, 23], "vertices": [2, 140, 39.45, 74.65, 0.00206, 131, 39.04, -0.01, 0.99794, 3, 140, 48.15, 59.91, 0.05303, 130, 101.3, -18.63, 8e-05, 131, 35.49, -16.75, 0.9469, 3, 140, 50.3, 43.24, 0.20257, 130, 90.9, -31.84, 0.02284, 131, 25.82, -30.5, 0.77459, 3, 140, 45.05, 27.24, 0.57505, 130, 75.79, -39.28, 0.06668, 131, 11.13, -38.74, 0.35827, 3, 140, 54.59, 11.77, 0.94189, 130, 71.42, -56.92, 0.00901, 131, 7.71, -56.59, 0.0491, 3, 140, 54.21, 1.33, 0.99582, 130, 63.7, -63.95, 0.00039, 131, 0.38, -64.03, 0.00379, 1, 140, 50.48, -7.73, 1, 1, 140, 45.7, -19.38, 1, 1, 140, 28.39, -26.65, 1, 2, 140, 5.55, -29.75, 0.97623, 130, 7.44, -50.94, 0.02377, 2, 140, -13.11, -26.81, 0.68764, 130, -3.52, -35.56, 0.31236, 2, 140, -21.58, -29.02, 0.50176, 130, -11.02, -31.05, 0.49824, 2, 140, -43.44, -20.04, 0.10266, 130, -19.9, -9.15, 0.89734, 2, 140, -52.01, -5.91, 0.00434, 130, -15.81, 6.86, 0.99566, 1, 130, -10.3, 19.35, 1, 1, 130, 11.18, 28.06, 1, 1, 130, 27.54, 26.81, 1, 2, 130, 49.16, 25.19, 0.94756, 131, -18.92, 24.21, 0.05244, 2, 130, 75.85, 23.17, 0.21104, 131, 7.84, 23.62, 0.78896, 2, 130, 96.07, 22.68, 0.00125, 131, 28.05, 24.22, 0.99875, 1, 131, 36.86, 16.17, 1, 3, 140, 27.87, 24.95, 0.54119, 130, 62.14, -28.61, 0.21614, 131, -3.07, -28.82, 0.24267, 3, 140, 6.45, 3.32, 0.95866, 130, 31.7, -28.44, 0.04101, 131, -33.48, -30.29, 0.00033, 2, 140, -9.78, -14.4, 0.76035, 130, 7.67, -29.25, 0.23965], "hull": 21, "edges": [28, 26, 26, 24, 22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 40, 40, 38, 38, 36, 36, 34, 34, 32, 28, 30, 32, 30, 4, 42, 42, 44, 44, 46, 46, 22], "width": 105, "height": 132}}, "zuoshou3": {"zuoshou3": {"type": "mesh", "uvs": [0.91821, 0.02285, 0.97396, 0.07235, 1, 0.1241, 1, 0.21635, 1, 0.33461, 0.97417, 0.47165, 0.9331, 0.56936, 0.88304, 0.6493, 0.81758, 0.72036, 0.72388, 0.78, 0.6135, 0.83203, 0.47745, 0.8904, 0.44536, 0.92593, 0.36065, 0.97923, 0.25796, 1, 0.1694, 1, 0.0918, 1, 0.05067, 0.98257, 0, 0.93267, 0, 0.83285, 0, 0.75059, 0, 0.65248, 0.03648, 0.57932, 0.08624, 0.4885, 0.15131, 0.41787, 0.24189, 0.34849, 0.33217, 0.29285, 0.42434, 0.22647, 0.45962, 0.1421, 0.54041, 0.07235, 0.64169, 0, 0.71679, 0, 0.81807, 0, 0.15444, 0.83007, 0.28532, 0.69698, 0.41462, 0.56338, 0.56481, 0.47721, 0.73377, 0.32872], "triangles": [3, 0, 1, 0, 3, 32, 3, 37, 32, 37, 31, 32, 1, 2, 3, 13, 14, 33, 33, 14, 15, 33, 15, 16, 16, 17, 33, 17, 18, 33, 12, 13, 34, 18, 19, 33, 11, 12, 34, 34, 13, 33, 10, 11, 35, 19, 20, 33, 11, 34, 35, 9, 10, 36, 34, 33, 21, 10, 35, 36, 9, 36, 8, 34, 21, 22, 21, 33, 20, 8, 36, 7, 22, 23, 34, 23, 24, 34, 34, 24, 35, 35, 24, 25, 6, 7, 37, 7, 36, 37, 6, 37, 5, 25, 26, 35, 35, 26, 36, 31, 37, 29, 29, 30, 31, 26, 27, 36, 37, 36, 27, 37, 27, 28, 28, 29, 37, 5, 37, 4, 4, 37, 3], "vertices": [1, 127, 54.65, -12.96, 1, 2, 128, -46.68, 44.98, 0.00202, 127, 42.79, 2.6, 0.99798, 2, 128, -40, 58.7, 0.04048, 127, 38.68, 17.29, 0.95952, 2, 128, -20.7, 73.55, 0.20008, 127, 43.23, 41.21, 0.79992, 2, 128, 4.04, 92.59, 0.46568, 127, 49.07, 71.88, 0.53432, 2, 128, 36.83, 109.31, 0.70343, 127, 62.47, 106.16, 0.29657, 2, 128, 63.81, 116.55, 0.82113, 127, 77.82, 129.5, 0.17887, 2, 128, 88.5, 119.06, 0.89214, 127, 94.61, 147.79, 0.10786, 2, 128, 113.79, 116.96, 0.93963, 127, 114.9, 163.02, 0.06037, 2, 128, 141.18, 107.18, 0.97403, 127, 141.87, 173.91, 0.02597, 2, 128, 169.64, 92.72, 0.99379, 127, 172.74, 182.01, 0.00621, 2, 128, 203.5, 73.98, 0.99991, 127, 210.51, 190.5, 9e-05, 1, 128, 216.05, 73.06, 1, 1, 128, 240.68, 64.12, 1, 1, 128, 261.37, 46.22, 1, 1, 128, 275.47, 27.9, 1, 1, 128, 287.82, 11.85, 1, 1, 128, 290.72, 0.54, 1, 1, 128, 288.34, -17.98, 1, 1, 128, 267.46, -34.05, 1, 1, 128, 250.24, -47.29, 1, 1, 128, 229.72, -63.09, 1, 1, 128, 208.6, -67.32, 1, 1, 128, 181.68, -71.65, 1, 1, 128, 156.55, -69.56, 1, 1, 128, 127.61, -61.99, 1, 1, 128, 101.6, -52.27, 1, 1, 128, 73.04, -43.89, 1, 1, 128, 49.77, -50.18, 1, 2, 128, 22.32, -44.7, 0.99393, 127, 153.95, -18.57, 0.00607, 2, 128, -8.93, -35.4, 0.73988, 127, 124.41, -32.39, 0.26012, 2, 128, -20.89, -19.86, 0.31332, 127, 105.16, -28.72, 0.68668, 2, 128, -37.01, 1.09, 0.00052, 127, 79.19, -23.78, 0.99948, 1, 128, 242.29, -2.55, 1, 1, 128, 193.62, 3.1, 1, 2, 128, 145.08, 8.33, 0.99999, 127, 210.46, 102.63, 1e-05, 2, 128, 103.15, 25.53, 0.99148, 127, 167.7, 87.61, 0.00852, 2, 128, 45.19, 36.57, 0.85291, 127, 117.04, 57.36, 0.14709], "hull": 33, "edges": [28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 64, 62, 64, 60, 62, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 36, 34, 34, 32, 28, 30, 30, 32, 36, 38, 38, 40, 40, 42], "width": 261, "height": 264}}}}], "animations": {"idle": {"slots": {"biyan": {"attachment": [{"time": 3, "name": "biyan"}, {"time": 3.1667}]}, "jiemao": {"attachment": [{"time": 3}, {"time": 3.1667, "name": "<PERSON><PERSON><PERSON>"}]}}, "bones": {"bone": {"translate": [{"curve": [0.25, 0, 0.75, 0, 0.25, 0, 0.75, -15.19]}, {"time": 1, "y": -15.19, "curve": [1.25, 0, 1.75, 0, 1.25, -15.19, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 0, 2.25, 0, 2.75, -15.19]}, {"time": 3, "y": -15.19, "curve": [3.25, 0, 3.75, 0, 3.25, -15.19, 3.75, 0]}, {"time": 4}]}, "bone3": {"scale": [{"curve": [0.25, 1, 0.75, 0.957, 0.25, 1, 0.75, 1]}, {"time": 1, "x": 0.957, "curve": [1.25, 0.957, 1.75, 1, 1.25, 1, 1.75, 1]}, {"time": 2, "curve": [2.25, 1, 2.75, 0.957, 2.25, 1, 2.75, 1]}, {"time": 3, "x": 0.957, "curve": [3.25, 0.957, 3.75, 1, 3.25, 1, 3.75, 1]}, {"time": 4}]}, "bone4": {"scale": [{"curve": [0.25, 1, 0.75, 0.993, 0.25, 1, 0.75, 1]}, {"time": 1, "x": 0.993, "curve": [1.25, 0.993, 1.75, 1, 1.25, 1, 1.75, 1]}, {"time": 2, "curve": [2.25, 1, 2.75, 0.993, 2.25, 1, 2.75, 1]}, {"time": 3, "x": 0.993, "curve": [3.25, 0.993, 3.75, 1, 3.25, 1, 3.75, 1]}, {"time": 4}]}, "bone6": {"scale": [{"curve": [0.25, 1, 0.75, 0.972, 0.25, 1, 0.75, 1]}, {"time": 1, "x": 0.972, "curve": [1.25, 0.972, 1.75, 1, 1.25, 1, 1.75, 1]}, {"time": 2, "curve": [2.25, 1, 2.75, 0.972, 2.25, 1, 2.75, 1]}, {"time": 3, "x": 0.972, "curve": [3.25, 0.972, 3.75, 1, 3.25, 1, 3.75, 1]}, {"time": 4}]}, "bone7": {"scale": [{"curve": [0.25, 1, 0.75, 0.986, 0.25, 1, 0.75, 1]}, {"time": 1, "x": 0.986, "curve": [1.25, 0.986, 1.75, 1, 1.25, 1, 1.75, 1]}, {"time": 2, "curve": [2.25, 1, 2.75, 0.986, 2.25, 1, 2.75, 1]}, {"time": 3, "x": 0.986, "curve": [3.25, 0.986, 3.75, 1, 3.25, 1, 3.75, 1]}, {"time": 4}]}, "bone8": {"rotate": [{"curve": [0.25, 0, 0.75, 2.28]}, {"time": 1, "value": 2.28, "curve": [1.25, 2.28, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 2.28]}, {"time": 3, "value": 2.28, "curve": [3.25, 2.28, 3.75, 0]}, {"time": 4}]}, "bone9": {"rotate": [{"value": 0.22, "curve": [0.062, 0.08, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, 2.28]}, {"time": 1.1667, "value": 2.28, "curve": [1.369, 2.28, 1.741, 0.73]}, {"time": 2, "value": 0.22, "curve": [2.062, 0.08, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, 2.28]}, {"time": 3.1667, "value": 2.28, "curve": [3.369, 2.28, 3.741, 0.73]}, {"time": 4, "value": 0.22}]}, "bone10": {"rotate": [{"value": 0.65, "curve": [0.127, 0.28, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, 2.28]}, {"time": 1.3333, "value": 2.28, "curve": [1.495, 2.28, 1.766, 1.3]}, {"time": 2, "value": 0.65, "curve": [2.127, 0.28, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, 2.28]}, {"time": 3.3333, "value": 2.28, "curve": [3.495, 2.28, 3.766, 1.3]}, {"time": 4, "value": 0.65}]}, "bone11": {"rotate": [{"value": 1.14, "curve": [0.188, 0.57, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, 2.28]}, {"time": 1.5, "value": 2.28, "curve": [1.625, 2.28, 1.813, 1.71]}, {"time": 2, "value": 1.14, "curve": [2.188, 0.57, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, 2.28]}, {"time": 3.5, "value": 2.28, "curve": [3.625, 2.28, 3.813, 1.71]}, {"time": 4, "value": 1.14}]}, "bone12": {"rotate": [{"value": -0.28, "curve": [0.062, -0.1, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -2.88]}, {"time": 1.1667, "value": -2.88, "curve": [1.369, -2.88, 1.741, -0.93]}, {"time": 2, "value": -0.28, "curve": [2.062, -0.1, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -2.88]}, {"time": 3.1667, "value": -2.88, "curve": [3.369, -2.88, 3.741, -0.93]}, {"time": 4, "value": -0.28}]}, "bone13": {"rotate": [{"value": -0.82, "curve": [0.127, -0.35, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, -2.88]}, {"time": 1.3333, "value": -2.88, "curve": [1.495, -2.88, 1.766, -1.64]}, {"time": 2, "value": -0.82, "curve": [2.127, -0.35, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, -2.88]}, {"time": 3.3333, "value": -2.88, "curve": [3.495, -2.88, 3.766, -1.64]}, {"time": 4, "value": -0.82}]}, "bone14": {"rotate": [{"value": -1.44, "curve": [0.188, -0.72, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, -2.88]}, {"time": 1.5, "value": -2.88, "curve": [1.625, -2.88, 1.813, -2.16]}, {"time": 2, "value": -1.44, "curve": [2.188, -0.72, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, -2.88]}, {"time": 3.5, "value": -2.88, "curve": [3.625, -2.88, 3.813, -2.16]}, {"time": 4, "value": -1.44}]}, "bone15": {"rotate": [{"value": -2.06, "curve": [0.234, -1.24, 0.505, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, -2.88]}, {"time": 1.6667, "value": -2.88, "curve": [1.755, -2.88, 1.873, -2.53]}, {"time": 2, "value": -2.06, "curve": [2.234, -1.24, 2.505, 0]}, {"time": 2.6667, "curve": [2.917, 0, 3.417, -2.88]}, {"time": 3.6667, "value": -2.88, "curve": [3.755, -2.88, 3.873, -2.53]}, {"time": 4, "value": -2.06}]}, "bone30": {"rotate": [{"curve": [0.25, 0, 0.75, -0.45]}, {"time": 1, "value": -0.45, "curve": [1.25, -0.45, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -0.45]}, {"time": 3, "value": -0.45, "curve": [3.25, -0.45, 3.75, 0]}, {"time": 4}]}, "bone31": {"rotate": [{"curve": [0.25, 0, 0.75, 0.87]}, {"time": 1, "value": 0.87, "curve": [1.25, 0.87, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 0.87]}, {"time": 3, "value": 0.87, "curve": [3.25, 0.87, 3.75, 0]}, {"time": 4}], "translate": [{"curve": [0.25, 0, 0.75, -0.79, 0.25, 0, 0.75, -0.12]}, {"time": 1, "x": -0.79, "y": -0.12, "curve": [1.25, -0.79, 1.75, 0, 1.25, -0.12, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -0.79, 2.25, 0, 2.75, -0.12]}, {"time": 3, "x": -0.79, "y": -0.12, "curve": [3.25, -0.79, 3.75, 0, 3.25, -0.12, 3.75, 0]}, {"time": 4}]}, "bone32": {"rotate": [{"curve": [0.25, 0, 0.75, 2.2]}, {"time": 1, "value": 2.2, "curve": [1.25, 2.2, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 2.2]}, {"time": 3, "value": 2.2, "curve": [3.25, 2.2, 3.75, 0]}, {"time": 4}], "translate": [{"curve": [0.25, 0, 0.75, -2.29, 0.25, 0, 0.75, 0.83]}, {"time": 1, "x": -2.29, "y": 0.83, "curve": [1.25, -2.29, 1.75, 0, 1.25, 0.83, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -2.29, 2.25, 0, 2.75, 0.83]}, {"time": 3, "x": -2.29, "y": 0.83, "curve": [3.25, -2.29, 3.75, 0, 3.25, 0.83, 3.75, 0]}, {"time": 4}], "scale": [{"curve": [0.25, 1, 0.75, 0.99, 0.25, 1, 0.75, 1]}, {"time": 1, "x": 0.99, "curve": [1.25, 0.99, 1.75, 1, 1.25, 1, 1.75, 1]}, {"time": 2, "curve": [2.25, 1, 2.75, 0.99, 2.25, 1, 2.75, 1]}, {"time": 3, "x": 0.99, "curve": [3.25, 0.99, 3.75, 1, 3.25, 1, 3.75, 1]}, {"time": 4}]}, "bone28": {"translate": [{"x": -1.69, "y": -1.3, "curve": [0.062, -0.63, 0.118, 0, 0.062, -0.49, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -17.55, 0.417, 0, 0.917, -13.5]}, {"time": 1.1667, "x": -17.55, "y": -13.5, "curve": [1.369, -17.55, 1.741, -5.66, 1.369, -13.5, 1.741, -4.35]}, {"time": 2, "x": -1.69, "y": -1.3, "curve": [2.062, -0.63, 2.118, 0, 2.062, -0.49, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -17.55, 2.417, 0, 2.917, -13.5]}, {"time": 3.1667, "x": -17.55, "y": -13.5, "curve": [3.369, -17.55, 3.741, -5.66, 3.369, -13.5, 3.741, -4.35]}, {"time": 4, "x": -1.69, "y": -1.3}]}, "bone33": {"rotate": [{"curve": [0.25, 0, 0.75, 0.97]}, {"time": 1, "value": 0.97, "curve": [1.25, 0.97, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 0.97]}, {"time": 3, "value": 0.97, "curve": [3.25, 0.97, 3.75, 0]}, {"time": 4}], "translate": [{"curve": [0.25, 0, 0.75, -3.85, 0.25, 0, 0.75, -0.21]}, {"time": 1, "x": -3.85, "y": -0.21, "curve": [1.25, -3.85, 1.75, 0, 1.25, -0.21, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -3.85, 2.25, 0, 2.75, -0.21]}, {"time": 3, "x": -3.85, "y": -0.21, "curve": [3.25, -3.85, 3.75, 0, 3.25, -0.21, 3.75, 0]}, {"time": 4}], "scale": [{"curve": [0.25, 1, 0.75, 0.977, 0.25, 1, 0.75, 1]}, {"time": 1, "x": 0.977, "curve": [1.25, 0.977, 1.75, 1, 1.25, 1, 1.75, 1]}, {"time": 2, "curve": [2.25, 1, 2.75, 0.977, 2.25, 1, 2.75, 1]}, {"time": 3, "x": 0.977, "curve": [3.25, 0.977, 3.75, 1, 3.25, 1, 3.75, 1]}, {"time": 4}]}, "bone34": {"rotate": [{"curve": [0.25, 0, 0.75, -0.84]}, {"time": 1, "value": -0.84, "curve": [1.25, -0.84, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -0.84]}, {"time": 3, "value": -0.84, "curve": [3.25, -0.84, 3.75, 0]}, {"time": 4}]}, "bone35": {"rotate": [{"curve": [0.25, 0, 0.75, -4.79]}, {"time": 1, "value": -4.79, "curve": [1.25, -4.79, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -4.79]}, {"time": 3, "value": -4.79, "curve": [3.25, -4.79, 3.75, 0]}, {"time": 4}]}, "bone36": {"rotate": [{"curve": [0.25, 0, 0.75, -1]}, {"time": 1, "value": -1, "curve": [1.25, -1, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -1]}, {"time": 3, "value": -1, "curve": [3.25, -1, 3.75, 0]}, {"time": 4}]}, "bone37": {"translate": [{"curve": [0.25, 0, 0.75, -5.58, 0.25, 0, 0.75, -9.55]}, {"time": 1, "x": -5.58, "y": -9.55, "curve": [1.25, -5.58, 1.75, 0, 1.25, -9.55, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -5.58, 2.25, 0, 2.75, -9.55]}, {"time": 3, "x": -5.58, "y": -9.55, "curve": [3.25, -5.58, 3.75, 0, 3.25, -9.55, 3.75, 0]}, {"time": 4}]}, "bone40": {"rotate": [{"curve": [0.25, 0, 0.75, 0.46]}, {"time": 1, "value": 0.46, "curve": [1.25, 0.46, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 0.46]}, {"time": 3, "value": 0.46, "curve": [3.25, 0.46, 3.75, 0]}, {"time": 4}]}, "bone41": {"rotate": [{"value": 0.04, "curve": [0.062, 0.02, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, 0.46]}, {"time": 1.1667, "value": 0.46, "curve": [1.369, 0.46, 1.741, 0.15]}, {"time": 2, "value": 0.04, "curve": [2.062, 0.02, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, 0.46]}, {"time": 3.1667, "value": 0.46, "curve": [3.369, 0.46, 3.741, 0.15]}, {"time": 4, "value": 0.04}]}, "bone42": {"rotate": [{"value": 0.13, "curve": [0.127, 0.06, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, 0.46]}, {"time": 1.3333, "value": 0.46, "curve": [1.495, 0.46, 1.766, 0.26]}, {"time": 2, "value": 0.13, "curve": [2.127, 0.06, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, 0.46]}, {"time": 3.3333, "value": 0.46, "curve": [3.495, 0.46, 3.766, 0.26]}, {"time": 4, "value": 0.13}]}, "bone43": {"rotate": [{"value": 1.17, "curve": [0.188, 0.58, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, 2.34]}, {"time": 1.5, "value": 2.34, "curve": [1.625, 2.34, 1.813, 1.75]}, {"time": 2, "value": 1.17, "curve": [2.188, 0.58, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, 2.34]}, {"time": 3.5, "value": 2.34, "curve": [3.625, 2.34, 3.813, 1.75]}, {"time": 4, "value": 1.17}]}, "bone44": {"rotate": [{"value": 1.67, "curve": [0.234, 1, 0.505, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, 2.34]}, {"time": 1.6667, "value": 2.34, "curve": [1.755, 2.34, 1.873, 2.05]}, {"time": 2, "value": 1.67, "curve": [2.234, 1, 2.505, 0]}, {"time": 2.6667, "curve": [2.917, 0, 3.417, 2.34]}, {"time": 3.6667, "value": 2.34, "curve": [3.755, 2.34, 3.873, 2.05]}, {"time": 4, "value": 1.67}]}, "bone45": {"rotate": [{"value": 2.03, "curve": [0.256, 1.45, 0.606, 0]}, {"time": 0.8, "curve": [1.05, 0, 1.55, 2.34]}, {"time": 1.8, "value": 2.34, "curve": [1.857, 2.34, 1.925, 2.22]}, {"time": 2, "value": 2.03, "curve": [2.256, 1.45, 2.606, 0]}, {"time": 2.8, "curve": [3.05, 0, 3.55, 2.34]}, {"time": 3.8, "value": 2.34, "curve": [3.857, 2.34, 3.925, 2.22]}, {"time": 4, "value": 2.03}]}, "bone46": {"rotate": [{"value": 2.28, "curve": [0.259, 2.01, 0.703, 0]}, {"time": 0.9333, "curve": [1.183, 0, 1.683, 2.34]}, {"time": 1.9333, "value": 2.34, "curve": [1.954, 2.34, 1.977, 2.32]}, {"time": 2, "value": 2.28, "curve": [2.259, 2.01, 2.703, 0]}, {"time": 2.9333, "curve": [3.183, 0, 3.683, 2.34]}, {"time": 3.9333, "value": 2.34, "curve": [3.954, 2.34, 3.977, 2.32]}, {"time": 4, "value": 2.28}]}, "bone47": {"rotate": [{"value": 2.28, "curve": [0.023, 2.32, 0.046, 2.34]}, {"time": 0.0667, "value": 2.34, "curve": [0.317, 2.34, 0.817, 0]}, {"time": 1.0667, "curve": [1.297, 0, 1.741, 2.01]}, {"time": 2, "value": 2.28, "curve": [2.023, 2.32, 2.046, 2.34]}, {"time": 2.0667, "value": 2.34, "curve": [2.317, 2.34, 2.817, 0]}, {"time": 3.0667, "curve": [3.297, 0, 3.741, 2.01]}, {"time": 4, "value": 2.28}]}, "bone48": {"rotate": [{"value": 2.11, "curve": [0.062, 2.25, 0.118, 2.34]}, {"time": 0.1667, "value": 2.34, "curve": [0.417, 2.34, 0.917, 0]}, {"time": 1.1667, "curve": [1.369, 0, 1.741, 1.58]}, {"time": 2, "value": 2.11, "curve": [2.062, 2.25, 2.118, 2.34]}, {"time": 2.1667, "value": 2.34, "curve": [2.417, 2.34, 2.917, 0]}, {"time": 3.1667, "curve": [3.369, 0, 3.741, 1.58]}, {"time": 4, "value": 2.11}]}, "bone49": {"rotate": [{"value": 1.86, "curve": [0.101, 2.14, 0.194, 2.34]}, {"time": 0.2667, "value": 2.34, "curve": [0.517, 2.34, 1.017, 0]}, {"time": 1.2667, "curve": [1.444, 0, 1.753, 1.21]}, {"time": 2, "value": 1.86, "curve": [2.101, 2.14, 2.194, 2.34]}, {"time": 2.2667, "value": 2.34, "curve": [2.517, 2.34, 3.017, 0]}, {"time": 3.2667, "curve": [3.444, 0, 3.753, 1.21]}, {"time": 4, "value": 1.86}]}, "bone50": {"rotate": [{"value": 1.48, "curve": [0.153, 1.94, 0.297, 2.34]}, {"time": 0.4, "value": 2.34, "curve": [0.65, 2.34, 1.15, 0]}, {"time": 1.4, "curve": [1.547, 0, 1.782, 0.82]}, {"time": 2, "value": 1.48, "curve": [2.153, 1.94, 2.297, 2.34]}, {"time": 2.4, "value": 2.34, "curve": [2.65, 2.34, 3.15, 0]}, {"time": 3.4, "curve": [3.547, 0, 3.782, 0.82]}, {"time": 4, "value": 1.48}]}, "bone51": {"rotate": [{"value": 1.67, "curve": [0.127, 2.05, 0.245, 2.34]}, {"time": 0.3333, "value": 2.34, "curve": [0.583, 2.34, 1.083, 0]}, {"time": 1.3333, "curve": [1.495, 0, 1.766, 1]}, {"time": 2, "value": 1.67, "curve": [2.127, 2.05, 2.245, 2.34]}, {"time": 2.3333, "value": 2.34, "curve": [2.583, 2.34, 3.083, 0]}, {"time": 3.3333, "curve": [3.495, 0, 3.766, 1]}, {"time": 4, "value": 1.67}]}, "bone52": {"rotate": [{"value": 1.27, "curve": [0.176, 1.82, 0.349, 2.34]}, {"time": 0.4667, "value": 2.34, "curve": [0.717, 2.34, 1.217, 0]}, {"time": 1.4667, "curve": [1.599, 0, 1.802, 0.66]}, {"time": 2, "value": 1.27, "curve": [2.176, 1.82, 2.349, 2.34]}, {"time": 2.4667, "value": 2.34, "curve": [2.717, 2.34, 3.217, 0]}, {"time": 3.4667, "curve": [3.599, 0, 3.802, 0.66]}, {"time": 4, "value": 1.27}]}, "bone53": {"rotate": [{"value": 1.06, "curve": [0.198, 1.68, 0.401, 2.34]}, {"time": 0.5333, "value": 2.34, "curve": [0.783, 2.34, 1.283, 0]}, {"time": 1.5333, "curve": [1.651, 0, 1.824, 0.52]}, {"time": 2, "value": 1.06, "curve": [2.198, 1.68, 2.401, 2.34]}, {"time": 2.5333, "value": 2.34, "curve": [2.783, 2.34, 3.283, 0]}, {"time": 3.5333, "curve": [3.651, 0, 3.824, 0.52]}, {"time": 4, "value": 1.06}]}, "bone54": {"rotate": [{"curve": [0.25, 0, 0.75, 7.14]}, {"time": 1, "value": 7.14, "curve": [1.25, 7.14, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 7.14]}, {"time": 3, "value": 7.14, "curve": [3.25, 7.14, 3.75, 0]}, {"time": 4}]}, "bone55": {"rotate": [{"value": 0.69, "curve": [0.062, 0.26, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, 7.14]}, {"time": 1.1667, "value": 7.14, "curve": [1.369, 7.14, 1.741, 2.3]}, {"time": 2, "value": 0.69, "curve": [2.062, 0.26, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, 7.14]}, {"time": 3.1667, "value": 7.14, "curve": [3.369, 7.14, 3.741, 2.3]}, {"time": 4, "value": 0.69}]}, "bone56": {"rotate": [{"value": 1.73, "curve": [0.115, 0.72, 0.219, 0]}, {"time": 0.3, "curve": [0.55, 0, 1.05, 7.14]}, {"time": 1.3, "value": 7.14, "curve": [1.47, 7.14, 1.759, 3.76]}, {"time": 2, "value": 1.73, "curve": [2.115, 0.72, 2.219, 0]}, {"time": 2.3, "curve": [2.55, 0, 3.05, 7.14]}, {"time": 3.3, "value": 7.14, "curve": [3.47, 7.14, 3.759, 3.76]}, {"time": 4, "value": 1.73}]}, "bone57": {"rotate": [{"value": 2.94, "curve": [0.165, 1.38, 0.323, 0]}, {"time": 0.4333, "curve": [0.683, 0, 1.183, 7.14]}, {"time": 1.4333, "value": 7.14, "curve": [1.573, 7.14, 1.792, 4.89]}, {"time": 2, "value": 2.94, "curve": [2.165, 1.38, 2.323, 0]}, {"time": 2.4333, "curve": [2.683, 0, 3.183, 7.14]}, {"time": 3.4333, "value": 7.14, "curve": [3.573, 7.14, 3.792, 4.89]}, {"time": 4, "value": 2.94}]}, "bone58": {"rotate": [{"value": 0.12, "curve": [0.127, 0.05, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, 0.43]}, {"time": 1.3333, "value": 0.43, "curve": [1.495, 0.43, 1.766, 0.25]}, {"time": 2, "value": 0.12, "curve": [2.127, 0.05, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, 0.43]}, {"time": 3.3333, "value": 0.43, "curve": [3.495, 0.43, 3.766, 0.25]}, {"time": 4, "value": 0.12}]}, "bone59": {"rotate": [{"value": 0.21, "curve": [0.117, 0.15, 0.234, 0.08]}, {"time": 0.3333, "value": 0.04, "curve": [0.395, 0.02, 0.452, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, 0.43]}, {"time": 1.5, "value": 0.43, "curve": [1.625, 0.43, 1.813, 0.32]}, {"time": 2, "value": 0.21, "curve": [2.117, 0.15, 2.234, 0.08]}, {"time": 2.3333, "value": 0.04, "curve": [2.395, 0.02, 2.452, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, 0.43]}, {"time": 3.5, "value": 0.43, "curve": [3.625, 0.43, 3.813, 0.32]}, {"time": 4, "value": 0.21}]}, "bone60": {"rotate": [{"value": 0.31, "curve": [0.108, 0.25, 0.225, 0.18]}, {"time": 0.3333, "value": 0.12, "curve": [0.461, 0.05, 0.578, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, 0.43]}, {"time": 1.6667, "value": 0.43, "curve": [1.755, 0.43, 1.873, 0.38]}, {"time": 2, "value": 0.31, "curve": [2.108, 0.25, 2.225, 0.18]}, {"time": 2.3333, "value": 0.12, "curve": [2.461, 0.05, 2.578, 0]}, {"time": 2.6667, "curve": [2.917, 0, 3.417, 0.43]}, {"time": 3.6667, "value": 0.43, "curve": [3.755, 0.43, 3.873, 0.38]}, {"time": 4, "value": 0.31}]}, "bone61": {"rotate": [{"value": 2.35, "curve": [0.1, 2.12, 0.216, 1.71]}, {"time": 0.3333, "value": 1.3, "curve": [0.521, 0.65, 0.708, 0]}, {"time": 0.8333, "curve": [1.083, 0, 1.583, 2.6]}, {"time": 1.8333, "value": 2.6, "curve": [1.882, 2.6, 1.938, 2.5]}, {"time": 2, "value": 2.35, "curve": [2.1, 2.12, 2.216, 1.71]}, {"time": 2.3333, "value": 1.3, "curve": [2.521, 0.65, 2.708, 0]}, {"time": 2.8333, "curve": [3.083, 0, 3.583, 2.6]}, {"time": 3.8333, "value": 2.6, "curve": [3.882, 2.6, 3.938, 2.5]}, {"time": 4, "value": 2.35}]}, "bone62": {"rotate": [{"value": 2.6, "curve": [0.088, 2.6, 0.206, 2.28]}, {"time": 0.3333, "value": 1.86, "curve": [0.568, 1.12, 0.838, 0]}, {"time": 1, "curve": [1.25, 0, 1.75, 2.6]}, {"time": 2, "value": 2.6, "curve": [2.088, 2.6, 2.206, 2.28]}, {"time": 2.3333, "value": 1.86, "curve": [2.568, 1.12, 2.838, 0]}, {"time": 3, "curve": [3.25, 0, 3.75, 2.6]}, {"time": 4, "value": 2.6}]}, "bone63": {"rotate": [{"value": 2.42, "curve": [0.049, 2.53, 0.094, 2.6]}, {"time": 0.1333, "value": 2.6, "curve": [0.19, 2.6, 0.258, 2.47]}, {"time": 0.3333, "value": 2.26, "curve": [0.59, 1.61, 0.939, 0]}, {"time": 1.1333, "curve": [1.345, 0, 1.74, 1.91]}, {"time": 2, "value": 2.42, "curve": [2.049, 2.53, 2.094, 2.6]}, {"time": 2.1333, "value": 2.6, "curve": [2.19, 2.6, 2.258, 2.47]}, {"time": 2.3333, "value": 2.26, "curve": [2.59, 1.61, 2.939, 0]}, {"time": 3.1333, "curve": [3.345, 0, 3.74, 1.91]}, {"time": 4, "value": 2.42}]}, "bone64": {"rotate": [{"value": 2.78, "curve": [0.101, 3.2, 0.194, 3.49]}, {"time": 0.2667, "value": 3.49, "curve": [0.288, 3.49, 0.31, 3.46]}, {"time": 0.3333, "value": 3.41, "curve": [0.592, 3.01, 1.037, 0]}, {"time": 1.2667, "curve": [1.444, 0, 1.753, 1.81]}, {"time": 2, "value": 2.78, "curve": [2.101, 3.2, 2.194, 3.49]}, {"time": 2.2667, "value": 3.49, "curve": [2.288, 3.49, 2.31, 3.46]}, {"time": 2.3333, "value": 3.41, "curve": [2.592, 3.01, 3.037, 0]}, {"time": 3.2667, "curve": [3.444, 0, 3.753, 1.81]}, {"time": 4, "value": 2.78}]}, "bone65": {"rotate": [{"value": 2.36, "curve": [0.125, 2.92, 0.242, 3.37]}, {"time": 0.3333, "value": 3.45, "curve": [0.345, 3.48, 0.356, 3.49]}, {"time": 0.3667, "value": 3.49, "curve": [0.617, 3.49, 1.117, 0]}, {"time": 1.3667, "curve": [1.521, 0, 1.774, 1.36]}, {"time": 2, "value": 2.36, "curve": [2.125, 2.92, 2.242, 3.37]}, {"time": 2.3333, "value": 3.45, "curve": [2.345, 3.48, 2.356, 3.49]}, {"time": 2.3667, "value": 3.49, "curve": [2.617, 3.49, 3.117, 0]}, {"time": 3.3667, "curve": [3.521, 0, 3.774, 1.36]}, {"time": 4, "value": 2.36}]}, "bone66": {"rotate": [{"value": 1.92, "curve": [0.119, 2.47, 0.236, 2.99]}, {"time": 0.3333, "value": 3.25, "curve": [0.382, 3.4, 0.427, 3.49]}, {"time": 0.4667, "value": 3.49, "curve": [0.717, 3.49, 1.217, 0]}, {"time": 1.4667, "curve": [1.599, 0, 1.802, 0.99]}, {"time": 2, "value": 1.92, "curve": [2.119, 2.47, 2.236, 2.99]}, {"time": 2.3333, "value": 3.25, "curve": [2.382, 3.4, 2.427, 3.49]}, {"time": 2.4667, "value": 3.49, "curve": [2.717, 3.49, 3.217, 0]}, {"time": 3.4667, "curve": [3.599, 0, 3.802, 0.99]}, {"time": 4, "value": 1.92}]}, "bone67": {"rotate": [{"value": 2.18, "curve": [0.113, 2.95, 0.23, 3.8]}, {"time": 0.3333, "value": 4.37, "curve": [0.422, 4.9, 0.502, 5.24]}, {"time": 0.5667, "value": 5.24, "curve": [0.817, 5.24, 1.317, 0]}, {"time": 1.5667, "curve": [1.677, 0, 1.835, 1.02]}, {"time": 2, "value": 2.18, "curve": [2.113, 2.95, 2.23, 3.8]}, {"time": 2.3333, "value": 4.37, "curve": [2.422, 4.9, 2.502, 5.24]}, {"time": 2.5667, "value": 5.24, "curve": [2.817, 5.24, 3.317, 0]}, {"time": 3.5667, "curve": [3.677, 0, 3.835, 1.02]}, {"time": 4, "value": 2.18}]}, "bone68": {"rotate": [{"value": 1.49, "curve": [0.108, 2.19, 0.225, 3.06]}, {"time": 0.3333, "value": 3.75, "curve": [0.461, 4.6, 0.578, 5.24]}, {"time": 0.6667, "value": 5.24, "curve": [0.917, 5.24, 1.417, 0]}, {"time": 1.6667, "curve": [1.755, 0, 1.873, 0.64]}, {"time": 2, "value": 1.49, "curve": [2.108, 2.19, 2.225, 3.06]}, {"time": 2.3333, "value": 3.75, "curve": [2.461, 4.6, 2.578, 5.24]}, {"time": 2.6667, "value": 5.24, "curve": [2.917, 5.24, 3.417, 0]}, {"time": 3.6667, "curve": [3.755, 0, 3.873, 0.64]}, {"time": 4, "value": 1.49}]}, "bone69": {"rotate": [{"value": 0.87, "curve": [0.103, 1.44, 0.22, 2.3]}, {"time": 0.3333, "value": 3.08, "curve": [0.498, 4.23, 0.656, 5.24]}, {"time": 0.7667, "value": 5.24, "curve": [1.017, 5.24, 1.517, 0]}, {"time": 1.7667, "curve": [1.832, 0, 1.912, 0.34]}, {"time": 2, "value": 0.87, "curve": [2.103, 1.44, 2.22, 2.3]}, {"time": 2.3333, "value": 3.08, "curve": [2.498, 4.23, 2.656, 5.24]}, {"time": 2.7667, "value": 5.24, "curve": [3.017, 5.24, 3.517, 0]}, {"time": 3.7667, "curve": [3.832, 0, 3.912, 0.34]}, {"time": 4, "value": 0.87}]}, "bone70": {"rotate": [{"value": 0.35, "curve": [0.098, 0.74, 0.215, 1.54]}, {"time": 0.3333, "value": 2.39, "curve": [0.531, 3.77, 0.734, 5.24]}, {"time": 0.8667, "value": 5.24, "curve": [1.117, 5.24, 1.617, 0]}, {"time": 1.8667, "curve": [1.906, 0, 1.951, 0.13]}, {"time": 2, "value": 0.35, "curve": [2.098, 0.74, 2.215, 1.54]}, {"time": 2.3333, "value": 2.39, "curve": [2.531, 3.77, 2.734, 5.24]}, {"time": 2.8667, "value": 5.24, "curve": [3.117, 5.24, 3.617, 0]}, {"time": 3.8667, "curve": [3.906, 0, 3.951, 0.13]}, {"time": 4, "value": 0.35}]}, "bone71": {"rotate": [{"value": 0.1, "curve": [0.188, 0.05, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, 0.21]}, {"time": 1.5, "value": 0.21, "curve": [1.625, 0.21, 1.813, 0.15]}, {"time": 2, "value": 0.1, "curve": [2.188, 0.05, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, 0.21]}, {"time": 3.5, "value": 0.21, "curve": [3.625, 0.21, 3.813, 0.15]}, {"time": 4, "value": 0.1}]}, "bone72": {"rotate": [{"value": 0.15, "curve": [0.167, 0.1, 0.353, 0.05]}, {"time": 0.5, "value": 0.02, "curve": [0.562, 0.01, 0.618, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, 0.21]}, {"time": 1.6667, "value": 0.21, "curve": [1.755, 0.21, 1.873, 0.18]}, {"time": 2, "value": 0.15, "curve": [2.167, 0.1, 2.353, 0.05]}, {"time": 2.5, "value": 0.02, "curve": [2.562, 0.01, 2.618, 0]}, {"time": 2.6667, "curve": [2.917, 0, 3.417, 0.21]}, {"time": 3.6667, "value": 0.21, "curve": [3.755, 0.21, 3.873, 0.18]}, {"time": 4, "value": 0.15}]}, "bone73": {"rotate": [{"value": 0.19, "curve": [0.147, 0.16, 0.333, 0.1]}, {"time": 0.5, "value": 0.06, "curve": [0.627, 0.03, 0.745, 0]}, {"time": 0.8333, "curve": [1.083, 0, 1.583, 0.21]}, {"time": 1.8333, "value": 0.21, "curve": [1.882, 0.21, 1.938, 0.2]}, {"time": 2, "value": 0.19, "curve": [2.147, 0.16, 2.333, 0.1]}, {"time": 2.5, "value": 0.06, "curve": [2.627, 0.03, 2.745, 0]}, {"time": 2.8333, "curve": [3.083, 0, 3.583, 0.21]}, {"time": 3.8333, "value": 0.21, "curve": [3.882, 0.21, 3.938, 0.2]}, {"time": 4, "value": 0.19}]}, "bone74": {"rotate": [{"value": 0.21, "curve": [0.125, 0.21, 0.313, 0.15]}, {"time": 0.5, "value": 0.1, "curve": [0.688, 0.05, 0.875, 0]}, {"time": 1, "curve": [1.25, 0, 1.75, 0.21]}, {"time": 2, "value": 0.21, "curve": [2.125, 0.21, 2.313, 0.15]}, {"time": 2.5, "value": 0.1, "curve": [2.688, 0.05, 2.875, 0]}, {"time": 3, "curve": [3.25, 0, 3.75, 0.21]}, {"time": 4, "value": 0.21}]}, "bone75": {"rotate": [{"value": 1.85, "curve": [0.062, 1.97, 0.118, 2.04]}, {"time": 0.1667, "value": 2.04, "curve": [0.255, 2.04, 0.373, 1.8]}, {"time": 0.5, "value": 1.46, "curve": [0.734, 0.88, 1.005, 0]}, {"time": 1.1667, "curve": [1.369, 0, 1.741, 1.39]}, {"time": 2, "value": 1.85, "curve": [2.062, 1.97, 2.118, 2.04]}, {"time": 2.1667, "value": 2.04, "curve": [2.255, 2.04, 2.373, 1.8]}, {"time": 2.5, "value": 1.46, "curve": [2.734, 0.88, 3.005, 0]}, {"time": 3.1667, "curve": [3.369, 0, 3.741, 1.39]}, {"time": 4, "value": 1.85}]}, "bone76": {"rotate": [{"value": 2.19, "curve": [0.127, 2.68, 0.245, 3.05]}, {"time": 0.3333, "value": 3.05, "curve": [0.382, 3.05, 0.438, 2.94]}, {"time": 0.5, "value": 2.76, "curve": [0.759, 2.07, 1.131, 0]}, {"time": 1.3333, "curve": [1.495, 0, 1.766, 1.31]}, {"time": 2, "value": 2.19, "curve": [2.127, 2.68, 2.245, 3.05]}, {"time": 2.3333, "value": 3.05, "curve": [2.382, 3.05, 2.438, 2.94]}, {"time": 2.5, "value": 2.76, "curve": [2.759, 2.07, 3.131, 0]}, {"time": 3.3333, "curve": [3.495, 0, 3.766, 1.31]}, {"time": 4, "value": 2.19}]}, "bone77": {"rotate": [{"value": 1.53, "curve": [0.188, 2.29, 0.375, 3.05]}, {"time": 0.5, "value": 3.05, "curve": [0.75, 3.05, 1.25, 0]}, {"time": 1.5, "curve": [1.625, 0, 1.813, 0.76]}, {"time": 2, "value": 1.53, "curve": [2.188, 2.29, 2.375, 3.05]}, {"time": 2.5, "value": 3.05, "curve": [2.75, 3.05, 3.25, 0]}, {"time": 3.5, "curve": [3.625, 0, 3.813, 0.76]}, {"time": 4, "value": 1.53}]}, "bone78": {"rotate": [{"value": 0.88, "curve": [0.167, 1.51, 0.353, 2.37]}, {"time": 0.5, "value": 2.76, "curve": [0.562, 2.94, 0.618, 3.05]}, {"time": 0.6667, "value": 3.05, "curve": [0.917, 3.05, 1.417, 0]}, {"time": 1.6667, "curve": [1.755, 0, 1.873, 0.38]}, {"time": 2, "value": 0.88, "curve": [2.167, 1.51, 2.353, 2.37]}, {"time": 2.5, "value": 2.76, "curve": [2.562, 2.94, 2.618, 3.05]}, {"time": 2.6667, "value": 3.05, "curve": [2.917, 3.05, 3.417, 0]}, {"time": 3.6667, "curve": [3.755, 0, 3.873, 0.38]}, {"time": 4, "value": 0.88}]}, "bone79": {"rotate": [{"value": 0.51, "curve": [0.155, 1.01, 0.341, 1.89]}, {"time": 0.5, "value": 2.44, "curve": [0.601, 2.8, 0.694, 3.05]}, {"time": 0.7667, "value": 3.05, "curve": [1.017, 3.05, 1.517, 0]}, {"time": 1.7667, "curve": [1.832, 0, 1.912, 0.2]}, {"time": 2, "value": 0.51, "curve": [2.155, 1.01, 2.341, 1.89]}, {"time": 2.5, "value": 2.44, "curve": [2.601, 2.8, 2.694, 3.05]}, {"time": 2.7667, "value": 3.05, "curve": [3.017, 3.05, 3.517, 0]}, {"time": 3.7667, "curve": [3.832, 0, 3.912, 0.2]}, {"time": 4, "value": 0.51}]}, "bone80": {"rotate": [{"value": 0.21, "curve": [0.143, 0.54, 0.329, 1.4]}, {"time": 0.5, "value": 2.06, "curve": [0.64, 2.62, 0.771, 3.05]}, {"time": 0.8667, "value": 3.05, "curve": [1.117, 3.05, 1.617, 0]}, {"time": 1.8667, "curve": [1.906, 0, 1.951, 0.08]}, {"time": 2, "value": 0.21, "curve": [2.143, 0.54, 2.329, 1.4]}, {"time": 2.5, "value": 2.06, "curve": [2.64, 2.62, 2.771, 3.05]}, {"time": 2.8667, "value": 3.05, "curve": [3.117, 3.05, 3.617, 0]}, {"time": 3.8667, "curve": [3.906, 0, 3.951, 0.08]}, {"time": 4, "value": 0.21}]}, "bone81": {"rotate": [{"value": 0.03, "curve": [0.13, 0.18, 0.317, 1.38]}, {"time": 0.5, "value": 2.5, "curve": [0.676, 3.59, 0.849, 4.6]}, {"time": 0.9667, "value": 4.6, "curve": [1.217, 4.6, 1.717, 0]}, {"time": 1.9667, "curve": [1.977, 0, 1.989, 0.01]}, {"time": 2, "value": 0.03, "curve": [2.13, 0.18, 2.317, 1.38]}, {"time": 2.5, "value": 2.5, "curve": [2.676, 3.59, 2.849, 4.6]}, {"time": 2.9667, "value": 4.6, "curve": [3.217, 4.6, 3.717, 0]}, {"time": 3.9667, "curve": [3.977, 0, 3.989, 0.01]}, {"time": 4, "value": 0.03}]}, "bone82": {"rotate": [{"value": 0.1, "curve": [0.023, 0.04, 0.046, 0]}, {"time": 0.0667, "curve": [0.177, 0, 0.335, 0.89]}, {"time": 0.5, "value": 1.9, "curve": [0.708, 3.15, 0.927, 4.6]}, {"time": 1.0667, "value": 4.6, "curve": [1.297, 4.6, 1.741, 0.63]}, {"time": 2, "value": 0.1, "curve": [2.023, 0.04, 2.046, 0]}, {"time": 2.0667, "curve": [2.177, 0, 2.335, 0.89]}, {"time": 2.5, "value": 1.9, "curve": [2.708, 3.15, 2.927, 4.6]}, {"time": 3.0667, "value": 4.6, "curve": [3.297, 4.6, 3.741, 0.63]}, {"time": 4, "value": 0.1}]}, "bone83": {"rotate": [{"value": 0.44, "curve": [0.062, 0.17, 0.118, 0]}, {"time": 0.1667, "curve": [0.255, 0, 0.373, 0.56]}, {"time": 0.5, "value": 1.31, "curve": [0.734, 2.63, 1.005, 4.6]}, {"time": 1.1667, "value": 4.6, "curve": [1.369, 4.6, 1.741, 1.48]}, {"time": 2, "value": 0.44, "curve": [2.062, 0.17, 2.118, 0]}, {"time": 2.1667, "curve": [2.255, 0, 2.373, 0.56]}, {"time": 2.5, "value": 1.31, "curve": [2.734, 2.63, 3.005, 4.6]}, {"time": 3.1667, "value": 4.6, "curve": [3.369, 4.6, 3.741, 1.48]}, {"time": 4, "value": 0.44}]}, "bone84": {"rotate": [{"value": 0.14, "curve": [0.062, 0.05, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, 1.5]}, {"time": 1.1667, "value": 1.5, "curve": [1.369, 1.5, 1.741, 0.48]}, {"time": 2, "value": 0.14, "curve": [2.062, 0.05, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, 1.5]}, {"time": 3.1667, "value": 1.5, "curve": [3.369, 1.5, 3.741, 0.48]}, {"time": 4, "value": 0.14}]}, "bone85": {"rotate": [{"value": 0.43, "curve": [0.127, 0.18, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, 1.5]}, {"time": 1.3333, "value": 1.5, "curve": [1.495, 1.5, 1.766, 0.86]}, {"time": 2, "value": 0.43, "curve": [2.127, 0.18, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, 1.5]}, {"time": 3.3333, "value": 1.5, "curve": [3.495, 1.5, 3.766, 0.86]}, {"time": 4, "value": 0.43}]}, "bone86": {"rotate": [{"value": 0.75, "curve": [0.188, 0.37, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, 1.5]}, {"time": 1.5, "value": 1.5, "curve": [1.625, 1.5, 1.813, 1.12]}, {"time": 2, "value": 0.75, "curve": [2.188, 0.37, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, 1.5]}, {"time": 3.5, "value": 1.5, "curve": [3.625, 1.5, 3.813, 1.12]}, {"time": 4, "value": 0.75}]}, "bone87": {"rotate": [{"value": 1.07, "curve": [0.234, 0.64, 0.505, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, 1.5]}, {"time": 1.6667, "value": 1.5, "curve": [1.755, 1.5, 1.873, 1.32]}, {"time": 2, "value": 1.07, "curve": [2.234, 0.64, 2.505, 0]}, {"time": 2.6667, "curve": [2.917, 0, 3.417, 1.5]}, {"time": 3.6667, "value": 1.5, "curve": [3.755, 1.5, 3.873, 1.32]}, {"time": 4, "value": 1.07}]}, "bone88": {"rotate": [{"curve": [0.25, 0, 0.75, -3.29]}, {"time": 1, "value": -3.29, "curve": [1.25, -3.29, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -3.29]}, {"time": 3, "value": -3.29, "curve": [3.25, -3.29, 3.75, 0]}, {"time": 4}]}, "bone89": {"rotate": [{"value": -0.32, "curve": [0.062, -0.12, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -3.29]}, {"time": 1.1667, "value": -3.29, "curve": [1.369, -3.29, 1.741, -1.06]}, {"time": 2, "value": -0.32, "curve": [2.062, -0.12, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -3.29]}, {"time": 3.1667, "value": -3.29, "curve": [3.369, -3.29, 3.741, -1.06]}, {"time": 4, "value": -0.32}]}, "bone90": {"rotate": [{"value": -0.93, "curve": [0.127, -0.4, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, -3.29]}, {"time": 1.3333, "value": -3.29, "curve": [1.495, -3.29, 1.766, -1.88]}, {"time": 2, "value": -0.93, "curve": [2.127, -0.4, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, -3.29]}, {"time": 3.3333, "value": -3.29, "curve": [3.495, -3.29, 3.766, -1.88]}, {"time": 4, "value": -0.93}]}, "bone91": {"rotate": [{"value": -1.65, "curve": [0.188, -0.82, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, -3.29]}, {"time": 1.5, "value": -3.29, "curve": [1.625, -3.29, 1.813, -2.47]}, {"time": 2, "value": -1.65, "curve": [2.188, -0.82, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, -3.29]}, {"time": 3.5, "value": -3.29, "curve": [3.625, -3.29, 3.813, -2.47]}, {"time": 4, "value": -1.65}]}, "bone92": {"rotate": [{"curve": [0.25, 0, 0.75, -2.34]}, {"time": 1, "value": -2.34, "curve": [1.25, -2.34, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -2.34]}, {"time": 3, "value": -2.34, "curve": [3.25, -2.34, 3.75, 0]}, {"time": 4}]}, "bone93": {"rotate": [{"value": -0.23, "curve": [0.062, -0.08, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -3.7]}, {"time": 1.1667, "value": -3.7, "curve": [1.417, -3.7, 1.917, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -3.7]}, {"time": 3.1667, "value": -3.7, "curve": [3.375, -3.7, 3.792, -0.23]}, {"time": 4, "value": -0.23}]}, "bone94": {"rotate": [{"value": -1.74, "curve": [0.127, -0.75, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, -6.13]}, {"time": 1.3333, "value": -6.13, "curve": [1.583, -6.13, 2.083, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, -6.13]}, {"time": 3.3333, "value": -6.13, "curve": [3.495, -6.13, 3.766, -3.5]}, {"time": 4, "value": -1.74}]}, "bone95": {"rotate": [{"value": -3.91, "curve": [0.188, -1.96, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, -7.83]}, {"time": 1.5, "value": -7.83, "curve": [1.75, -7.83, 2.25, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, -7.83]}, {"time": 3.5, "value": -7.83, "curve": [3.625, -7.83, 3.813, -5.87]}, {"time": 4, "value": -3.91}]}, "bone96": {"rotate": [{"value": -14.2, "curve": [0.234, -8.52, 0.505, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, -19.83]}, {"time": 1.6667, "value": -19.83, "curve": [1.917, -19.83, 2.417, 0]}, {"time": 2.6667, "curve": [2.917, 0, 3.417, -19.83]}, {"time": 3.6667, "value": -19.83, "curve": [3.755, -19.83, 3.873, -17.42]}, {"time": 4, "value": -14.2}]}, "bone97": {"rotate": [{"value": -0.3, "curve": [0.062, -0.11, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -3.09]}, {"time": 1.1667, "value": -3.09, "curve": [1.369, -3.09, 1.741, -1]}, {"time": 2, "value": -0.3, "curve": [2.062, -0.11, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -3.09]}, {"time": 3.1667, "value": -3.09, "curve": [3.369, -3.09, 3.741, -1]}, {"time": 4, "value": -0.3}]}, "bone98": {"rotate": [{"value": -2.53, "curve": [0.127, -1.08, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, -8.91]}, {"time": 1.3333, "value": -8.91, "curve": [1.495, -8.91, 1.766, -5.08]}, {"time": 2, "value": -2.53, "curve": [2.127, -1.08, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, -8.91]}, {"time": 3.3333, "value": -8.91, "curve": [3.495, -8.91, 3.766, -5.08]}, {"time": 4, "value": -2.53}]}, "bone99": {"rotate": [{"value": -4.06, "curve": [0.176, -1.97, 0.349, 0]}, {"time": 0.4667, "curve": [0.717, 0, 1.217, -8.91]}, {"time": 1.4667, "value": -8.91, "curve": [1.599, -8.91, 1.802, -6.4]}, {"time": 2, "value": -4.06, "curve": [2.176, -1.97, 2.349, 0]}, {"time": 2.4667, "curve": [2.717, 0, 3.217, -8.91]}, {"time": 3.4667, "value": -8.91, "curve": [3.599, -8.91, 3.802, -6.4]}, {"time": 4, "value": -4.06}]}, "bone100": {"rotate": [{"value": -5.63, "curve": [0.218, -3.13, 0.453, 0]}, {"time": 0.6, "curve": [0.85, 0, 1.35, -8.91]}, {"time": 1.6, "value": -8.91, "curve": [1.703, -8.91, 1.847, -7.42]}, {"time": 2, "value": -5.63, "curve": [2.218, -3.13, 2.453, 0]}, {"time": 2.6, "curve": [2.85, 0, 3.35, -8.91]}, {"time": 3.6, "value": -8.91, "curve": [3.703, -8.91, 3.847, -7.42]}, {"time": 4, "value": -5.63}]}, "bone101": {"rotate": [{"value": -7.1, "curve": [0.247, -4.63, 0.556, 0]}, {"time": 0.7333, "curve": [0.983, 0, 1.483, -8.91]}, {"time": 1.7333, "value": -8.91, "curve": [1.806, -8.91, 1.899, -8.18]}, {"time": 2, "value": -7.1, "curve": [2.247, -4.63, 2.556, 0]}, {"time": 2.7333, "curve": [2.983, 0, 3.483, -8.91]}, {"time": 3.7333, "value": -8.91, "curve": [3.806, -8.91, 3.899, -8.18]}, {"time": 4, "value": -7.1}]}, "bone102": {"rotate": [{"value": -8.29, "curve": [0.26, -6.55, 0.655, 0]}, {"time": 0.8667, "curve": [1.117, 0, 1.617, -8.91]}, {"time": 1.8667, "value": -8.91, "curve": [1.906, -8.91, 1.951, -8.68]}, {"time": 2, "value": -8.29, "curve": [2.26, -6.55, 2.655, 0]}, {"time": 2.8667, "curve": [3.117, 0, 3.617, -8.91]}, {"time": 3.8667, "value": -8.91, "curve": [3.906, -8.91, 3.951, -8.68]}, {"time": 4, "value": -8.29}]}, "bone103": {"rotate": [{"curve": [0.25, 0, 0.75, -0.51]}, {"time": 1, "value": -0.51, "curve": [1.25, -0.51, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -0.51]}, {"time": 3, "value": -0.51, "curve": [3.25, -0.51, 3.75, 0]}, {"time": 4}]}, "bone104": {"rotate": [{"value": -0.05, "curve": [0.062, -0.02, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -0.51]}, {"time": 1.1667, "value": -0.51, "curve": [1.369, -0.51, 1.741, -0.16]}, {"time": 2, "value": -0.05, "curve": [2.062, -0.02, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -0.51]}, {"time": 3.1667, "value": -0.51, "curve": [3.369, -0.51, 3.741, -0.16]}, {"time": 4, "value": -0.05}]}, "bone105": {"rotate": [{"value": -0.14, "curve": [0.127, -0.06, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, -0.51]}, {"time": 1.3333, "value": -0.51, "curve": [1.495, -0.51, 1.766, -0.29]}, {"time": 2, "value": -0.14, "curve": [2.127, -0.06, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, -0.51]}, {"time": 3.3333, "value": -0.51, "curve": [3.495, -0.51, 3.766, -0.29]}, {"time": 4, "value": -0.14}]}, "bone106": {"rotate": [{"value": -0.23, "curve": [0.176, -0.11, 0.349, 0]}, {"time": 0.4667, "curve": [0.717, 0, 1.217, -0.51]}, {"time": 1.4667, "value": -0.51, "curve": [1.599, -0.51, 1.802, -0.36]}, {"time": 2, "value": -0.23, "curve": [2.176, -0.11, 2.349, 0]}, {"time": 2.4667, "curve": [2.717, 0, 3.217, -0.51]}, {"time": 3.4667, "value": -0.51, "curve": [3.599, -0.51, 3.802, -0.36]}, {"time": 4, "value": -0.23}]}, "bone107": {"rotate": [{"value": -0.32, "curve": [0.218, -0.18, 0.453, 0]}, {"time": 0.6, "curve": [0.85, 0, 1.35, -0.51]}, {"time": 1.6, "value": -0.51, "curve": [1.703, -0.51, 1.847, -0.42]}, {"time": 2, "value": -0.32, "curve": [2.218, -0.18, 2.453, 0]}, {"time": 2.6, "curve": [2.85, 0, 3.35, -0.51]}, {"time": 3.6, "value": -0.51, "curve": [3.703, -0.51, 3.847, -0.42]}, {"time": 4, "value": -0.32}]}, "bone108": {"rotate": [{"curve": [0.25, 0, 0.75, 1.4]}, {"time": 1, "value": 1.4, "curve": [1.25, 1.4, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 1.4]}, {"time": 3, "value": 1.4, "curve": [3.25, 1.4, 3.75, 0]}, {"time": 4}]}, "bone109": {"rotate": [{"value": 0.06, "curve": [0.036, 0.02, 0.07, 0]}, {"time": 0.1, "curve": [0.35, 0, 0.85, 1.4]}, {"time": 1.1, "value": 1.4, "curve": [1.321, 1.4, 1.74, 0.28]}, {"time": 2, "value": 0.06, "curve": [2.036, 0.02, 2.07, 0]}, {"time": 2.1, "curve": [2.35, 0, 2.85, 1.4]}, {"time": 3.1, "value": 1.4, "curve": [3.321, 1.4, 3.74, 0.28]}, {"time": 4, "value": 0.06}]}, "bone110": {"rotate": [{"value": 0.18, "curve": [0.075, 0.07, 0.143, 0]}, {"time": 0.2, "curve": [0.45, 0, 0.95, 1.4]}, {"time": 1.2, "value": 1.4, "curve": [1.394, 1.4, 1.744, 0.53]}, {"time": 2, "value": 0.18, "curve": [2.075, 0.07, 2.143, 0]}, {"time": 2.2, "curve": [2.45, 0, 2.95, 1.4]}, {"time": 3.2, "value": 1.4, "curve": [3.394, 1.4, 3.744, 0.53]}, {"time": 4, "value": 0.18}]}, "bone111": {"rotate": [{"value": 0.34, "curve": [0.115, 0.14, 0.219, 0]}, {"time": 0.3, "curve": [0.55, 0, 1.05, 1.4]}, {"time": 1.3, "value": 1.4, "curve": [1.47, 1.4, 1.759, 0.74]}, {"time": 2, "value": 0.34, "curve": [2.115, 0.14, 2.219, 0]}, {"time": 2.3, "curve": [2.55, 0, 3.05, 1.4]}, {"time": 3.3, "value": 1.4, "curve": [3.47, 1.4, 3.759, 0.74]}, {"time": 4, "value": 0.34}]}, "bone112": {"rotate": [{"value": 0.51, "curve": [0.153, 0.23, 0.297, 0]}, {"time": 0.4, "curve": [0.65, 0, 1.15, 1.4]}, {"time": 1.4, "value": 1.4, "curve": [1.547, 1.4, 1.782, 0.91]}, {"time": 2, "value": 0.51, "curve": [2.153, 0.23, 2.297, 0]}, {"time": 2.4, "curve": [2.65, 0, 3.15, 1.4]}, {"time": 3.4, "value": 1.4, "curve": [3.547, 1.4, 3.782, 0.91]}, {"time": 4, "value": 0.51}]}, "bone113": {"rotate": [{"value": 0.76, "curve": [0.198, 0.39, 0.401, 0]}, {"time": 0.5333, "curve": [0.783, 0, 1.283, 1.4]}, {"time": 1.5333, "value": 1.4, "curve": [1.651, 1.4, 1.824, 1.09]}, {"time": 2, "value": 0.76, "curve": [2.198, 0.39, 2.401, 0]}, {"time": 2.5333, "curve": [2.783, 0, 3.283, 1.4]}, {"time": 3.5333, "value": 1.4, "curve": [3.651, 1.4, 3.824, 1.09]}, {"time": 4, "value": 0.76}]}, "bone114": {"rotate": [{"curve": [0.25, 0, 0.75, 2.73]}, {"time": 1, "value": 2.73, "curve": [1.25, 2.73, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 2.73]}, {"time": 3, "value": 2.73, "curve": [3.25, 2.73, 3.75, 0]}, {"time": 4}]}, "bone115": {"rotate": [{"value": 0.26, "curve": [0.062, 0.1, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, 2.73]}, {"time": 1.1667, "value": 2.73, "curve": [1.369, 2.73, 1.741, 0.88]}, {"time": 2, "value": 0.26, "curve": [2.062, 0.1, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, 2.73]}, {"time": 3.1667, "value": 2.73, "curve": [3.369, 2.73, 3.741, 0.88]}, {"time": 4, "value": 0.26}]}, "bone116": {"rotate": [{"value": 0.77, "curve": [0.127, 0.33, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, 2.73]}, {"time": 1.3333, "value": 2.73, "curve": [1.495, 2.73, 1.766, 1.56]}, {"time": 2, "value": 0.77, "curve": [2.127, 0.33, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, 2.73]}, {"time": 3.3333, "value": 2.73, "curve": [3.495, 2.73, 3.766, 1.56]}, {"time": 4, "value": 0.77}]}, "bone117": {"rotate": [{"value": 0.31, "curve": [0.062, 0.12, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, 3.2]}, {"time": 1.1667, "value": 3.2, "curve": [1.369, 3.2, 1.741, 1.03]}, {"time": 2, "value": 0.31, "curve": [2.062, 0.12, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, 3.2]}, {"time": 3.1667, "value": 3.2, "curve": [3.369, 3.2, 3.741, 1.03]}, {"time": 4, "value": 0.31}], "translate": [{"x": 0.08, "y": -0.71, "curve": [0.062, 0.03, 0.118, 0, 0.062, -0.27, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, 0.79, 0.417, 0, 0.917, -7.36]}, {"time": 1.1667, "x": 0.79, "y": -7.36, "curve": [1.369, 0.79, 1.741, 0.25, 1.369, -7.36, 1.741, -2.37]}, {"time": 2, "x": 0.08, "y": -0.71, "curve": [2.062, 0.03, 2.118, 0, 2.062, -0.27, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, 0.79, 2.417, 0, 2.917, -7.36]}, {"time": 3.1667, "x": 0.79, "y": -7.36, "curve": [3.369, 0.79, 3.741, 0.25, 3.369, -7.36, 3.741, -2.37]}, {"time": 4, "x": 0.08, "y": -0.71}]}, "bone118": {"rotate": [{"value": 0.91, "curve": [0.127, 0.39, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, 3.2]}, {"time": 1.3333, "value": 3.2, "curve": [1.495, 3.2, 1.766, 1.82]}, {"time": 2, "value": 0.91, "curve": [2.127, 0.39, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, 3.2]}, {"time": 3.3333, "value": 3.2, "curve": [3.495, 3.2, 3.766, 1.82]}, {"time": 4, "value": 0.91}]}, "bone119": {"rotate": [{"value": 1.6, "curve": [0.188, 0.8, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, 3.2]}, {"time": 1.5, "value": 3.2, "curve": [1.625, 3.2, 1.813, 2.4]}, {"time": 2, "value": 1.6, "curve": [2.188, 0.8, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, 3.2]}, {"time": 3.5, "value": 3.2, "curve": [3.625, 3.2, 3.813, 2.4]}, {"time": 4, "value": 1.6}]}, "bone120": {"rotate": [{"value": -0.15, "curve": [0.062, -0.05, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -1.51]}, {"time": 1.1667, "value": -1.51, "curve": [1.369, -1.51, 1.741, -0.49]}, {"time": 2, "value": -0.15, "curve": [2.062, -0.05, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -1.51]}, {"time": 3.1667, "value": -1.51, "curve": [3.369, -1.51, 3.741, -0.49]}, {"time": 4, "value": -0.15}]}, "bone121": {"rotate": [{"value": -0.43, "curve": [0.127, -0.18, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, -1.51]}, {"time": 1.3333, "value": -1.51, "curve": [1.495, -1.51, 1.766, -0.86]}, {"time": 2, "value": -0.43, "curve": [2.127, -0.18, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, -1.51]}, {"time": 3.3333, "value": -1.51, "curve": [3.495, -1.51, 3.766, -0.86]}, {"time": 4, "value": -0.43}]}, "bone122": {"rotate": [{"value": -0.75, "curve": [0.188, -0.38, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, -1.51]}, {"time": 1.5, "value": -1.51, "curve": [1.625, -1.51, 1.813, -1.13]}, {"time": 2, "value": -0.75, "curve": [2.188, -0.38, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, -1.51]}, {"time": 3.5, "value": -1.51, "curve": [3.625, -1.51, 3.813, -1.13]}, {"time": 4, "value": -0.75}]}, "bone123": {"rotate": [{"value": -0.17, "curve": [0.062, -0.06, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -1.78]}, {"time": 1.1667, "value": -1.78, "curve": [1.369, -1.78, 1.741, -0.57]}, {"time": 2, "value": -0.17, "curve": [2.062, -0.06, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -1.78]}, {"time": 3.1667, "value": -1.78, "curve": [3.369, -1.78, 3.741, -0.57]}, {"time": 4, "value": -0.17}]}, "bone124": {"rotate": [{"value": -0.51, "curve": [0.127, -0.22, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, -1.78]}, {"time": 1.3333, "value": -1.78, "curve": [1.495, -1.78, 1.766, -1.01]}, {"time": 2, "value": -0.51, "curve": [2.127, -0.22, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, -1.78]}, {"time": 3.3333, "value": -1.78, "curve": [3.495, -1.78, 3.766, -1.01]}, {"time": 4, "value": -0.51}]}, "bone125": {"rotate": [{"curve": [0.25, 0, 0.75, -3.6]}, {"time": 1, "value": -3.6, "curve": [1.25, -3.6, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -3.6]}, {"time": 3, "value": -3.6, "curve": [3.25, -3.6, 3.75, 0]}, {"time": 4}]}, "bone126": {"rotate": [{"value": -0.35, "curve": [0.062, -0.13, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -3.6]}, {"time": 1.1667, "value": -3.6, "curve": [1.369, -3.6, 1.741, -1.16]}, {"time": 2, "value": -0.35, "curve": [2.062, -0.13, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -3.6]}, {"time": 3.1667, "value": -3.6, "curve": [3.369, -3.6, 3.741, -1.16]}, {"time": 4, "value": -0.35}], "translate": [{"time": 0.1667, "curve": [0.311, 0, 0.497, 0.8, 0.311, 0, 0.497, -0.2]}, {"time": 0.7, "x": 1.94, "y": -0.49, "curve": [1.146, 1.23, 1.685, 0, 1.146, -0.31, 1.685, 0]}, {"time": 2, "curve": "stepped"}, {"time": 2.1667, "curve": [2.311, 0, 2.497, 0.8, 2.311, 0, 2.497, -0.2]}, {"time": 2.7, "x": 1.94, "y": -0.49, "curve": [3.146, 1.23, 3.685, 0, 3.146, -0.31, 3.685, 0]}, {"time": 4}], "scale": [{"time": 0.1667, "curve": [0.311, 1, 0.497, 1.017, 0.311, 1, 0.497, 1]}, {"time": 0.7, "x": 1.04, "curve": [1.146, 1.025, 1.685, 1, 1.146, 1, 1.685, 1]}, {"time": 2, "curve": "stepped"}, {"time": 2.1667, "curve": [2.311, 1, 2.497, 1.017, 2.311, 1, 2.497, 1]}, {"time": 2.7, "x": 1.04, "curve": [3.146, 1.025, 3.685, 1, 3.146, 1, 3.685, 1]}, {"time": 4}]}, "bone127": {"rotate": [{"curve": [0.25, 0, 0.75, -2.02]}, {"time": 1, "value": -2.02, "curve": [1.25, -2.02, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -2.02]}, {"time": 3, "value": -2.02, "curve": [3.25, -2.02, 3.75, 0]}, {"time": 4}]}, "bone128": {"rotate": [{"value": -0.57, "curve": [0.062, -0.21, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -5.86]}, {"time": 1.1667, "value": -5.86, "curve": [1.369, -5.86, 1.741, -1.89]}, {"time": 2, "value": -0.57, "curve": [2.062, -0.21, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -5.86]}, {"time": 3.1667, "value": -5.86, "curve": [3.369, -5.86, 3.741, -1.89]}, {"time": 4, "value": -0.57}]}, "bone129": {"rotate": [{"value": -1.66, "curve": [0.127, -0.71, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, -5.86]}, {"time": 1.3333, "value": -5.86, "curve": [1.495, -5.86, 1.766, -3.34]}, {"time": 2, "value": -1.66, "curve": [2.127, -0.71, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, -5.86]}, {"time": 3.3333, "value": -5.86, "curve": [3.495, -5.86, 3.766, -3.34]}, {"time": 4, "value": -1.66}]}, "bone130": {"rotate": [{"value": -2.93, "curve": [0.188, -1.47, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, -5.86]}, {"time": 1.5, "value": -5.86, "curve": [1.625, -5.86, 1.813, -4.4]}, {"time": 2, "value": -2.93, "curve": [2.188, -1.47, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, -5.86]}, {"time": 3.5, "value": -5.86, "curve": [3.625, -5.86, 3.813, -4.4]}, {"time": 4, "value": -2.93}]}, "bone131": {"rotate": [{"curve": [0.25, 0, 0.75, -6.62]}, {"time": 1, "value": -6.62, "curve": [1.25, -6.62, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -6.62]}, {"time": 3, "value": -6.62, "curve": [3.25, -6.62, 3.75, 0]}, {"time": 4}]}, "bone132": {"rotate": [{"curve": [0.25, 0, 0.75, 10.8]}, {"time": 1, "value": 10.8, "curve": [1.25, 10.8, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 10.8]}, {"time": 3, "value": 10.8, "curve": [3.25, 10.8, 3.75, 0]}, {"time": 4}]}, "bone133": {"rotate": [{"curve": [0.25, 0, 0.75, -7.36]}, {"time": 1, "value": -7.36, "curve": [1.25, -7.36, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -7.36]}, {"time": 3, "value": -7.36, "curve": [3.25, -7.36, 3.75, 0]}, {"time": 4}]}, "bone134": {"rotate": [{"curve": [0.25, 0, 0.75, 8.2]}, {"time": 1, "value": 8.2, "curve": [1.25, 8.2, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 8.2]}, {"time": 3, "value": 8.2, "curve": [3.25, 8.2, 3.75, 0]}, {"time": 4}]}, "bone135": {"rotate": [{"curve": [0.25, 0, 0.75, 0.44]}, {"time": 1, "value": 0.44, "curve": [1.25, 0.44, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 0.44]}, {"time": 3, "value": 0.44, "curve": [3.25, 0.44, 3.75, 0]}, {"time": 4}]}, "bone136": {"rotate": [{"curve": [0.25, 0, 0.75, 2.67]}, {"time": 1, "value": 2.67, "curve": [1.25, 2.67, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 2.67]}, {"time": 3, "value": 2.67, "curve": [3.25, 2.67, 3.75, 0]}, {"time": 4}]}, "bone137": {"rotate": [{"curve": [0.25, 0, 0.75, 2.67]}, {"time": 1, "value": 2.67, "curve": [1.25, 2.67, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 2.67]}, {"time": 3, "value": 2.67, "curve": [3.25, 2.67, 3.75, 0]}, {"time": 4}]}, "bone138": {"rotate": [{"curve": [0.25, 0, 0.75, 2.67]}, {"time": 1, "value": 2.67, "curve": [1.25, 2.67, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 2.67]}, {"time": 3, "value": 2.67, "curve": [3.25, 2.67, 3.75, 0]}, {"time": 4}]}, "bone139": {"rotate": [{"curve": [0.25, 0, 0.75, -4.53]}, {"time": 1, "value": -4.53, "curve": [1.25, -4.53, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -4.53]}, {"time": 3, "value": -4.53, "curve": [3.25, -4.53, 3.75, 0]}, {"time": 4}]}, "bone140": {"rotate": [{"curve": [0.25, 0, 0.75, -5.94]}, {"time": 1, "value": -5.94, "curve": [1.25, -5.94, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -5.94]}, {"time": 3, "value": -5.94, "curve": [3.25, -5.94, 3.75, 0]}, {"time": 4}]}, "bone141": {"rotate": [{"curve": [0.25, 0, 0.75, 4.45]}, {"time": 1, "value": 4.45, "curve": [1.25, 4.45, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 4.45]}, {"time": 3, "value": 4.45, "curve": [3.25, 4.45, 3.75, 0]}, {"time": 4}]}, "bone142": {"rotate": [{"curve": [0.25, 0, 0.75, -9.13]}, {"time": 1, "value": -9.13, "curve": [1.25, -9.13, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -9.13]}, {"time": 3, "value": -9.13, "curve": [3.25, -9.13, 3.75, 0]}, {"time": 4}]}, "bone144": {"rotate": [{"curve": [0.25, 0, 0.75, -1.03]}, {"time": 1, "value": -1.03, "curve": [1.25, -1.03, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -1.03]}, {"time": 3, "value": -1.03, "curve": [3.25, -1.03, 3.75, 0]}, {"time": 4}]}, "bone145": {"rotate": [{"curve": [0.25, 0, 0.75, -1.03]}, {"time": 1, "value": -1.03, "curve": [1.25, -1.03, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -1.03]}, {"time": 3, "value": -1.03, "curve": [3.25, -1.03, 3.75, 0]}, {"time": 4}]}, "bone146": {"rotate": [{"curve": [0.25, 0, 0.75, -1.03]}, {"time": 1, "value": -1.03, "curve": [1.25, -1.03, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -1.03]}, {"time": 3, "value": -1.03, "curve": [3.25, -1.03, 3.75, 0]}, {"time": 4}]}, "bone147": {"rotate": [{"curve": [0.25, 0, 0.75, -1.54]}, {"time": 1, "value": -1.54, "curve": [1.25, -1.54, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -1.54]}, {"time": 3, "value": -1.54, "curve": [3.25, -1.54, 3.75, 0]}, {"time": 4}]}, "bone148": {"rotate": [{"curve": [0.25, 0, 0.75, -3.38]}, {"time": 1, "value": -3.38, "curve": [1.25, -3.38, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -3.38]}, {"time": 3, "value": -3.38, "curve": [3.25, -3.38, 3.75, 0]}, {"time": 4}]}, "bone149": {"rotate": [{"curve": [0.25, 0, 0.75, 0.32]}, {"time": 1, "value": 0.32, "curve": [1.25, 0.32, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 0.32]}, {"time": 3, "value": 0.32, "curve": [3.25, 0.32, 3.75, 0]}, {"time": 4}]}, "bone151": {"rotate": [{"curve": [0.25, 0, 0.75, 5.17]}, {"time": 1, "value": 5.17, "curve": [1.25, 5.17, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 5.17]}, {"time": 3, "value": 5.17, "curve": [3.25, 5.17, 3.75, 0]}, {"time": 4}]}, "bone152": {"rotate": [{"curve": [0.25, 0, 0.75, -11.36]}, {"time": 1, "value": -11.36, "curve": [1.25, -11.36, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -11.36]}, {"time": 3, "value": -11.36, "curve": [3.25, -11.36, 3.75, 0]}, {"time": 4}]}, "bone153": {"rotate": [{"curve": [0.25, 0, 0.75, 0.75]}, {"time": 1, "value": 0.75, "curve": [1.25, 0.75, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 0.75]}, {"time": 3, "value": 0.75, "curve": [3.25, 0.75, 3.75, 0]}, {"time": 4}]}, "bone154": {"rotate": [{"curve": [0.25, 0, 0.75, 0.75]}, {"time": 1, "value": 0.75, "curve": [1.25, 0.75, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 0.75]}, {"time": 3, "value": 0.75, "curve": [3.25, 0.75, 3.75, 0]}, {"time": 4}]}, "bone155": {"rotate": [{"curve": [0.25, 0, 0.75, 4.88]}, {"time": 1, "value": 4.88, "curve": [1.25, 4.88, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 4.88]}, {"time": 3, "value": 4.88, "curve": [3.25, 4.88, 3.75, 0]}, {"time": 4}]}, "bone157": {"rotate": [{"curve": [0.25, 0, 0.75, -1.65]}, {"time": 1, "value": -1.65, "curve": [1.25, -1.65, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -1.65]}, {"time": 3, "value": -1.65, "curve": [3.25, -1.65, 3.75, 0]}, {"time": 4}]}, "bone158": {"rotate": [{"curve": [0.25, 0, 0.75, 8.2]}, {"time": 1, "value": 8.2, "curve": [1.25, 8.2, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, 8.2]}, {"time": 3, "value": 8.2, "curve": [3.25, 8.2, 3.75, 0]}, {"time": 4}]}, "bone16": {"rotate": [{"curve": [0.25, 0, 0.75, -3]}, {"time": 1, "value": -3, "curve": [1.25, -3, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -3]}, {"time": 3, "value": -3, "curve": [3.25, -3, 3.75, 0]}, {"time": 4}]}, "bone17": {"rotate": [{"value": -0.29, "curve": [0.062, -0.11, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -3]}, {"time": 1.1667, "value": -3, "curve": [1.369, -3, 1.741, -0.97]}, {"time": 2, "value": -0.29, "curve": [2.062, -0.11, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -3]}, {"time": 3.1667, "value": -3, "curve": [3.369, -3, 3.741, -0.97]}, {"time": 4, "value": -0.29}]}, "bone18": {"rotate": [{"value": -0.85, "curve": [0.127, -0.36, 0.245, 0]}, {"time": 0.3333, "curve": [0.583, 0, 1.083, -3]}, {"time": 1.3333, "value": -3, "curve": [1.495, -3, 1.766, -1.71]}, {"time": 2, "value": -0.85, "curve": [2.127, -0.36, 2.245, 0]}, {"time": 2.3333, "curve": [2.583, 0, 3.083, -3]}, {"time": 3.3333, "value": -3, "curve": [3.495, -3, 3.766, -1.71]}, {"time": 4, "value": -0.85}]}, "bone159": {"rotate": [{"curve": [0.25, 0, 0.75, -2.02]}, {"time": 1, "value": -2.02, "curve": [1.25, -2.02, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -2.02]}, {"time": 3, "value": -2.02, "curve": [3.25, -2.02, 3.75, 0]}, {"time": 4}]}, "bone160": {"rotate": [{"value": -0.19, "curve": [0.062, -0.07, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -2.02]}, {"time": 1.1667, "value": -2.02, "curve": [1.369, -2.02, 1.741, -0.65]}, {"time": 2, "value": -0.19, "curve": [2.062, -0.07, 2.118, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -2.02]}, {"time": 3.1667, "value": -2.02, "curve": [3.369, -2.02, 3.741, -0.65]}, {"time": 4, "value": -0.19}]}, "bone161": {"rotate": [{"value": -1.01, "curve": [0.188, -0.5, 0.375, 0]}, {"time": 0.5, "curve": [0.75, 0, 1.25, -2.02]}, {"time": 1.5, "value": -2.02, "curve": [1.625, -2.02, 1.813, -1.51]}, {"time": 2, "value": -1.01, "curve": [2.188, -0.5, 2.375, 0]}, {"time": 2.5, "curve": [2.75, 0, 3.25, -2.02]}, {"time": 3.5, "value": -2.02, "curve": [3.625, -2.02, 3.813, -1.51]}, {"time": 4, "value": -1.01}]}, "bone38": {"translate": [{"curve": [0.25, 0, 0.75, -2.72, 0.25, 0, 0.75, -6.04]}, {"time": 1, "x": -2.72, "y": -6.04, "curve": [1.25, -2.72, 1.75, 0, 1.25, -6.04, 1.75, 0]}, {"time": 2, "curve": [2.25, 0, 2.75, -2.72, 2.25, 0, 2.75, -6.04]}, {"time": 3, "x": -2.72, "y": -6.04, "curve": [3.25, -2.72, 3.75, 0, 3.25, -6.04, 3.75, 0]}, {"time": 4}]}, "bone29": {"translate": [{"x": -0.92, "y": 0.08, "curve": [0.062, -0.35, 0.118, 0, 0.062, 0.03, 0.118, 0]}, {"time": 0.1667, "curve": [0.417, 0, 0.917, -9.57, 0.417, 0, 0.917, 0.84]}, {"time": 1.1667, "x": -9.57, "y": 0.84, "curve": [1.417, -9.57, 1.917, 0, 1.417, 0.84, 1.917, 0]}, {"time": 2.1667, "curve": [2.417, 0, 2.917, -9.57, 2.417, 0, 2.917, 0.84]}, {"time": 3.1667, "x": -9.57, "y": 0.84, "curve": [3.369, -9.57, 3.741, -3.09, 3.369, 0.84, 3.741, 0.27]}, {"time": 4, "x": -0.92, "y": 0.08}]}}, "attachments": {"default": {"jiemao": {"jiemao": {"deform": [{"time": 2.8333, "offset": 100, "vertices": [0.24445, 0.19373, 0.24457, 0.19376], "curve": [2.875, 0, 2.958, 1]}, {"time": 3, "vertices": [-1.37244, -2.95715, -1.37274, -2.95721, -2.28973, -5.96588, -2.29028, -5.96603, -8.26282, -5.83365, -8.26373, -5.83389, -11.55701, -6.42142, -11.55859, -6.42178, -15.85577, -6.94626, -15.85791, -6.94669, -19.82007, -9.53442, -19.82172, -9.53467, -8.96448, -11.966, -8.96649, -11.96646, -7.92847, -7.0593, -7.92932, -7.05939, -3.34601, -1.48395, -3.34644, -1.48404, -2.19232, -1.94351, -2.19318, -1.94376, -0.86743, -1.47729, -0.8678, -1.47739, -0.40436, -0.4794, -0.40448, -0.4794, -1.0224, -2.07239, -1.02264, -2.07242, -2.52759, -5.31558, -2.52814, -5.31573, -10.03925, -11.05725, -10.04059, -11.05731, -15.85663, -13.62891, -15.85852, -13.62912, -17.97083, -17.08066, -17.97479, -17.08121, -20.14484, -17.00146, -20.14832, -17.00201, -16.9809, -16.70618, -16.98437, -16.7066, -13.75867, -12.97495, -13.76251, -12.97546, -8.70911, -6.95657, -8.71094, -6.95685, -2.80872, -2.47641, -2.8092, -2.47644, 0, 0, 0, 0, -3.68768, -3.90903, -3.68817, -3.90909, -8.41577, -2.08881, -8.41724, -2.08923, -17.14258, -1.95975, -17.14471, -1.9603, -24.08917, -4.92014, -24.09119, -4.92062, -25.88513, -7.70139, -25.88855, -7.7023, -26.36041, -9.72449, -26.36292, -9.72522, -22.9765, -11.32477, -22.97852, -11.32538, -16.34161, -6.72672, -16.34332, -6.72726, -11.47717, -5.09595, -11.479, -5.09628, -5.66138, -1.87128, -5.66327, -1.8718, -1.95447, -3.0065, -1.95477, -3.00659, -0.83765, -1.28848, -0.83783, -1.28857, -1.92505, 0.52689, -1.92615, 0.52689, -3.00824, 1.09088, -3.00958, 1.09067, -7.659, -0.63235, -7.66113, -0.63269, -14.50122, -1.81366, -14.50372, -1.81403, -19.01862, -2.87173, -19.0213, -2.87216, -18.73608, -6.17081, -18.7403, -6.17114, -10.23059, -15.53143, -10.23322, -15.53204, -11.79858, -18.33997, -11.80127, -18.34045, -11.65454, -19.54739, -11.65778, -19.54807, -10.20728, -17.77872, -10.21045, -17.77957, -9.51666, -12.91257, -9.51959, -12.9133, -6.50336, -8.67804, -6.50415, -8.67838, -3.01349, -3.38477, -3.01379, -3.38489], "curve": [3.042, 0, 3.125, 1]}, {"time": 3.1667, "vertices": [-1.37244, -2.95715, -1.37274, -2.95721, -2.28973, -5.96588, -2.29028, -5.96603, -8.26282, -5.83365, -8.26373, -5.83389, -11.55701, -6.42142, -11.55859, -6.42178, -15.85577, -6.94626, -15.85791, -6.94669, -22.48309, -10.15182, -22.48419, -10.15182, -8.96448, -11.966, -8.96649, -11.96646, -7.92847, -7.0593, -7.92932, -7.05939, -3.34601, -1.48395, -3.34644, -1.48404, -2.19232, -1.94351, -2.19318, -1.94376, -0.86743, -1.47729, -0.8678, -1.47739, -0.40436, -0.4794, -0.40448, -0.4794, -1.0224, -2.07239, -1.02264, -2.07242, -2.52759, -5.31558, -2.52814, -5.31573, -10.03925, -11.05725, -10.04059, -11.05731, -14.97144, -13.11221, -14.97327, -13.11237, -18.35901, -17.75836, -18.36285, -17.75885, -20.73749, -17.65567, -20.7406, -17.65607, -18.92822, -16.58401, -18.9314, -16.58438, -15.88599, -12.62552, -15.88965, -12.62604, -11.15424, -6.67386, -11.15527, -6.67392, -8.58038, -1.4996, -8.57983, -1.4989, 0, 0, 0, 0, -3.68768, -3.90903, -3.68817, -3.90909, -8.85284, -4.25955, -8.85358, -4.25967, -16.49146, -6.65393, -16.49292, -6.65405, -25.13934, -7.22635, -25.14105, -7.22653, -28.56842, -10.67197, -28.57068, -10.67261, -29.34344, -11.44, -29.34509, -11.4407, -22.9765, -11.32477, -22.97852, -11.32538, -16.34161, -6.72672, -16.34332, -6.72726, -11.47717, -5.09595, -11.479, -5.09628, -5.66138, -1.87128, -5.66327, -1.8718, -1.95447, -3.0065, -1.95477, -3.00659, -0.83765, -1.28848, -0.83783, -1.28857, -1.92505, 0.52689, -1.92615, 0.52689, -3.00824, 1.09088, -3.00958, 1.09067, -15.95477, 1.16718, -15.95587, 1.16748, -23.5672, -2.159, -23.56738, -2.15851, -29.0083, -3.1196, -29.0097, -3.1196, -26.47443, -6.66357, -26.4776, -6.66342, -10.23059, -15.53143, -10.23322, -15.53204, -11.79858, -18.33997, -11.80127, -18.34045, -11.94861, -19.41034, -11.95142, -19.41089, -10.20728, -17.77872, -10.21045, -17.77957, -8.88257, -12.78, -8.88507, -12.78064, -6.50336, -8.67804, -6.50415, -8.67838, -2.94281, -2.77371, -2.94305, -2.7738], "curve": [3.208, 0, 3.292, 1]}, {"time": 3.3333, "offset": 100, "vertices": [0.24445, 0.19373, 0.24457, 0.19376]}]}}, "lian": {"lian": {"deform": [{"time": 2.8333, "offset": 278, "vertices": [0.17987, 0.14011, 0.17987, 0.14011], "curve": [2.875, 0, 2.958, 1]}, {"time": 3, "offset": 20, "vertices": [-3.03528, -2.4173, -3.03467, -2.4173, -6.70892, -2.69431, -6.70813, -2.69424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, -15.65527, -6.70544, -15.65277, -6.70474, -26.73505, -14.50131, -26.73297, -14.50092, -29.48938, -18.69019, -29.48651, -18.68991, -24.39886, -18.40363, -24.39722, -18.40344, -20.07153, -14.33945, -20.06964, -14.33914, -12.71594, -8.06924, -12.71399, -8.06903, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61328, -0.48843, -0.61316, -0.48843, -4.69055, -3.23456, -4.68982, -3.2345, -5.63904, -2.98776, -5.63812, -2.98758, -2.54132, -0.27008, -2.54102, -0.26999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0094, 1.83319, 1.00995, 1.83334, 0.75549, 0.60181, 0.75574, 0.60181, 1.23352, 0.77673, 1.23389, 0.77673, 1.38281, 0.07233, 1.38324, 0.07242, 2.18738, 0.09573, 2.18817, 0.09589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.37903, -7.50079, -5.37811, -7.50055, -9.27417, -13.06271, -9.27319, -13.06247, -12.49634, -17.33194, -12.49518, -17.33154, -14.53308, -18.57562, -14.53198, -18.57535, -16.85986, -14.94177, -16.85822, -14.94162, -9.33264, -8.00055, -9.33191, -8.00052, -0.74176, -0.96918, -0.74164, -0.96918, 0, 0, 0, 0, 0.07062, 0.62381, 0.07074, 0.62384, 0.53363, 0.99261, 0.53381, 0.99268, 2.15234, 1.33575, 2.15259, 1.33578, 1.45776, 0.78253, 1.45795, 0.78256, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648], "curve": [3.042, 0, 3.125, 1]}, {"time": 3.1667, "offset": 20, "vertices": [-3.03528, -2.4173, -3.03467, -2.4173, -6.70892, -2.69431, -6.70813, -2.69424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, -18.77032, -6.49435, -18.76788, -6.49396, -28.38104, -15.80576, -28.37897, -15.80542, -30.02228, -19.11255, -30.01947, -19.1123, -25.43958, -19.00009, -25.43799, -18.99997, -20.07153, -14.33945, -20.06964, -14.33914, -12.71594, -8.06924, -12.71399, -8.06903, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61328, -0.48843, -0.61316, -0.48843, -4.69055, -3.23456, -4.68982, -3.2345, -5.63904, -2.98776, -5.63812, -2.98758, -2.54132, -0.27008, -2.54102, -0.26999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0094, 1.83319, 1.00995, 1.83334, 2.33911, 2.66074, 2.33929, 2.66043, 3.44897, 1.1929, 3.44934, 1.1926, 3.90411, -0.74271, 3.9046, -0.74295, 4.70862, -0.71942, 4.70959, -0.71951, 1.55713, -0.10559, 1.55725, -0.10574, 0, 0, 0, 0, 0, 0, -5.37903, -7.50079, -5.37811, -7.50055, -9.27417, -13.06271, -9.27319, -13.06247, -12.49634, -17.33194, -12.49518, -17.33154, -14.53308, -18.57562, -14.53198, -18.57535, -16.85986, -14.94177, -16.85822, -14.94162, -9.33264, -8.00055, -9.33191, -8.00052, -0.74176, -0.96918, -0.74164, -0.96918, 0, 0, 0, 0, 0.52478, 1.25668, 0.52478, 1.25671, 2.91663, 3.15381, 2.9165, 3.15387, 5.51086, 4.21414, 5.51093, 4.21411, 4.49994, 2.2861, 4.49969, 2.28592, 2.19135, 0.50781, 2.19116, 0.50778, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648], "curve": [3.208, 0, 3.292, 1]}, {"time": 3.3333, "offset": 278, "vertices": [0.17987, 0.14011, 0.17987, 0.14011]}]}}}}}, "xiuxian": {"slots": {"biyan": {"attachment": [{"time": 0.4333, "name": "biyan"}, {"time": 0.8667}, {"time": 3.5667, "name": "biyan"}, {"time": 3.7667}]}}, "bones": {"bone": {"rotate": [{"curve": [0.167, 0, 0.5, -2.3]}, {"time": 0.6667, "value": -2.3, "curve": [0.786, -5.28, 0.923, -0.87]}, {"time": 1, "value": 1.98, "curve": [1.083, 1.98, 1.25, 2.43]}, {"time": 1.3333, "value": 2.43, "curve": [1.417, 2.43, 1.583, 1.5]}, {"time": 1.6667, "value": 1.5, "curve": [1.75, 1.5, 1.917, 1.98]}, {"time": 2, "value": 1.98, "curve": "stepped"}, {"time": 2.8333, "value": 1.98, "curve": [3.047, 1.42, 3.338, 0]}, {"time": 3.5}], "translate": [{"curve": [0.167, 0, 0.5, 9.32, 0.167, 0, 0.5, -6.89]}, {"time": 0.6667, "x": 9.32, "y": -6.89, "curve": [0.786, 26.03, 0.923, 1.3, 0.786, -11.33, 0.923, -4.76]}, {"time": 1, "x": -14.68, "y": -0.51, "curve": [1.083, -14.68, 1.25, -22.82, 1.083, -0.51, 1.25, -0.87]}, {"time": 1.3333, "x": -22.82, "y": -0.87, "curve": [1.417, -22.82, 1.583, -14.83, 1.417, -0.87, 1.583, -0.66]}, {"time": 1.6667, "x": -14.83, "y": -0.66, "curve": [1.75, -14.83, 1.917, -15.81, 1.75, -0.66, 1.917, -0.69]}, {"time": 2, "x": -15.81, "y": -0.69, "curve": "stepped"}, {"time": 2.8333, "x": -15.81, "y": -0.69, "curve": [3.047, -11.29, 3.338, 0, 3.047, -4.83, 3.338, -15.19]}, {"time": 3.5, "y": -15.19, "curve": [3.75, 0, 4.25, 0, 3.75, -15.19, 4.25, 0]}, {"time": 4.5}]}, "bone30": {"rotate": [{"curve": [0.167, 0, 0.5, -0.66]}, {"time": 0.6667, "value": -0.66, "curve": [0.786, -1.6, 0.923, -0.21]}, {"time": 1, "value": 0.7, "curve": [1.083, 0.7, 1.25, 1.15]}, {"time": 1.3333, "value": 1.15, "curve": [1.417, 1.15, 1.583, 0.22]}, {"time": 1.6667, "value": 0.22, "curve": [1.75, 0.22, 1.917, 0.7]}, {"time": 2, "value": 0.7, "curve": "stepped"}, {"time": 2.8333, "value": 0.7, "curve": [3.047, 0.37, 3.338, -0.45]}, {"time": 3.5, "value": -0.45, "curve": [3.75, -0.45, 4.25, 0]}, {"time": 4.5}]}, "bone31": {"rotate": [{"curve": [0.167, 0, 0.5, -0.48]}, {"time": 0.6667, "value": -0.48, "curve": [0.786, -2.07, 0.923, 0.29]}, {"time": 1, "value": 1.81, "curve": [1.083, 1.81, 1.25, 2.26]}, {"time": 1.3333, "value": 2.26, "curve": [1.417, 2.26, 1.583, 1.33]}, {"time": 1.6667, "value": 1.33, "curve": [1.75, 1.33, 1.917, 1.81]}, {"time": 2, "value": 1.81, "curve": "stepped"}, {"time": 2.8333, "value": 1.81, "curve": [3.047, 1.54, 3.338, 0.87]}, {"time": 3.5, "value": 0.87, "curve": [3.75, 0.87, 4.25, 0]}, {"time": 4.5}], "translate": [{"time": 2.8333, "curve": [3.047, -0.23, 3.338, -0.79, 3.047, -0.04, 3.338, -0.12]}, {"time": 3.5, "x": -0.79, "y": -0.12, "curve": [3.75, -0.79, 4.25, 0, 3.75, -0.12, 4.25, 0]}, {"time": 4.5}]}, "bone32": {"rotate": [{"curve": [0.167, 0, 0.5, 0.66]}, {"time": 0.6667, "value": 0.66, "curve": [0.786, -0.49, 0.923, 1.22]}, {"time": 1, "value": 2.32, "curve": [1.1, 2.32, 1.3, 2.77]}, {"time": 1.4, "value": 2.77, "curve": [1.483, 2.77, 1.65, 1.84]}, {"time": 1.7333, "value": 1.84, "curve": [1.817, 1.84, 1.983, 2.32]}, {"time": 2.0667, "value": 2.32, "curve": "stepped"}, {"time": 2.8333, "value": 2.32, "curve": [3.047, 2.29, 3.338, 2.2]}, {"time": 3.5, "value": 2.2, "curve": [3.75, 2.2, 4.25, 0]}, {"time": 4.5}], "translate": [{"curve": [0.167, 0, 0.5, -6.41, 0.167, 0, 0.5, 2.29]}, {"time": 0.6667, "x": -6.41, "y": 2.29, "curve": [0.786, -10.87, 0.923, -4.27, 0.786, 3.88, 0.923, 1.52]}, {"time": 1, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, -0.65, 3.338, -2.29, 3.047, 0.24, 3.338, 0.83]}, {"time": 3.5, "x": -2.29, "y": 0.83, "curve": [3.75, -2.29, 4.25, 0, 3.75, 0.83, 4.25, 0]}, {"time": 4.5}], "scale": [{"time": 2.8333, "curve": [3.047, 0.997, 3.338, 0.99, 3.047, 1, 3.338, 1]}, {"time": 3.5, "x": 0.99, "curve": [3.75, 0.99, 4.25, 1, 3.75, 1, 4.25, 1]}, {"time": 4.5}]}, "bone33": {"rotate": [{"curve": [0.167, 0, 0.5, -2.64]}, {"time": 0.6667, "value": -2.64, "curve": [0.786, -5.78, 0.923, -1.13]}, {"time": 1, "value": 1.87, "curve": [1.117, 1.87, 1.35, 2.33]}, {"time": 1.4667, "value": 2.33, "curve": [1.55, 2.33, 1.717, 1.4]}, {"time": 1.8, "value": 1.4, "curve": [1.883, 1.4, 2.05, 1.88]}, {"time": 2.1333, "value": 1.88, "curve": "stepped"}, {"time": 2.8333, "value": 1.88, "curve": [3.047, 1.62, 3.338, 0.97]}, {"time": 3.5, "value": 0.97, "curve": [3.75, 0.97, 4.25, 0]}, {"time": 4.5}], "translate": [{"curve": [0.167, 0, 0.5, -4.18, 0.167, 0, 0.5, -0.01]}, {"time": 0.6667, "x": -4.18, "y": -0.01, "curve": [0.786, -7.09, 0.923, -2.79, 0.786, -0.01, 0.923, 0]}, {"time": 1, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, -1.1, 3.338, -3.85, 3.047, -0.06, 3.338, -0.21]}, {"time": 3.5, "x": -3.85, "y": -0.21, "curve": [3.75, -3.85, 4.25, 0, 3.75, -0.21, 4.25, 0]}, {"time": 4.5}], "scale": [{"time": 2.8333, "curve": [3.047, 0.994, 3.338, 0.977, 3.047, 1, 3.338, 1]}, {"time": 3.5, "x": 0.977, "curve": [3.75, 0.977, 4.25, 1, 3.75, 1, 4.25, 1]}, {"time": 4.5}]}, "bone3": {"scale": [{"time": 2.8333, "curve": [3.022, 0.993, 3.261, 0.975, 3.022, 1, 3.261, 1]}, {"time": 3.5, "x": 0.957, "curve": [3.75, 0.957, 4.25, 1, 3.75, 1, 4.25, 1]}, {"time": 4.5}]}, "bone4": {"scale": [{"time": 2.8333, "curve": [3.022, 0.999, 3.261, 0.996, 3.022, 1, 3.261, 1]}, {"time": 3.5, "x": 0.993, "curve": [3.75, 0.993, 4.25, 1, 3.75, 1, 4.25, 1]}, {"time": 4.5}]}, "bone6": {"scale": [{"time": 2.8333, "curve": [3.022, 0.996, 3.261, 0.984, 3.022, 1, 3.261, 1]}, {"time": 3.5, "x": 0.972, "curve": [3.75, 0.972, 4.25, 1, 3.75, 1, 4.25, 1]}, {"time": 4.5}]}, "bone7": {"scale": [{"time": 2.8333, "curve": [3.022, 0.998, 3.261, 0.992, 3.022, 1, 3.261, 1]}, {"time": 3.5, "x": 0.986, "curve": [3.75, 0.986, 4.25, 1, 3.75, 1, 4.25, 1]}, {"time": 4.5}]}, "bone8": {"rotate": [{"curve": [0.192, 0, 0.575, -1.3]}, {"time": 0.7667, "value": -1.3, "curve": [0.886, -4.19, 1.023, 0.09]}, {"time": 1.1, "value": 2.86, "curve": [1.183, 2.86, 1.35, 3.64]}, {"time": 1.4333, "value": 3.64, "curve": [1.517, 3.64, 1.683, -1.6]}, {"time": 1.7667, "value": -1.6, "curve": [1.877, -0.68, 1.988, 0.27]}, {"time": 2.1, "value": 1.25, "curve": [2.32, 0.84, 2.543, 0.43]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.011, 0.18, 3.255, 1.23]}, {"time": 3.5, "value": 2.28, "curve": [3.75, 2.28, 4.25, 0]}, {"time": 4.5}]}, "bone9": {"rotate": [{"value": 0.22, "curve": [0.322, -0.73, 0.615, -1.3]}, {"time": 0.8667, "value": -1.3, "curve": [0.986, -4.19, 1.123, 0.09]}, {"time": 1.2, "value": 2.86, "curve": [1.283, 2.86, 1.45, 3.64]}, {"time": 1.5333, "value": 3.64, "curve": [1.617, 3.64, 1.783, -1.6]}, {"time": 1.8667, "value": -1.6, "curve": [1.977, -0.68, 2.088, 0.27]}, {"time": 2.2, "value": 1.25, "curve": [2.409, 0.84, 2.621, 0.43]}, {"time": 2.8333, "curve": "stepped"}, {"time": 2.8667, "curve": [3.067, 0, 3.367, 1.14]}, {"time": 3.6667, "value": 2.28, "curve": [3.869, 2.28, 4.241, 0.73]}, {"time": 4.5, "value": 0.22}]}, "bone10": {"rotate": [{"value": 0.65, "curve": [0.37, -0.46, 0.711, -1.3]}, {"time": 0.9667, "value": -1.3, "curve": [1.086, -4.19, 1.223, 0.09]}, {"time": 1.3, "value": 2.86, "curve": [1.383, 2.86, 1.55, 3.64]}, {"time": 1.6333, "value": 3.64, "curve": [1.717, 3.64, 1.883, -1.6]}, {"time": 1.9667, "value": -1.6, "curve": [2.077, -0.68, 2.188, 0.27]}, {"time": 2.3, "value": 1.25, "curve": [2.476, 0.84, 2.654, 0.43]}, {"time": 2.8333, "curve": "stepped"}, {"time": 2.9667, "curve": [3.183, 0, 3.508, 1.14]}, {"time": 3.8333, "value": 2.28, "curve": [3.995, 2.28, 4.266, 1.3]}, {"time": 4.5, "value": 0.65}]}, "bone11": {"rotate": [{"value": 1.14, "curve": [0.4, -0.08, 0.8, -1.3]}, {"time": 1.0667, "value": -1.3, "curve": [1.186, -4.19, 1.323, 0.09]}, {"time": 1.4, "value": 2.86, "curve": [1.483, 2.86, 1.65, 3.64]}, {"time": 1.7333, "value": 3.64, "curve": [1.817, 3.64, 1.983, -1.6]}, {"time": 2.0667, "value": -1.6, "curve": [2.177, -0.68, 2.288, 0.27]}, {"time": 2.4, "value": 1.25, "curve": [2.62, 0.84, 2.843, 0.43]}, {"time": 3.0667, "curve": [3.175, 0, 3.338, 0.57]}, {"time": 3.5, "value": 1.14, "curve": [3.688, 1.71, 3.875, 2.28]}, {"time": 4, "value": 2.28, "curve": [4.125, 2.28, 4.313, 1.71]}, {"time": 4.5, "value": 1.14}]}, "bone12": {"rotate": [{"value": -0.28, "curve": [0.247, 1.67, 0.473, 2.84]}, {"time": 0.6667, "value": 2.84, "curve": [0.786, 7, 0.923, 0.85]}, {"time": 1, "value": -3.13, "curve": [1.083, -3.13, 1.25, -4.93]}, {"time": 1.3333, "value": -4.93, "curve": [1.417, -4.93, 1.583, 1.53]}, {"time": 1.6667, "value": 1.53, "curve": [1.777, 0.56, 1.888, -0.45]}, {"time": 2, "value": -1.49, "curve": [2.109, -1.02, 2.221, -0.51]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.09, -0.73, 3.378, -1.8]}, {"time": 3.6667, "value": -2.88, "curve": [3.869, -2.88, 4.241, -0.93]}, {"time": 4.5, "value": -0.28}]}, "bone13": {"rotate": [{"value": -0.82, "curve": [0.293, 1.27, 0.564, 2.84]}, {"time": 0.7667, "value": 2.84, "curve": [0.886, 7, 1.023, 0.85]}, {"time": 1.1, "value": -3.13, "curve": [1.183, -3.13, 1.35, -4.93]}, {"time": 1.4333, "value": -4.93, "curve": [1.517, -4.93, 1.683, 1.53]}, {"time": 1.7667, "value": 1.53, "curve": [1.877, 0.56, 1.988, -0.45]}, {"time": 2.1, "value": -1.49, "curve": [2.209, -1.02, 2.321, -0.51]}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.136, -0.67, 3.485, -1.78]}, {"time": 3.8333, "value": -2.88, "curve": [3.995, -2.88, 4.266, -1.64]}, {"time": 4.5, "value": -0.82}]}, "bone14": {"rotate": [{"value": -1.44, "curve": [0.325, 0.7, 0.65, 2.84]}, {"time": 0.8667, "value": 2.84, "curve": [0.986, 7, 1.123, 0.85]}, {"time": 1.2, "value": -3.13, "curve": [1.283, -3.13, 1.45, -4.93]}, {"time": 1.5333, "value": -4.93, "curve": [1.617, -4.93, 1.783, 1.53]}, {"time": 1.8667, "value": 1.53, "curve": [1.977, 0.56, 2.088, -0.45]}, {"time": 2.2, "value": -1.49, "curve": [2.309, -1.02, 2.421, -0.51]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.031, -0.3, 3.265, -0.87]}, {"time": 3.5, "value": -1.44, "curve": [3.688, -2.16, 3.875, -2.88]}, {"time": 4, "value": -2.88, "curve": [4.125, -2.88, 4.313, -2.16]}, {"time": 4.5, "value": -1.44}]}, "bone15": {"rotate": [{"value": -2.06, "curve": [0.34, -0.1, 0.732, 2.84]}, {"time": 0.9667, "value": 2.84, "curve": [1.086, 7, 1.223, 0.85]}, {"time": 1.3, "value": -3.13, "curve": [1.383, -3.13, 1.55, -4.93]}, {"time": 1.6333, "value": -4.93, "curve": [1.717, -4.93, 1.883, 1.53]}, {"time": 1.9667, "value": 1.53, "curve": [2.077, 0.56, 2.188, -0.45]}, {"time": 2.3, "value": -1.49, "curve": [2.409, -1.02, 2.521, -0.51]}, {"time": 2.6333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.024, -0.35, 3.262, -1.21]}, {"time": 3.5, "value": -2.06, "curve": [3.734, -2.39, 4.005, -2.88]}, {"time": 4.1667, "value": -2.88, "curve": [4.255, -2.88, 4.373, -2.53]}, {"time": 4.5, "value": -2.06}]}, "bone28": {"translate": [{"x": -1.69, "y": -1.3, "curve": [0.309, -5.74, 0.591, -8.16, 0.309, -7.33, 0.591, -10.95]}, {"time": 0.8333, "x": -8.16, "y": -10.95, "curve": [0.953, -25.89, 1.09, 0.34, 0.953, -15.38, 1.09, -8.83]}, {"time": 1.1667, "x": 17.31, "y": -4.58, "curve": [1.25, 17.31, 1.417, 23.8, 1.25, -4.58, 1.417, -2.96]}, {"time": 1.5, "x": 23.8, "y": -2.96, "curve": [1.583, 23.8, 1.75, -8.75, 1.583, -2.96, 1.75, -3.07]}, {"time": 1.8333, "x": -8.75, "y": -3.07, "curve": [1.917, -8.75, 2.083, -0.6, 1.917, -3.07, 2.083, -1.03]}, {"time": 2.1667, "x": -0.6, "y": -1.03, "curve": [2.25, -0.6, 2.417, -5.78, 2.25, -1.03, 2.417, -4.73]}, {"time": 2.5, "x": -5.78, "y": -4.73, "curve": [2.583, -5.78, 2.75, 0, 2.583, -4.73, 2.75, 0]}, {"time": 2.8333, "curve": [3.042, 0, 3.458, -17.55, 3.042, 0, 3.458, -13.5]}, {"time": 3.6667, "x": -17.55, "y": -13.5, "curve": [3.875, -17.55, 4.292, -1.69, 3.875, -13.5, 4.292, -1.3]}, {"time": 4.5, "x": -1.69, "y": -1.3}]}, "bone29": {"translate": [{"x": -3.01, "y": 0.26, "curve": [0.309, -12.47, 0.591, -18.14, 0.309, -0.44, 0.591, -0.86]}, {"time": 0.8333, "x": -18.14, "y": -0.86, "curve": [0.971, -33.33, 1.134, 1.7, 0.971, -4.14, 1.134, 3.43]}, {"time": 1.1667, "x": 5.87, "y": 4.33, "curve": [1.25, 5.87, 1.417, 9.27, 1.25, 4.33, 1.417, 5.56]}, {"time": 1.5, "x": 9.27, "y": 5.56, "curve": [1.583, 9.27, 1.75, -13.14, 1.583, 5.56, 1.75, -4.73]}, {"time": 1.8333, "x": -13.14, "y": -4.73, "curve": [1.917, -13.14, 2.083, 6.78, 1.917, -4.73, 2.083, 2.44]}, {"time": 2.1667, "x": 6.78, "y": 2.44, "curve": [2.25, 6.78, 2.417, -6.77, 2.25, 2.44, 2.417, -2.44]}, {"time": 2.5, "x": -6.77, "y": -2.44, "curve": [2.583, -6.77, 2.75, 0, 2.583, -2.44, 2.75, 0]}, {"time": 2.8333, "curve": [3.042, 0, 3.458, -24.63, 3.042, 0, 3.458, 5.11]}, {"time": 3.6667, "x": -24.63, "y": 5.11, "curve": [3.875, -24.63, 4.292, -3.01, 3.875, 5.11, 4.292, 0.26]}, {"time": 4.5, "x": -3.01, "y": 0.26}]}, "bone34": {"rotate": [{"curve": [0.167, 0, 0.5, -0.55]}, {"time": 0.6667, "value": -0.55, "curve": [0.786, -1.07, 0.923, -0.29]}, {"time": 1, "value": 0.21, "curve": [1.125, 0.21, 1.375, 0.66]}, {"time": 1.5, "value": 0.66, "curve": [1.583, 0.66, 1.75, -0.27]}, {"time": 1.8333, "value": -0.27, "curve": [1.917, -0.27, 2.083, 0.21]}, {"time": 2.1667, "value": 0.21, "curve": "stepped"}, {"time": 2.8333, "value": 0.21, "curve": [3.047, -0.09, 3.338, -0.84]}, {"time": 3.5, "value": -0.84, "curve": [3.75, -0.84, 4.25, 0]}, {"time": 4.5}]}, "bone35": {"rotate": [{"curve": [0.167, 0, 0.5, 0.89]}, {"time": 0.6667, "value": 0.89, "curve": [0.786, -0.6, 0.923, 1.6]}, {"time": 1, "value": 3.01, "curve": [1.142, 3.01, 1.425, 3.47]}, {"time": 1.5667, "value": 3.47, "curve": [1.65, 3.47, 1.817, 2.54]}, {"time": 1.9, "value": 2.54, "curve": [1.983, 2.54, 2.15, 3.02]}, {"time": 2.2333, "value": 3.02, "curve": "stepped"}, {"time": 2.8333, "value": 3.02, "curve": [3.047, 0.79, 3.338, -4.79]}, {"time": 3.5, "value": -4.79, "curve": [3.75, -4.79, 4.25, 0]}, {"time": 4.5}]}, "bone36": {"rotate": [{"curve": [0.167, 0, 0.5, -1.44]}, {"time": 0.6667, "value": -1.44, "curve": [0.786, -8.93, 0.923, 2.15]}, {"time": 1, "value": 9.31, "curve": [1.158, 9.31, 1.475, 9.76]}, {"time": 1.6333, "value": 9.76, "curve": [1.717, 9.76, 1.883, 8.83]}, {"time": 1.9667, "value": 8.83, "curve": [2.05, 8.83, 2.217, 9.31]}, {"time": 2.3, "value": 9.31, "curve": "stepped"}, {"time": 2.8333, "value": 9.31, "curve": [3.047, 6.37, 3.338, -1]}, {"time": 3.5, "value": -1, "curve": [3.75, -1, 4.25, 0]}, {"time": 4.5}]}, "bone37": {"translate": [{"curve": [0.167, 0, 0.5, -11.69, 0.167, 0, 0.5, 0.11]}, {"time": 0.6667, "x": -11.69, "y": 0.11, "curve": [0.786, -35.94, 0.923, -0.05, 0.786, 15.71, 0.923, -7.37]}, {"time": 1, "x": 23.16, "y": -22.3, "curve": [1.083, 23.16, 1.25, 24.63, 1.083, -22.3, 1.25, -27.58]}, {"time": 1.3333, "x": 24.63, "y": -27.58, "curve": [1.417, 24.63, 1.583, 0, 1.417, -27.58, 1.583, 0]}, {"time": 1.6667, "curve": [1.75, 0, 1.917, 5.09, 1.75, 0, 1.917, -7.8]}, {"time": 2, "x": 5.09, "y": -7.8, "curve": [2.083, 5.09, 2.25, 2.77, 2.083, -7.8, 2.25, -4.46]}, {"time": 2.3333, "x": 2.77, "y": -4.46, "curve": "stepped"}, {"time": 3.5, "x": 2.77, "y": -4.46, "curve": [3.75, 2.77, 4.25, 0, 3.75, -4.46, 4.25, 0]}, {"time": 4.5}]}, "bone38": {"translate": [{"curve": [0.222, 0, 0.554, 0, 0.222, 0, 0.646, 0]}, {"time": 0.6667, "x": -15.77, "y": 1.52, "curve": [0.813, -36.24, 0.917, 14.16, 0.813, 12.39, 0.917, -14.37]}, {"time": 1, "x": 14.16, "y": -14.37, "curve": [1.083, 14.16, 1.25, 14.16, 1.083, -14.37, 1.25, -13.98]}, {"time": 1.3333, "x": 14.16, "y": -13.98, "curve": [1.417, 14.16, 1.583, -1.91, 1.417, -13.98, 1.583, -2.54]}, {"time": 1.6667, "x": -1.91, "y": -2.54, "curve": [1.75, -1.91, 1.917, 0.71, 1.75, -2.54, 1.917, -5.98]}, {"time": 2, "x": 0.71, "y": -5.98, "curve": [2.083, 0.71, 2.25, -0.06, 2.083, -5.98, 2.25, -5.22]}, {"time": 2.3333, "x": -0.06, "y": -5.22, "curve": "stepped"}, {"time": 2.7333, "x": -0.06, "y": -5.22, "curve": [2.883, -0.06, 3.183, -2.44, 2.883, -5.22, 3.183, 4.58]}, {"time": 3.3333, "x": -2.44, "y": 4.58, "curve": [3.417, -2.44, 3.583, -6.68, 3.417, 4.58, 3.583, 8.1]}, {"time": 3.6667, "x": -6.68, "y": 8.1, "curve": [3.75, -6.68, 3.917, 0, 3.75, 8.1, 3.917, 0]}, {"time": 4}]}, "bone40": {"rotate": [{"curve": [0.167, 0, 0.5, 0.34]}, {"time": 0.6667, "value": 0.34, "curve": [0.786, -0.42, 0.923, 0.7]}, {"time": 1, "value": 1.42, "curve": [1.083, 1.42, 1.25, 5.24]}, {"time": 1.3333, "value": 5.24, "curve": [1.417, 5.24, 1.583, -1.49]}, {"time": 1.6667, "value": -1.49, "curve": [1.75, -1.49, 1.917, 0.39]}, {"time": 2, "value": 0.39, "curve": [2.083, 0.39, 2.25, -0.38]}, {"time": 2.3333, "value": -0.38, "curve": [2.417, -0.38, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.136, 0.02, 3.43, 0.4]}, {"time": 3.5, "value": 0.46, "curve": [3.75, 0.46, 4.25, 0]}, {"time": 4.5}]}, "bone41": {"rotate": [{"value": 0.04, "curve": [0.247, 0.23, 0.473, 0.34]}, {"time": 0.6667, "value": 0.34, "curve": [0.786, 0.22, 0.923, 0.4]}, {"time": 1, "value": 0.51, "curve": [1.083, 0.51, 1.25, 5.38]}, {"time": 1.3333, "value": 5.38, "curve": [1.417, 5.38, 1.583, -2.46]}, {"time": 1.6667, "value": -2.46, "curve": [1.75, -2.46, 1.917, 1.47]}, {"time": 2, "value": 1.47, "curve": [2.083, 1.47, 2.25, -0.38]}, {"time": 2.3333, "value": -0.38, "curve": [2.417, -0.38, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.01, 3.338, 0.04]}, {"time": 3.5, "value": 0.04, "curve": [3.562, 0.3, 3.618, 0.46]}, {"time": 3.6667, "value": 0.46, "curve": [3.869, 0.46, 4.241, 0.15]}, {"time": 4.5, "value": 0.04}]}, "bone42": {"rotate": [{"value": 0.13, "curve": [0.255, 0.25, 0.49, 0.34]}, {"time": 0.6667, "value": 0.34, "curve": [0.786, 0.22, 0.923, 0.4]}, {"time": 1, "value": 0.51, "curve": [1.083, 0.51, 1.25, 5.38]}, {"time": 1.3333, "value": 5.38, "curve": [1.442, 5.38, 1.658, -2.46]}, {"time": 1.7667, "value": -2.46, "curve": [1.85, -2.46, 2.017, 1.47]}, {"time": 2.1, "value": 1.47, "curve": [2.183, 1.47, 2.35, -0.38]}, {"time": 2.4333, "value": -0.38, "curve": [2.517, -0.38, 2.683, 0]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.024, 0.02, 3.336, 0.13]}, {"time": 3.5, "value": 0.13, "curve": [3.627, 0.32, 3.745, 0.46]}, {"time": 3.8333, "value": 0.46, "curve": [3.995, 0.46, 4.266, 0.26]}, {"time": 4.5, "value": 0.13}]}, "bone43": {"rotate": [{"value": 1.17, "curve": [0.25, 0.75, 0.5, 0.34]}, {"time": 0.6667, "value": 0.34, "curve": [0.786, 0.22, 0.923, 0.4]}, {"time": 1, "value": 0.51, "curve": [1.083, 0.51, 1.25, 5.38]}, {"time": 1.3333, "value": 5.38, "curve": [1.467, 5.38, 1.733, -2.46]}, {"time": 1.8667, "value": -2.46, "curve": [1.95, -2.46, 2.117, 1.47]}, {"time": 2.2, "value": 1.47, "curve": [2.283, 1.47, 2.45, -0.38]}, {"time": 2.5333, "value": -0.38, "curve": [2.607, -0.38, 2.747, -0.08]}, {"time": 2.8333, "value": -0.02, "curve": [2.845, -0.01, 2.857, 0]}, {"time": 2.8667, "curve": [3.025, 0, 3.342, 1.17]}, {"time": 3.5, "value": 1.17, "curve": [3.688, 1.75, 3.875, 2.34]}, {"time": 4, "value": 2.34, "curve": [4.125, 2.34, 4.313, 1.75]}, {"time": 4.5, "value": 1.17}]}, "bone44": {"rotate": [{"value": 1.67, "curve": [0.234, 1.14, 0.505, 0.34]}, {"time": 0.6667, "value": 0.34, "curve": [0.786, -10.58, 0.923, 5.58]}, {"time": 1, "value": 16.02, "curve": [1.083, 16.02, 1.25, 13.14]}, {"time": 1.3333, "value": 13.14, "curve": [1.492, 13.14, 1.808, -2.46]}, {"time": 1.9667, "value": -2.46, "curve": [2.05, -2.46, 2.217, 1.47]}, {"time": 2.3, "value": 1.47, "curve": [2.383, 1.47, 2.55, -0.38]}, {"time": 2.6333, "value": -0.38, "curve": [2.682, -0.38, 2.761, -0.25]}, {"time": 2.8333, "value": -0.14, "curve": [2.884, -0.06, 2.932, 0]}, {"time": 2.9667, "curve": [3.1, 0, 3.367, 1.67]}, {"time": 3.5, "value": 1.67, "curve": [3.734, 1.94, 4.005, 2.34]}, {"time": 4.1667, "value": 2.34, "curve": [4.255, 2.34, 4.373, 2.05]}, {"time": 4.5, "value": 1.67}]}, "bone45": {"rotate": [{"value": 2.03, "curve": [0.214, 1.55, 0.505, 0.34]}, {"time": 0.6667, "value": 0.34, "curve": [0.786, -10.58, 0.923, 5.58]}, {"time": 1, "value": 16.02, "curve": [1.083, 16.02, 1.25, 13.14]}, {"time": 1.3333, "value": 13.14, "curve": [1.517, 13.14, 1.883, -2.46]}, {"time": 2.0667, "value": -2.46, "curve": [2.15, -2.46, 2.317, 1.47]}, {"time": 2.4, "value": 1.47, "curve": [2.483, 1.47, 2.65, -0.38]}, {"time": 2.7333, "value": -0.38, "curve": [2.76, -0.38, 2.795, -0.35]}, {"time": 2.8333, "value": -0.29, "curve": [2.914, -0.18, 3.01, 0]}, {"time": 3.0667, "curve": [3.175, 0, 3.392, 2.03]}, {"time": 3.5, "value": 2.03, "curve": [3.756, 2.12, 4.106, 2.34]}, {"time": 4.3, "value": 2.34, "curve": [4.357, 2.34, 4.425, 2.22]}, {"time": 4.5, "value": 2.03}]}, "bone46": {"rotate": [{"value": 2.28, "curve": [0.185, 2.05, 0.502, 0.34]}, {"time": 0.6667, "value": 0.34, "curve": [0.786, -10.58, 0.923, 5.58]}, {"time": 1, "value": 16.02, "curve": [1.083, 16.02, 1.25, 13.14]}, {"time": 1.3333, "value": 13.14, "curve": [1.542, 13.14, 1.958, -2.46]}, {"time": 2.1667, "value": -2.46, "curve": [2.25, -2.46, 2.417, 1.47]}, {"time": 2.5, "value": 1.47, "curve": [2.583, 1.47, 2.75, -0.38]}, {"time": 2.8333, "value": -0.38, "curve": [2.917, -0.38, 3.083, 0]}, {"time": 3.1667, "curve": [3.25, 0, 3.417, 2.28]}, {"time": 3.5, "value": 2.28, "curve": [3.759, 2.29, 4.203, 2.34]}, {"time": 4.4333, "value": 2.34, "curve": [4.454, 2.34, 4.477, 2.32]}, {"time": 4.5, "value": 2.28}]}, "bone47": {"rotate": [{"value": 2.28, "curve": [0.235, -1.18, 0.458, -3.03]}, {"time": 0.6667, "value": -3.03, "curve": [0.786, -16.29, 0.923, 3.33]}, {"time": 1, "value": 16.02, "curve": [1.083, 16.02, 1.25, 13.14]}, {"time": 1.3333, "value": 13.14, "curve": [1.567, 13.14, 2.033, -2.46]}, {"time": 2.2667, "value": -2.46, "curve": [2.35, -2.46, 2.517, 1.47]}, {"time": 2.6, "value": 1.47, "curve": [2.657, 1.47, 2.753, 0.59]}, {"time": 2.8333, "value": 0.06, "curve": [2.872, -0.2, 2.906, -0.38]}, {"time": 2.9333, "value": -0.38, "curve": [3.017, -0.38, 3.183, 0]}, {"time": 3.2667, "curve": [3.325, 0, 3.442, 2.28]}, {"time": 3.5, "value": 2.28, "curve": [3.523, 0.8, 3.546, 0]}, {"time": 3.5667, "curve": [3.797, 0, 4.241, 2.01]}, {"time": 4.5, "value": 2.28}]}, "bone48": {"rotate": [{"value": 2.11, "curve": [0.247, -1.1, 0.473, -3.03]}, {"time": 0.6667, "value": -3.03, "curve": [0.786, -5.26, 0.923, -1.96]}, {"time": 1, "value": 0.17, "curve": [1.083, 0.17, 1.25, -11.35]}, {"time": 1.3333, "value": -11.35, "curve": [1.592, -11.35, 2.108, -2.56]}, {"time": 2.3667, "value": -2.56, "curve": [2.45, -2.56, 2.617, 7.9]}, {"time": 2.7, "value": 7.9, "curve": [2.734, 7.9, 2.782, 5.65]}, {"time": 2.8333, "value": 2.96, "curve": [2.906, -0.82, 2.984, -5.54]}, {"time": 3.0333, "value": -5.54, "curve": [3.117, -5.54, 3.283, 0]}, {"time": 3.3667, "curve": [3.4, 0, 3.467, 2.11]}, {"time": 3.5, "value": 2.11, "curve": [3.562, 0.79, 3.618, 0]}, {"time": 3.6667, "curve": [3.869, 0, 4.241, 1.58]}, {"time": 4.5, "value": 2.11}]}, "bone49": {"rotate": [{"value": 1.86, "curve": [0.254, -1.05, 0.484, -3.03]}, {"time": 0.6667, "value": -3.03, "curve": [0.786, 12.09, 0.923, -10.28]}, {"time": 1, "value": -24.75, "curve": [1.083, -24.75, 1.25, -12.55]}, {"time": 1.3333, "value": -12.55, "curve": [1.617, -12.55, 2.183, -2.56]}, {"time": 2.4667, "value": -2.56, "curve": [2.55, -2.56, 2.717, 7.9]}, {"time": 2.8, "value": 7.9, "curve": [2.81, 7.9, 2.821, 7.69]}, {"time": 2.8333, "value": 7.33, "curve": [2.92, 5.18, 3.06, -5.54]}, {"time": 3.1333, "value": -5.54, "curve": [3.217, -5.54, 3.383, 0]}, {"time": 3.4667, "curve": [3.475, 0, 3.492, 1.86]}, {"time": 3.5, "value": 1.86, "curve": [3.601, 0.75, 3.694, 0]}, {"time": 3.7667, "curve": [3.944, 0, 4.253, 1.21]}, {"time": 4.5, "value": 1.86}]}, "bone50": {"rotate": [{"value": 1.48, "curve": [0.254, -0.98, 0.495, -3.03]}, {"time": 0.6667, "value": -3.03, "curve": [0.786, 12.09, 0.923, -10.28]}, {"time": 1, "value": -24.75, "curve": [1.083, -24.75, 1.25, -26.8]}, {"time": 1.3333, "value": -26.8, "curve": [1.642, -26.8, 2.258, -2.56]}, {"time": 2.5667, "value": -2.56, "curve": [2.631, -2.56, 2.748, 3.94]}, {"time": 2.8333, "value": 6.54, "curve": [2.858, 7.38, 2.881, 7.9]}, {"time": 2.9, "value": 7.9, "curve": [2.983, 7.9, 3.15, -5.54]}, {"time": 3.2333, "value": -5.54, "curve": [3.3, -5.54, 3.433, 1.48]}, {"time": 3.5, "value": 1.48, "curve": [3.653, 0.67, 3.797, 0]}, {"time": 3.9, "curve": [4.047, 0, 4.282, 0.82]}, {"time": 4.5, "value": 1.48}]}, "bone51": {"rotate": [{"value": 1.67, "curve": [0.255, 1.82, 0.49, 1.93]}, {"time": 0.6667, "value": 1.93, "curve": [0.786, 6.22, 0.923, -0.13]}, {"time": 1, "value": -4.24, "curve": [1.083, -4.24, 1.25, -11.48]}, {"time": 1.3333, "value": -11.48, "curve": [1.542, -11.48, 1.958, -2.56]}, {"time": 2.1667, "value": -2.56, "curve": [2.25, -2.56, 2.417, 7.9]}, {"time": 2.5, "value": 7.9, "curve": [2.583, 7.9, 2.75, -5.54]}, {"time": 2.8333, "value": -5.54, "curve": [2.917, -5.54, 3.083, 0]}, {"time": 3.1667, "curve": [3.25, 0, 3.417, 1.67]}, {"time": 3.5, "value": 1.67, "curve": [3.627, 0.72, 3.745, 0]}, {"time": 3.8333, "curve": [3.995, 0, 4.266, 1]}, {"time": 4.5, "value": 1.67}]}, "bone52": {"rotate": [{"value": 1.27, "curve": [0.252, 1.61, 0.499, 1.93]}, {"time": 0.6667, "value": 1.93, "curve": [0.786, 21.07, 0.923, -7.26]}, {"time": 1, "value": -25.58, "curve": [1.083, -25.58, 1.25, -27.21]}, {"time": 1.3333, "value": -27.21, "curve": [1.567, -27.21, 2.033, -2.56]}, {"time": 2.2667, "value": -2.56, "curve": [2.35, -2.56, 2.517, 7.9]}, {"time": 2.6, "value": 7.9, "curve": [2.657, 7.9, 2.753, 1.53]}, {"time": 2.8333, "value": -2.28, "curve": [2.872, -4.18, 2.906, -5.54]}, {"time": 2.9333, "value": -5.54, "curve": [3.017, -5.54, 3.183, 0]}, {"time": 3.2667, "curve": [3.325, 0, 3.442, 1.27]}, {"time": 3.5, "value": 1.27, "curve": [3.676, 0.61, 3.849, 0]}, {"time": 3.9667, "curve": [4.099, 0, 4.302, 0.66]}, {"time": 4.5, "value": 1.27}]}, "bone53": {"rotate": [{"value": 1.06, "curve": [0.248, 1.48, 0.501, 1.93]}, {"time": 0.6667, "value": 1.93, "curve": [0.786, 21.07, 0.923, -7.26]}, {"time": 1, "value": -25.58, "curve": [1.083, -25.58, 1.25, -27.21]}, {"time": 1.3333, "value": -27.21, "curve": [1.6, -27.21, 2.133, -2.56]}, {"time": 2.4, "value": -2.56, "curve": [2.483, -2.56, 2.65, 7.9]}, {"time": 2.7333, "value": 7.9, "curve": [2.76, 7.9, 2.795, 6.54]}, {"time": 2.8333, "value": 4.64, "curve": [2.914, 0.83, 3.01, -5.54]}, {"time": 3.0667, "value": -5.54, "curve": [3.15, -5.54, 3.317, 0]}, {"time": 3.4, "curve": [3.425, 0, 3.475, 1.06]}, {"time": 3.5, "value": 1.06, "curve": [3.698, 0.55, 3.901, 0]}, {"time": 4.0333, "curve": [4.151, 0, 4.324, 0.52]}, {"time": 4.5, "value": 1.06}]}, "bone54": {"rotate": [{"time": 2.8333, "curve": [3.022, 1.09, 3.261, 4.12]}, {"time": 3.5, "value": 7.14, "curve": [3.75, 7.14, 4.25, 0]}, {"time": 4.5}]}, "bone55": {"rotate": [{"value": 0.69, "curve": [0.247, 0.26, 0.473, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, 0.11, 3.261, 0.4]}, {"time": 3.5, "value": 0.69, "curve": [3.562, 4.72, 3.618, 7.14]}, {"time": 3.6667, "value": 7.14, "curve": [3.869, 7.14, 4.241, 2.3]}, {"time": 4.5, "value": 0.69}]}, "bone56": {"rotate": [{"value": 1.73, "curve": [0.254, 0.72, 0.487, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, 0.26, 3.261, 1]}, {"time": 3.5, "value": 1.73, "curve": [3.615, 4.89, 3.719, 7.14]}, {"time": 3.8, "value": 7.14, "curve": [3.97, 7.14, 4.259, 3.76]}, {"time": 4.5, "value": 1.73}]}, "bone57": {"rotate": [{"value": 2.94, "curve": [0.253, 1.38, 0.497, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, 0.45, 3.261, 1.7]}, {"time": 3.5, "value": 2.94, "curve": [3.665, 5.17, 3.823, 7.14]}, {"time": 3.9333, "value": 7.14, "curve": [4.073, 7.14, 4.292, 4.89]}, {"time": 4.5, "value": 2.94}]}, "bone58": {"rotate": [{"value": 0.12, "curve": [0.255, -0.18, 0.49, -0.4]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, -3.38, 0.923, 1.03]}, {"time": 1, "value": 3.88, "curve": [1.083, 3.88, 1.25, 5.14]}, {"time": 1.3333, "value": 5.14, "curve": [1.417, 5.14, 1.583, -3.01]}, {"time": 1.6667, "value": -3.01, "curve": [1.75, -3.01, 1.917, 1.62]}, {"time": 2, "value": 1.62, "curve": [2.083, 1.62, 2.25, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.04, 3.338, 0.12]}, {"time": 3.5, "value": 0.12, "curve": [3.627, 0.3, 3.745, 0.43]}, {"time": 3.8333, "value": 0.43, "curve": [3.995, 0.43, 4.266, 0.25]}, {"time": 4.5, "value": 0.12}]}, "bone59": {"rotate": [{"value": 0.21, "curve": [0.234, -0.03, 0.468, -0.27]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, -3.38, 0.923, 1.03]}, {"time": 1, "value": 3.88, "curve": [1.083, 3.88, 1.25, 5.14]}, {"time": 1.3333, "value": 5.14, "curve": [1.417, 5.14, 1.583, -3.01]}, {"time": 1.6667, "value": -3.01, "curve": [1.75, -3.01, 1.917, 1.62]}, {"time": 2, "value": 1.62, "curve": [2.083, 1.62, 2.25, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.06, 3.338, 0.21]}, {"time": 3.5, "value": 0.21, "curve": [3.675, 0.3, 3.851, 0.39]}, {"time": 4, "value": 0.43, "curve": [4.125, 0.43, 4.313, 0.32]}, {"time": 4.5, "value": 0.21}]}, "bone60": {"rotate": [{"value": 0.31, "curve": [0.217, 0.09, 0.45, -0.18]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, -3.38, 0.923, 1.03]}, {"time": 1, "value": 3.88, "curve": [1.083, 3.88, 1.25, 5.14]}, {"time": 1.3333, "value": 5.14, "curve": [1.417, 5.14, 1.583, -3.01]}, {"time": 1.6667, "value": -3.01, "curve": [1.75, -3.01, 1.917, 1.62]}, {"time": 2, "value": 1.62, "curve": [2.083, 1.62, 2.25, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.09, 3.338, 0.31]}, {"time": 3.5, "value": 0.31, "curve": [3.717, 0.35, 3.95, 0.39]}, {"time": 4.1667, "value": 0.43, "curve": [4.255, 0.43, 4.373, 0.38]}, {"time": 4.5, "value": 0.31}]}, "bone61": {"rotate": [{"value": 2.35, "curve": [0.199, 1.75, 0.433, 0.68]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, -3.38, 0.923, 1.03]}, {"time": 1, "value": 3.88, "curve": [1.083, 3.88, 1.25, 5.14]}, {"time": 1.3333, "value": 5.14, "curve": [1.417, 5.14, 1.583, -3.01]}, {"time": 1.6667, "value": -3.01, "curve": [1.75, -3.01, 1.917, 1.62]}, {"time": 2, "value": 1.62, "curve": [2.083, 1.62, 2.25, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.67, 3.338, 2.35]}, {"time": 3.5, "value": 2.35, "curve": [3.749, 2.4, 4.041, 2.5]}, {"time": 4.3333, "value": 2.6, "curve": [4.382, 2.6, 4.438, 2.5]}, {"time": 4.5, "value": 2.35}]}, "bone62": {"rotate": [{"value": 2.6, "curve": [0.176, 2.6, 0.412, 1.31]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, -3.38, 0.923, 1.03]}, {"time": 1, "value": 3.88, "curve": [1.083, 3.88, 1.25, 5.14]}, {"time": 1.3333, "value": 5.14, "curve": [1.417, 5.14, 1.583, -3.01]}, {"time": 1.6667, "value": -3.01, "curve": [1.75, -3.01, 1.917, 1.62]}, {"time": 2, "value": 1.62, "curve": [2.083, 1.62, 2.25, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 3.5, "curve": [3.75, 0, 4.25, 2.6]}, {"time": 4.5, "value": 2.6}]}, "bone63": {"rotate": [{"value": 2.42, "curve": [0.244, 0.63, 0.469, -0.4]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, -3.38, 0.923, 1.03]}, {"time": 1, "value": 3.88, "curve": [1.083, 3.88, 1.25, 5.14]}, {"time": 1.3333, "value": 5.14, "curve": [1.417, 5.14, 1.583, -3.01]}, {"time": 1.6667, "value": -3.01, "curve": [1.75, -3.01, 1.917, 1.62]}, {"time": 2, "value": 1.62, "curve": [2.083, 1.62, 2.25, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.69, 3.338, 2.42]}, {"time": 3.5, "value": 2.42, "curve": [3.549, 0.88, 3.594, 0]}, {"time": 3.6333, "curve": [3.845, 0, 4.24, 1.91]}, {"time": 4.5, "value": 2.42}]}, "bone64": {"rotate": [{"value": 2.78, "curve": [0.254, 0.89, 0.484, -0.4]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, -3.38, 0.923, 1.03]}, {"time": 1, "value": 3.88, "curve": [1.083, 3.88, 1.25, 5.14]}, {"time": 1.3333, "value": 5.14, "curve": [1.442, 5.14, 1.658, -3.01]}, {"time": 1.7667, "value": -3.01, "curve": [1.85, -3.01, 2.017, 1.62]}, {"time": 2.1, "value": 1.62, "curve": [2.183, 1.62, 2.35, 0]}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.024, 0.43, 3.336, 2.78]}, {"time": 3.5, "value": 2.78, "curve": [3.601, 1.13, 3.694, 0]}, {"time": 3.7667, "curve": [3.944, 0, 4.253, 1.81]}, {"time": 4.5, "value": 2.78}]}, "bone65": {"rotate": [{"value": 2.36, "curve": [0.25, 0.94, 0.485, -0.2]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, -3.38, 0.923, 1.03]}, {"time": 1, "value": 3.88, "curve": [1.083, 3.88, 1.25, 5.14]}, {"time": 1.3333, "value": 5.14, "curve": [1.467, 5.14, 1.733, -3.01]}, {"time": 1.8667, "value": -3.01, "curve": [1.95, -3.01, 2.117, 1.62]}, {"time": 2.2, "value": 1.62, "curve": [2.283, 1.62, 2.45, 0]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.8667, "curve": [3.025, 0, 3.342, 2.36]}, {"time": 3.5, "value": 2.36, "curve": [3.638, 1.15, 3.767, 0.17]}, {"time": 3.8667, "curve": [4.021, 0, 4.274, 1.36]}, {"time": 4.5, "value": 2.36}]}, "bone66": {"rotate": [{"value": 1.92, "curve": [0.237, 0.96, 0.471, 0.04]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, 0.02, 0.923, -0.6]}, {"time": 1, "value": -1, "curve": [1.083, -1, 1.25, 0.25]}, {"time": 1.3333, "value": 0.25, "curve": [1.492, 0.25, 1.808, -3.01]}, {"time": 1.9667, "value": -3.01, "curve": [2.05, -3.01, 2.217, 1.62]}, {"time": 2.3, "value": 1.62, "curve": [2.383, 1.62, 2.55, 0]}, {"time": 2.6333, "curve": "stepped"}, {"time": 2.9667, "curve": [3.1, 0, 3.367, 1.92]}, {"time": 3.5, "value": 1.92, "curve": [3.666, 1.12, 3.83, 0.37]}, {"time": 3.9667, "curve": [4.099, 0, 4.302, 0.99]}, {"time": 4.5, "value": 1.92}]}, "bone67": {"rotate": [{"value": 2.18, "curve": [0.227, 1.26, 0.46, 0.27]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, 14.39, 0.923, -7.49]}, {"time": 1, "value": -21.65, "curve": [1.083, -21.65, 1.25, -26.95]}, {"time": 1.3333, "value": -26.95, "curve": [1.517, -26.95, 1.883, -9.48]}, {"time": 2.0667, "value": -9.48, "curve": [2.15, -9.48, 2.317, 6.19]}, {"time": 2.4, "value": 6.19, "curve": [2.483, 6.19, 2.65, 0]}, {"time": 2.7333, "curve": "stepped"}, {"time": 3.0667, "curve": [3.175, 0, 3.392, 2.18]}, {"time": 3.5, "value": 2.18, "curve": [3.693, 1.4, 3.891, 0.56]}, {"time": 4.0667, "curve": [4.177, 0, 4.335, 1.02]}, {"time": 4.5, "value": 2.18}]}, "bone68": {"rotate": [{"value": 1.49, "curve": [0.217, 0.91, 0.45, 0.18]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, 8.16, 0.923, -4.51]}, {"time": 1, "value": -12.7, "curve": [1.083, -12.7, 1.25, -18.01]}, {"time": 1.3333, "value": -18.01, "curve": [1.542, -18.01, 1.958, -9.48]}, {"time": 2.1667, "value": -9.48, "curve": [2.25, -9.48, 2.417, 6.19]}, {"time": 2.5, "value": 6.19, "curve": [2.583, 6.19, 2.75, 0]}, {"time": 2.8333, "curve": "stepped"}, {"time": 3.1667, "curve": [3.25, 0, 3.417, 1.49]}, {"time": 3.5, "value": 1.49, "curve": [3.717, 1.03, 3.95, 0.46]}, {"time": 4.1667, "curve": [4.255, 0, 4.373, 0.64]}, {"time": 4.5, "value": 1.49}]}, "bone69": {"rotate": [{"value": 0.87, "curve": [0.206, 0.54, 0.44, 0.05]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, 8.16, 0.923, -4.51]}, {"time": 1, "value": -12.7, "curve": [1.083, -12.7, 1.25, -18.01]}, {"time": 1.3333, "value": -18.01, "curve": [1.567, -18.01, 2.033, -9.48]}, {"time": 2.2667, "value": -9.48, "curve": [2.35, -9.48, 2.517, 6.19]}, {"time": 2.6, "value": 6.19, "curve": [2.657, 6.19, 2.753, 3.26]}, {"time": 2.8333, "value": 1.5, "curve": [2.872, 0.62, 2.906, 0]}, {"time": 2.9333, "curve": "stepped"}, {"time": 3.2667, "curve": [3.325, 0, 3.442, 0.87]}, {"time": 3.5, "value": 0.87, "curve": [3.737, 0.64, 4.006, 0.31]}, {"time": 4.2667, "curve": [4.332, 0, 4.412, 0.34]}, {"time": 4.5, "value": 0.87}]}, "bone70": {"rotate": [{"value": 0.35, "curve": [0.195, 0.2, 0.429, -0.09]}, {"time": 0.6667, "value": -0.4, "curve": [0.786, 8.16, 0.923, -4.51]}, {"time": 1, "value": -12.7, "curve": [1.083, -12.7, 1.25, -18.01]}, {"time": 1.3333, "value": -18.01, "curve": [1.592, -18.01, 2.108, -9.48]}, {"time": 2.3667, "value": -9.48, "curve": [2.45, -9.48, 2.617, 6.19]}, {"time": 2.7, "value": 6.19, "curve": [2.734, 6.19, 2.782, 5.16]}, {"time": 2.8333, "value": 3.91, "curve": [2.906, 2.17, 2.984, 0]}, {"time": 3.0333, "curve": "stepped"}, {"time": 3.3667, "curve": [3.4, 0, 3.467, 0.35]}, {"time": 3.5, "value": 0.35, "curve": [3.754, 0.28, 4.058, 0.14]}, {"time": 4.3667, "curve": [4.406, 0, 4.451, 0.13]}, {"time": 4.5, "value": 0.35}]}, "bone71": {"rotate": [{"value": 0.1, "curve": [0.25, -0.31, 0.5, -0.72]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -3.26, 0.923, 0.5]}, {"time": 1, "value": 2.93, "curve": [1.083, 2.93, 1.25, 3.45]}, {"time": 1.3333, "value": 3.45, "curve": [1.417, 3.45, 1.583, -2.55]}, {"time": 1.6667, "value": -2.55, "curve": [1.75, -2.55, 1.917, 0.42]}, {"time": 2, "value": 0.42, "curve": [2.083, 0.42, 2.25, -0.37]}, {"time": 2.3333, "value": -0.37, "curve": [2.417, -0.37, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.136, 0, 3.43, 0.09]}, {"time": 3.5, "value": 0.1, "curve": [3.688, 0.15, 3.875, 0.21]}, {"time": 4, "value": 0.21, "curve": [4.125, 0.21, 4.313, 0.15]}, {"time": 4.5, "value": 0.1}]}, "bone72": {"rotate": [{"value": 0.15, "curve": [0.222, -0.14, 0.47, -0.54]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -3.26, 0.923, 0.5]}, {"time": 1, "value": 2.93, "curve": [1.083, 2.93, 1.25, 3.45]}, {"time": 1.3333, "value": 3.45, "curve": [1.417, 3.45, 1.583, -2.55]}, {"time": 1.6667, "value": -2.55, "curve": [1.75, -2.55, 1.917, 0.42]}, {"time": 2, "value": 0.42, "curve": [2.083, 0.42, 2.25, -0.37]}, {"time": 2.3333, "value": -0.37, "curve": [2.417, -0.37, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.136, 0.01, 3.43, 0.13]}, {"time": 3.5, "value": 0.15, "curve": [3.722, 0.17, 3.97, 0.19]}, {"time": 4.1667, "value": 0.21, "curve": [4.255, 0.21, 4.373, 0.18]}, {"time": 4.5, "value": 0.15}]}, "bone73": {"rotate": [{"value": 0.19, "curve": [0.197, 0, 0.444, -0.42]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -3.26, 0.923, 0.5]}, {"time": 1, "value": 2.93, "curve": [1.083, 2.93, 1.25, 3.45]}, {"time": 1.3333, "value": 3.45, "curve": [1.417, 3.45, 1.583, -2.55]}, {"time": 1.6667, "value": -2.55, "curve": [1.75, -2.55, 1.917, 0.42]}, {"time": 2, "value": 0.42, "curve": [2.083, 0.42, 2.25, -0.37]}, {"time": 2.3333, "value": -0.37, "curve": [2.417, -0.37, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.136, 0.01, 3.43, 0.16]}, {"time": 3.5, "value": 0.19, "curve": [3.746, 0.19, 4.056, 0.2]}, {"time": 4.3333, "value": 0.21, "curve": [4.382, 0.21, 4.438, 0.2]}, {"time": 4.5, "value": 0.19}]}, "bone74": {"rotate": [{"value": 0.21, "curve": [0.167, 0.21, 0.417, -0.26]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -3.26, 0.923, 0.5]}, {"time": 1, "value": 2.93, "curve": [1.083, 2.93, 1.25, 3.45]}, {"time": 1.3333, "value": 3.45, "curve": [1.417, 3.45, 1.583, -2.55]}, {"time": 1.6667, "value": -2.55, "curve": [1.75, -2.55, 1.917, 0.42]}, {"time": 2, "value": 0.42, "curve": [2.083, 0.42, 2.25, -0.37]}, {"time": 2.3333, "value": -0.37, "curve": [2.417, -0.37, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 3.5, "curve": [3.75, 0, 4.25, 0.21]}, {"time": 4.5, "value": 0.21}]}, "bone75": {"rotate": [{"value": 1.85, "curve": [0.247, 0.24, 0.473, -0.72]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -3.26, 0.923, 0.5]}, {"time": 1, "value": 2.93, "curve": [1.083, 2.93, 1.25, 3.45]}, {"time": 1.3333, "value": 3.45, "curve": [1.417, 3.45, 1.583, -2.55]}, {"time": 1.6667, "value": -2.55, "curve": [1.75, -2.55, 1.917, 0.42]}, {"time": 2, "value": 0.42, "curve": [2.083, 0.42, 2.25, -0.37]}, {"time": 2.3333, "value": -0.37, "curve": [2.417, -0.37, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.136, 0.09, 3.43, 1.62]}, {"time": 3.5, "value": 1.85, "curve": [3.562, 0.69, 3.618, 0]}, {"time": 3.6667, "curve": [3.869, 0, 4.241, 1.39]}, {"time": 4.5, "value": 1.85}]}, "bone76": {"rotate": [{"value": 2.19, "curve": [0.255, 0.53, 0.49, -0.72]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -3.26, 0.923, 0.5]}, {"time": 1, "value": 2.93, "curve": [1.083, 2.93, 1.25, 3.45]}, {"time": 1.3333, "value": 3.45, "curve": [1.417, 3.45, 1.583, -2.55]}, {"time": 1.6667, "value": -2.55, "curve": [1.75, -2.55, 1.917, 0.42]}, {"time": 2, "value": 0.42, "curve": [2.083, 0.42, 2.25, -0.37]}, {"time": 2.3333, "value": -0.37, "curve": [2.417, -0.37, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.136, 0.1, 3.43, 1.92]}, {"time": 3.5, "value": 2.19, "curve": [3.627, 0.94, 3.745, 0]}, {"time": 3.8333, "curve": [3.995, 0, 4.266, 1.31]}, {"time": 4.5, "value": 2.19}]}, "bone77": {"rotate": [{"value": 1.53, "curve": [0.25, 0.4, 0.5, -0.72]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -3.26, 0.923, 0.5]}, {"time": 1, "value": 2.93, "curve": [1.083, 2.93, 1.25, 3.45]}, {"time": 1.3333, "value": 3.45, "curve": [1.417, 3.45, 1.583, -2.55]}, {"time": 1.6667, "value": -2.55, "curve": [1.75, -2.55, 1.917, 0.42]}, {"time": 2, "value": 0.42, "curve": [2.083, 0.42, 2.25, -0.37]}, {"time": 2.3333, "value": -0.37, "curve": [2.417, -0.37, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.136, 0.07, 3.43, 1.34]}, {"time": 3.5, "value": 1.53, "curve": [3.688, 0.76, 3.875, 0]}, {"time": 4, "curve": [4.125, 0, 4.313, 0.76]}, {"time": 4.5, "value": 1.53}]}, "bone78": {"rotate": [{"value": 0.88, "curve": [0.222, 0.35, 0.47, -0.39]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -4.99, 0.923, 1.33]}, {"time": 1, "value": 5.41, "curve": [1.083, 5.41, 1.25, 5.93]}, {"time": 1.3333, "value": 5.93, "curve": [1.417, 5.93, 1.583, -0.07]}, {"time": 1.6667, "value": -0.07, "curve": [1.75, -0.07, 1.917, 0.42]}, {"time": 2, "value": 0.42, "curve": [2.083, 0.42, 2.25, -0.37]}, {"time": 2.3333, "value": -0.37, "curve": [2.417, -0.37, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.136, 0.04, 3.43, 0.77]}, {"time": 3.5, "value": 0.88, "curve": [3.722, 0.59, 3.97, 0.18]}, {"time": 4.1667, "curve": [4.255, 0, 4.373, 0.38]}, {"time": 4.5, "value": 0.88}]}, "bone79": {"rotate": [{"value": 0.51, "curve": [0.207, 0.19, 0.455, -0.37]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -3.26, 0.923, 0.5]}, {"time": 1, "value": 2.93, "curve": [1.083, 2.93, 1.25, 3.45]}, {"time": 1.3333, "value": 3.45, "curve": [1.442, 3.45, 1.658, -2.55]}, {"time": 1.7667, "value": -2.55, "curve": [1.85, -2.55, 2.017, 0.42]}, {"time": 2.1, "value": 0.42, "curve": [2.183, 0.42, 2.35, -0.37]}, {"time": 2.4333, "value": -0.37, "curve": [2.517, -0.37, 2.683, 0]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.122, -0.12, 3.433, 0.44]}, {"time": 3.5, "value": 0.51, "curve": [3.738, 0.38, 4.023, 0.15]}, {"time": 4.2667, "curve": [4.332, 0, 4.412, 0.2]}, {"time": 4.5, "value": 0.51}]}, "bone80": {"rotate": [{"value": 0.21, "curve": [0.191, 0.04, 0.439, -0.39]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -8.5, 0.923, 3.01]}, {"time": 1, "value": 10.46, "curve": [1.083, 10.46, 1.25, -9.53]}, {"time": 1.3333, "value": -9.53, "curve": [1.467, -9.53, 1.733, -8.15]}, {"time": 1.8667, "value": -8.15, "curve": [1.95, -8.15, 2.117, 0.03]}, {"time": 2.2, "value": 0.03, "curve": [2.283, 0.03, 2.45, -5.15]}, {"time": 2.5333, "value": -5.15, "curve": [2.607, -5.15, 2.747, -1.04]}, {"time": 2.8333, "value": -0.22, "curve": [2.845, -0.08, 2.857, 0]}, {"time": 2.8667, "curve": [3.128, -0.13, 3.438, 0.17]}, {"time": 3.5, "value": 0.21, "curve": [3.749, 0.17, 4.071, 0.07]}, {"time": 4.3667, "curve": [4.406, 0, 4.451, 0.08]}, {"time": 4.5, "value": 0.21}]}, "bone81": {"rotate": [{"value": 0.03, "curve": [0.173, -0.01, 0.423, -0.38]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -8.5, 0.923, 3.01]}, {"time": 1, "value": 10.46, "curve": [1.083, 10.46, 1.25, -4.97]}, {"time": 1.3333, "value": -4.97, "curve": [1.492, -4.97, 1.808, -8.15]}, {"time": 1.9667, "value": -8.15, "curve": [2.05, -8.15, 2.217, 0.03]}, {"time": 2.3, "value": 0.03, "curve": [2.383, 0.03, 2.55, -5.15]}, {"time": 2.6333, "value": -5.15, "curve": [2.682, -5.15, 2.761, -3.34]}, {"time": 2.8333, "value": -1.9, "curve": [2.884, -0.86, 2.932, 0]}, {"time": 2.9667, "curve": [3.187, -0.02, 3.448, 0.02]}, {"time": 3.5, "value": 0.03, "curve": [3.751, 0.03, 4.113, 0.01]}, {"time": 4.4667, "curve": [4.477, 0, 4.489, 0.01]}, {"time": 4.5, "value": 0.03}]}, "bone82": {"rotate": [{"value": 0.1, "curve": [0.235, -0.43, 0.458, -0.72]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -8.5, 0.923, 3.01]}, {"time": 1, "value": 10.46, "curve": [1.083, 10.46, 1.25, -4.97]}, {"time": 1.3333, "value": -4.97, "curve": [1.517, -4.97, 1.883, -8.15]}, {"time": 2.0667, "value": -8.15, "curve": [2.15, -8.15, 2.317, 0.03]}, {"time": 2.4, "value": 0.03, "curve": [2.483, 0.03, 2.65, -5.15]}, {"time": 2.7333, "value": -5.15, "curve": [2.76, -5.15, 2.795, -4.63]}, {"time": 2.8333, "value": -3.91, "curve": [2.914, -2.44, 3.01, 0]}, {"time": 3.0667, "curve": [3.245, -0.07, 3.458, 0.09]}, {"time": 3.5, "value": 0.1, "curve": [3.523, 3.03, 3.546, 4.6]}, {"time": 3.5667, "value": 4.6, "curve": [3.797, 4.6, 4.241, 0.63]}, {"time": 4.5, "value": 0.1}]}, "bone83": {"rotate": [{"value": 0.44, "curve": [0.247, -0.28, 0.473, -0.72]}, {"time": 0.6667, "value": -0.72, "curve": [0.786, -3.26, 0.923, 0.5]}, {"time": 1, "value": 2.93, "curve": [1.083, 2.93, 1.25, -12.5]}, {"time": 1.3333, "value": -12.5, "curve": [1.542, -12.5, 1.958, -15.68]}, {"time": 2.1667, "value": -15.68, "curve": [2.25, -15.68, 2.417, 0.03]}, {"time": 2.5, "value": 0.03, "curve": [2.583, 0.03, 2.75, -5.15]}, {"time": 2.8333, "value": -5.15, "curve": [2.917, -5.15, 3.083, 0]}, {"time": 3.1667, "curve": [3.304, -0.28, 3.467, 0.37]}, {"time": 3.5, "value": 0.44, "curve": [3.562, 3.04, 3.618, 4.6]}, {"time": 3.6667, "value": 4.6, "curve": [3.869, 4.6, 4.241, 1.48]}, {"time": 4.5, "value": 0.44}]}, "bone84": {"rotate": [{"value": 0.14, "curve": [0.247, 0.05, 0.473, 0]}, {"time": 0.6667, "curve": [0.786, -0.95, 0.923, 0.45]}, {"time": 1, "value": 1.36, "curve": [1.083, 1.36, 1.25, 1.08]}, {"time": 1.3333, "value": 1.08, "curve": [1.442, 1.08, 1.658, -1.48]}, {"time": 1.7667, "value": -1.48, "curve": [1.85, -1.48, 2.017, 0.95]}, {"time": 2.1, "value": 0.95, "curve": [2.183, 0.95, 2.35, 0]}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.011, 0.01, 3.255, 0.08]}, {"time": 3.5, "value": 0.14, "curve": [3.562, 0.99, 3.618, 1.5]}, {"time": 3.6667, "value": 1.5, "curve": [3.869, 1.5, 4.241, 0.48]}, {"time": 4.5, "value": 0.14}]}, "bone85": {"rotate": [{"value": 0.43, "curve": [0.255, 0.18, 0.49, 0]}, {"time": 0.6667, "curve": [0.786, -0.95, 0.923, 0.45]}, {"time": 1, "value": 1.36, "curve": [1.083, 1.36, 1.25, 1.08]}, {"time": 1.3333, "value": 1.08, "curve": [1.467, 1.08, 1.733, -1.48]}, {"time": 1.8667, "value": -1.48, "curve": [1.95, -1.48, 2.117, 0.95]}, {"time": 2.2, "value": 0.95, "curve": [2.283, 0.95, 2.45, 0]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.8667, "curve": [3.025, 0, 3.263, 0.21]}, {"time": 3.5, "value": 0.43, "curve": [3.627, 1.04, 3.745, 1.5]}, {"time": 3.8333, "value": 1.5, "curve": [3.995, 1.5, 4.266, 0.86]}, {"time": 4.5, "value": 0.43}]}, "bone86": {"rotate": [{"value": 0.75, "curve": [0.25, 0.37, 0.5, 0]}, {"time": 0.6667, "curve": [0.786, -0.95, 0.923, 0.45]}, {"time": 1, "value": 1.36, "curve": [1.083, 1.36, 1.25, 1.08]}, {"time": 1.3333, "value": 1.08, "curve": [1.492, 1.08, 1.808, -1.48]}, {"time": 1.9667, "value": -1.48, "curve": [2.05, -1.48, 2.217, 0.95]}, {"time": 2.3, "value": 0.95, "curve": [2.383, 0.95, 2.55, 0]}, {"time": 2.6333, "curve": "stepped"}, {"time": 2.9667, "curve": [3.1, 0, 3.3, 0.37]}, {"time": 3.5, "value": 0.75, "curve": [3.688, 1.12, 3.875, 1.5]}, {"time": 4, "value": 1.5, "curve": [4.125, 1.5, 4.313, 1.12]}, {"time": 4.5, "value": 0.75}]}, "bone87": {"rotate": [{"value": 1.07, "curve": [0.234, 0.64, 0.505, 0]}, {"time": 0.6667, "curve": [0.786, -0.95, 0.923, 0.45]}, {"time": 1, "value": 1.36, "curve": [1.083, 1.36, 1.25, 1.08]}, {"time": 1.3333, "value": 1.08, "curve": [1.517, 1.08, 1.883, -1.48]}, {"time": 2.0667, "value": -1.48, "curve": [2.15, -1.48, 2.317, 0.95]}, {"time": 2.4, "value": 0.95, "curve": [2.483, 0.95, 2.65, 0]}, {"time": 2.7333, "curve": "stepped"}, {"time": 3.0667, "curve": [3.175, 0, 3.338, 0.54]}, {"time": 3.5, "value": 1.07, "curve": [3.734, 1.24, 4.005, 1.5]}, {"time": 4.1667, "value": 1.5, "curve": [4.255, 1.5, 4.373, 1.32]}, {"time": 4.5, "value": 1.07}]}, "bone88": {"rotate": [{"time": 0.6667, "curve": [0.786, -1, 0.923, 0.48]}, {"time": 1, "value": 1.44, "curve": [1.083, 1.44, 1.25, 2.55]}, {"time": 1.3333, "value": 2.55, "curve": [1.417, 2.55, 1.583, -3.5]}, {"time": 1.6667, "value": -3.5, "curve": [1.75, -3.5, 1.917, -0.82]}, {"time": 2, "value": -0.82, "curve": [2.083, -0.82, 2.25, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, -0.94, 3.338, -3.29]}, {"time": 3.5, "value": -3.29, "curve": [3.75, -3.29, 4.25, 0]}, {"time": 4.5}]}, "bone89": {"rotate": [{"value": -0.32, "curve": [0.247, -0.12, 0.473, 0]}, {"time": 0.6667, "curve": [0.786, -1, 0.923, 0.48]}, {"time": 1, "value": 1.44, "curve": [1.083, 1.44, 1.25, 2.55]}, {"time": 1.3333, "value": 2.55, "curve": [1.442, 2.55, 1.658, -3.5]}, {"time": 1.7667, "value": -3.5, "curve": [1.85, -3.5, 2.017, -0.82]}, {"time": 2.1, "value": -0.82, "curve": [2.183, -0.82, 2.35, 0]}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.024, -0.05, 3.336, -0.32]}, {"time": 3.5, "value": -0.32, "curve": [3.562, -2.18, 3.618, -3.29]}, {"time": 3.6667, "value": -3.29, "curve": [3.869, -3.29, 4.241, -1.06]}, {"time": 4.5, "value": -0.32}]}, "bone90": {"rotate": [{"value": -0.93, "curve": [0.255, -0.4, 0.49, 0]}, {"time": 0.6667, "curve": [0.786, -1, 0.923, 0.48]}, {"time": 1, "value": 1.44, "curve": [1.083, 1.44, 1.25, 2.55]}, {"time": 1.3333, "value": 2.55, "curve": [1.467, 2.55, 1.733, -3.5]}, {"time": 1.8667, "value": -3.5, "curve": [1.95, -3.5, 2.117, -0.82]}, {"time": 2.2, "value": -0.82, "curve": [2.283, -0.82, 2.45, 0]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.8667, "curve": [3.025, 0, 3.342, -0.93]}, {"time": 3.5, "value": -0.93, "curve": [3.627, -2.28, 3.745, -3.29]}, {"time": 3.8333, "value": -3.29, "curve": [3.995, -3.29, 4.266, -1.88]}, {"time": 4.5, "value": -0.93}]}, "bone91": {"rotate": [{"value": -1.65, "curve": [0.25, -0.82, 0.5, 0]}, {"time": 0.6667, "curve": [0.786, -1, 0.923, 0.48]}, {"time": 1, "value": 1.44, "curve": [1.083, 1.44, 1.25, 2.55]}, {"time": 1.3333, "value": 2.55, "curve": [1.492, 2.55, 1.808, -3.5]}, {"time": 1.9667, "value": -3.5, "curve": [2.05, -3.5, 2.217, -0.82]}, {"time": 2.3, "value": -0.82, "curve": [2.383, -0.82, 2.55, 0]}, {"time": 2.6333, "curve": "stepped"}, {"time": 2.9667, "curve": [3.1, 0, 3.367, -1.65]}, {"time": 3.5, "value": -1.65, "curve": [3.688, -2.47, 3.875, -3.29]}, {"time": 4, "value": -3.29, "curve": [4.125, -3.29, 4.313, -2.47]}, {"time": 4.5, "value": -1.65}]}, "bone92": {"rotate": [{"time": 0.6667, "curve": [0.786, 2.78, 0.923, -1.33]}, {"time": 1, "value": -4, "curve": [1.083, -4, 1.25, -5.01]}, {"time": 1.3333, "value": -5.01, "curve": [1.442, -5.01, 1.658, 5.48]}, {"time": 1.7667, "value": 5.48, "curve": [1.85, 5.48, 2.017, -2.02]}, {"time": 2.1, "value": -2.02, "curve": [2.158, -2.02, 2.273, 0]}, {"time": 2.3308, "curve": [2.356, 0, 2.408, 0.97]}, {"time": 2.4333, "value": 0.97, "curve": [2.517, 0.97, 2.683, 0]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.011, -0.19, 3.255, -1.26]}, {"time": 3.5, "value": -2.34, "curve": [3.75, -2.34, 4.25, 0]}, {"time": 4.5}]}, "bone93": {"rotate": [{"value": -0.23, "curve": [0.247, -0.08, 0.473, 0]}, {"time": 0.6667, "curve": [0.786, 2.91, 0.923, -1.39]}, {"time": 1, "value": -4.18, "curve": [1.083, -4.18, 1.25, -5.19]}, {"time": 1.3333, "value": -5.19, "curve": [1.467, -5.19, 1.733, 10.13]}, {"time": 1.8667, "value": 10.13, "curve": [1.95, 10.13, 2.117, -2.02]}, {"time": 2.2, "value": -2.02, "curve": [2.233, -2.02, 2.298, -0.23]}, {"time": 2.3308, "value": -0.23, "curve": [2.406, 0.52, 2.475, 0.97]}, {"time": 2.5333, "value": 0.97, "curve": [2.607, 0.97, 2.747, 0.2]}, {"time": 2.8333, "value": 0.04, "curve": [2.845, 0.01, 2.857, 0]}, {"time": 2.8667, "curve": [3.025, 0, 3.263, -0.11]}, {"time": 3.5, "value": -0.23, "curve": [3.562, -2.4, 3.618, -3.7]}, {"time": 3.6667, "value": -3.7, "curve": [3.875, -3.7, 4.292, -0.23]}, {"time": 4.5, "value": -0.23}]}, "bone94": {"rotate": [{"value": -1.74, "curve": [0.255, -0.75, 0.49, 0]}, {"time": 0.6667, "curve": [0.786, 10.49, 0.923, -5.03]}, {"time": 1, "value": -15.07, "curve": [1.083, -15.07, 1.25, -16.08]}, {"time": 1.3333, "value": -16.08, "curve": [1.492, -16.08, 1.808, 14.57]}, {"time": 1.9667, "value": 14.57, "curve": [2.05, 14.57, 2.217, -7.43]}, {"time": 2.3, "value": -7.43, "curve": [2.308, -7.43, 2.323, -1.74]}, {"time": 2.3308, "value": -1.74, "curve": [2.446, -0.19, 2.553, 0.97]}, {"time": 2.6333, "value": 0.97, "curve": [2.682, 0.97, 2.761, 0.63]}, {"time": 2.8333, "value": 0.36, "curve": [2.884, 0.16, 2.932, 0]}, {"time": 2.9667, "curve": [3.1, 0, 3.3, -0.87]}, {"time": 3.5, "value": -1.74, "curve": [3.627, -4.25, 3.745, -6.13]}, {"time": 3.8333, "value": -6.13, "curve": [4, -6.13, 4.333, -1.74]}, {"time": 4.5, "value": -1.74}]}, "bone95": {"rotate": [{"value": -3.91, "curve": [0.25, -1.96, 0.5, 0]}, {"time": 0.6667, "curve": [0.786, 10.49, 0.923, -5.03]}, {"time": 1, "value": -15.07, "curve": [1.083, -15.07, 1.25, -16.08]}, {"time": 1.3333, "value": -16.08, "curve": [1.517, -16.08, 1.883, 14.57]}, {"time": 2.0667, "value": 14.57, "curve": [2.133, 14.57, 2.265, -3.91]}, {"time": 2.3308, "value": -3.91, "curve": [2.357, -5.67, 2.383, -7.43]}, {"time": 2.4, "value": -7.43, "curve": [2.483, -7.43, 2.65, 0.97]}, {"time": 2.7333, "value": 0.97, "curve": [2.76, 0.97, 2.795, 0.87]}, {"time": 2.8333, "value": 0.73, "curve": [2.914, 0.46, 3.01, 0]}, {"time": 3.0667, "curve": [3.175, 0, 3.338, -1.96]}, {"time": 3.5, "value": -3.91, "curve": [3.688, -5.87, 3.875, -7.83]}, {"time": 4, "value": -7.83, "curve": [4.125, -7.83, 4.375, -3.91]}, {"time": 4.5, "value": -3.91}]}, "bone96": {"rotate": [{"value": -14.2, "curve": [0.234, -8.52, 0.505, 0]}, {"time": 0.6667, "curve": [0.786, 10.49, 0.923, -5.03]}, {"time": 1, "value": -15.07, "curve": [1.083, -15.07, 1.25, -16.08]}, {"time": 1.3333, "value": -16.08, "curve": [1.542, -16.08, 1.958, 14.57]}, {"time": 2.1667, "value": 14.57, "curve": [2.208, 14.57, 2.29, -14.2]}, {"time": 2.3308, "value": -14.2, "curve": [2.39, -11.49, 2.459, -7.43]}, {"time": 2.5, "value": -7.43, "curve": [2.583, -7.43, 2.75, 0.97]}, {"time": 2.8333, "value": 0.97, "curve": [2.917, 0.97, 3.083, 0]}, {"time": 3.1667, "curve": [3.25, 0, 3.375, -7.1]}, {"time": 3.5, "value": -14.2, "curve": [3.734, -16.45, 4.005, -19.83]}, {"time": 4.1667, "value": -19.83, "curve": [4.25, -19.83, 4.417, -14.2]}, {"time": 4.5, "value": -14.2}]}, "bone97": {"rotate": [{"value": -0.3, "curve": [0.247, -0.11, 0.473, 0]}, {"time": 0.6667, "curve": [0.786, 5.86, 0.923, -2.81]}, {"time": 1, "value": -8.42, "curve": [1.083, -8.42, 1.25, -13.64]}, {"time": 1.3333, "value": -13.64, "curve": [1.417, -13.64, 1.583, 7.43]}, {"time": 1.6667, "value": 7.43, "curve": [1.75, 7.43, 1.917, -2.14]}, {"time": 2, "value": -2.14, "curve": [2.083, -2.14, 2.25, 1.61]}, {"time": 2.3333, "value": 1.61, "curve": [2.417, 1.61, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, -0.05, 3.261, -0.17]}, {"time": 3.5, "value": -0.3, "curve": [3.562, -2.04, 3.618, -3.09]}, {"time": 3.6667, "value": -3.09, "curve": [3.869, -3.09, 4.241, -1]}, {"time": 4.5, "value": -0.3}]}, "bone98": {"rotate": [{"value": -2.53, "curve": [0.255, -1.08, 0.49, 0]}, {"time": 0.6667, "curve": [0.786, 5.86, 0.923, -2.81]}, {"time": 1, "value": -8.42, "curve": [1.083, -8.42, 1.25, -13.64]}, {"time": 1.3333, "value": -13.64, "curve": [1.442, -13.64, 1.658, 7.43]}, {"time": 1.7667, "value": 7.43, "curve": [1.85, 7.43, 2.017, -2.14]}, {"time": 2.1, "value": -2.14, "curve": [2.183, -2.14, 2.35, 1.61]}, {"time": 2.4333, "value": 1.61, "curve": [2.517, 1.61, 2.683, 0]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.011, -0.2, 3.255, -1.37]}, {"time": 3.5, "value": -2.53, "curve": [3.627, -6.18, 3.745, -8.91]}, {"time": 3.8333, "value": -8.91, "curve": [3.995, -8.91, 4.266, -5.08]}, {"time": 4.5, "value": -2.53}]}, "bone99": {"rotate": [{"value": -4.06, "curve": [0.252, -1.97, 0.499, 0]}, {"time": 0.6667, "curve": [0.786, 5.86, 0.923, -2.81]}, {"time": 1, "value": -8.42, "curve": [1.083, -8.42, 1.25, -13.64]}, {"time": 1.3333, "value": -13.64, "curve": [1.467, -13.64, 1.733, 7.43]}, {"time": 1.8667, "value": 7.43, "curve": [1.95, 7.43, 2.117, -2.14]}, {"time": 2.2, "value": -2.14, "curve": [2.283, -2.14, 2.45, 1.61]}, {"time": 2.5333, "value": 1.61, "curve": [2.607, 1.61, 2.747, 0.32]}, {"time": 2.8333, "value": 0.07, "curve": [2.845, 0.02, 2.857, 0]}, {"time": 2.8667, "curve": [3.025, 0, 3.263, -2.03]}, {"time": 3.5, "value": -4.06, "curve": [3.676, -6.56, 3.849, -8.91]}, {"time": 3.9667, "value": -8.91, "curve": [4.099, -8.91, 4.302, -6.4]}, {"time": 4.5, "value": -4.06}]}, "bone100": {"rotate": [{"value": -5.63, "curve": [0.242, -3.13, 0.503, 0]}, {"time": 0.6667, "curve": [0.786, 5.86, 0.923, -2.81]}, {"time": 1, "value": -8.42, "curve": [1.083, -8.42, 1.25, -13.64]}, {"time": 1.3333, "value": -13.64, "curve": [1.492, -13.64, 1.808, 7.43]}, {"time": 1.9667, "value": 7.43, "curve": [2.05, 7.43, 2.217, -2.14]}, {"time": 2.3, "value": -2.14, "curve": [2.383, -2.14, 2.55, 1.61]}, {"time": 2.6333, "value": 1.61, "curve": [2.682, 1.61, 2.761, 1.04]}, {"time": 2.8333, "value": 0.59, "curve": [2.884, 0.27, 2.932, 0]}, {"time": 2.9667, "curve": [3.1, 0, 3.3, -2.82]}, {"time": 3.5, "value": -5.63, "curve": [3.718, -7.09, 3.953, -8.91]}, {"time": 4.1, "value": -8.91, "curve": [4.203, -8.91, 4.347, -7.42]}, {"time": 4.5, "value": -5.63}]}, "bone101": {"rotate": [{"value": -7.1, "curve": [0.225, -4.63, 0.505, 0]}, {"time": 0.6667, "curve": [0.786, 5.86, 0.923, -2.81]}, {"time": 1, "value": -8.42, "curve": [1.083, -8.42, 1.25, -13.64]}, {"time": 1.3333, "value": -13.64, "curve": [1.517, -13.64, 1.883, 7.43]}, {"time": 2.0667, "value": 7.43, "curve": [2.15, 7.43, 2.317, -2.14]}, {"time": 2.4, "value": -2.14, "curve": [2.483, -2.14, 2.65, 1.61]}, {"time": 2.7333, "value": 1.61, "curve": [2.76, 1.61, 2.795, 1.44]}, {"time": 2.8333, "value": 1.22, "curve": [2.914, 0.76, 3.01, 0]}, {"time": 3.0667, "curve": [3.175, 0, 3.338, -3.55]}, {"time": 3.5, "value": -7.1, "curve": [3.747, -7.73, 4.056, -8.91]}, {"time": 4.2333, "value": -8.91, "curve": [4.306, -8.91, 4.399, -8.18]}, {"time": 4.5, "value": -7.1}]}, "bone102": {"rotate": [{"value": -8.29, "curve": [0.2, -6.55, 0.504, 0]}, {"time": 0.6667, "curve": [0.786, 5.86, 0.923, -2.81]}, {"time": 1, "value": -8.42, "curve": [1.083, -8.42, 1.25, -13.64]}, {"time": 1.3333, "value": -13.64, "curve": [1.542, -13.64, 1.958, 7.43]}, {"time": 2.1667, "value": 7.43, "curve": [2.25, 7.43, 2.417, -2.14]}, {"time": 2.5, "value": -2.14, "curve": [2.583, -2.14, 2.75, 1.61]}, {"time": 2.8333, "value": 1.61, "curve": [2.917, 1.61, 3.083, 0]}, {"time": 3.1667, "curve": [3.25, 0, 3.375, -4.15]}, {"time": 3.5, "value": -8.29, "curve": [3.76, -8.42, 4.155, -8.91]}, {"time": 4.3667, "value": -8.91, "curve": [4.406, -8.91, 4.451, -8.68]}, {"time": 4.5, "value": -8.29}]}, "bone103": {"rotate": [{"curve": [0.167, 0, 0.5, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.417, -2.8, 1.583, 0]}, {"time": 1.6667, "curve": [1.75, 0, 1.917, -0.76]}, {"time": 2, "value": -0.76, "curve": [2.083, -0.76, 2.25, 0.22]}, {"time": 2.3333, "value": 0.22, "curve": [2.417, 0.22, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, -0.08, 3.261, -0.29]}, {"time": 3.5, "value": -0.51, "curve": [3.75, -0.51, 4.25, 0]}, {"time": 4.5}]}, "bone104": {"rotate": [{"value": -0.05, "curve": [0.247, 0.31, 0.473, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.442, -2.8, 1.658, 0]}, {"time": 1.7667, "curve": [1.85, 0, 2.017, -0.76]}, {"time": 2.1, "value": -0.76, "curve": [2.183, -0.76, 2.35, 0.22]}, {"time": 2.4333, "value": 0.22, "curve": [2.517, 0.22, 2.683, 0]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.011, 0, 3.255, -0.03]}, {"time": 3.5, "value": -0.05, "curve": [3.562, -0.33, 3.618, -0.51]}, {"time": 3.6667, "value": -0.51, "curve": [3.869, -0.51, 4.241, -0.16]}, {"time": 4.5, "value": -0.05}]}, "bone105": {"rotate": [{"value": -0.14, "curve": [0.255, 0.24, 0.49, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.467, -2.8, 1.733, 0]}, {"time": 1.8667, "curve": [1.95, 0, 2.117, -0.76]}, {"time": 2.2, "value": -0.76, "curve": [2.283, -0.76, 2.45, 0.22]}, {"time": 2.5333, "value": 0.22, "curve": [2.607, 0.22, 2.747, 0.04]}, {"time": 2.8333, "value": 0.01, "curve": [2.845, 0, 2.857, 0]}, {"time": 2.8667, "curve": [3.025, 0, 3.263, -0.07]}, {"time": 3.5, "value": -0.14, "curve": [3.627, -0.35, 3.745, -0.51]}, {"time": 3.8333, "value": -0.51, "curve": [3.995, -0.51, 4.266, -0.29]}, {"time": 4.5, "value": -0.14}]}, "bone106": {"rotate": [{"value": -0.23, "curve": [0.252, 0.16, 0.499, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.492, -2.8, 1.808, 0]}, {"time": 1.9667, "curve": [2.05, 0, 2.217, -0.76]}, {"time": 2.3, "value": -0.76, "curve": [2.383, -0.76, 2.55, 0.22]}, {"time": 2.6333, "value": 0.22, "curve": [2.682, 0.22, 2.761, 0.14]}, {"time": 2.8333, "value": 0.08, "curve": [2.884, 0.04, 2.932, 0]}, {"time": 2.9667, "curve": [3.1, 0, 3.3, -0.12]}, {"time": 3.5, "value": -0.23, "curve": [3.676, -0.37, 3.849, -0.51]}, {"time": 3.9667, "value": -0.51, "curve": [4.099, -0.51, 4.302, -0.36]}, {"time": 4.5, "value": -0.23}]}, "bone107": {"rotate": [{"value": -0.32, "curve": [0.242, 0.06, 0.503, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.517, -2.8, 1.883, 0]}, {"time": 2.0667, "curve": [2.15, 0, 2.317, -0.76]}, {"time": 2.4, "value": -0.76, "curve": [2.483, -0.76, 2.65, 0.22]}, {"time": 2.7333, "value": 0.22, "curve": [2.76, 0.22, 2.795, 0.2]}, {"time": 2.8333, "value": 0.17, "curve": [2.914, 0.1, 3.01, 0]}, {"time": 3.0667, "curve": [3.175, 0, 3.338, -0.16]}, {"time": 3.5, "value": -0.32, "curve": [3.718, -0.4, 3.953, -0.51]}, {"time": 4.1, "value": -0.51, "curve": [4.203, -0.51, 4.347, -0.42]}, {"time": 4.5, "value": -0.32}]}, "bone108": {"rotate": [{"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.417, 3.33, 1.583, -1.51]}, {"time": 1.6667, "value": -1.51, "curve": [1.75, -1.51, 1.917, 0.78]}, {"time": 2, "value": 0.78, "curve": [2.083, 0.78, 2.25, -0.44]}, {"time": 2.3333, "value": -0.44, "curve": [2.417, -0.44, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, 0.21, 3.261, 0.81]}, {"time": 3.5, "value": 1.4, "curve": [3.75, 1.4, 4.25, 0]}, {"time": 4.5}]}, "bone109": {"rotate": [{"value": 0.06, "curve": [0.24, 0.02, 0.463, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.442, 3.33, 1.658, -1.51]}, {"time": 1.7667, "value": -1.51, "curve": [1.85, -1.51, 2.017, 0.78]}, {"time": 2.1, "value": 0.78, "curve": [2.183, 0.78, 2.35, -0.44]}, {"time": 2.4333, "value": -0.44, "curve": [2.517, -0.44, 2.683, 0]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.011, 0, 3.255, 0.03]}, {"time": 3.5, "value": 0.06, "curve": [3.536, 0.92, 3.57, 1.4]}, {"time": 3.6, "value": 1.4, "curve": [3.821, 1.4, 4.24, 0.28]}, {"time": 4.5, "value": 0.06}]}, "bone110": {"rotate": [{"value": 0.18, "curve": [0.25, 0.07, 0.477, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.467, 3.33, 1.733, -1.51]}, {"time": 1.8667, "value": -1.51, "curve": [1.95, -1.51, 2.117, 0.78]}, {"time": 2.2, "value": 0.78, "curve": [2.283, 0.78, 2.45, -0.44]}, {"time": 2.5333, "value": -0.44, "curve": [2.607, -0.44, 2.747, -0.09]}, {"time": 2.8333, "value": -0.02, "curve": [2.845, -0.01, 2.857, 0]}, {"time": 2.8667, "curve": [3.025, 0, 3.263, 0.09]}, {"time": 3.5, "value": 0.18, "curve": [3.575, 0.93, 3.643, 1.4]}, {"time": 3.7, "value": 1.4, "curve": [3.894, 1.4, 4.244, 0.53]}, {"time": 4.5, "value": 0.18}]}, "bone111": {"rotate": [{"value": 0.34, "curve": [0.254, 0.14, 0.487, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.492, 3.33, 1.808, -1.51]}, {"time": 1.9667, "value": -1.51, "curve": [2.05, -1.51, 2.217, 0.78]}, {"time": 2.3, "value": 0.78, "curve": [2.383, 0.78, 2.55, -0.44]}, {"time": 2.6333, "value": -0.44, "curve": [2.682, -0.44, 2.761, -0.29]}, {"time": 2.8333, "value": -0.16, "curve": [2.884, -0.07, 2.932, 0]}, {"time": 2.9667, "curve": [3.1, 0, 3.3, 0.17]}, {"time": 3.5, "value": 0.34, "curve": [3.615, 0.96, 3.719, 1.4]}, {"time": 3.8, "value": 1.4, "curve": [3.97, 1.4, 4.259, 0.74]}, {"time": 4.5, "value": 0.34}]}, "bone112": {"rotate": [{"value": 0.51, "curve": [0.254, 0.23, 0.495, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.517, 3.33, 1.883, -1.51]}, {"time": 2.0667, "value": -1.51, "curve": [2.15, -1.51, 2.317, 0.78]}, {"time": 2.4, "value": 0.78, "curve": [2.483, 0.78, 2.65, -0.44]}, {"time": 2.7333, "value": -0.44, "curve": [2.76, -0.44, 2.795, -0.4]}, {"time": 2.8333, "value": -0.34, "curve": [2.914, -0.21, 3.01, 0]}, {"time": 3.0667, "curve": [3.175, 0, 3.338, 0.26]}, {"time": 3.5, "value": 0.51, "curve": [3.653, 1, 3.797, 1.4]}, {"time": 3.9, "value": 1.4, "curve": [4.047, 1.4, 4.282, 0.91]}, {"time": 4.5, "value": 0.51}]}, "bone113": {"rotate": [{"value": 0.76, "curve": [0.248, 0.39, 0.501, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.542, 3.33, 1.958, -1.51]}, {"time": 2.1667, "value": -1.51, "curve": [2.25, -1.51, 2.417, 0.78]}, {"time": 2.5, "value": 0.78, "curve": [2.583, 0.78, 2.75, -0.44]}, {"time": 2.8333, "value": -0.44, "curve": [2.917, -0.44, 3.083, 0]}, {"time": 3.1667, "curve": [3.25, 0, 3.375, 0.38]}, {"time": 3.5, "value": 0.76, "curve": [3.698, 1.07, 3.901, 1.4]}, {"time": 4.0333, "value": 1.4, "curve": [4.151, 1.4, 4.324, 1.09]}, {"time": 4.5, "value": 0.76}]}, "bone114": {"rotate": [{"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.517, 3.33, 1.883, -1.51]}, {"time": 2.0667, "value": -1.51, "curve": [2.15, -1.51, 2.317, 0.78]}, {"time": 2.4, "value": 0.78, "curve": [2.483, 0.78, 2.65, -0.44]}, {"time": 2.7333, "value": -0.44, "curve": [2.76, -0.44, 2.795, -0.4]}, {"time": 2.8333, "value": -0.34, "curve": [2.914, -0.21, 3.01, 0]}, {"time": 3.0667, "curve": [3.175, 0, 3.338, 1.36]}, {"time": 3.5, "value": 2.73, "curve": [3.75, 2.73, 4.25, 0]}, {"time": 4.5}]}, "bone115": {"rotate": [{"value": 0.26, "curve": [0.247, 0.1, 0.473, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.542, 3.33, 1.958, -1.51]}, {"time": 2.1667, "value": -1.51, "curve": [2.25, -1.51, 2.417, 0.78]}, {"time": 2.5, "value": 0.78, "curve": [2.583, 0.78, 2.75, -0.44]}, {"time": 2.8333, "value": -0.44, "curve": [2.917, -0.44, 3.083, 0]}, {"time": 3.1667, "curve": [3.25, 0, 3.375, 0.13]}, {"time": 3.5, "value": 0.26, "curve": [3.562, 1.8, 3.618, 2.73]}, {"time": 3.6667, "value": 2.73, "curve": [3.869, 2.73, 4.241, 0.88]}, {"time": 4.5, "value": 0.26}]}, "bone116": {"rotate": [{"value": 0.77, "curve": [0.255, 0.33, 0.49, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.567, 3.33, 2.033, -1.51]}, {"time": 2.2667, "value": -1.51, "curve": [2.35, -1.51, 2.517, 0.78]}, {"time": 2.6, "value": 0.78, "curve": [2.657, 0.78, 2.753, 0.2]}, {"time": 2.8333, "value": -0.15, "curve": [2.872, -0.32, 2.906, -0.44]}, {"time": 2.9333, "value": -0.44, "curve": [3.017, -0.44, 3.183, 0]}, {"time": 3.2667, "curve": [3.325, 0, 3.413, 0.39]}, {"time": 3.5, "value": 0.77, "curve": [3.627, 1.89, 3.745, 2.73]}, {"time": 3.8333, "value": 2.73, "curve": [3.995, 2.73, 4.266, 1.56]}, {"time": 4.5, "value": 0.77}]}, "bone117": {"rotate": [{"value": 0.31, "curve": [0.247, 0.12, 0.473, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.5, 3.33, 1.833, -1.51]}, {"time": 2, "value": -1.51, "curve": [2.083, -1.51, 2.25, 0.78]}, {"time": 2.3333, "value": 0.78, "curve": [2.417, 0.78, 2.583, -0.44]}, {"time": 2.6667, "value": -0.44, "curve": [2.708, -0.44, 2.771, -0.33]}, {"time": 2.8333, "value": -0.22, "curve": [2.896, -0.11, 2.958, 0]}, {"time": 3, "curve": [3.125, 0, 3.313, 0.15]}, {"time": 3.5, "value": 0.31, "curve": [3.562, 2.11, 3.618, 3.2]}, {"time": 3.6667, "value": 3.2, "curve": [3.869, 3.2, 4.241, 1.03]}, {"time": 4.5, "value": 0.31}], "translate": [{"x": 0.08, "y": -0.71, "curve": [0.247, 0.03, 0.473, 0, 0.247, -0.27, 0.473, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 3, "curve": [3.125, 0, 3.313, 0.04, 3.125, 0, 3.313, -0.35]}, {"time": 3.5, "x": 0.08, "y": -0.71, "curve": [3.562, 0.52, 3.618, 0.79, 3.562, -4.86, 3.618, -7.36]}, {"time": 3.6667, "x": 0.79, "y": -7.36, "curve": [3.869, 0.79, 4.241, 0.25, 3.869, -7.36, 4.241, -2.37]}, {"time": 4.5, "x": 0.08, "y": -0.71}]}, "bone118": {"rotate": [{"value": 0.91, "curve": [0.255, 0.39, 0.49, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.525, 3.33, 1.908, -1.51]}, {"time": 2.1, "value": -1.51, "curve": [2.183, -1.51, 2.35, 0.78]}, {"time": 2.4333, "value": 0.78, "curve": [2.517, 0.78, 2.683, -0.44]}, {"time": 2.7667, "value": -0.44, "curve": [2.786, -0.44, 2.808, -0.42]}, {"time": 2.8333, "value": -0.39, "curve": [2.919, -0.28, 3.035, 0]}, {"time": 3.1, "curve": [3.2, 0, 3.35, 0.45]}, {"time": 3.5, "value": 0.91, "curve": [3.627, 2.22, 3.745, 3.2]}, {"time": 3.8333, "value": 3.2, "curve": [3.995, 3.2, 4.266, 1.82]}, {"time": 4.5, "value": 0.91}]}, "bone119": {"rotate": [{"value": 1.6, "curve": [0.25, 0.8, 0.5, 0]}, {"time": 0.6667, "curve": [0.786, -1.39, 0.923, 0.67]}, {"time": 1, "value": 2, "curve": [1.083, 2, 1.25, 3.33]}, {"time": 1.3333, "value": 3.33, "curve": [1.55, 3.33, 1.983, -1.51]}, {"time": 2.2, "value": -1.51, "curve": [2.283, -1.51, 2.45, 0.78]}, {"time": 2.5333, "value": 0.78, "curve": [2.607, 0.78, 2.747, -0.2]}, {"time": 2.8333, "value": -0.39, "curve": [2.845, -0.43, 2.857, -0.44]}, {"time": 2.8667, "value": -0.44, "curve": [2.95, -0.44, 3.117, 0]}, {"time": 3.2, "curve": [3.275, 0, 3.388, 0.8]}, {"time": 3.5, "value": 1.6, "curve": [3.688, 2.4, 3.875, 3.2]}, {"time": 4, "value": 3.2, "curve": [4.125, 3.2, 4.313, 2.4]}, {"time": 4.5, "value": 1.6}]}, "bone120": {"rotate": [{"value": -0.15, "curve": [0.247, 0.28, 0.473, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.542, -2.8, 1.958, 0]}, {"time": 2.1667, "curve": [2.25, 0, 2.417, -0.76]}, {"time": 2.5, "value": -0.76, "curve": [2.583, -0.76, 2.75, 0.22]}, {"time": 2.8333, "value": 0.22, "curve": [2.917, 0.22, 3.083, 0]}, {"time": 3.1667, "curve": [3.25, 0, 3.375, -0.07]}, {"time": 3.5, "value": -0.15, "curve": [3.562, -1, 3.618, -1.51]}, {"time": 3.6667, "value": -1.51, "curve": [3.869, -1.51, 4.241, -0.49]}, {"time": 4.5, "value": -0.15}]}, "bone121": {"rotate": [{"value": -0.43, "curve": [0.255, 0.12, 0.49, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.567, -2.8, 2.033, 0]}, {"time": 2.2667, "curve": [2.35, 0, 2.517, -0.76]}, {"time": 2.6, "value": -0.76, "curve": [2.657, -0.76, 2.753, -0.3]}, {"time": 2.8333, "value": -0.02, "curve": [2.872, 0.12, 2.906, 0.22]}, {"time": 2.9333, "value": 0.22, "curve": [3.017, 0.22, 3.183, 0]}, {"time": 3.2667, "curve": [3.325, 0, 3.413, -0.21]}, {"time": 3.5, "value": -0.43, "curve": [3.627, -1.04, 3.745, -1.51]}, {"time": 3.8333, "value": -1.51, "curve": [3.995, -1.51, 4.266, -0.86]}, {"time": 4.5, "value": -0.43}]}, "bone122": {"rotate": [{"value": -0.75, "curve": [0.25, -0.11, 0.5, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.592, -2.8, 2.108, 0]}, {"time": 2.3667, "curve": [2.45, 0, 2.617, -0.76]}, {"time": 2.7, "value": -0.76, "curve": [2.734, -0.76, 2.782, -0.6]}, {"time": 2.8333, "value": -0.4, "curve": [2.906, -0.13, 2.984, 0.22]}, {"time": 3.0333, "value": 0.22, "curve": [3.117, 0.22, 3.283, 0]}, {"time": 3.3667, "curve": [3.4, 0, 3.45, -0.38]}, {"time": 3.5, "value": -0.75, "curve": [3.688, -1.13, 3.875, -1.51]}, {"time": 4, "value": -1.51, "curve": [4.125, -1.51, 4.313, -1.13]}, {"time": 4.5, "value": -0.75}]}, "bone123": {"rotate": [{"value": -0.17, "curve": [0.247, 0.27, 0.473, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.542, -2.8, 1.958, 0]}, {"time": 2.1667, "curve": [2.25, 0, 2.417, -0.76]}, {"time": 2.5, "value": -0.76, "curve": [2.583, -0.76, 2.75, 0.22]}, {"time": 2.8333, "value": 0.22, "curve": [2.917, 0.22, 3.083, 0]}, {"time": 3.1667, "curve": [3.25, 0, 3.375, -0.09]}, {"time": 3.5, "value": -0.17, "curve": [3.562, -1.18, 3.618, -1.78]}, {"time": 3.6667, "value": -1.78, "curve": [3.869, -1.78, 4.241, -0.57]}, {"time": 4.5, "value": -0.17}]}, "bone124": {"rotate": [{"value": -0.51, "curve": [0.255, 0.09, 0.49, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.567, -2.8, 2.033, 0]}, {"time": 2.2667, "curve": [2.35, 0, 2.517, -0.76]}, {"time": 2.6, "value": -0.76, "curve": [2.657, -0.76, 2.753, -0.3]}, {"time": 2.8333, "value": -0.02, "curve": [2.872, 0.12, 2.906, 0.22]}, {"time": 2.9333, "value": 0.22, "curve": [3.017, 0.22, 3.183, 0]}, {"time": 3.2667, "curve": [3.325, 0, 3.413, -0.25]}, {"time": 3.5, "value": -0.51, "curve": [3.627, -1.23, 3.745, -1.78]}, {"time": 3.8333, "value": -1.78, "curve": [3.995, -1.78, 4.266, -1.01]}, {"time": 4.5, "value": -0.51}]}, "bone125": {"rotate": [{"curve": [0.167, 0, 0.5, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.417, -2.8, 1.583, 0]}, {"time": 1.6667, "curve": [1.75, 0, 1.917, -0.76]}, {"time": 2, "value": -0.76, "curve": [2.083, -0.76, 2.25, 0.22]}, {"time": 2.3333, "value": 0.22, "curve": [2.417, 0.22, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, -0.55, 3.261, -2.07]}, {"time": 3.5, "value": -3.6, "curve": [3.75, -3.6, 4.25, 0]}, {"time": 4.5}]}, "bone126": {"rotate": [{"value": -0.35, "curve": [0.247, 0.2, 0.473, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.417, -2.8, 1.583, 0]}, {"time": 1.6667, "curve": [1.75, 0, 1.917, -0.76]}, {"time": 2, "value": -0.76, "curve": [2.083, -0.76, 2.25, 0.22]}, {"time": 2.3333, "value": 0.22, "curve": [2.417, 0.22, 2.583, 0]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, -0.05, 3.261, -0.2]}, {"time": 3.5, "value": -0.35, "curve": [3.562, -2.38, 3.618, -3.6]}, {"time": 3.6667, "value": -3.6, "curve": [3.869, -3.6, 4.241, -1.16]}, {"time": 4.5, "value": -0.35}]}, "bone127": {"rotate": [{"curve": [0.167, 0, 0.5, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.442, -2.8, 1.658, 0]}, {"time": 1.7667, "curve": [1.85, 0, 2.017, -0.76]}, {"time": 2.1, "value": -0.76, "curve": [2.183, -0.76, 2.35, 0.22]}, {"time": 2.4333, "value": 0.22, "curve": [2.517, 0.22, 2.683, 0]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.011, -0.16, 3.255, -1.09]}, {"time": 3.5, "value": -2.02, "curve": [3.75, -2.02, 4.25, 0]}, {"time": 4.5}]}, "bone128": {"rotate": [{"value": -0.57, "curve": [0.247, 0.12, 0.473, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.467, -2.8, 1.733, 0]}, {"time": 1.8667, "curve": [1.95, 0, 2.117, -0.76]}, {"time": 2.2, "value": -0.76, "curve": [2.283, -0.76, 2.45, 0.22]}, {"time": 2.5333, "value": 0.22, "curve": [2.607, 0.22, 2.747, 0.04]}, {"time": 2.8333, "value": 0.01, "curve": [2.845, 0, 2.857, 0]}, {"time": 2.8667, "curve": [3.025, 0, 3.263, -0.28]}, {"time": 3.5, "value": -0.57, "curve": [3.562, -3.88, 3.618, -5.86]}, {"time": 3.6667, "value": -5.86, "curve": [3.869, -5.86, 4.241, -1.89]}, {"time": 4.5, "value": -0.57}]}, "bone129": {"rotate": [{"value": -1.66, "curve": [0.255, -0.41, 0.49, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.492, -2.8, 1.808, 0]}, {"time": 1.9667, "curve": [2.05, 0, 2.217, -0.76]}, {"time": 2.3, "value": -0.76, "curve": [2.383, -0.76, 2.55, 0.22]}, {"time": 2.6333, "value": 0.22, "curve": [2.682, 0.22, 2.761, 0.14]}, {"time": 2.8333, "value": 0.08, "curve": [2.884, 0.04, 2.932, 0]}, {"time": 2.9667, "curve": [3.1, 0, 3.3, -0.83]}, {"time": 3.5, "value": -1.66, "curve": [3.627, -4.06, 3.745, -5.86]}, {"time": 3.8333, "value": -5.86, "curve": [3.995, -5.86, 4.266, -3.34]}, {"time": 4.5, "value": -1.66}]}, "bone130": {"rotate": [{"value": -2.93, "curve": [0.25, -1.2, 0.5, 0.53]}, {"time": 0.6667, "value": 0.53, "curve": [0.786, 2.17, 0.923, -0.26]}, {"time": 1, "value": -1.83, "curve": [1.083, -1.83, 1.25, -2.8]}, {"time": 1.3333, "value": -2.8, "curve": [1.517, -2.8, 1.883, 0]}, {"time": 2.0667, "curve": [2.15, 0, 2.317, -0.76]}, {"time": 2.4, "value": -0.76, "curve": [2.483, -0.76, 2.65, 0.22]}, {"time": 2.7333, "value": 0.22, "curve": [2.76, 0.22, 2.795, 0.2]}, {"time": 2.8333, "value": 0.17, "curve": [2.914, 0.1, 3.01, 0]}, {"time": 3.0667, "curve": [3.175, 0, 3.338, -1.47]}, {"time": 3.5, "value": -2.93, "curve": [3.688, -4.4, 3.875, -5.86]}, {"time": 4, "value": -5.86, "curve": [4.125, -5.86, 4.313, -4.4]}, {"time": 4.5, "value": -2.93}]}, "bone131": {"rotate": [{"curve": [0.167, 0, 0.5, 1.06]}, {"time": 0.6667, "value": 1.06, "curve": [0.786, 1.81, 0.923, 0.71]}, {"time": 1, "curve": [1.083, 0, 1.25, 0.24]}, {"time": 1.3333, "value": 0.24, "curve": [1.417, 0.24, 1.583, 0.14]}, {"time": 1.6667, "value": 0.14, "curve": [1.991, 0.1, 2.327, 0.05]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, -1.89, 3.338, -6.62]}, {"time": 3.5, "value": -6.62, "curve": [3.75, -6.62, 4.25, 0]}, {"time": 4.5}]}, "bone132": {"rotate": [{"curve": [0.167, 0, 0.5, 12.86]}, {"time": 0.6667, "value": 12.86, "curve": [0.786, 12.48, 0.923, 13.05]}, {"time": 1, "value": 13.41, "curve": [1.083, 13.41, 1.25, 14.87]}, {"time": 1.3333, "value": 14.87, "curve": [1.417, 14.87, 1.583, 10.82]}, {"time": 1.6667, "value": 10.82, "curve": [1.75, 10.82, 1.917, 11.9]}, {"time": 2, "value": 11.9, "curve": [2.083, 11.9, 2.25, 11.4]}, {"time": 2.3333, "value": 11.4, "curve": "stepped"}, {"time": 2.8333, "value": 11.4, "curve": [3.047, 11.23, 3.338, 10.8]}, {"time": 3.5, "value": 10.8, "curve": [3.75, 10.8, 4.25, 0]}, {"time": 4.5}]}, "bone133": {"rotate": [{"curve": [0.167, 0, 0.5, -8.35]}, {"time": 0.6667, "value": -8.35, "curve": [0.786, 1.94, 0.923, -13.29]}, {"time": 1, "value": -23.13, "curve": [1.083, -23.13, 1.25, -25.87]}, {"time": 1.3333, "value": -25.87, "curve": [1.417, -25.87, 1.583, -21.4]}, {"time": 1.6667, "value": -21.4, "curve": [1.75, -21.4, 1.917, -24.97]}, {"time": 2, "value": -24.97, "curve": [2.083, -24.97, 2.25, -23.56]}, {"time": 2.3333, "value": -23.56, "curve": "stepped"}, {"time": 2.8333, "value": -23.56, "curve": [3.047, -18.93, 3.338, -7.36]}, {"time": 3.5, "value": -7.36, "curve": [3.75, -7.36, 4.25, 0]}, {"time": 4.5}]}, "bone134": {"rotate": [{"curve": [0.167, 0, 0.5, 22.76]}, {"time": 0.6667, "value": 22.76, "curve": [0.786, 38.6, 0.923, 15.16]}, {"time": 1, "curve": [1.083, 0, 1.25, -9.63]}, {"time": 1.3333, "value": -9.63, "curve": [1.417, -9.63, 1.583, 3.05]}, {"time": 1.6667, "value": 3.05, "curve": [1.777, 3.39, 1.888, 3.74]}, {"time": 2, "value": 4.1, "curve": [2.11, 2.77, 2.221, 1.4]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 2.34, 3.338, 8.2]}, {"time": 3.5, "value": 8.2, "curve": [3.75, 8.2, 4.25, 0]}, {"time": 4.5}]}, "bone135": {"rotate": [{"curve": [0.167, 0, 0.5, 5.07]}, {"time": 0.6667, "value": 5.07, "curve": [0.786, 8.6, 0.923, 3.38]}, {"time": 1, "curve": [1.083, 0, 1.25, -5.23]}, {"time": 1.3333, "value": -5.23, "curve": [1.417, -5.23, 1.583, 0.68]}, {"time": 1.6667, "value": 0.68, "curve": [1.777, 1.27, 1.888, 1.88]}, {"time": 2, "value": 2.52, "curve": [2.11, 1.7, 2.221, 0.86]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.13, 3.338, 0.44]}, {"time": 3.5, "value": 0.44, "curve": [3.75, 0.44, 4.25, 0]}, {"time": 4.5}]}, "bone136": {"rotate": [{"curve": [0.167, 0, 0.5, 8.89]}, {"time": 0.6667, "value": 8.89, "curve": [0.786, 21.69, 0.923, 2.76]}, {"time": 1, "value": -9.49, "curve": [1.083, -9.49, 1.25, -10.99]}, {"time": 1.3333, "value": -10.99, "curve": [1.417, -10.99, 1.583, -7.02]}, {"time": 1.6667, "value": -7.02, "curve": [1.777, -5.61, 1.888, -4.14]}, {"time": 2, "value": -2.62, "curve": [2.11, -1.77, 2.221, -0.9]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.76, 3.338, 2.67]}, {"time": 3.5, "value": 2.67, "curve": [3.75, 2.67, 4.25, 0]}, {"time": 4.5}]}, "bone137": {"rotate": [{"curve": [0.167, 0, 0.5, 6.85]}, {"time": 0.6667, "value": 6.85, "curve": [0.786, 16.64, 0.923, 2.15]}, {"time": 1, "value": -7.22, "curve": [1.083, -7.22, 1.25, -12.62]}, {"time": 1.3333, "value": -12.62, "curve": [1.417, -12.62, 1.583, -5.33]}, {"time": 1.6667, "value": -5.33, "curve": [1.777, -4.1, 1.888, -2.82]}, {"time": 2, "value": -1.5, "curve": [2.11, -1.01, 2.221, -0.51]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.76, 3.338, 2.67]}, {"time": 3.5, "value": 2.67, "curve": [3.75, 2.67, 4.25, 0]}, {"time": 4.5}]}, "bone138": {"rotate": [{"curve": [0.167, 0, 0.5, 24.38]}, {"time": 0.6667, "value": 24.38, "curve": [0.786, 45.14, 0.923, 14.42]}, {"time": 1, "value": -5.44, "curve": [1.083, -5.44, 1.25, -6.1]}, {"time": 1.3333, "value": -6.1, "curve": [1.417, -6.1, 1.583, -1.44]}, {"time": 1.6667, "value": -1.44, "curve": [1.777, -0.63, 1.888, 0.22]}, {"time": 2, "value": 1.1, "curve": [2.11, 0.74, 2.221, 0.38]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, 0.76, 3.338, 2.67]}, {"time": 3.5, "value": 2.67, "curve": [3.75, 2.67, 4.25, 0]}, {"time": 4.5}]}, "bone139": {"rotate": [{"curve": [0.167, 0, 0.5, 1.34]}, {"time": 0.6667, "value": 1.34, "curve": [0.786, 2.27, 0.923, 0.89]}, {"time": 1, "curve": [1.083, 0, 1.25, 0.3]}, {"time": 1.3333, "value": 0.3, "curve": [1.417, 0.3, 1.583, 0.18]}, {"time": 1.6667, "value": 0.18, "curve": [1.991, 0.13, 2.327, 0.06]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, -1.29, 3.338, -4.53]}, {"time": 3.5, "value": -4.53, "curve": [3.75, -4.53, 4.25, 0]}, {"time": 4.5}]}, "bone140": {"rotate": [{"curve": [0.167, 0, 0.5, 0.38]}, {"time": 0.6667, "value": 0.38, "curve": [0.786, 1.82, 0.923, -0.31]}, {"time": 1, "value": -1.69, "curve": [1.083, -1.69, 1.25, -3.58]}, {"time": 1.3333, "value": -3.58, "curve": [1.417, -3.58, 1.583, -1.24]}, {"time": 1.6667, "value": -1.24, "curve": [1.75, -1.24, 1.917, -1.97]}, {"time": 2, "value": -1.97, "curve": [2.083, -1.97, 2.25, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, -1.7, 3.338, -5.94]}, {"time": 3.5, "value": -5.94, "curve": [3.75, -5.94, 4.25, 0]}, {"time": 4.5}]}, "bone141": {"rotate": [{"curve": [0.167, 0, 0.5, -1.54]}, {"time": 0.6667, "value": -1.54, "curve": [0.786, -8.17, 0.923, 1.63]}, {"time": 1, "value": 7.97, "curve": [1.083, 7.97, 1.25, 9.89]}, {"time": 1.3333, "value": 9.89, "curve": [1.417, 9.89, 1.583, 8.77]}, {"time": 1.6667, "value": 8.77, "curve": [1.75, 8.77, 1.917, 9.51]}, {"time": 2, "value": 9.51, "curve": "stepped"}, {"time": 2.8333, "value": 9.51, "curve": [3.047, 8.06, 3.338, 4.45]}, {"time": 3.5, "value": 4.45, "curve": [3.75, 4.45, 4.25, 0]}, {"time": 4.5}]}, "bone142": {"rotate": [{"curve": [0.167, 0, 0.5, -17.17]}, {"time": 0.6667, "value": -17.17, "curve": [0.786, -35.38, 0.923, -8.43]}, {"time": 1, "value": 8.99, "curve": [1.083, 8.99, 1.25, 12.91]}, {"time": 1.3333, "value": 12.91, "curve": [1.417, 12.91, 1.583, 4.77]}, {"time": 1.6667, "value": 4.77, "curve": [1.75, 4.77, 1.917, 6.7]}, {"time": 2, "value": 6.7, "curve": [2.083, 6.7, 2.25, 0]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.047, -2.61, 3.338, -9.13]}, {"time": 3.5, "value": -9.13, "curve": [3.75, -9.13, 4.25, 0]}, {"time": 4.5}]}, "bone144": {"rotate": [{"time": 2.8333, "curve": [3.022, -0.16, 3.261, -0.59]}, {"time": 3.5, "value": -1.03, "curve": [3.75, -1.03, 4.25, 0]}, {"time": 4.5}]}, "bone145": {"rotate": [{"time": 2.8333, "curve": [3.022, -0.16, 3.261, -0.59]}, {"time": 3.5, "value": -1.03, "curve": [3.75, -1.03, 4.25, 0]}, {"time": 4.5}]}, "bone146": {"rotate": [{"time": 2.8333, "curve": [3.022, -0.16, 3.261, -0.59]}, {"time": 3.5, "value": -1.03, "curve": [3.75, -1.03, 4.25, 0]}, {"time": 4.5}]}, "bone147": {"rotate": [{"time": 2.8333, "curve": [3.022, -0.23, 3.261, -0.89]}, {"time": 3.5, "value": -1.54, "curve": [3.75, -1.54, 4.25, 0]}, {"time": 4.5}]}, "bone148": {"rotate": [{"time": 2.8333, "curve": [3.022, -0.52, 3.261, -1.95]}, {"time": 3.5, "value": -3.38, "curve": [3.75, -3.38, 4.25, 0]}, {"time": 4.5}]}, "bone149": {"rotate": [{"time": 2.8333, "curve": [3.022, 0.05, 3.261, 0.18]}, {"time": 3.5, "value": 0.32, "curve": [3.75, 0.32, 4.25, 0]}, {"time": 4.5}]}, "bone151": {"rotate": [{"curve": [0.167, 0, 0.5, -7.77]}, {"time": 0.6667, "value": -7.77, "curve": [0.786, -18.76, 0.923, -2.5]}, {"time": 1, "value": 8.02, "curve": [1.083, 8.02, 1.25, -5.56]}, {"time": 1.3333, "value": -5.56, "curve": [1.417, -5.56, 1.583, 3.12]}, {"time": 1.6667, "value": 3.12, "curve": [1.777, 1.83, 1.888, 0.49]}, {"time": 2, "value": -0.9, "curve": [2.109, -0.61, 2.221, -0.31]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.039, 1.3, 3.269, 3.23]}, {"time": 3.5, "value": 5.17, "curve": [3.75, 5.17, 4.25, 0]}, {"time": 4.5}]}, "bone152": {"rotate": [{"curve": [0.167, 0, 0.5, -7.77]}, {"time": 0.6667, "value": -7.77, "curve": [0.786, -18.76, 0.923, -2.5]}, {"time": 1, "value": 8.02, "curve": [1.083, 8.02, 1.25, -5.56]}, {"time": 1.3333, "value": -5.56, "curve": [1.417, -5.56, 1.583, 3.12]}, {"time": 1.6667, "value": 3.12, "curve": [1.777, 1.83, 1.888, 0.49]}, {"time": 2, "value": -0.9, "curve": [2.109, -0.61, 2.221, -0.31]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.039, -2.86, 3.269, -7.11]}, {"time": 3.5, "value": -11.36, "curve": [3.75, -11.36, 4.25, 0]}, {"time": 4.5}]}, "bone153": {"rotate": [{"time": 2.8333, "curve": [3.022, 0.12, 3.261, 0.43]}, {"time": 3.5, "value": 0.75, "curve": [3.75, 0.75, 4.25, 0]}, {"time": 4.5}]}, "bone154": {"rotate": [{"time": 2.8333, "curve": [3.022, 0.12, 3.261, 0.43]}, {"time": 3.5, "value": 0.75, "curve": [3.75, 0.75, 4.25, 0]}, {"time": 4.5}]}, "bone155": {"rotate": [{"time": 2.8333, "curve": [3.022, 0.75, 3.261, 2.81]}, {"time": 3.5, "value": 4.88, "curve": [3.75, 4.88, 4.25, 0]}, {"time": 4.5}]}, "bone157": {"rotate": [{"time": 2.8333, "curve": [3.022, -0.25, 3.261, -0.95]}, {"time": 3.5, "value": -1.65, "curve": [3.75, -1.65, 4.25, 0]}, {"time": 4.5}]}, "bone158": {"rotate": [{"time": 1, "curve": [1.083, 0, 1.25, -19.61]}, {"time": 1.3333, "value": -19.61, "curve": [1.417, -19.61, 1.583, 0]}, {"time": 1.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, 1.25, 3.261, 4.72]}, {"time": 3.5, "value": 8.2, "curve": [3.75, 8.2, 4.25, 0]}, {"time": 4.5}]}, "bone16": {"rotate": [{"curve": [0.167, 0, 0.5, -4.03]}, {"time": 0.6667, "value": -4.03, "curve": [0.786, -11.21, 0.923, -0.58]}, {"time": 1, "value": 6.3, "curve": [1.083, 6.3, 1.25, 8.28]}, {"time": 1.3333, "value": 8.28, "curve": [1.417, 8.28, 1.583, -4.44]}, {"time": 1.6667, "value": -4.44, "curve": [1.777, -2.75, 1.888, -0.99]}, {"time": 2, "value": 0.83, "curve": [2.11, -0.07, 2.221, -1]}, {"time": 2.3333, "value": -1.95, "curve": [2.444, -1.31, 2.555, -0.66]}, {"time": 2.6667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.022, -0.46, 3.261, -1.73]}, {"time": 3.5, "value": -3, "curve": [3.75, -3, 4.25, 0]}, {"time": 4.5}]}, "bone17": {"rotate": [{"value": -0.29, "curve": [0.284, -2.63, 0.544, -4.03]}, {"time": 0.7667, "value": -4.03, "curve": [0.886, -11.21, 1.023, -0.58]}, {"time": 1.1, "value": 6.3, "curve": [1.183, 6.3, 1.35, 8.28]}, {"time": 1.4333, "value": 8.28, "curve": [1.517, 8.28, 1.683, -4.44]}, {"time": 1.7667, "value": -4.44, "curve": [1.877, -2.75, 1.988, -0.99]}, {"time": 2.1, "value": 0.83, "curve": [2.21, -0.07, 2.321, -1]}, {"time": 2.4333, "value": -1.95, "curve": [2.544, -1.31, 2.655, -0.66]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [3.055, -0.24, 3.361, -1.62]}, {"time": 3.6667, "value": -3, "curve": [3.869, -3, 4.241, -0.97]}, {"time": 4.5, "value": -0.29}]}, "bone18": {"rotate": [{"value": -0.85, "curve": [0.331, -2.67, 0.637, -4.03]}, {"time": 0.8667, "value": -4.03, "curve": [0.986, -11.21, 1.123, -0.58]}, {"time": 1.2, "value": 6.3, "curve": [1.283, 6.3, 1.45, 8.28]}, {"time": 1.5333, "value": 8.28, "curve": [1.617, 8.28, 1.783, -4.44]}, {"time": 1.8667, "value": -4.44, "curve": [1.977, -2.75, 2.088, -0.99]}, {"time": 2.2, "value": 0.83, "curve": [2.31, -0.07, 2.421, -1]}, {"time": 2.5333, "value": -1.95, "curve": [2.633, -1.38, 2.733, -0.79]}, {"time": 2.8333, "value": -0.2, "curve": [2.844, -0.13, 2.856, -0.07]}, {"time": 2.8667, "curve": [3.108, 0, 3.471, -1.5]}, {"time": 3.8333, "value": -3, "curve": [3.995, -3, 4.266, -1.71]}, {"time": 4.5, "value": -0.85}]}, "bone159": {"rotate": [{"curve": [0.167, 0, 0.5, -1.12]}, {"time": 0.6667, "value": -1.12, "curve": [0.786, -5.75, 0.923, 1.1]}, {"time": 1, "value": 5.52, "curve": [1.083, 5.52, 1.25, 7.92]}, {"time": 1.3333, "value": 7.92, "curve": [1.417, 7.92, 1.583, -2.92]}, {"time": 1.6667, "value": -2.92, "curve": [1.777, -1.03, 1.888, 0.94]}, {"time": 2, "value": 2.97, "curve": [2.109, 2.03, 2.221, 1.03]}, {"time": 2.3333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.039, -0.51, 3.269, -1.26]}, {"time": 3.5, "value": -2.02, "curve": [3.75, -2.02, 4.25, 0]}, {"time": 4.5}]}, "bone160": {"rotate": [{"value": -0.19, "curve": [0.247, -0.78, 0.473, -1.12]}, {"time": 0.6667, "value": -1.12, "curve": [0.786, -5.75, 0.923, 1.1]}, {"time": 1, "value": 5.52, "curve": [1.083, 5.52, 1.25, 7.92]}, {"time": 1.3333, "value": 7.92, "curve": [1.442, 7.92, 1.658, -2.92]}, {"time": 1.7667, "value": -2.92, "curve": [1.877, -1.03, 1.988, 0.94]}, {"time": 2.1, "value": 2.97, "curve": [2.209, 2.03, 2.321, 1.03]}, {"time": 2.4333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.035, -0.05, 3.268, -0.12]}, {"time": 3.5, "value": -0.19, "curve": [3.562, -1.34, 3.618, -2.02]}, {"time": 3.6667, "value": -2.02, "curve": [3.869, -2.02, 4.241, -0.65]}, {"time": 4.5, "value": -0.19}]}, "bone161": {"rotate": [{"value": -1.01, "curve": [0.25, -1.07, 0.5, -1.12]}, {"time": 0.6667, "value": -1.12, "curve": [0.786, -5.75, 0.923, 1.1]}, {"time": 1, "value": 5.52, "curve": [1.083, 5.52, 1.25, 7.92]}, {"time": 1.3333, "value": 7.92, "curve": [1.467, 7.92, 1.733, -2.92]}, {"time": 1.8667, "value": -2.92, "curve": [1.977, -1.03, 2.088, 0.94]}, {"time": 2.2, "value": 2.97, "curve": [2.309, 2.03, 2.421, 1.03]}, {"time": 2.5333, "curve": "stepped"}, {"time": 2.8333, "curve": [3.031, -0.21, 3.265, -0.61]}, {"time": 3.5, "value": -1.01, "curve": [3.688, -1.51, 3.875, -2.02]}, {"time": 4, "value": -2.02, "curve": [4.125, -2.02, 4.313, -1.51]}, {"time": 4.5, "value": -1.01}]}}, "attachments": {"default": {"jiemao": {"jiemao": {"deform": [{"time": 0.2667, "offset": 100, "vertices": [0.24445, 0.19373, 0.24457, 0.19376], "curve": [0.308, 0, 0.392, 1]}, {"time": 0.4333, "vertices": [-1.37244, -2.95715, -1.37274, -2.95721, -2.28973, -5.96588, -2.29028, -5.96603, -8.26282, -5.83365, -8.26373, -5.83389, -11.55701, -6.42142, -11.55859, -6.42178, -15.85577, -6.94626, -15.85791, -6.94669, -19.82007, -9.53442, -19.82172, -9.53467, -8.96448, -11.966, -8.96649, -11.96646, -7.92847, -7.0593, -7.92932, -7.05939, -3.34601, -1.48395, -3.34644, -1.48404, -2.19232, -1.94351, -2.19318, -1.94376, -0.86743, -1.47729, -0.8678, -1.47739, -0.40436, -0.4794, -0.40448, -0.4794, -1.0224, -2.07239, -1.02264, -2.07242, -2.52759, -5.31558, -2.52814, -5.31573, -10.03925, -11.05725, -10.04059, -11.05731, -15.85663, -13.62891, -15.85852, -13.62912, -17.97083, -17.08066, -17.97479, -17.08121, -20.14484, -17.00146, -20.14832, -17.00201, -16.9809, -16.70618, -16.98437, -16.7066, -13.75867, -12.97495, -13.76251, -12.97546, -8.70911, -6.95657, -8.71094, -6.95685, -2.80872, -2.47641, -2.8092, -2.47644, 0, 0, 0, 0, -3.68768, -3.90903, -3.68817, -3.90909, -8.41577, -2.08881, -8.41724, -2.08923, -17.14258, -1.95975, -17.14471, -1.9603, -24.08917, -4.92014, -24.09119, -4.92062, -25.88513, -7.70139, -25.88855, -7.7023, -26.36041, -9.72449, -26.36292, -9.72522, -22.9765, -11.32477, -22.97852, -11.32538, -16.34161, -6.72672, -16.34332, -6.72726, -11.47717, -5.09595, -11.479, -5.09628, -5.66138, -1.87128, -5.66327, -1.8718, -1.95447, -3.0065, -1.95477, -3.00659, -0.83765, -1.28848, -0.83783, -1.28857, -1.92505, 0.52689, -1.92615, 0.52689, -3.00824, 1.09088, -3.00958, 1.09067, -7.659, -0.63235, -7.66113, -0.63269, -14.50122, -1.81366, -14.50372, -1.81403, -19.01862, -2.87173, -19.0213, -2.87216, -18.73608, -6.17081, -18.7403, -6.17114, -10.23059, -15.53143, -10.23322, -15.53204, -11.79858, -18.33997, -11.80127, -18.34045, -11.65454, -19.54739, -11.65778, -19.54807, -10.20728, -17.77872, -10.21045, -17.77957, -9.51666, -12.91257, -9.51959, -12.9133, -6.50336, -8.67804, -6.50415, -8.67838, -3.01349, -3.38477, -3.01379, -3.38489], "curve": [0.475, 0, 0.558, 1]}, {"time": 0.6, "vertices": [-1.37244, -2.95715, -1.37274, -2.95721, -2.28973, -5.96588, -2.29028, -5.96603, -8.26282, -5.83365, -8.26373, -5.83389, -11.55701, -6.42142, -11.55859, -6.42178, -15.85577, -6.94626, -15.85791, -6.94669, -22.48309, -10.15182, -22.48419, -10.15182, -8.96448, -11.966, -8.96649, -11.96646, -7.92847, -7.0593, -7.92932, -7.05939, -3.34601, -1.48395, -3.34644, -1.48404, -2.19232, -1.94351, -2.19318, -1.94376, -0.86743, -1.47729, -0.8678, -1.47739, -0.40436, -0.4794, -0.40448, -0.4794, -1.0224, -2.07239, -1.02264, -2.07242, -2.52759, -5.31558, -2.52814, -5.31573, -10.03925, -11.05725, -10.04059, -11.05731, -14.97144, -13.11221, -14.97327, -13.11237, -18.35901, -17.75836, -18.36285, -17.75885, -20.73749, -17.65567, -20.7406, -17.65607, -18.92822, -16.58401, -18.9314, -16.58438, -15.88599, -12.62552, -15.88965, -12.62604, -11.15424, -6.67386, -11.15527, -6.67392, -8.58038, -1.4996, -8.57983, -1.4989, 0, 0, 0, 0, -3.68768, -3.90903, -3.68817, -3.90909, -8.85284, -4.25955, -8.85358, -4.25967, -16.49146, -6.65393, -16.49292, -6.65405, -25.13934, -7.22635, -25.14105, -7.22653, -28.56842, -10.67197, -28.57068, -10.67261, -29.34344, -11.44, -29.34509, -11.4407, -22.9765, -11.32477, -22.97852, -11.32538, -16.34161, -6.72672, -16.34332, -6.72726, -11.47717, -5.09595, -11.479, -5.09628, -5.66138, -1.87128, -5.66327, -1.8718, -1.95447, -3.0065, -1.95477, -3.00659, -0.83765, -1.28848, -0.83783, -1.28857, -1.92505, 0.52689, -1.92615, 0.52689, -3.00824, 1.09088, -3.00958, 1.09067, -15.95477, 1.16718, -15.95587, 1.16748, -23.5672, -2.159, -23.56738, -2.15851, -29.0083, -3.1196, -29.0097, -3.1196, -26.47443, -6.66357, -26.4776, -6.66342, -10.23059, -15.53143, -10.23322, -15.53204, -11.79858, -18.33997, -11.80127, -18.34045, -11.94861, -19.41034, -11.95142, -19.41089, -10.20728, -17.77872, -10.21045, -17.77957, -8.88257, -12.78, -8.88507, -12.78064, -6.50336, -8.67804, -6.50415, -8.67838, -2.94281, -2.77371, -2.94305, -2.7738], "curve": [0.642, 0, 0.725, 1]}, {"time": 0.7667, "offset": 100, "vertices": [0.24445, 0.19373, 0.24457, 0.19376], "curve": "stepped"}, {"time": 3.4, "offset": 100, "vertices": [0.24445, 0.19373, 0.24457, 0.19376], "curve": [3.442, 0, 3.525, 1]}, {"time": 3.5667, "vertices": [-1.37244, -2.95715, -1.37274, -2.95721, -2.28973, -5.96588, -2.29028, -5.96603, -8.26282, -5.83365, -8.26373, -5.83389, -11.55701, -6.42142, -11.55859, -6.42178, -15.85577, -6.94626, -15.85791, -6.94669, -19.82007, -9.53442, -19.82172, -9.53467, -8.96448, -11.966, -8.96649, -11.96646, -7.92847, -7.0593, -7.92932, -7.05939, -3.34601, -1.48395, -3.34644, -1.48404, -2.19232, -1.94351, -2.19318, -1.94376, -0.86743, -1.47729, -0.8678, -1.47739, -0.40436, -0.4794, -0.40448, -0.4794, -1.0224, -2.07239, -1.02264, -2.07242, -2.52759, -5.31558, -2.52814, -5.31573, -10.03925, -11.05725, -10.04059, -11.05731, -15.85663, -13.62891, -15.85852, -13.62912, -17.97083, -17.08066, -17.97479, -17.08121, -20.14484, -17.00146, -20.14832, -17.00201, -16.9809, -16.70618, -16.98437, -16.7066, -13.75867, -12.97495, -13.76251, -12.97546, -8.70911, -6.95657, -8.71094, -6.95685, -2.80872, -2.47641, -2.8092, -2.47644, 0, 0, 0, 0, -3.68768, -3.90903, -3.68817, -3.90909, -8.41577, -2.08881, -8.41724, -2.08923, -17.14258, -1.95975, -17.14471, -1.9603, -24.08917, -4.92014, -24.09119, -4.92062, -25.88513, -7.70139, -25.88855, -7.7023, -26.36041, -9.72449, -26.36292, -9.72522, -22.9765, -11.32477, -22.97852, -11.32538, -16.34161, -6.72672, -16.34332, -6.72726, -11.47717, -5.09595, -11.479, -5.09628, -5.66138, -1.87128, -5.66327, -1.8718, -1.95447, -3.0065, -1.95477, -3.00659, -0.83765, -1.28848, -0.83783, -1.28857, -1.92505, 0.52689, -1.92615, 0.52689, -3.00824, 1.09088, -3.00958, 1.09067, -7.659, -0.63235, -7.66113, -0.63269, -14.50122, -1.81366, -14.50372, -1.81403, -19.01862, -2.87173, -19.0213, -2.87216, -18.73608, -6.17081, -18.7403, -6.17114, -10.23059, -15.53143, -10.23322, -15.53204, -11.79858, -18.33997, -11.80127, -18.34045, -11.65454, -19.54739, -11.65778, -19.54807, -10.20728, -17.77872, -10.21045, -17.77957, -9.51666, -12.91257, -9.51959, -12.9133, -6.50336, -8.67804, -6.50415, -8.67838, -3.01349, -3.38477, -3.01379, -3.38489], "curve": [3.608, 0, 3.692, 1]}, {"time": 3.7333, "vertices": [-1.37244, -2.95715, -1.37274, -2.95721, -2.28973, -5.96588, -2.29028, -5.96603, -8.26282, -5.83365, -8.26373, -5.83389, -11.55701, -6.42142, -11.55859, -6.42178, -15.85577, -6.94626, -15.85791, -6.94669, -22.48309, -10.15182, -22.48419, -10.15182, -8.96448, -11.966, -8.96649, -11.96646, -7.92847, -7.0593, -7.92932, -7.05939, -3.34601, -1.48395, -3.34644, -1.48404, -2.19232, -1.94351, -2.19318, -1.94376, -0.86743, -1.47729, -0.8678, -1.47739, -0.40436, -0.4794, -0.40448, -0.4794, -1.0224, -2.07239, -1.02264, -2.07242, -2.52759, -5.31558, -2.52814, -5.31573, -10.03925, -11.05725, -10.04059, -11.05731, -14.97144, -13.11221, -14.97327, -13.11237, -18.35901, -17.75836, -18.36285, -17.75885, -20.73749, -17.65567, -20.7406, -17.65607, -18.92822, -16.58401, -18.9314, -16.58438, -15.88599, -12.62552, -15.88965, -12.62604, -11.15424, -6.67386, -11.15527, -6.67392, -8.58038, -1.4996, -8.57983, -1.4989, 0, 0, 0, 0, -3.68768, -3.90903, -3.68817, -3.90909, -8.85284, -4.25955, -8.85358, -4.25967, -16.49146, -6.65393, -16.49292, -6.65405, -25.13934, -7.22635, -25.14105, -7.22653, -28.56842, -10.67197, -28.57068, -10.67261, -29.34344, -11.44, -29.34509, -11.4407, -22.9765, -11.32477, -22.97852, -11.32538, -16.34161, -6.72672, -16.34332, -6.72726, -11.47717, -5.09595, -11.479, -5.09628, -5.66138, -1.87128, -5.66327, -1.8718, -1.95447, -3.0065, -1.95477, -3.00659, -0.83765, -1.28848, -0.83783, -1.28857, -1.92505, 0.52689, -1.92615, 0.52689, -3.00824, 1.09088, -3.00958, 1.09067, -15.95477, 1.16718, -15.95587, 1.16748, -23.5672, -2.159, -23.56738, -2.15851, -29.0083, -3.1196, -29.0097, -3.1196, -26.47443, -6.66357, -26.4776, -6.66342, -10.23059, -15.53143, -10.23322, -15.53204, -11.79858, -18.33997, -11.80127, -18.34045, -11.94861, -19.41034, -11.95142, -19.41089, -10.20728, -17.77872, -10.21045, -17.77957, -8.88257, -12.78, -8.88507, -12.78064, -6.50336, -8.67804, -6.50415, -8.67838, -2.94281, -2.77371, -2.94305, -2.7738], "curve": [3.775, 0, 3.858, 1]}, {"time": 3.9, "offset": 100, "vertices": [0.24445, 0.19373, 0.24457, 0.19376]}]}}, "lian": {"lian": {"deform": [{"time": 0.2667, "offset": 278, "vertices": [0.17987, 0.14011, 0.17987, 0.14011], "curve": [0.308, 0, 0.392, 1]}, {"time": 0.4333, "offset": 20, "vertices": [-3.03528, -2.4173, -3.03467, -2.4173, -6.70892, -2.69431, -6.70813, -2.69424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, -15.65527, -6.70544, -15.65277, -6.70474, -26.73505, -14.50131, -26.73297, -14.50092, -29.48938, -18.69019, -29.48651, -18.68991, -24.39886, -18.40363, -24.39722, -18.40344, -20.07153, -14.33945, -20.06964, -14.33914, -12.71594, -8.06924, -12.71399, -8.06903, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61328, -0.48843, -0.61316, -0.48843, -4.69055, -3.23456, -4.68982, -3.2345, -5.63904, -2.98776, -5.63812, -2.98758, -2.54132, -0.27008, -2.54102, -0.26999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0094, 1.83319, 1.00995, 1.83334, 0.75549, 0.60181, 0.75574, 0.60181, 1.23352, 0.77673, 1.23389, 0.77673, 1.38281, 0.07233, 1.38324, 0.07242, 2.18738, 0.09573, 2.18817, 0.09589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.37903, -7.50079, -5.37811, -7.50055, -9.27417, -13.06271, -9.27319, -13.06247, -12.49634, -17.33194, -12.49518, -17.33154, -14.53308, -18.57562, -14.53198, -18.57535, -16.85986, -14.94177, -16.85822, -14.94162, -9.33264, -8.00055, -9.33191, -8.00052, -0.74176, -0.96918, -0.74164, -0.96918, 0, 0, 0, 0, 0.07062, 0.62381, 0.07074, 0.62384, 0.53363, 0.99261, 0.53381, 0.99268, 2.15234, 1.33575, 2.15259, 1.33578, 1.45776, 0.78253, 1.45795, 0.78256, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648], "curve": [0.475, 0, 0.558, 1]}, {"time": 0.6, "offset": 20, "vertices": [-3.03528, -2.4173, -3.03467, -2.4173, -6.70892, -2.69431, -6.70813, -2.69424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, -18.77032, -6.49435, -18.76788, -6.49396, -28.38104, -15.80576, -28.37897, -15.80542, -30.02228, -19.11255, -30.01947, -19.1123, -25.43958, -19.00009, -25.43799, -18.99997, -20.07153, -14.33945, -20.06964, -14.33914, -12.71594, -8.06924, -12.71399, -8.06903, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61328, -0.48843, -0.61316, -0.48843, -4.69055, -3.23456, -4.68982, -3.2345, -5.63904, -2.98776, -5.63812, -2.98758, -2.54132, -0.27008, -2.54102, -0.26999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0094, 1.83319, 1.00995, 1.83334, 2.33911, 2.66074, 2.33929, 2.66043, 3.44897, 1.1929, 3.44934, 1.1926, 3.90411, -0.74271, 3.9046, -0.74295, 4.70862, -0.71942, 4.70959, -0.71951, 1.55713, -0.10559, 1.55725, -0.10574, 0, 0, 0, 0, 0, 0, -5.37903, -7.50079, -5.37811, -7.50055, -9.27417, -13.06271, -9.27319, -13.06247, -12.49634, -17.33194, -12.49518, -17.33154, -14.53308, -18.57562, -14.53198, -18.57535, -16.85986, -14.94177, -16.85822, -14.94162, -9.33264, -8.00055, -9.33191, -8.00052, -0.74176, -0.96918, -0.74164, -0.96918, 0, 0, 0, 0, 0.52478, 1.25668, 0.52478, 1.25671, 2.91663, 3.15381, 2.9165, 3.15387, 5.51086, 4.21414, 5.51093, 4.21411, 4.49994, 2.2861, 4.49969, 2.28592, 2.19135, 0.50781, 2.19116, 0.50778, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648], "curve": [0.642, 0, 0.725, 1]}, {"time": 0.7667, "offset": 278, "vertices": [0.17987, 0.14011, 0.17987, 0.14011], "curve": "stepped"}, {"time": 3.4, "offset": 278, "vertices": [0.17987, 0.14011, 0.17987, 0.14011], "curve": [3.442, 0, 3.525, 1]}, {"time": 3.5667, "offset": 20, "vertices": [-3.03528, -2.4173, -3.03467, -2.4173, -6.70892, -2.69431, -6.70813, -2.69424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, -15.65527, -6.70544, -15.65277, -6.70474, -26.73505, -14.50131, -26.73297, -14.50092, -29.48938, -18.69019, -29.48651, -18.68991, -24.39886, -18.40363, -24.39722, -18.40344, -20.07153, -14.33945, -20.06964, -14.33914, -12.71594, -8.06924, -12.71399, -8.06903, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61328, -0.48843, -0.61316, -0.48843, -4.69055, -3.23456, -4.68982, -3.2345, -5.63904, -2.98776, -5.63812, -2.98758, -2.54132, -0.27008, -2.54102, -0.26999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0094, 1.83319, 1.00995, 1.83334, 0.75549, 0.60181, 0.75574, 0.60181, 1.23352, 0.77673, 1.23389, 0.77673, 1.38281, 0.07233, 1.38324, 0.07242, 2.18738, 0.09573, 2.18817, 0.09589, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.37903, -7.50079, -5.37811, -7.50055, -9.27417, -13.06271, -9.27319, -13.06247, -12.49634, -17.33194, -12.49518, -17.33154, -14.53308, -18.57562, -14.53198, -18.57535, -16.85986, -14.94177, -16.85822, -14.94162, -9.33264, -8.00055, -9.33191, -8.00052, -0.74176, -0.96918, -0.74164, -0.96918, 0, 0, 0, 0, 0.07062, 0.62381, 0.07074, 0.62384, 0.53363, 0.99261, 0.53381, 0.99268, 2.15234, 1.33575, 2.15259, 1.33578, 1.45776, 0.78253, 1.45795, 0.78256, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648], "curve": [3.608, 0, 3.692, 1]}, {"time": 3.7333, "offset": 20, "vertices": [-3.03528, -2.4173, -3.03467, -2.4173, -6.70892, -2.69431, -6.70813, -2.69424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, -18.77032, -6.49435, -18.76788, -6.49396, -28.38104, -15.80576, -28.37897, -15.80542, -30.02228, -19.11255, -30.01947, -19.1123, -25.43958, -19.00009, -25.43799, -18.99997, -20.07153, -14.33945, -20.06964, -14.33914, -12.71594, -8.06924, -12.71399, -8.06903, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61328, -0.48843, -0.61316, -0.48843, -4.69055, -3.23456, -4.68982, -3.2345, -5.63904, -2.98776, -5.63812, -2.98758, -2.54132, -0.27008, -2.54102, -0.26999, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0094, 1.83319, 1.00995, 1.83334, 2.33911, 2.66074, 2.33929, 2.66043, 3.44897, 1.1929, 3.44934, 1.1926, 3.90411, -0.74271, 3.9046, -0.74295, 4.70862, -0.71942, 4.70959, -0.71951, 1.55713, -0.10559, 1.55725, -0.10574, 0, 0, 0, 0, 0, 0, -5.37903, -7.50079, -5.37811, -7.50055, -9.27417, -13.06271, -9.27319, -13.06247, -12.49634, -17.33194, -12.49518, -17.33154, -14.53308, -18.57562, -14.53198, -18.57535, -16.85986, -14.94177, -16.85822, -14.94162, -9.33264, -8.00055, -9.33191, -8.00052, -0.74176, -0.96918, -0.74164, -0.96918, 0, 0, 0, 0, 0.52478, 1.25668, 0.52478, 1.25671, 2.91663, 3.15381, 2.9165, 3.15387, 5.51086, 4.21414, 5.51093, 4.21411, 4.49994, 2.2861, 4.49969, 2.28592, 2.19135, 0.50781, 2.19116, 0.50778, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -5.34308, -1.60654, -5.34253, -1.60648, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, -3.03528, -2.4173, -3.03467, -2.4173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.34308, -1.60654, -5.34253, -1.60648], "curve": [3.775, 0, 3.858, 1]}, {"time": 3.9, "offset": 278, "vertices": [0.17987, 0.14011, 0.17987, 0.14011]}]}}}}}}}