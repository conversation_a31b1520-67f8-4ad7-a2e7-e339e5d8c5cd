{"skeleton": {"hash": "EmXhpCXEflA", "spine": "4.2.43", "x": -422.73, "y": -23.26, "width": 946.14, "height": 1153.26, "images": "./images/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -99.3, "y": 205.18}, {"name": "bone2", "parent": "bone", "length": 145.47, "rotation": -78.26, "x": 3.17, "y": -8.2}, {"name": "bone3", "parent": "bone2", "length": 127.84, "rotation": -76.47, "x": 145.47}, {"name": "1", "parent": "root", "x": -182.14, "y": -0.02, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone4", "parent": "bone", "length": 139.42, "rotation": -47.42, "x": 106.76, "y": 8.45}, {"name": "bone5", "parent": "bone4", "length": 112.66, "rotation": -49.18, "x": 139.42}, {"name": "2", "parent": "root", "x": 88.85, "y": -0.95, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone6", "parent": "bone", "length": 112.64, "rotation": 38.32, "x": 12.63, "y": 0.92}, {"name": "bone7", "parent": "bone6", "length": 83.15, "rotation": -27.17, "x": 112.64}, {"name": "bone8", "parent": "bone", "length": 28.04, "rotation": -136.47, "x": -6.64, "y": -9.39}, {"name": "bone9", "parent": "bone", "length": 179.32, "rotation": 92.93, "x": 0.73, "y": 31.34, "inherit": "noScale"}, {"name": "bone10", "parent": "bone9", "length": 191.32, "rotation": -25.11, "x": 179.79, "y": -0.67, "inherit": "noScale"}, {"name": "bone11", "parent": "bone10", "length": 166.45, "rotation": -19.96, "x": 191.32}, {"name": "bone12", "parent": "bone11", "length": 165.08, "rotation": 46.32, "x": 165.89, "y": -1.09}, {"name": "bone13", "parent": "bone12", "length": 102.89, "rotation": 1.41, "x": 165.08}, {"name": "bone14", "parent": "bone13", "length": 76.49, "rotation": -10.67, "x": -3.6, "y": -64.31, "color": "abe323ff"}, {"name": "bone15", "parent": "bone13", "x": -3.44, "y": -24.82}, {"name": "bone16", "parent": "bone15", "x": -47.25, "y": 125.13, "color": "abe323ff"}, {"name": "bone17", "parent": "bone11", "length": 177.78, "rotation": -62.83, "x": 162.28, "y": -9.05}, {"name": "bone18", "parent": "bone17", "length": 282.53, "rotation": -67.32, "x": 176.88, "y": -1.53}, {"name": "bone19", "parent": "bone18", "length": 318.67, "rotation": -18.55, "x": 279.7, "y": -2.33}, {"name": "bone20", "parent": "bone19", "length": 77.87, "rotation": -11.3, "x": 318.67}, {"name": "bone21", "parent": "bone11", "length": 165.13, "rotation": 159.53, "x": 137.61, "y": 2.55}, {"name": "bone22", "parent": "bone21", "length": 256.87, "rotation": 59.81, "x": 158.2, "y": 0.8}, {"name": "bone23", "parent": "bone22", "length": 212.58, "rotation": -39.82, "x": 256.87}, {"name": "bone24", "parent": "bone23", "length": 68.46, "rotation": 1.85, "x": 210.91, "y": -1.46}, {"name": "bone25", "parent": "bone24", "length": 28.41, "rotation": 28.04, "x": 68.46}, {"name": "bone26", "parent": "bone25", "length": 32.63, "rotation": 3.26, "x": 28.41}, {"name": "bone27", "parent": "bone24", "length": 62.65, "rotation": 9.55, "x": 78.74, "y": -15.35}, {"name": "bone28", "parent": "bone27", "length": 41.44, "rotation": 22.77, "x": 62.65}, {"name": "bone29", "parent": "bone28", "length": 40.93, "rotation": 1.04, "x": 41.44}, {"name": "bone30", "parent": "bone24", "length": 35.65, "rotation": 17.99, "x": 83.98, "y": 0.95}, {"name": "bone31", "parent": "bone30", "length": 34.91, "rotation": 24.1, "x": 35.65}, {"name": "bone32", "parent": "bone31", "length": 31.06, "rotation": 5.42, "x": 34.91}, {"name": "bone33", "parent": "bone24", "length": 50.74, "rotation": 17.69, "x": 52.43, "y": 25.74}, {"name": "bone34", "parent": "bone33", "length": 24.53, "rotation": 39.41, "x": 50.74}, {"name": "bone35", "parent": "bone34", "length": 24.87, "rotation": 47.52, "x": 24.53}, {"name": "bone36", "parent": "bone24", "length": 183.82, "rotation": 148.04, "x": 117.9, "y": 33.82}, {"name": "bone37", "parent": "bone8", "length": 205.33, "rotation": 162.56, "x": -9.13, "y": 23.4}, {"name": "bone38", "parent": "bone8", "length": 177.25, "rotation": 115.57, "x": 3.04, "y": 5.62}, {"name": "bone39", "parent": "bone6", "length": 77.23, "rotation": -98.35, "x": 53.79, "y": -23.4}, {"name": "bone40", "parent": "bone39", "length": 95.01, "rotation": 33.47, "x": 77.23}, {"name": "bone41", "parent": "bone40", "length": 72.39, "rotation": -2.94, "x": 95.01}, {"name": "bone42", "parent": "bone41", "length": 95.67, "rotation": 3.46, "x": 72.39}, {"name": "3", "parent": "root", "x": 242.56, "y": 26, "color": "ff3f00ff", "icon": "ik"}, {"name": "bone43", "parent": "bone10", "x": 75.53, "y": 120.01, "color": "abe323ff"}, {"name": "bone44", "parent": "bone10", "x": 124.8, "y": 40.34, "color": "abe323ff"}, {"name": "bone45", "parent": "bone17", "length": 128.66, "rotation": -95.82, "x": 144.95, "y": -5.33, "color": "abe323ff"}, {"name": "bone46", "parent": "bone45", "length": 100.63, "rotation": 1.81, "x": 128.66, "color": "abe323ff"}, {"name": "bone47", "parent": "bone46", "length": 113.46, "rotation": 2.62, "x": 100.63, "color": "abe323ff"}, {"name": "bone48", "parent": "bone17", "length": 102.85, "rotation": -121.53, "x": 33.44, "y": -18.6, "color": "abe323ff"}, {"name": "bone49", "parent": "bone48", "length": 94.66, "rotation": -11.98, "x": 102.85, "color": "abe323ff"}, {"name": "bone50", "parent": "bone49", "length": 86.51, "rotation": 33.5, "x": 95.31, "y": -0.4, "color": "abe323ff"}, {"name": "bone51", "parent": "bone50", "length": 103.67, "rotation": 22.04, "x": 86.51, "color": "abe323ff"}, {"name": "bone52", "parent": "bone11", "length": 46.77, "rotation": -30.06, "x": -62.24, "y": 72.76}, {"name": "bone53", "parent": "bone52", "length": 37.06, "rotation": 3.81, "x": 46.77}, {"name": "bone54", "parent": "bone53", "length": 26.22, "rotation": 25.39, "x": 37.06, "color": "abe323ff"}, {"name": "bone55", "parent": "bone54", "length": 22.15, "rotation": 57.43, "x": 26.22, "color": "abe323ff"}, {"name": "bone56", "parent": "bone55", "length": 22, "rotation": 41.4, "x": 22.15, "color": "abe323ff"}, {"name": "bone57", "parent": "bone21", "length": 22.34, "rotation": -110.49, "x": 38.56, "y": -1.73}, {"name": "bone58", "parent": "bone57", "length": 22.46, "rotation": 5.98, "x": 22.34}, {"name": "bone59", "parent": "bone58", "length": 21.58, "rotation": 9.18, "x": 22.46}, {"name": "bone60", "parent": "bone17", "length": 50.67, "rotation": -156.24, "x": 128.89, "y": 71.6}, {"name": "bone61", "parent": "bone60", "length": 42.96, "rotation": 23.94, "x": 51.77, "y": 0.35}, {"name": "bone62", "parent": "bone61", "length": 53.14, "rotation": 7.14, "x": 42.96}, {"name": "bone63", "parent": "bone62", "length": 45.38, "rotation": 30.86, "x": 53.14, "color": "abe323ff"}, {"name": "bone64", "parent": "bone63", "length": 36.06, "rotation": 6.02, "x": 45.38, "color": "abe323ff"}, {"name": "bone65", "parent": "bone64", "length": 26.73, "rotation": 3.23, "x": 36.06, "color": "abe323ff"}, {"name": "bone66", "parent": "bone65", "length": 24.86, "rotation": 5.24, "x": 26.73, "color": "abe323ff"}, {"name": "bone67", "parent": "bone17", "length": 38.66, "rotation": -114.48, "x": 55.08, "y": 85.47}, {"name": "bone68", "parent": "bone67", "length": 39.45, "rotation": 3.02, "x": 38.66}, {"name": "bone69", "parent": "bone68", "length": 38.26, "rotation": 16.21, "x": 39.76, "y": 0.22}, {"name": "bone70", "parent": "bone10", "length": 30.9, "rotation": -164.53, "x": 25.4, "y": 65.84, "color": "abe323ff"}, {"name": "bone71", "parent": "bone70", "length": 24.67, "rotation": -5.97, "x": 30.9, "color": "abe323ff"}, {"name": "bone72", "parent": "bone71", "length": 22.48, "rotation": 28.2, "x": 24.67, "color": "abe323ff"}, {"name": "bone73", "parent": "bone10", "length": 30.47, "rotation": -166.92, "x": 100.16, "y": -21.95, "color": "abe323ff"}, {"name": "bone74", "parent": "bone73", "length": 31.86, "rotation": -1.8, "x": 30.47, "color": "abe323ff"}, {"name": "bone75", "parent": "bone74", "length": 28.28, "rotation": 10.89, "x": 31.86, "color": "abe323ff"}, {"name": "bone76", "parent": "bone17", "length": 72.32, "rotation": -90.81, "x": 64.15, "y": -5.33, "color": "abe323ff"}, {"name": "bone77", "parent": "bone76", "length": 86.64, "rotation": 21.29, "x": 73.78, "y": 0.41}, {"name": "bone78", "parent": "bone77", "length": 84.64, "rotation": 18.76, "x": 86.64}, {"name": "bone79", "parent": "bone17", "length": 43.98, "rotation": 64.15, "x": 66.67, "y": 8.66}, {"name": "bone80", "parent": "bone79", "length": 46.45, "rotation": -33.11, "x": 43.98}, {"name": "bone81", "parent": "bone13", "x": -10.22, "y": 119.36}, {"name": "bone82", "parent": "bone81", "x": 30.78, "y": -89.85}, {"name": "bone83", "parent": "bone13", "length": 43.02, "rotation": 62.26, "x": 81.55, "y": 100.8, "color": "abe323ff"}, {"name": "bone84", "parent": "bone83", "length": 42.76, "rotation": 52.04, "x": 43.02, "color": "abe323ff"}, {"name": "bone85", "parent": "bone84", "length": 43.67, "rotation": 42.82, "x": 43.62, "y": 0.57, "color": "abe323ff"}, {"name": "bone86", "parent": "bone85", "length": 39.79, "rotation": 32.13, "x": 43.67, "color": "abe323ff"}, {"name": "bone87", "parent": "bone86", "length": 38.64, "rotation": -9.34, "x": 39.79, "color": "abe323ff"}, {"name": "bone88", "parent": "bone87", "length": 33.75, "rotation": -51.06, "x": 38.64, "color": "abe323ff"}, {"name": "bone89", "parent": "bone88", "length": 28.73, "rotation": 7.76, "x": 33.75, "color": "abe323ff"}, {"name": "bone90", "parent": "bone89", "length": 24.32, "rotation": 55.54, "x": 29.38, "y": -0.08, "color": "abe323ff"}, {"name": "bone91", "parent": "bone90", "length": 25.5, "rotation": 47.55, "x": 24.32, "color": "abe323ff"}, {"name": "bone92", "parent": "bone13", "length": 39.27, "rotation": 36.46, "x": 88.74, "y": 93.44, "color": "abe323ff"}, {"name": "bone93", "parent": "bone92", "length": 53.97, "rotation": 51.31, "x": 40.8, "y": 0.75, "color": "abe323ff"}, {"name": "bone94", "parent": "bone93", "length": 44.11, "rotation": 36.39, "x": 53.97, "color": "abe323ff"}, {"name": "bone95", "parent": "bone94", "length": 40.49, "rotation": 27.7, "x": 44.6, "y": -0.41, "color": "abe323ff"}, {"name": "bone96", "parent": "bone95", "length": 35.9, "rotation": 26.6, "x": 41.96, "y": 0.61, "color": "abe323ff"}, {"name": "bone97", "parent": "bone96", "length": 28.25, "rotation": 39.13, "x": 35.9, "color": "abe323ff"}, {"name": "bone98", "parent": "bone13", "length": 39.34, "rotation": 12.45, "x": 99.81, "y": 81.39, "color": "abe323ff"}, {"name": "bone99", "parent": "bone98", "length": 42.1, "rotation": 45.9, "x": 39.34, "color": "abe323ff"}, {"name": "bone100", "parent": "bone99", "length": 46.16, "rotation": 36.54, "x": 42.1, "color": "abe323ff"}, {"name": "bone101", "parent": "bone100", "length": 39.23, "rotation": 34.51, "x": 46.16, "color": "abe323ff"}, {"name": "bone102", "parent": "bone101", "length": 29.89, "rotation": 19.16, "x": 39.23, "color": "abe323ff"}, {"name": "bone103", "parent": "bone13", "length": 37.5, "rotation": -5.09, "x": 108.2, "y": 58.79, "color": "abe323ff"}, {"name": "bone104", "parent": "bone103", "length": 48.92, "rotation": 40.05, "x": 37.5, "color": "abe323ff"}, {"name": "bone105", "parent": "bone104", "length": 41.06, "rotation": 40.06, "x": 48.92, "color": "abe323ff"}, {"name": "bone106", "parent": "bone105", "length": 63.22, "rotation": 40.3, "x": 41.06, "color": "abe323ff"}, {"name": "bone107", "parent": "bone13", "length": 45.41, "rotation": -86.69, "x": 105.51, "y": 52.3, "color": "abe323ff"}, {"name": "bone108", "parent": "bone107", "length": 46.96, "rotation": 6.81, "x": 45.41, "color": "abe323ff"}, {"name": "bone109", "parent": "bone108", "length": 42.39, "rotation": 27.68, "x": 47.6, "y": -0.18, "color": "abe323ff"}, {"name": "bone110", "parent": "bone109", "length": 36.27, "rotation": 37.57, "x": 42.39, "color": "abe323ff"}, {"name": "bone111", "parent": "bone110", "length": 27.74, "rotation": 32.5, "x": 36.27, "color": "abe323ff"}, {"name": "bone112", "parent": "bone13", "length": 34.91, "rotation": -95.6, "x": 170.68, "y": -87.4, "color": "abe323ff"}, {"name": "bone113", "parent": "bone112", "length": 34.88, "rotation": -44.26, "x": 34.91, "color": "abe323ff"}, {"name": "bone114", "parent": "bone113", "length": 33.04, "rotation": -34, "x": 34.88, "color": "abe323ff"}, {"name": "bone115", "parent": "bone114", "length": 30.85, "rotation": -24.34, "x": 33.36, "y": -0.07, "color": "abe323ff"}, {"name": "bone116", "parent": "bone115", "length": 29.06, "rotation": -45.47, "x": 30.85, "color": "abe323ff"}, {"name": "bone117", "parent": "bone13", "length": 53.42, "rotation": -147.82, "x": 153.76, "y": -104.96, "color": "abe323ff"}, {"name": "bone118", "parent": "bone117", "length": 67.21, "rotation": -24.61, "x": 53.42, "color": "abe323ff"}, {"name": "bone119", "parent": "bone118", "length": 53.53, "rotation": -25.69, "x": 67.21, "color": "abe323ff"}, {"name": "bone120", "parent": "bone119", "length": 47.06, "rotation": -10.59, "x": 53.53, "color": "abe323ff"}, {"name": "bone121", "parent": "bone120", "length": 43.74, "rotation": 14.79, "x": 48.51, "y": 0.62, "color": "abe323ff"}, {"name": "bone122", "parent": "bone121", "length": 35.37, "rotation": 46.05, "x": 43.74, "color": "abe323ff"}, {"name": "bone123", "parent": "bone122", "length": 34.31, "rotation": 29.66, "x": 35.37, "color": "abe323ff"}, {"name": "bone124", "parent": "bone123", "length": 29.56, "rotation": 23.64, "x": 34.31, "color": "abe323ff"}, {"name": "bone125", "parent": "bone13", "length": 31.42, "rotation": -129.91, "x": 146.01, "y": -124.18, "color": "abe323ff"}, {"name": "bone126", "parent": "bone125", "length": 37.05, "rotation": -7.53, "x": 31.42, "color": "abe323ff"}, {"name": "bone127", "parent": "bone126", "length": 33.82, "rotation": 7.77, "x": 37.05, "color": "abe323ff"}, {"name": "bone128", "parent": "bone127", "length": 28.91, "rotation": -23.14, "x": 33.82, "color": "abe323ff"}, {"name": "bone129", "parent": "bone128", "length": 23.5, "rotation": -34.79, "x": 28.91, "color": "abe323ff"}, {"name": "bone130", "parent": "bone129", "length": 23.68, "rotation": -47.93, "x": 23.5, "color": "abe323ff"}, {"name": "bone131", "parent": "bone13", "length": 50.3, "rotation": 172.33, "x": 99.06, "y": -113.88, "color": "abe323ff"}, {"name": "bone132", "parent": "bone131", "length": 37.55, "rotation": -0.15, "x": 50.3, "color": "abe323ff"}, {"name": "bone133", "parent": "bone132", "length": 41.82, "rotation": 20.5, "x": 37.55, "color": "abe323ff"}, {"name": "bone134", "parent": "bone133", "length": 33.85, "rotation": 37.69, "x": 41.82, "color": "abe323ff"}, {"name": "bone135", "parent": "bone134", "length": 32.44, "rotation": 21.71, "x": 33.85, "color": "abe323ff"}, {"name": "bone136", "parent": "bone135", "length": 23.5, "rotation": -47.94, "x": 32.44, "color": "abe323ff"}, {"name": "bone137", "parent": "bone136", "length": 22.22, "rotation": -40.13, "x": 23.5, "color": "abe323ff"}, {"name": "bone138", "parent": "bone137", "length": 21.32, "rotation": -46.46, "x": 22.22, "color": "abe323ff"}, {"name": "bone139", "parent": "bone117", "length": 39.11, "rotation": -0.13, "x": 75, "y": 7.21, "color": "abe323ff"}, {"name": "bone140", "parent": "bone139", "length": 38.86, "rotation": -14.88, "x": 39.73, "y": 0.08, "color": "abe323ff"}, {"name": "bone141", "parent": "bone140", "length": 33.65, "rotation": 5.48, "x": 38.86, "color": "abe323ff"}, {"name": "bone142", "parent": "bone141", "length": 32.76, "rotation": 27.2, "x": 34.04, "y": -0.21, "color": "abe323ff"}, {"name": "bone143", "parent": "bone142", "length": 33.85, "rotation": 27.79, "x": 32.76, "color": "abe323ff"}, {"name": "bone144", "parent": "bone143", "length": 30.12, "rotation": -23.2, "x": 33.85, "color": "abe323ff"}, {"name": "bone145", "parent": "bone144", "length": 24.37, "rotation": -63.17, "x": 29.9, "y": 0.38, "color": "abe323ff"}, {"name": "bone146", "parent": "bone145", "length": 27.26, "rotation": -44.09, "x": 24.37, "color": "abe323ff"}, {"name": "bone147", "parent": "bone146", "length": 21.6, "rotation": -0.8, "x": 27.55, "y": -0.33, "color": "abe323ff"}, {"name": "bone148", "parent": "bone118", "length": 60.95, "rotation": -4.87, "x": 119.3, "y": 14.69, "color": "abe323ff"}, {"name": "bone149", "parent": "bone148", "length": 55.09, "rotation": -1.09, "x": 60.95, "color": "abe323ff"}, {"name": "bone150", "parent": "bone149", "length": 48.42, "rotation": 37.27, "x": 55.09, "color": "abe323ff"}, {"name": "bone151", "parent": "bone150", "length": 47.85, "rotation": 49.29, "x": 48.42, "color": "abe323ff"}, {"name": "bone152", "parent": "bone151", "length": 49.35, "rotation": 17.9, "x": 50.44, "y": 1.09, "color": "abe323ff"}, {"name": "bone153", "parent": "bone152", "length": 31.59, "rotation": -39.02, "x": 49.35, "color": "abe323ff"}, {"name": "bone154", "parent": "bone153", "length": 25.41, "rotation": -64.11, "x": 32.38, "y": -0.41, "color": "abe323ff"}, {"name": "bone155", "parent": "bone154", "length": 28.44, "rotation": -39.06, "x": 27.27, "y": -0.28, "color": "abe323ff"}, {"name": "bone156", "parent": "bone155", "length": 21.19, "rotation": -47.49, "x": 28.44, "color": "abe323ff"}, {"name": "bone157", "parent": "bone156", "length": 14.72, "rotation": -62.17, "x": 21.19, "color": "abe323ff"}, {"name": "bone158", "parent": "bone13", "length": 59.46, "rotation": -152.96, "x": -13.44, "y": -180.62, "color": "abe323ff"}, {"name": "bone159", "parent": "bone158", "length": 43.83, "rotation": 9.24, "x": 60.24, "y": 0.17, "color": "abe323ff"}, {"name": "bone160", "parent": "bone159", "length": 41.65, "rotation": 9.7, "x": 43.83, "color": "abe323ff"}, {"name": "bone161", "parent": "bone160", "length": 55.41, "rotation": 25.51, "x": 41.65, "color": "abe323ff"}, {"name": "bone162", "parent": "bone161", "length": 37.9, "rotation": -58.93, "x": 55.41, "color": "abe323ff"}, {"name": "bone163", "parent": "bone162", "length": 42.82, "rotation": -21.18, "x": 37.9, "color": "abe323ff"}, {"name": "bone164", "parent": "bone163", "length": 26.73, "rotation": 33.36, "x": 42.82, "color": "abe323ff"}, {"name": "bone165", "parent": "bone164", "length": 30.84, "rotation": 44.86, "x": 26.73, "color": "abe323ff"}, {"name": "bone166", "parent": "bone165", "length": 23.2, "rotation": 53.89, "x": 30.84, "color": "abe323ff"}, {"name": "bone167", "parent": "bone166", "length": 16.88, "rotation": 50.91, "x": 23.2, "color": "abe323ff"}, {"name": "bone168", "parent": "bone13", "length": 74.25, "rotation": 169.49, "x": 19.25, "y": -114.07, "color": "abe323ff"}, {"name": "bone169", "parent": "bone168", "length": 64.05, "rotation": 15.31, "x": 74.25, "color": "abe323ff"}, {"name": "bone170", "parent": "bone169", "length": 74.6, "rotation": 39, "x": 64.05, "color": "abe323ff"}, {"name": "bone171", "parent": "bone170", "length": 59.96, "rotation": 23.21, "x": 74.6, "color": "abe323ff"}, {"name": "bone172", "parent": "bone171", "length": 65.74, "rotation": 10.82, "x": 59.79, "y": 0.55, "color": "abe323ff"}, {"name": "bone173", "parent": "bone172", "length": 39.15, "rotation": -74.95, "x": 65.74, "color": "abe323ff"}, {"name": "bone174", "parent": "bone173", "length": 43.7, "rotation": -28.6, "x": 39.15, "color": "abe323ff"}, {"name": "bone175", "parent": "bone174", "length": 46.4, "rotation": -56.2, "x": 43.7, "color": "abe323ff"}, {"name": "bone176", "parent": "bone13", "length": 72.44, "rotation": -111.9, "x": -181.8, "y": -98.05, "color": "abe323ff"}, {"name": "bone177", "parent": "bone176", "length": 61.03, "rotation": -4.47, "x": 72.44, "color": "abe323ff"}, {"name": "bone178", "parent": "bone177", "length": 48.33, "rotation": -51.04, "x": 61.03, "color": "abe323ff"}, {"name": "bone179", "parent": "bone178", "length": 51.16, "rotation": -40.81, "x": 48.33, "color": "abe323ff"}, {"name": "bone180", "parent": "bone179", "length": 41.2, "rotation": 36.43, "x": 51.16, "color": "abe323ff"}, {"name": "bone181", "parent": "bone172", "length": 48.36, "rotation": -53.13, "x": 73.51, "y": -20.25, "color": "abe323ff"}, {"name": "bone182", "parent": "bone181", "length": 50.17, "rotation": -33.25, "x": 48.36, "color": "abe323ff"}, {"name": "bone183", "parent": "bone182", "length": 42.22, "rotation": -30.27, "x": 50.17, "color": "abe323ff"}, {"name": "bone184", "parent": "bone183", "length": 42.47, "rotation": 36.67, "x": 41.33, "y": 0.19, "color": "abe323ff"}, {"name": "bone185", "parent": "bone184", "length": 37.86, "rotation": -18.21, "x": 42.47, "color": "abe323ff"}, {"name": "bone186", "parent": "bone185", "length": 19.07, "rotation": -42.64, "x": 37.86, "color": "abe323ff"}, {"name": "bone187", "parent": "bone186", "length": 15.47, "rotation": -37.38, "x": 19.07, "color": "abe323ff"}, {"name": "bone188", "parent": "bone184", "length": 27.58, "rotation": 58.77, "x": 41.81, "y": 10.34, "color": "abe323ff"}, {"name": "bone189", "parent": "bone188", "length": 23.35, "rotation": 59.26, "x": 27.58, "color": "abe323ff"}, {"name": "bone190", "parent": "bone189", "length": 20.59, "rotation": 54.92, "x": 23.35, "color": "abe323ff"}, {"name": "bone191", "parent": "bone13", "length": 40.27, "rotation": -169.82, "x": 27.33, "y": 157.1, "color": "abe323ff"}, {"name": "bone192", "parent": "bone191", "length": 29.53, "rotation": -12.51, "x": 40.27, "color": "abe323ff"}, {"name": "bone193", "parent": "bone192", "length": 29.17, "rotation": -8.24, "x": 29.53, "color": "abe323ff"}, {"name": "bone194", "parent": "bone193", "length": 32.4, "rotation": 31.2, "x": 29.17, "color": "abe323ff"}, {"name": "bone195", "parent": "bone194", "length": 27.72, "rotation": 46.99, "x": 32.4, "color": "abe323ff"}, {"name": "bone196", "parent": "bone195", "length": 23.74, "rotation": 41.98, "x": 27.72, "color": "abe323ff"}, {"name": "bone198", "parent": "bone36", "rotation": -17.28, "x": -1.32, "y": 3.97, "scaleX": 2, "scaleY": 2, "inherit": "noRotationOrReflection"}, {"name": "bone199", "parent": "bone36", "rotation": -17.28, "x": -0.07, "y": 3.58, "scaleX": 2, "scaleY": 2}], "slots": [{"name": "toufa15", "bone": "root", "attachment": "toufa15"}, {"name": "toufa14", "bone": "root", "attachment": "toufa14"}, {"name": "toufa13", "bone": "root", "attachment": "toufa13"}, {"name": "toufa12", "bone": "root", "attachment": "toufa12"}, {"name": "toufa11", "bone": "root", "attachment": "toufa11"}, {"name": "toufa10", "bone": "root", "attachment": "toufa10"}, {"name": "toufa9", "bone": "root", "attachment": "toufa9"}, {"name": "toufa8", "bone": "root", "attachment": "toufa8"}, {"name": "toufa7", "bone": "root", "attachment": "toufa7"}, {"name": "toufa6", "bone": "root", "attachment": "toufa6"}, {"name": "dao2", "bone": "root", "attachment": "dao2"}, {"name": "zuoshou3", "bone": "root", "attachment": "zuoshou3"}, {"name": "zuoshou2", "bone": "root", "attachment": "zuoshou2"}, {"name": "zuoshouzhi1", "bone": "root", "attachment": "zuoshouzhi1"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "dao", "bone": "root", "attachment": "dao"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "zuoshou1", "bone": "root", "attachment": "zuoshou1"}, {"name": "waitao4", "bone": "root", "attachment": "waitao4"}, {"name": "waitao3", "bone": "root", "attachment": "waitao3"}, {"name": "shenti", "bone": "root", "attachment": "shenti"}, {"name": "tun", "bone": "root", "attachment": "tun"}, {"name": "yaodai1", "bone": "root", "attachment": "yaodai1"}, {"name": "ya<PERSON>i", "bone": "root", "attachment": "ya<PERSON>i"}, {"name": "xiong", "bone": "root", "attachment": "xiong"}, {"name": "waitao1", "bone": "root", "attachment": "waitao1"}, {"name": "waitao", "bone": "root", "attachment": "waitao"}, {"name": "ji<PERSON>i", "bone": "root", "attachment": "ji<PERSON>i"}, {"name": "yiling3", "bone": "root", "attachment": "yiling3"}, {"name": "bozhi", "bone": "root", "attachment": "bozhi"}, {"name": "yiling2", "bone": "root", "attachment": "yiling2"}, {"name": "you<PERSON>ou", "bone": "root", "attachment": "you<PERSON>ou"}, {"name": "yiling", "bone": "root", "attachment": "yiling"}, {"name": "yan<PERSON>i", "bone": "root", "attachment": "yan<PERSON>i"}, {"name": "tongkong", "bone": "root", "attachment": "tongkong"}, {"name": "lian", "bone": "root", "attachment": "lian"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "biyan", "bone": "root"}, {"name": "toufa5", "bone": "root", "attachment": "toufa5"}, {"name": "toufa4", "bone": "root", "attachment": "toufa4"}, {"name": "erduo", "bone": "root", "attachment": "erduo"}, {"name": "toufa3", "bone": "root", "attachment": "toufa3"}, {"name": "toufa2", "bone": "root", "attachment": "toufa2"}, {"name": "toufa1", "bone": "root", "attachment": "toufa1"}, {"name": "toufa", "bone": "root", "attachment": "toufa"}, {"name": "daoguang2", "bone": "bone198", "attachment": "1/a_04", "blend": "additive"}, {"name": "daoguang3", "bone": "bone199", "blend": "additive"}], "ik": [{"name": "1", "bones": ["bone3"], "target": "1", "compress": true, "stretch": true}, {"name": "2", "order": 1, "bones": ["bone5"], "target": "2", "compress": true, "stretch": true}, {"name": "3", "order": 2, "bones": ["bone42"], "target": "3"}], "transform": [{"name": "6", "order": 15, "bones": ["bone14"], "target": "bone16", "rotation": -10.67, "x": 45.57, "y": -165, "mixRotate": 0, "mixX": 0.2, "mixScaleX": 0, "mixShearY": 0}, {"name": "7", "order": 16, "bones": ["bone81"], "target": "bone16", "x": 40.47, "y": 19.05, "mixRotate": 0, "mixX": 0.5, "mixScaleX": 0, "mixShearY": 0}], "physics": [{"name": "bone43", "order": 136, "bone": "bone43", "x": 1, "y": 1, "inertia": 0.3, "strength": 30, "damping": 0.85}, {"name": "bone44", "order": 135, "bone": "bone44", "x": 1, "y": 1, "inertia": 0.3, "strength": 30, "damping": 0.85}, {"name": "bone45", "order": 17, "bone": "bone45", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone46", "order": 18, "bone": "bone46", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone47", "order": 19, "bone": "bone47", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true, "mixGlobal": true}, {"name": "bone48", "order": 4, "bone": "bone48", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone49", "order": 12, "bone": "bone49", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone50", "order": 13, "bone": "bone50", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone51", "order": 14, "bone": "bone51", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone54", "order": 5, "bone": "bone54", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone55", "order": 6, "bone": "bone55", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone56", "order": 7, "bone": "bone56", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone63", "order": 8, "bone": "bone63", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone64", "order": 9, "bone": "bone64", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone65", "order": 10, "bone": "bone65", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone66", "order": 11, "bone": "bone66", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone70", "order": 20, "bone": "bone70", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone71", "order": 21, "bone": "bone71", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone72", "order": 22, "bone": "bone72", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone73", "order": 23, "bone": "bone73", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone74", "order": 24, "bone": "bone74", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone75", "order": 25, "bone": "bone75", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone76", "order": 3, "bone": "bone76", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone83", "order": 126, "bone": "bone83", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone84", "order": 127, "bone": "bone84", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone85", "order": 128, "bone": "bone85", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone86", "order": 129, "bone": "bone86", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone87", "order": 130, "bone": "bone87", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone88", "order": 131, "bone": "bone88", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "bone89", "order": 132, "bone": "bone89", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone90", "order": 133, "bone": "bone90", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone91", "order": 134, "bone": "bone91", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone92", "order": 120, "bone": "bone92", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone93", "order": 121, "bone": "bone93", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone94", "order": 122, "bone": "bone94", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone95", "order": 123, "bone": "bone95", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone96", "order": 124, "bone": "bone96", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone97", "order": 125, "bone": "bone97", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone98", "order": 115, "bone": "bone98", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone99", "order": 116, "bone": "bone99", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone100", "order": 117, "bone": "bone100", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone101", "order": 118, "bone": "bone101", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone102", "order": 119, "bone": "bone102", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone103", "order": 105, "bone": "bone103", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "bone104", "order": 112, "bone": "bone104", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "bone105", "order": 113, "bone": "bone105", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "bone106", "order": 114, "bone": "bone106", "rotate": 1, "inertia": 0.5, "damping": 0.85}, {"name": "bone112", "order": 100, "bone": "bone112", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone113", "order": 101, "bone": "bone113", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone114", "order": 102, "bone": "bone114", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone115", "order": 103, "bone": "bone115", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone116", "order": 104, "bone": "bone116", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone117", "order": 42, "bone": "bone117", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone118", "order": 43, "bone": "bone118", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone119", "order": 44, "bone": "bone119", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone120", "order": 45, "bone": "bone120", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone121", "order": 46, "bone": "bone121", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone122", "order": 47, "bone": "bone122", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone123", "order": 48, "bone": "bone123", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone124", "order": 63, "bone": "bone124", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone125", "order": 94, "bone": "bone125", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone126", "order": 95, "bone": "bone126", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone127", "order": 96, "bone": "bone127", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone128", "order": 97, "bone": "bone128", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone129", "order": 98, "bone": "bone129", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone130", "order": 99, "bone": "bone130", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone131", "order": 52, "bone": "bone131", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone132", "order": 53, "bone": "bone132", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone133", "order": 54, "bone": "bone133", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone134", "order": 55, "bone": "bone134", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone135", "order": 56, "bone": "bone135", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone136", "order": 60, "bone": "bone136", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone137", "order": 61, "bone": "bone137", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone138", "order": 62, "bone": "bone138", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone139", "order": 85, "bone": "bone139", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone140", "order": 86, "bone": "bone140", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone141", "order": 87, "bone": "bone141", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone142", "order": 88, "bone": "bone142", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone143", "order": 89, "bone": "bone143", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone144", "order": 90, "bone": "bone144", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone145", "order": 91, "bone": "bone145", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone146", "order": 92, "bone": "bone146", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone147", "order": 93, "bone": "bone147", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone148", "order": 58, "bone": "bone148", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone149", "order": 59, "bone": "bone149", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone150", "order": 77, "bone": "bone150", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone151", "order": 78, "bone": "bone151", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone152", "order": 79, "bone": "bone152", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone153", "order": 80, "bone": "bone153", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone154", "order": 81, "bone": "bone154", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone155", "order": 82, "bone": "bone155", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone156", "order": 83, "bone": "bone156", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone157", "order": 84, "bone": "bone157", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone158", "order": 32, "bone": "bone158", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone159", "order": 33, "bone": "bone159", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone160", "order": 34, "bone": "bone160", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone161", "order": 35, "bone": "bone161", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone162", "order": 36, "bone": "bone162", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone163", "order": 37, "bone": "bone163", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone164", "order": 38, "bone": "bone164", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone165", "order": 39, "bone": "bone165", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone166", "order": 40, "bone": "bone166", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone167", "order": 41, "bone": "bone167", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone168", "order": 49, "bone": "bone168", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone169", "order": 50, "bone": "bone169", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone170", "order": 51, "bone": "bone170", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone171", "order": 72, "bone": "bone171", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone172", "order": 73, "bone": "bone172", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone173", "order": 74, "bone": "bone173", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone174", "order": 75, "bone": "bone174", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone175", "order": 76, "bone": "bone175", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone176", "order": 57, "bone": "bone176", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone177", "order": 68, "bone": "bone177", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone178", "order": 69, "bone": "bone178", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone179", "order": 70, "bone": "bone179", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone180", "order": 71, "bone": "bone180", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone181", "order": 26, "bone": "bone181", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone182", "order": 27, "bone": "bone182", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone183", "order": 28, "bone": "bone183", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone184", "order": 64, "bone": "bone184", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone185", "order": 65, "bone": "bone185", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone186", "order": 66, "bone": "bone186", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone187", "order": 67, "bone": "bone187", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone188", "order": 29, "bone": "bone188", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone189", "order": 30, "bone": "bone189", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone190", "order": 31, "bone": "bone190", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone191", "order": 106, "bone": "bone191", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true, "mixGlobal": true}, {"name": "bone192", "order": 107, "bone": "bone192", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone193", "order": 108, "bone": "bone193", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone194", "order": 109, "bone": "bone194", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone195", "order": 110, "bone": "bone195", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}, {"name": "bone196", "order": 111, "bone": "bone196", "rotate": 1, "inertia": 0.5, "damping": 0.85, "inertiaGlobal": true, "strengthGlobal": true}], "skins": [{"name": "default", "attachments": {"biyan": {"biyan": {"type": "mesh", "uvs": [0.90949, 0, 0.94329, 0, 0.98938, 0.01869, 0.97372, 0.28128, 0.92, 0.46483, 0.87139, 0.56569, 0.80677, 0.61907, 0.66445, 0.67856, 0.60827, 0.67747, 0.60206, 0.65664, 0.34813, 0.81218, 0.33745, 0.87501, 0.21138, 1, 0.20972, 1, 0.12794, 1, 0.01201, 0.92221, 0.00468, 0.90121, 0.01964, 0.72522, 0.02916, 0.69855, 0.06363, 0.65952, 0.11464, 0.64498, 0.21527, 0.6451, 0.3582, 0.80541, 0.58973, 0.66339, 0.58909, 0.37394, 0.61676, 0.26302, 0.72913, 0.08988, 0.83395, 0.01804], "triangles": [4, 26, 3, 24, 6, 7, 6, 24, 25, 7, 8, 9, 9, 23, 24, 7, 9, 24, 13, 14, 21, 14, 20, 21, 9, 10, 22, 21, 12, 13, 11, 12, 21, 11, 21, 10, 10, 21, 22, 9, 22, 23, 5, 6, 25, 5, 25, 26, 4, 5, 26, 3, 26, 27, 15, 17, 14, 20, 14, 17, 17, 18, 19, 17, 19, 20, 15, 16, 17, 3, 0, 1, 0, 3, 27, 3, 1, 2], "vertices": [2, 17, 52.18, 31.98, 0.64398, 18, 97.92, -93.52, 0.35602, 2, 17, 51.67, 26.73, 0.68374, 18, 97.4, -98.77, 0.31626, 2, 17, 49.24, 19.74, 0.72754, 18, 94.97, -105.75, 0.27246, 2, 17, 25.17, 24.56, 0.54201, 18, 70.91, -100.94, 0.45799, 2, 17, 9, 34.56, 0.37609, 18, 54.74, -90.93, 0.62391, 2, 17, 0.41, 43.03, 0.45805, 18, 46.14, -82.47, 0.54195, 2, 17, -3.55, 53.54, 0.36067, 18, 42.18, -71.96, 0.63933, 2, 17, -6.89, 76.18, 0.16371, 18, 38.84, -49.32, 0.83629, 2, 17, -5.93, 84.89, 0.11168, 18, 39.8, -40.61, 0.88832, 2, 17, -3.91, 85.67, 0.1133, 18, 41.82, -39.83, 0.8867, 2, 17, -14.44, 126.5, 0.10993, 18, 31.29, 1, 0.89007, 2, 17, -20.1, 128.73, 0.14161, 18, 25.64, 3.23, 0.85839, 2, 17, -29.75, 149.44, 0.38359, 18, 15.99, 23.94, 0.61641, 2, 17, -29.72, 149.69, 0.38599, 18, 16.01, 24.2, 0.61401, 2, 17, -28.48, 162.39, 0.51119, 18, 17.26, 36.89, 0.48881, 2, 17, -19.51, 179.68, 0.67469, 18, 26.22, 54.19, 0.32531, 2, 17, -17.46, 180.63, 0.68175, 18, 28.28, 55.13, 0.31825, 2, 17, -1.39, 176.71, 0.62008, 18, 44.34, 51.21, 0.37992, 2, 17, 0.93, 174.99, 0.60025, 18, 46.66, 49.49, 0.39975, 2, 17, 4.02, 169.29, 0.53665, 18, 49.75, 43.79, 0.46335, 2, 17, 4.59, 161.24, 0.4474, 18, 50.32, 35.74, 0.5526, 2, 17, 3.04, 145.61, 0.27426, 18, 48.78, 20.11, 0.72574, 2, 17, -13.97, 124.88, 0.10793, 18, 31.76, -0.62, 0.89207, 2, 17, -4.35, 87.64, 0.10694, 18, 41.38, -37.86, 0.89306, 2, 17, 22.45, 85.11, 0.2692, 18, 68.18, -40.38, 0.7308, 2, 17, 32.3, 79.81, 0.36455, 18, 78.03, -45.69, 0.63545, 2, 17, 46.61, 60.8, 0.4079, 18, 92.34, -64.7, 0.5921, 2, 17, 51.66, 43.87, 0.54571, 18, 97.4, -81.63, 0.45429], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54], "width": 156, "height": 93}}, "bozhi": {"bozhi": {"type": "mesh", "uvs": [0.85932, 0, 0.96623, 0, 0.95607, 0.09606, 0.91114, 0.34837, 0.92614, 0.58011, 1, 0.71864, 1, 0.73408, 0.92156, 0.77784, 0.57579, 0.929, 0.44965, 0.96504, 0.25661, 1, 0.17575, 1, 0.126, 0.87366, 0.03828, 0.71022, 0.01293, 0.65545, 0.0501, 0.55206, 0.09857, 0.47271, 0.20225, 0.38137, 0.33726, 0.28654, 0.81331, 0.00021], "triangles": [2, 0, 1, 2, 19, 0, 3, 19, 2, 18, 19, 3, 13, 14, 15, 7, 4, 5, 18, 3, 4, 6, 7, 5, 16, 8, 13, 13, 15, 16, 13, 8, 12, 4, 17, 18, 17, 4, 16, 7, 8, 4, 16, 4, 8, 9, 12, 8, 10, 11, 12, 9, 10, 12], "vertices": [1, 14, 164, -53.25, 1, 1, 14, 163.17, -64.66, 1, 1, 14, 148.59, -62.5, 1, 1, 14, 110.44, -54.89, 1, 1, 14, 74.96, -53.9, 1, 1, 14, 53.25, -60.24, 1, 1, 14, 50.89, -60.06, 1, 1, 14, 44.83, -51.2, 1, 1, 14, 24.46, -12.62, 1, 1, 14, 19.95, 1.25, 1, 1, 14, 16.12, 22.24, 1, 1, 14, 16.75, 30.86, 1, 1, 14, 36.42, 34.76, 1, 1, 14, 62.04, 42.3, 1, 1, 14, 70.6, 44.39, 1, 1, 14, 86.08, 39.27, 1, 1, 14, 97.81, 33.21, 1, 1, 14, 110.94, 21.13, 1, 1, 14, 124.36, 5.66, 1, 1, 14, 164.33, -48.33, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 6, 8], "width": 107, "height": 153}}, "dao": {"dao": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 38, 377.62, -158.65, 1, 1, 38, -116.05, -5.1, 1, 1, 38, -47.44, 215.48, 1, 1, 38, 446.23, 61.93, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 517, "height": 231}}, "dao2": {"dao2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 40, 654.04, 46.75, 1, 1, 40, -183.02, -272.86, 1, 1, 40, -289.67, 6.48, 1, 1, 40, 547.39, 326.08, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 896, "height": 299}}, "daoguang2": {"1/a_01": {"rotation": 17.28, "width": 128, "height": 128}, "1/a_02": {"rotation": 17.28, "width": 128, "height": 128}, "1/a_03": {"rotation": 17.28, "width": 128, "height": 128}, "1/a_04": {"rotation": 17.28, "width": 128, "height": 128}}, "daoguang3": {"1/kirino_zhuan_X_0": {"width": 256, "height": 256}}, "daoqiao": {"daoqiao": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 39, 153.47, -165.48, 1, 1, 39, -138.4, -22.53, 1, 1, 39, -52.62, 152.6, 1, 1, 39, 239.25, 9.64, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 325, "height": 195}}, "erduo": {"erduo": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 16, -18.88, -34.68, 1, 1, 16, -23.12, 13.13, 1, 1, 16, 79.47, 22.23, 1, 1, 16, 83.72, -25.58, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 103}}, "jiandai": {"jiandai": {"type": "mesh", "uvs": [0.88049, 0, 0.95931, 0.01966, 0.99995, 0.04934, 1, 0.04981, 1, 0.05887, 0.96369, 0.05909, 0.88528, 0.04963, 0.81967, 0.04982, 0.73359, 0.07288, 0.67207, 0.09792, 0.56707, 0.17952, 0.52105, 0.22807, 0.46543, 0.31362, 0.48226, 0.33349, 0.45266, 0.50528, 0.45268, 0.54708, 0.50402, 0.66957, 0.61898, 0.81992, 0.61754, 0.8862, 0.57313, 0.92892, 0.44077, 0.9933, 0.42092, 1, 0.39143, 0.99467, 0.18858, 0.7889, 0.10106, 0.64325, 0.08622, 0.59933, 0, 0.52182, 0, 0.43121, 0.01927, 0.31674, 0.09976, 0.29785, 0.1414, 0.23163, 0.22082, 0.14439, 0.3681, 0.06359, 0.4911, 0.01664, 0.60034, 0], "triangles": [6, 0, 1, 5, 6, 1, 7, 34, 0, 7, 0, 6, 2, 5, 1, 2, 3, 5, 4, 5, 3, 8, 34, 7, 9, 34, 8, 9, 33, 34, 33, 9, 32, 10, 32, 9, 31, 32, 10, 11, 31, 10, 30, 31, 11, 12, 30, 11, 29, 30, 12, 29, 13, 27, 13, 29, 12, 27, 28, 29, 14, 26, 27, 14, 27, 13, 25, 14, 15, 25, 26, 14, 24, 25, 15, 24, 15, 16, 23, 24, 16, 23, 16, 17, 18, 23, 17, 19, 23, 18, 19, 22, 23, 20, 22, 19, 21, 22, 20], "vertices": [1, 83, 51.08, -5.31, 1, 1, 83, 61.21, -14.84, 1, 1, 83, 64.69, -25.82, 1, 1, 83, 64.66, -25.97, 1, 1, 83, 63.85, -28.78, 1, 1, 83, 58.35, -27.27, 1, 1, 83, 47.36, -20.93, 1, 2, 82, 65.44, -35.65, 0.00415, 83, 37.45, -18.13, 0.99585, 3, 79, -47.39, 51.34, 0.0002, 82, 50.97, -30.29, 0.11512, 83, 22.4, -21.55, 0.88468, 3, 79, -36.98, 44.24, 0.01224, 82, 38.54, -28.26, 0.43411, 83, 10.88, -26.64, 0.55366, 3, 79, -7.13, 35.55, 0.37254, 82, 7.82, -33.01, 0.59644, 83, -12.26, -47.41, 0.03102, 3, 79, 9.92, 32.86, 0.77451, 82, -8.78, -37.79, 0.22467, 83, -23.54, -60.47, 0.00081, 3, 80, -21.05, 42.07, 0.01945, 79, 38.89, 31.97, 0.97674, 82, -35.4, -49.25, 0.00382, 3, 80, -14.4, 44.09, 0.05681, 79, 44.35, 36.26, 0.94294, 82, -38.53, -55.44, 0.00025, 3, 81, -32.82, 47.2, 0.00418, 80, 40.38, 34.13, 0.94933, 79, 99.01, 46.88, 0.04649, 3, 81, -20.51, 41.65, 0.0478, 80, 53.82, 32.84, 0.94713, 79, 112, 50.55, 0.00506, 2, 81, 18.87, 32.73, 0.78396, 80, 93.98, 37.06, 0.21604, 1, 81, 70.56, 29.22, 1, 1, 81, 89.98, 20.21, 1, 1, 81, 99.69, 8.18, 1, 1, 81, 110.11, -19.31, 1, 1, 81, 110.8, -23.04, 1, 1, 81, 107.33, -26.55, 1, 1, 81, 33.65, -28.26, 1, 2, 81, -14.89, -21.45, 0.15906, 80, 79.44, -25.1, 0.84094, 2, 81, -28.78, -17.74, 0.00731, 80, 65.09, -26.05, 0.99269, 1, 80, 38.87, -37.12, 1, 2, 80, 9.74, -34.31, 0.94969, 79, 95.31, -28.02, 0.05031, 2, 80, -26.77, -27.75, 0.20859, 79, 58.91, -35.16, 0.79141, 2, 80, -31.63, -14.58, 0.05533, 79, 49.6, -24.66, 0.94467, 1, 79, 27.24, -24.19, 1, 2, 79, -3.27, -19.85, 0.80179, 82, -19.13, 15.54, 0.19821, 1, 82, 15.74, 15.1, 1, 2, 82, 39.83, 10.4, 0.98268, 83, -9.15, 6.45, 0.01732, 1, 83, 8.82, 6.86, 1], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 30, 32, 42, 44], "width": 157, "height": 323}}, "jiemao": {"jiemao": {"type": "mesh", "uvs": [0.98658, 0, 1, 0, 1, 0.02767, 0.9845, 0.12144, 0.96118, 0.19131, 0.77484, 0.31106, 0.69665, 0.42255, 0.6587, 0.63991, 0.65896, 0.75333, 0.36159, 1, 0.33924, 1, 0.2488, 0.88575, 0.14283, 0.85551, 0.07592, 0.97499, 0.05071, 0.97378, 0, 0.92869, 0, 0.89454, 0.0399, 0.85965, 0.09262, 0.76258, 0.23549, 0.76313, 0.37991, 0.95899, 0.62638, 0.7674, 0.62428, 0.65393, 0.62703, 0.41787, 0.72997, 0.20836, 0.92312, 0.01239], "triangles": [12, 19, 11, 10, 11, 20, 9, 10, 20, 9, 20, 21, 9, 21, 8, 5, 6, 24, 22, 23, 7, 22, 7, 8, 21, 22, 8, 6, 23, 24, 7, 23, 6, 5, 24, 25, 4, 5, 25, 20, 11, 19, 12, 18, 19, 0, 1, 2, 3, 25, 0, 3, 0, 2, 4, 25, 3, 15, 16, 17, 17, 13, 14, 12, 17, 18, 15, 17, 14, 12, 13, 17], "vertices": [2, 17, 48.41, 24.27, 0.67139, 18, 94.15, -101.23, 0.32861, 2, 17, 48.21, 22.2, 0.69294, 18, 93.95, -103.3, 0.30706, 2, 17, 46.12, 22.4, 0.67889, 18, 91.85, -103.09, 0.32111, 2, 17, 39.26, 25.49, 0.6037, 18, 84.99, -100.01, 0.3963, 2, 17, 34.33, 29.61, 0.52988, 18, 80.06, -95.89, 0.47012, 2, 17, 28.09, 59.24, 0.40599, 18, 73.82, -66.26, 0.59401, 2, 17, 20.84, 72.13, 0.2516, 18, 66.57, -53.37, 0.7484, 2, 17, 4.97, 79.59, 0.10687, 18, 50.71, -45.91, 0.89313, 2, 17, -3.61, 80.39, 0.07413, 18, 42.12, -45.1, 0.92587, 2, 17, -17.77, 128.09, 0.15949, 18, 27.96, 2.6, 0.84051, 2, 17, -17.43, 131.54, 0.19286, 18, 28.3, 6.04, 0.80714, 2, 17, -7.42, 144.65, 0.32555, 18, 38.31, 19.15, 0.67445, 2, 17, -3.53, 160.77, 0.49799, 18, 42.2, 35.27, 0.50201, 2, 17, -11.56, 171.98, 0.62675, 18, 34.17, 46.48, 0.37325, 2, 17, -11.09, 175.86, 0.66885, 18, 34.65, 50.36, 0.33115, 2, 17, -6.91, 183.35, 0.74743, 18, 38.82, 57.85, 0.25257, 2, 17, -4.33, 183.09, 0.74297, 18, 41.41, 57.59, 0.25703, 2, 17, -2.29, 176.68, 0.6719, 18, 43.44, 51.18, 0.3281, 2, 17, 4.25, 167.83, 0.57639, 18, 49.99, 42.33, 0.42361, 2, 17, 2.05, 145.79, 0.33454, 18, 47.79, 20.29, 0.66546, 2, 17, -14.95, 124.96, 0.12979, 18, 30.79, -0.53, 0.87021, 1, 18, 41.55, -39.97, 1, 1, 18, 50.17, -40.49, 1, 2, 17, 22.24, 82.83, 0.16311, 18, 67.98, -42.67, 0.83689, 2, 17, 36.53, 65.4, 0.3925, 18, 82.27, -60.1, 0.6075, 2, 17, 48.44, 34.15, 0.55858, 18, 94.17, -91.35, 0.44142], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 16, 18], "width": 155, "height": 76}}, "lian": {"lian": {"type": "mesh", "uvs": [0.67618, 0, 0.77972, 0.02768, 0.86718, 0.06699, 0.88764, 0.08303, 0.9454, 0.13541, 0.98002, 0.21827, 1, 0.31269, 1, 0.51593, 0.95943, 0.66162, 0.89967, 0.75714, 0.83686, 0.83151, 0.69299, 0.96098, 0.60776, 1, 0.55073, 0.99904, 0.42868, 0.91626, 0.32589, 0.85458, 0.22772, 0.76096, 0.16024, 0.63789, 0.14168, 0.61932, 0.05689, 0.54381, 0.02365, 0.47905, 0, 0.38011, 0, 0.31861, 0.01188, 0.25546, 0.04484, 0.19571, 0.1135, 0.13887, 0.17915, 0.10199, 0.45038, 0.02496, 0.5679, 0, 0.3508, 0.70554, 0.18694, 0.41809, 0.22054, 0.37121, 0.27843, 0.37208, 0.35755, 0.39466, 0.41684, 0.42992, 0.44607, 0.46118, 0.48114, 0.501, 0.51434, 0.57147, 0.53345, 0.63607, 0.5429, 0.69512, 0.52047, 0.74271, 0.48256, 0.77582, 0.44158, 0.78579, 0.39663, 0.79078, 0.35531, 0.78195, 0.32443, 0.75612, 0.31703, 0.70554, 0.31992, 0.65691, 0.30095, 0.60471, 0.27457, 0.57268, 0.23887, 0.53968, 0.19977, 0.50312, 0.18426, 0.45619, 0.64987, 0.79568, 0.70573, 0.71403, 0.73049, 0.64066, 0.73852, 0.53924, 0.74193, 0.46134, 0.72698, 0.37526, 0.7023, 0.31279, 0.66219, 0.25031, 0.5866, 0.18505, 0.4817, 0.12674, 0.37062, 0.1198, 0.27035, 0.15173, 0.19785, 0.18783, 0.1454, 0.24753, 0.09912, 0.3225, 0.07135, 0.41691, 0.09757, 0.51549, 0.12997, 0.56131, 0.17162, 0.60435, 0.20247, 0.65017, 0.23333, 0.68765, 0.247, 0.72776, 0.27273, 0.76925, 0.30489, 0.79819, 0.33277, 0.82907, 0.36814, 0.85512, 0.4045, 0.87061, 0.4487, 0.87869, 0.52225, 0.86888, 0.5878, 0.84002], "triangles": [76, 75, 45, 77, 76, 44, 73, 72, 48, 63, 33, 64, 53, 39, 54, 65, 32, 66, 33, 63, 62, 33, 62, 61, 34, 61, 60, 34, 60, 59, 58, 35, 59, 57, 36, 58, 56, 37, 57, 55, 38, 56, 54, 39, 55, 82, 40, 53, 67, 66, 31, 32, 65, 64, 30, 68, 67, 69, 68, 52, 72, 71, 49, 74, 73, 46, 75, 74, 46, 78, 77, 43, 81, 41, 82, 78, 43, 79, 80, 41, 81, 79, 42, 80, 44, 45, 29, 44, 29, 43, 41, 42, 29, 42, 43, 29, 76, 45, 44, 77, 44, 43, 72, 49, 48, 34, 49, 50, 71, 50, 49, 69, 52, 51, 50, 32, 33, 50, 51, 32, 34, 50, 33, 48, 35, 36, 48, 36, 37, 47, 37, 38, 40, 39, 53, 31, 66, 32, 34, 33, 61, 35, 34, 59, 36, 35, 58, 57, 37, 36, 38, 37, 56, 33, 32, 64, 39, 38, 55, 49, 34, 35, 31, 30, 67, 52, 68, 30, 30, 32, 52, 32, 51, 52, 30, 31, 32, 50, 70, 51, 48, 49, 35, 47, 48, 37, 47, 73, 48, 46, 73, 47, 29, 47, 38, 29, 38, 39, 46, 47, 29, 40, 29, 39, 45, 46, 29, 45, 75, 46, 41, 29, 40, 41, 40, 82, 79, 43, 42, 80, 42, 41, 12, 13, 81, 82, 12, 81, 14, 80, 81, 13, 14, 81, 11, 12, 82, 16, 75, 76, 15, 16, 76, 16, 17, 73, 50, 71, 70, 18, 70, 71, 17, 18, 71, 70, 69, 51, 19, 69, 70, 63, 26, 27, 62, 27, 28, 63, 27, 62, 61, 28, 0, 62, 28, 61, 1, 60, 61, 1, 61, 0, 60, 1, 2, 60, 4, 59, 3, 60, 2, 4, 60, 3, 59, 4, 5, 59, 5, 6, 58, 59, 6, 57, 58, 6, 57, 6, 7, 56, 57, 7, 8, 56, 7, 55, 56, 8, 9, 55, 8, 54, 55, 9, 10, 54, 9, 53, 54, 10, 11, 53, 10, 82, 53, 11, 64, 26, 63, 65, 26, 64, 25, 26, 65, 66, 25, 65, 24, 25, 66, 23, 24, 66, 67, 23, 66, 22, 23, 67, 21, 22, 67, 68, 21, 67, 20, 21, 68, 20, 68, 69, 19, 20, 69, 19, 70, 18, 17, 71, 72, 73, 17, 72, 16, 73, 74, 16, 74, 75, 15, 76, 77, 15, 77, 78, 14, 79, 80, 78, 79, 14, 15, 78, 14], "vertices": [2, 17, 130.27, 29, 0.8, 18, 176, -96.5, 0.2, 2, 17, 120.74, 5.59, 0.8, 18, 166.47, -119.91, 0.2, 2, 17, 108.57, -13.78, 0.8, 18, 154.3, -139.28, 0.2, 2, 17, 103.95, -18.14, 0.8, 18, 149.69, -143.64, 0.2, 2, 17, 89.08, -30.26, 0.8, 18, 134.82, -155.76, 0.2, 2, 17, 66.85, -36.22, 0.8, 18, 112.58, -161.72, 0.2, 2, 17, 41.96, -38.48, 0.8, 18, 87.7, -163.98, 0.2, 2, 17, -10.63, -33.32, 0.8, 18, 35.1, -158.82, 0.2, 2, 17, -47.4, -20.18, 0.8, 18, -1.67, -145.68, 0.2, 2, 17, -70.75, -3.84, 0.8, 18, -25.02, -129.34, 0.2, 2, 17, -88.56, 12.67, 0.8, 18, -42.83, -112.82, 0.2, 2, 17, -118.78, 49.46, 0.8, 18, -73.05, -76.04, 0.2, 2, 17, -126.93, 70.3, 0.8, 18, -81.2, -55.2, 0.2, 2, 17, -125.38, 83.56, 0.8, 18, -79.65, -41.94, 0.2, 2, 17, -101.17, 109.88, 0.8, 18, -55.44, -15.62, 0.2, 2, 17, -82.87, 132.26, 0.8, 18, -37.13, 6.76, 0.2, 2, 17, -56.4, 152.74, 0.8, 18, -10.67, 27.24, 0.2, 2, 17, -23.01, 165.33, 0.8, 18, 22.72, 39.84, 0.2, 2, 17, -17.79, 169.19, 0.8, 18, 27.95, 43.69, 0.2, 2, 17, 3.69, 187.02, 0.8, 18, 49.42, 61.52, 0.2, 2, 17, 21.21, 193.11, 0.8, 18, 66.94, 67.62, 0.2, 2, 17, 47.35, 196.11, 0.8, 18, 93.08, 70.61, 0.2, 2, 17, 63.26, 194.55, 0.8, 18, 108.99, 69.05, 0.2, 2, 17, 79.33, 190.18, 0.8, 18, 125.07, 64.69, 0.2, 2, 17, 94.04, 180.99, 0.8, 18, 139.77, 55.49, 0.2, 2, 17, 107.18, 163.56, 0.8, 18, 152.91, 38.06, 0.2, 2, 17, 115.22, 147.34, 0.8, 18, 160.96, 21.84, 0.2, 2, 17, 128.96, 82.22, 0.8, 18, 174.7, -43.28, 0.2, 2, 17, 132.74, 54.21, 0.8, 18, 178.47, -71.28, 0.2, 1, 18, 0.86, -2.82, 1, 2, 17, 33.25, 153.54, 0.2, 18, 78.99, 28.04, 0.8, 2, 17, 44.61, 144.53, 0.2, 18, 90.35, 19.03, 0.8, 2, 17, 43.07, 131.07, 0.2, 18, 88.8, 5.57, 0.8, 2, 17, 35.42, 113.21, 0.2, 18, 81.15, -12.28, 0.8, 2, 17, 24.94, 100.3, 0.2, 18, 70.68, -25.2, 0.8, 2, 17, 16.18, 94.29, 0.2, 18, 61.92, -31.21, 0.8, 2, 17, 5.08, 87.13, 0.2, 18, 50.82, -38.37, 0.8, 2, 17, -13.91, 81.19, 0.2, 18, 31.82, -44.31, 0.8, 2, 17, -31.06, 78.38, 0.2, 18, 14.67, -47.12, 0.8, 2, 17, -46.56, 77.67, 0.2, 18, -0.83, -47.83, 0.8, 2, 17, -58.36, 84.1, 0.2, 18, -12.63, -41.4, 0.8, 2, 17, -66.06, 93.77, 0.2, 18, -20.33, -31.73, 0.8, 2, 17, -67.71, 103.57, 0.2, 18, -21.97, -21.93, 0.8, 2, 17, -67.97, 114.16, 0.2, 18, -22.24, -11.34, 0.8, 2, 17, -64.75, 123.56, 0.2, 18, -19.01, -1.94, 0.8, 2, 17, -57.36, 130.1, 0.2, 18, -11.62, 4.6, 0.8, 2, 17, -44.1, 130.54, 0.2, 18, 1.63, 5.04, 0.8, 2, 17, -31.58, 128.63, 0.2, 18, 14.15, 3.13, 0.8, 2, 17, -17.64, 131.72, 0.2, 18, 28.09, 6.23, 0.8, 2, 17, -8.75, 137.05, 0.2, 18, 36.98, 11.56, 0.8, 2, 17, 0.6, 144.53, 0.2, 18, 46.34, 19.03, 0.8, 2, 17, 10.96, 152.71, 0.2, 18, 56.69, 27.21, 0.8, 2, 17, 23.45, 155.13, 0.2, 18, 69.19, 29.63, 0.8, 2, 17, -75.02, 55.31, 0.5, 18, -29.29, -70.19, 0.5, 2, 17, -55.17, 40.23, 0.5, 18, -9.44, -85.27, 0.5, 2, 17, -36.75, 32.6, 0.5, 18, 8.98, -92.89, 0.5, 2, 17, -10.69, 28.16, 0.5, 18, 35.04, -97.34, 0.5, 2, 17, 9.39, 25.39, 0.5, 18, 55.12, -100.11, 0.5, 2, 17, 32, 26.69, 0.5, 18, 77.74, -98.81, 0.5, 2, 17, 48.73, 30.85, 0.5, 18, 94.47, -94.65, 0.5, 2, 17, 65.82, 38.61, 0.5, 18, 111.55, -86.89, 0.5, 2, 17, 84.43, 54.56, 0.5, 18, 130.16, -70.94, 0.5, 2, 17, 101.91, 77.51, 0.5, 18, 147.65, -47.99, 0.5, 2, 17, 106.24, 103.2, 0.5, 18, 151.98, -22.3, 0.5, 2, 17, 100.27, 127.36, 0.5, 18, 146, 1.86, 0.5, 2, 17, 92.58, 145.16, 0.5, 18, 138.32, 19.66, 0.5, 2, 17, 78.33, 158.89, 0.5, 18, 124.07, 33.39, 0.5, 2, 17, 59.99, 171.57, 0.5, 18, 105.72, 46.07, 0.5, 2, 17, 36.19, 180.43, 0.5, 18, 81.93, 54.93, 0.5, 2, 17, 10.09, 176.82, 0.5, 18, 55.82, 51.33, 0.5, 1, 17, -2.51, 170.44, 1, 2, 17, -14.59, 161.83, 0.5, 18, 31.14, 36.34, 0.5, 2, 17, -27.15, 155.81, 0.5, 18, 18.58, 30.31, 0.5, 2, 17, -37.56, 149.58, 0.5, 18, 8.17, 24.08, 0.5, 2, 17, -48.25, 147.41, 0.5, 18, -2.52, 21.91, 0.5, 2, 17, -59.57, 142.47, 0.5, 18, -13.84, 16.97, 0.5, 2, 17, -67.8, 135.71, 0.5, 18, -22.06, 10.22, 0.5, 2, 17, -76.42, 130.01, 0.5, 18, -30.69, 4.51, 0.5, 2, 17, -83.97, 122.43, 0.5, 18, -38.24, -3.07, 0.5, 2, 17, -88.81, 114.35, 0.5, 18, -43.08, -11.14, 0.5, 2, 17, -91.91, 104.27, 0.5, 18, -46.18, -21.23, 0.5, 2, 17, -91.05, 86.89, 0.5, 18, -45.32, -38.61, 0.5, 2, 17, -85.08, 70.89, 0.5, 18, -39.34, -54.61, 0.5], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 24, 26, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 60, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 106], "width": 234, "height": 260}}, "shenti": {"shenti": {"type": "mesh", "uvs": [0.28843, 0, 0.49116, 0.0217, 0.62122, 0.04929, 0.80153, 0.1006, 0.91941, 0.1509, 0.93189, 0.16529, 0.92779, 0.24766, 0.91055, 0.40185, 1, 0.55381, 0.99633, 0.58366, 0.9365, 0.63539, 0.75336, 0.73508, 0.34887, 0.90583, 0.17893, 0.99298, 0.14761, 1, 0.10828, 1, 0.08977, 0.98407, 0.07539, 0.96017, 0.0753, 0.85564, 0.09253, 0.64168, 0.01737, 0.32047, 0, 0.20366, 0, 0.05775, 0.01488, 0.03922, 0.05766, 0.00292, 0.07832, 0], "triangles": [2, 20, 1, 1, 20, 0, 7, 2, 3, 6, 3, 4, 6, 7, 3, 6, 4, 5, 17, 13, 14, 14, 15, 16, 18, 12, 13, 17, 14, 16, 13, 17, 18, 18, 19, 12, 12, 19, 11, 11, 19, 7, 11, 7, 10, 20, 2, 7, 7, 19, 20, 24, 25, 23, 0, 20, 25, 9, 10, 8, 8, 10, 7, 25, 20, 21, 22, 23, 25, 25, 21, 22], "vertices": [1, 12, 36.31, -20.46, 1, 2, 11, 196.41, -77.26, 0.07415, 12, 47.55, -62.29, 0.92585, 2, 11, 188.17, -104.44, 0.15072, 12, 51.62, -90.41, 0.84928, 2, 11, 173.51, -141.97, 0.23999, 12, 54.26, -130.61, 0.76001, 2, 11, 159.77, -166.29, 0.27763, 12, 52.14, -158.46, 0.72237, 2, 11, 156.07, -168.75, 0.28051, 12, 49.84, -162.26, 0.71949, 2, 11, 135.71, -166.84, 0.31932, 12, 30.59, -169.16, 0.68068, 2, 11, 97.71, -161.23, 0.47583, 12, -6.2, -180.21, 0.52417, 2, 11, 59.11, -178.24, 0.58575, 12, -33.94, -212, 0.41425, 2, 11, 51.75, -177.09, 0.59067, 12, -41.09, -214.07, 0.40933, 2, 11, 39.59, -163.76, 0.6129, 12, -57.76, -207.17, 0.3871, 2, 11, 16.88, -123.72, 0.73265, 12, -95.31, -180.54, 0.26735, 2, 11, -21.02, -35.92, 0.98263, 12, -166.88, -117.12, 0.01737, 2, 11, -40.76, 1.17, 0.99994, 12, -200.5, -91.91, 6e-05, 1, 11, -42.16, 7.89, 1, 1, 11, -41.74, 16.21, 1, 1, 11, -37.59, 19.93, 1, 1, 11, -31.51, 22.67, 1, 1, 11, -5.63, 21.37, 1, 1, 11, 47.18, 15, 1, 1, 11, 127.55, 26.84, 1, 1, 11, 156.67, 29.04, 1, 2, 11, 192.81, 27.19, 0.99813, 12, -0.03, 30.76, 0.00187, 2, 11, 197.24, 23.8, 0.97792, 12, 5.41, 29.57, 0.02208, 2, 11, 205.76, 14.29, 0.80938, 12, 17.17, 24.57, 0.19062, 2, 11, 206.26, 9.88, 0.72061, 12, 19.5, 20.79, 0.27939], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 36, 38, 16, 18, 14, 16], "width": 212, "height": 248}}, "tongkong": {"tongkong": {"type": "mesh", "uvs": [1, 0.5, 0.81235, 0.60346, 0.29892, 1, 0, 1, 0, 0.5, 0.24638, 0.45854, 0.64041, 0.28544, 0.75504, 0, 1, 0], "triangles": [2, 6, 1, 0, 1, 7, 1, 6, 7, 7, 8, 0, 3, 5, 2, 2, 5, 6, 3, 4, 5], "vertices": [2, 84, 10.48, -104.55, 0.00515, 85, -20.3, -14.7, 0.99485, 1, 85, -25.35, 8.04, 1, 2, 84, -16.28, -18.81, 0.91456, 85, -47.06, 71.05, 0.08544, 1, 84, -12.84, 16.3, 1, 2, 84, 21.99, 12.88, 0.99947, 85, -8.79, 102.74, 0.00053, 1, 84, 22.04, -16.33, 1, 1, 85, -1.21, 26.06, 1, 2, 84, 48.13, -79.2, 0.01, 85, 17.35, 10.65, 0.99, 1, 85, 14.53, -18.12, 1], "hull": 9, "edges": [6, 8, 8, 10, 4, 6, 10, 4, 14, 16, 14, 12, 12, 2, 0, 16, 2, 0, 2, 4, 10, 12], "width": 118, "height": 70}}, "toufa": {"toufa": {"type": "mesh", "uvs": [0.68232, 0, 0.83926, 0.04327, 0.95687, 0.12585, 1, 0.21312, 1, 0.26452, 0.9988, 0.30775, 0.95521, 0.30665, 0.94591, 0.32871, 0.92382, 0.32898, 0.86807, 0.27158, 0.82905, 0.25163, 0.69648, 0.20013, 0.59825, 0.20046, 0.50104, 0.22595, 0.41365, 0.27134, 0.26638, 0.38423, 0.22673, 0.42713, 0.13092, 0.62943, 0.11126, 0.75483, 0.14453, 0.86525, 0.21364, 0.96673, 0.25577, 0.96196, 0.24389, 0.98183, 0.20912, 1, 0.14969, 1, 0.11118, 0.96934, 0.06116, 0.90943, 0.02382, 0.79703, 0, 0.54351, 0, 0.47103, 0.03347, 0.40149, 0.14031, 0.2406, 0.23389, 0.14518, 0.41802, 0.03998, 0.53392, 0], "triangles": [23, 20, 22, 23, 24, 20, 24, 25, 20, 22, 20, 21, 25, 19, 20, 25, 26, 19, 26, 27, 19, 27, 18, 19, 18, 27, 17, 17, 27, 28, 17, 28, 16, 30, 16, 29, 16, 28, 29, 16, 30, 31, 15, 16, 31, 31, 32, 15, 15, 32, 14, 14, 32, 33, 13, 14, 33, 12, 13, 34, 13, 33, 34, 12, 0, 11, 12, 34, 0, 11, 0, 1, 7, 8, 6, 6, 8, 9, 5, 6, 4, 4, 6, 3, 3, 6, 9, 3, 9, 2, 9, 10, 2, 10, 1, 2, 10, 11, 1], "vertices": [1, 96, 13.25, -12.62, 1, 2, 95, 39.3, -9.52, 0.88774, 96, -8.96, -5.25, 0.11226, 1, 95, 19.37, -14.35, 1, 1, 95, 6.14, -10.78, 1, 1, 95, 0.8, -5.96, 1, 1, 95, -3.58, -1.78, 1, 1, 95, 0.74, 2.78, 1, 1, 95, -0.66, 5.84, 1, 1, 95, 1.45, 8.23, 1, 2, 95, 12.79, 8.81, 0.99944, 96, -11.22, 26.9, 0.00056, 2, 95, 18.63, 11.11, 0.9801, 96, -5.78, 23.78, 0.0199, 2, 95, 36.77, 20.45, 0.28192, 96, 12.86, 15.46, 0.71808, 3, 95, 46.21, 30.99, 0.01116, 96, 26.98, 14.68, 0.98028, 97, -13.02, 27.83, 0.00856, 2, 96, 41.16, 17.42, 0.74777, 97, 0.02, 21.62, 0.25223, 2, 96, 54.1, 23.03, 0.17899, 97, 13.76, 18.46, 0.82101, 2, 97, 40.17, 17.05, 0.92536, 98, 4.2, 17.51, 0.07464, 2, 97, 48.4, 18.02, 0.50488, 98, 11.93, 14.54, 0.49512, 2, 98, 43.38, 12.66, 0.16276, 99, 6.67, 10.14, 0.83724, 2, 99, 23.98, 6.08, 0.99733, 100, -5.41, 12.24, 0.00267, 2, 99, 39.74, 9.76, 0.03511, 100, 9.14, 5.15, 0.96489, 1, 100, 26.31, 2.69, 1, 1, 100, 29.97, 7.57, 1, 1, 100, 30.83, 4.42, 1, 1, 100, 29.26, -0.97, 1, 1, 100, 23.4, -7.22, 1, 1, 100, 16.48, -8.32, 1, 1, 100, 5.43, -7.83, 1, 1, 99, 28.99, -6.9, 1, 2, 98, 39.5, -9.36, 0.86015, 99, -6.66, -7.81, 0.13985, 1, 98, 30.13, -13.25, 1, 1, 98, 19.29, -12.54, 1, 2, 97, 41.27, -10.02, 0.22855, 98, -7.41, -6.97, 0.77145, 2, 97, 22.37, -11.67, 0.98267, 98, -24.92, 0.36, 0.01733, 2, 96, 51.57, -9.27, 0.93669, 97, -7.43, -6.04, 0.06331, 1, 96, 34.58, -13.88, 1], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68], "width": 144, "height": 140}}, "toufa1": {"toufa1": {"type": "mesh", "uvs": [0.76325, 0, 0.87071, 0.02032, 0.96138, 0.06516, 1, 0.11163, 1, 0.12862, 0.96546, 0.14986, 0.80051, 0.14113, 0.67121, 0.19607, 0.6288, 0.27924, 0.58639, 0.36242, 0.6053, 0.47031, 0.62421, 0.57819, 0.60472, 0.63056, 0.5309, 0.71712, 0.32969, 0.79227, 0.27267, 0.86327, 0.38687, 0.95031, 0.38651, 0.96707, 0.33083, 1, 0.25686, 1, 0.1636, 0.9732, 0.01498, 0.89487, 0, 0.87798, 0, 0.82812, 6e-05, 0.81432, 0.08229, 0.75204, 0.24035, 0.7009, 0.28889, 0.66387, 0.29382, 0.59074, 0.27889, 0.5524, 0.16543, 0.42104, 0.16537, 0.33098, 0.1653, 0.24091, 0.21002, 0.16711, 0.34177, 0.07874, 0.50861, 0.02025, 0.61609, 0], "triangles": [5, 6, 2, 6, 0, 1, 6, 1, 2, 2, 3, 5, 4, 5, 3, 6, 36, 0, 6, 7, 36, 35, 36, 7, 34, 35, 7, 8, 34, 7, 33, 34, 8, 32, 33, 8, 9, 31, 32, 8, 9, 32, 30, 31, 9, 30, 9, 10, 29, 30, 10, 29, 10, 11, 28, 29, 11, 12, 28, 11, 27, 28, 12, 13, 27, 12, 14, 27, 13, 26, 27, 14, 14, 25, 26, 15, 25, 14, 15, 23, 25, 23, 24, 25, 21, 22, 23, 15, 21, 23, 20, 21, 15, 15, 19, 20, 15, 16, 19, 16, 18, 19, 17, 18, 16], "vertices": [2, 87, -16.81, -9.11, 0.07872, 86, 39.87, -18.86, 0.92128, 1, 86, 23.59, -20.19, 1, 1, 86, 7.34, -15.14, 1, 1, 86, -2.07, -6.88, 1, 1, 86, -3.62, -3.08, 1, 1, 86, -0.91, 3.54, 1, 4, 89, -27.77, 75.58, 1e-05, 88, -20.03, 49.24, 0.00312, 87, -4.54, 23.07, 0.10121, 86, 22.04, 10.61, 0.89567, 4, 89, -19.77, 54.06, 0.02282, 88, -1.82, 35.27, 0.1805, 87, 18.32, 25.2, 0.65561, 86, 34.41, 29.94, 0.14107, 4, 89, -1.97, 42.98, 0.19872, 88, 19.15, 35.35, 0.51304, 87, 33.64, 39.51, 0.28354, 86, 32.55, 50.83, 0.0047, 4, 90, -28.82, 27.59, 0.012, 89, 15.83, 31.9, 0.64338, 88, 40.11, 35.43, 0.29447, 87, 48.96, 53.83, 0.05015, 4, 90, -2.67, 27.82, 0.45164, 89, 41.66, 27.89, 0.53212, 88, 64.13, 45.77, 0.01561, 87, 59.55, 77.73, 0.00063, 3, 91, -31.35, 5.84, 0.00012, 90, 23.47, 28.06, 0.97219, 89, 67.5, 23.88, 0.02768, 3, 91, -20.5, 12.87, 0.06328, 90, 35.76, 24.03, 0.93657, 89, 78.97, 17.91, 0.00015, 2, 91, 1.75, 20.27, 0.77902, 90, 55.5, 11.38, 0.22098, 3, 94, 1.71, 40.59, 0.00036, 92, 3.22, 12.45, 0.65301, 91, 35.26, 12.77, 0.34663, 3, 94, 1.35, 21.59, 0.14537, 93, 9.31, 15.57, 0.17722, 92, 21.81, 16.4, 0.67742, 3, 94, 25.16, 9.45, 0.98872, 93, 34.33, 24.95, 0.00359, 92, 28.24, 42.34, 0.00769, 2, 94, 26.8, 5.76, 0.99704, 92, 31.46, 44.78, 0.00296, 1, 94, 22.79, -4.82, 1, 1, 94, 13.04, -9.31, 1, 2, 94, -1.94, -9.09, 0.57493, 93, 29.72, -7.57, 0.42507, 1, 93, 5.17, -22.34, 1, 2, 93, 0.63, -23.17, 0.98899, 92, 48.84, -12.67, 0.01101, 2, 93, -10.81, -19.51, 0.78269, 92, 39.35, -20.03, 0.21731, 2, 93, -13.97, -18.48, 0.70637, 92, 36.71, -22.06, 0.29363, 2, 93, -24.64, -2.55, 0.10083, 92, 17.55, -21.84, 0.89917, 3, 92, -6.24, -11.28, 0.21063, 91, 29.09, -12.02, 0.78332, 90, 47.57, -30.18, 0.00605, 2, 91, 17.82, -13.46, 0.84134, 90, 39.36, -22.32, 0.15866, 3, 91, 4.96, -25.55, 0.18671, 90, 21.89, -19.92, 0.77737, 89, 58.15, -23.21, 0.03592, 3, 91, 0.04, -33.66, 0.0381, 90, 12.48, -21.19, 0.74637, 89, 48.66, -22.93, 0.21553, 3, 90, -20.61, -34.52, 0.01674, 89, 13.85, -30.72, 0.96049, 88, 71.74, -18.65, 0.02277, 2, 89, -7.14, -25.17, 0.54862, 88, 51.01, -25.11, 0.45138, 2, 89, -28.12, -19.62, 0.05691, 88, 30.29, -31.57, 0.94309, 2, 88, 11.38, -30.66, 0.99108, 87, 72.81, -14.18, 0.00892, 2, 88, -14.63, -18.75, 0.29817, 87, 45.63, -23.12, 0.70183, 1, 87, 17.63, -23.28, 1, 2, 87, 1.69, -19.74, 0.88202, 86, 59.63, -10.81, 0.11798], "hull": 37, "edges": [0, 72, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 64, 66, 66, 68, 68, 70, 70, 72, 14, 16, 16, 18, 18, 20, 20, 22, 60, 62, 62, 64], "width": 145, "height": 241}}, "toufa2": {"toufa2": {"type": "mesh", "uvs": [0.64163, 0, 0.77714, 0.06008, 0.85857, 0.15024, 0.93174, 0.25955, 0.97929, 0.386, 1, 0.56736, 1, 0.67071, 0.96491, 0.74584, 0.85041, 0.87201, 0.83667, 0.90412, 0.82102, 0.90372, 0.76201, 0.74193, 0.70174, 0.68879, 0.55993, 0.61363, 0.41627, 0.61312, 0.30541, 0.6988, 0.26892, 0.74126, 0.20327, 0.86967, 0.10375, 0.95705, 0.07947, 0.9895, 0.04822, 0.98937, 0.03, 0.95649, 0.00033, 0.85092, 0, 0.83996, 0, 0.75245, 0.0286, 0.60047, 0.10214, 0.43277, 0.26802, 0.19366, 0.37414, 0.08025, 0.51266, 0], "triangles": [17, 24, 25, 17, 23, 24, 22, 18, 21, 17, 25, 16, 22, 23, 18, 17, 18, 23, 19, 20, 21, 18, 19, 21, 15, 26, 27, 14, 15, 27, 16, 26, 15, 25, 26, 16, 13, 14, 28, 27, 28, 14, 13, 29, 0, 13, 28, 29, 13, 0, 1, 12, 13, 1, 12, 1, 2, 12, 2, 3, 4, 11, 3, 11, 12, 3, 4, 5, 11, 5, 7, 11, 6, 7, 5, 8, 11, 7, 10, 11, 8, 9, 10, 8], "vertices": [2, 102, 39.28, -19.78, 0.84762, 103, -14.04, -14.21, 0.15238, 1, 102, 16.86, -24.52, 1, 2, 101, 56.57, -15.05, 0.13033, 102, 1.18, -22.85, 0.86967, 2, 101, 43.19, -23.31, 0.60896, 102, -14.06, -18.99, 0.39104, 2, 101, 29.59, -27.08, 0.91555, 102, -26.23, -11.85, 0.08445, 2, 101, 12.5, -25.09, 0.9997, 102, -36.69, 1.81, 0.0003, 1, 101, 3.37, -22.11, 1, 1, 101, -1.49, -14.48, 1, 2, 101, -6.83, 7.01, 0.99999, 102, -27.1, 38.04, 1e-05, 1, 101, -8.97, 10.08, 1, 2, 101, -8.14, 12.51, 0.99998, 102, -24.06, 42.8, 2e-05, 4, 101, 9.16, 17.05, 0.92278, 102, -8.76, 33.54, 0.06743, 103, -20.89, 57.23, 0.00971, 104, -22.83, 85.15, 8e-05, 4, 101, 16.92, 24.92, 0.67857, 102, 2.29, 33.44, 0.26518, 103, -12.07, 50.57, 0.05364, 104, -19.34, 74.66, 0.00261, 4, 101, 30.77, 44.86, 0.15149, 102, 26.26, 37.37, 0.41598, 103, 9.52, 39.46, 0.36834, 104, -7.83, 53.28, 0.06419, 5, 101, 38.12, 67.25, 0.0128, 102, 47.44, 47.68, 0.09539, 103, 32.68, 35.13, 0.48232, 104, 8.79, 36.58, 0.40082, 105, -16.75, 44.54, 0.00867, 5, 101, 36.17, 87.01, 0.00012, 102, 60.28, 62.82, 0.00831, 103, 52.01, 39.65, 0.1289, 104, 27.28, 29.36, 0.68742, 105, -1.65, 31.65, 0.17525, 4, 102, 63.92, 69, 0.00192, 103, 58.61, 42.44, 0.05165, 104, 34.31, 27.92, 0.5868, 105, 4.51, 27.99, 0.35964, 3, 103, 71.37, 52.23, 0.00226, 104, 50.36, 28.75, 0.2128, 105, 19.95, 23.51, 0.78494, 2, 104, 67.65, 22.96, 0.01335, 105, 34.38, 12.36, 0.98665, 2, 104, 72.6, 22.28, 0.00256, 105, 38.83, 10.09, 0.99744, 2, 104, 76.21, 18.64, 8e-05, 105, 41.06, 5.47, 0.99992, 1, 105, 39.61, 1.45, 1, 1, 105, 32.89, -7.21, 1, 1, 105, 32, -7.7, 1, 1, 105, 24.67, -11.25, 1, 2, 104, 52.92, -9.21, 0.00489, 105, 9.91, -13.19, 0.99511, 2, 104, 33.36, -11.71, 0.93717, 105, -9.38, -9.13, 0.06283, 2, 103, 49.48, -7.66, 0.47304, 104, -1.6, -8.19, 0.52696, 1, 103, 30.45, -14.86, 1, 2, 102, 58.29, -10.49, 0.03665, 103, 6.76, -18.06, 0.96335], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 164, "height": 93}}, "toufa3": {"toufa3": {"type": "mesh", "uvs": [0.44808, 0, 0.46584, 0, 0.50367, 0.03249, 0.58773, 0.02986, 0.67445, 0.07943, 0.8081, 0.19582, 0.84581, 0.24943, 0.88897, 0.24698, 0.93674, 0.33261, 0.97999, 0.42785, 1, 0.59978, 1, 0.65913, 0.99617, 0.69602, 0.96829, 0.77317, 0.92338, 0.85189, 0.8375, 0.94465, 0.7357, 0.99043, 0.63809, 1, 0.59492, 1, 0.42409, 0.97419, 0.42404, 0.93081, 0.4156, 0.84806, 0.34335, 0.75668, 0.24249, 0.6896, 0.17644, 0.68979, 0.0202, 0.71633, 0, 0.71438, 0, 0.70117, 0.00319, 0.64016, 0.0267, 0.51333, 0.1348, 0.32623, 0.24675, 0.19509, 0.24943, 0.14558, 0.2871, 0.079, 0.38133, 0.06239, 0.40296, 0.05426], "triangles": [24, 29, 30, 29, 25, 28, 30, 23, 24, 25, 27, 28, 25, 26, 27, 25, 29, 24, 23, 30, 31, 33, 31, 32, 33, 34, 31, 22, 31, 34, 23, 31, 22, 35, 0, 1, 2, 35, 1, 2, 22, 35, 22, 34, 35, 21, 2, 3, 4, 21, 3, 2, 21, 22, 20, 21, 16, 4, 16, 21, 20, 18, 19, 8, 6, 7, 6, 16, 5, 4, 5, 16, 14, 6, 8, 9, 10, 8, 10, 11, 13, 8, 10, 14, 11, 12, 13, 13, 14, 10, 15, 6, 14, 15, 16, 6, 16, 17, 20, 20, 17, 18], "vertices": [4, 111, -12.63, 151.99, 0.05144, 114, 113.86, 98.5, 0.14763, 107, 84.77, -70.69, 0.49874, 108, -18.05, -77.18, 0.30219, 4, 111, -7.67, 150.59, 0.05442, 114, 111.81, 93.78, 0.15468, 107, 81.43, -74.6, 0.50657, 108, -23.13, -78.02, 0.28433, 4, 111, 1.47, 142.59, 0.06967, 114, 102.64, 85.8, 0.18984, 107, 70.32, -79.54, 0.53061, 108, -34.8, -74.65, 0.20989, 4, 111, 25.05, 136.39, 0.10479, 114, 93.32, 63.27, 0.28808, 107, 54.79, -98.33, 0.51586, 108, -58.79, -79.05, 0.09126, 4, 111, 47.1, 121.89, 0.13149, 114, 75.98, 43.38, 0.40867, 107, 32.37, -112.25, 0.42632, 108, -84.9, -75.27, 0.03352, 5, 111, 79.33, 93.35, 0.09699, 113, 64.63, 36.19, 0.01158, 114, 43.36, 15.29, 0.72874, 107, -7.07, -129.51, 0.16117, 108, -126.2, -63.11, 0.00151, 7, 111, 87.52, 82.08, 0.0418, 112, 73.56, 54.31, 0.00083, 113, 57.82, 24.04, 0.01701, 114, 31.08, 8.7, 0.88397, 106, 106.69, -114.55, 2e-05, 107, -20.73, -132.21, 0.05627, 108, -138.39, -56.37, 0.0001, 3, 114, 26.46, -2.94, 1, 106, 106.98, -127.07, 0, 108, -150.68, -58.8, 0, 3, 113, 48.73, -4.11, 0.01519, 114, 8.3, -10.16, 0.98481, 108, -166.59, -47.46, 0, 3, 113, 35.55, -18.9, 0.89841, 114, -10.76, -15.55, 0.10159, 108, -181.47, -34.38, 0, 2, 112, 67.3, -17.4, 0.16074, 113, 9.13, -28.98, 0.83926, 2, 112, 60.73, -24.34, 0.33688, 113, -0.31, -30.48, 0.66312, 2, 112, 55.84, -27.9, 0.45371, 113, -6.35, -30.31, 0.54629, 2, 112, 41.43, -31.37, 0.77375, 113, -19.89, -24.27, 0.22625, 2, 112, 23.26, -31.63, 0.98282, 113, -34.45, -13.4, 0.01718, 2, 111, 54.87, -25.01, 0.00145, 112, -5.09, -25.37, 0.99855, 4, 110, 72.56, -21.03, 0.0014, 111, 24.46, -24.11, 0.70936, 112, -31.61, -10.44, 0.28924, 108, -126.34, 66.54, 0, 4, 110, 44.35, -18.17, 0.54136, 111, -3.21, -17.92, 0.4521, 112, -53.23, 7.89, 0.00654, 108, -98.67, 72.68, 0, 3, 110, 31.99, -16.24, 0.94534, 111, -15.26, -14.53, 0.05466, 108, -86.32, 74.72, 0, 3, 110, -16.32, -4.46, 0.00177, 106, -8.9, 8.79, 0.99823, 108, -36.76, 78.7, 0, 2, 106, -1.91, 8.74, 0.99946, 107, -24.55, 32.05, 0.00054, 4, 106, 11.43, 11.07, 0.93673, 107, -12.83, 25.25, 0.0607, 108, -31.02, 59.06, 0.00246, 109, -16.78, 91.67, 0.00011, 4, 106, 26.33, 31.89, 0.27144, 107, 11.97, 31.6, 0.60276, 108, -7.95, 47.96, 0.10031, 109, -6.36, 68.28, 0.02549, 4, 106, 37.39, 61.04, 0.01206, 107, 39.19, 46.8, 0.28605, 108, 22.67, 42.08, 0.37974, 109, 13.19, 43.99, 0.32214, 4, 106, 37.53, 80.19, 0.00012, 107, 51.62, 61.37, 0.07707, 108, 41.56, 45.23, 0.20452, 109, 29.64, 34.17, 0.71829, 2, 108, 89.19, 87.5, 7e-05, 109, 93.3, 35.61, 0.99993, 1, 109, 75.57, 11.28, 1, 1, 109, 74.48, 9.46, 1, 1, 109, 68.64, 1.51, 1, 1, 109, 52.3, -12.51, 1, 3, 114, 101.87, 202.75, 1e-05, 108, 63.02, -10.55, 0.07203, 109, 9.93, -22.25, 0.92796, 5, 111, -77.34, 137.57, 0.00177, 114, 108.3, 164.57, 0.01653, 107, 98.88, -5.91, 0.05398, 108, 34.44, -36.67, 0.91103, 109, -28.77, -23.69, 0.01669, 5, 111, -74.43, 145.04, 0.0032, 114, 115.3, 160.68, 0.02369, 107, 104.43, -11.68, 0.08832, 108, 34.97, -44.66, 0.88302, 109, -33.53, -30.13, 0.00177, 4, 111, -61.01, 152.39, 0.00666, 114, 120.78, 146.39, 0.03552, 107, 105.47, -26.95, 0.15007, 108, 25.94, -57.02, 0.80775, 4, 111, -33.98, 147.56, 0.02709, 114, 112.35, 120.26, 0.08717, 107, 89.73, -49.45, 0.37014, 108, -0.59, -64.12, 0.5156, 4, 111, -27.59, 147.13, 0.03589, 114, 111.06, 113.98, 0.10866, 107, 86.65, -55.07, 0.42966, 108, -6.56, -66.43, 0.42578], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 290, "height": 161}}, "toufa4": {"toufa4": {"type": "mesh", "uvs": [0.77365, 0, 0.7809, 0, 0.84156, 0.02118, 0.91935, 0.09057, 0.98048, 0.21568, 1, 0.31546, 1, 0.38392, 0.97392, 0.49383, 0.89978, 0.6211, 0.82963, 0.6941, 0.64524, 0.6703, 0.62528, 0.74071, 0.63408, 0.92199, 0.62378, 0.98348, 0.60763, 1, 0.58887, 1, 0.52963, 0.95939, 0.48679, 0.91176, 0.39719, 0.73138, 0.36057, 0.68203, 0.26532, 0.59068, 0.17554, 0.54429, 0, 0.49505, 0, 0.47767, 0.04505, 0.44637, 0.14953, 0.44195, 0.35758, 0.41258, 0.42171, 0.38902, 0.64489, 0.30266, 0.70825, 0.23766, 0.74426, 0.14816, 0.75372, 0.11597, 0.77328, 0.02188], "triangles": [14, 15, 13, 13, 15, 12, 12, 15, 16, 16, 17, 12, 17, 11, 12, 17, 18, 11, 11, 18, 10, 18, 19, 10, 9, 10, 8, 29, 8, 28, 4, 8, 29, 4, 29, 30, 20, 26, 19, 19, 27, 10, 19, 26, 27, 4, 30, 31, 4, 31, 32, 2, 32, 1, 32, 0, 1, 27, 28, 10, 8, 10, 28, 7, 8, 4, 32, 3, 4, 20, 21, 26, 22, 24, 21, 24, 25, 21, 21, 25, 26, 22, 23, 24, 6, 7, 5, 2, 3, 32, 7, 4, 5], "vertices": [1, 17, 168.79, -69.98, 1, 1, 17, 168.67, -71.19, 1, 1, 17, 164.22, -80.99, 1, 1, 17, 151.62, -92.89, 1, 1, 17, 130.2, -101.11, 1, 1, 17, 113.59, -102.78, 1, 1, 17, 102.42, -101.68, 1, 1, 17, 84.91, -95.56, 1, 1, 17, 65.35, -81.13, 1, 1, 17, 54.59, -68.23, 1, 1, 17, 61.49, -37.78, 1, 1, 17, 50.33, -33.32, 1, 1, 17, 20.6, -31.89, 1, 1, 17, 10.73, -29.18, 1, 1, 17, 8.3, -26.22, 1, 1, 17, 8.6, -23.08, 1, 1, 17, 16.2, -13.83, 1, 1, 17, 24.68, -7.43, 1, 1, 17, 55.59, 4.67, 1, 1, 17, 64.25, 10, 1, 1, 17, 80.72, 24.46, 1, 1, 17, 89.76, 38.73, 1, 1, 17, 100.67, 67.3, 1, 1, 17, 103.51, 67.02, 1, 1, 17, 107.88, 58.98, 1, 1, 17, 106.89, 41.44, 1, 1, 17, 108.27, 6.19, 1, 1, 17, 111.07, -4.91, 1, 1, 17, 121.5, -43.61, 1, 1, 17, 131.07, -55.24, 1, 1, 17, 145.09, -62.69, 1, 1, 17, 150.19, -64.79, 1, 1, 17, 165.23, -69.57, 1], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 168, "height": 164}}, "toufa5": {"toufa5": {"type": "mesh", "uvs": [0.45201, 0, 0.57934, 0.0379, 0.91236, 0.26884, 1, 0.44518, 1, 0.63864, 0.9103, 0.80529, 0.79377, 0.92894, 0.62189, 1, 0.4406, 1, 0.43442, 0.97914, 0.63388, 0.92365, 0.69539, 0.81194, 0.71921, 0.69604, 0.65849, 0.52605, 0.55325, 0.41365, 0.00276, 0.15317, 0, 0.12979, 0, 0.084, 0.05562, 0.02583, 0.30379, 0], "triangles": [16, 17, 18, 19, 15, 16, 19, 0, 14, 19, 16, 18, 14, 15, 19, 14, 0, 1, 14, 1, 2, 13, 14, 2, 3, 13, 2, 13, 3, 4, 12, 13, 4, 5, 12, 4, 11, 12, 5, 6, 11, 5, 10, 11, 6, 7, 8, 9, 10, 7, 9, 7, 10, 6], "vertices": [2, 116, -11.44, 10.39, 0.1425, 115, 33.97, 15.43, 0.8575, 2, 116, -0.84, 14.48, 0.70609, 115, 44.41, 10.96, 0.29391, 2, 117, -5.47, 13.21, 0.45447, 116, 37.73, 14.02, 0.54553, 2, 118, -22.11, 7.65, 0.01907, 117, 16.36, 16.01, 0.98093, 2, 118, 0.17, 12.62, 0.96312, 117, 38.71, 11.37, 0.03688, 1, 118, 20.96, 9.73, 1, 2, 119, 1.96, 7.11, 0.95299, 118, 37.29, 3.59, 0.04701, 1, 119, 18.35, 6.77, 1, 1, 119, 30.97, -1.1, 1, 1, 119, 30.1, -3.45, 1, 1, 119, 12.75, -0.36, 1, 2, 119, 1.5, -8.88, 0.26687, 118, 25.57, -7.29, 0.73313, 1, 118, 11.8, -8.37, 1, 3, 118, -6.69, -17.6, 0.18025, 117, 20.01, -13.35, 0.77253, 116, 44, -22.25, 0.04722, 4, 118, -17.75, -28.92, 0.00377, 117, 5.26, -19.09, 0.39382, 116, 28.57, -18.77, 0.57628, 115, 42.27, -33.38, 0.02613, 1, 115, -2.87, -2.65, 1, 1, 115, -3.09, 0.11, 1, 1, 115, -3.09, 5.52, 1, 1, 115, 1.47, 12.38, 1, 1, 115, 21.82, 15.43, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 82, "height": 118}}, "toufa6": {"toufa6": {"type": "mesh", "uvs": [0.06695, 0, 0.09009, 0, 0.13491, 0.06769, 0.2386, 0.14569, 0.2851, 0.17497, 0.73032, 0.32959, 0.94217, 0.49027, 1, 0.64292, 1, 0.72999, 0.93214, 0.88214, 0.84084, 0.97375, 0.75356, 1, 0.5471, 1, 0.52217, 0.94687, 0.40494, 0.90384, 0.59517, 0.82216, 0.64033, 0.70038, 0.53906, 0.51694, 0.38412, 0.43138, 0.20443, 0.37702, 0.10374, 0.32624, 0, 0.16984, 0, 0.06747, 0.03735, 0.01119], "triangles": [2, 21, 22, 1, 23, 0, 3, 21, 2, 20, 3, 4, 1, 2, 23, 23, 2, 22, 3, 20, 21, 19, 20, 4, 18, 19, 4, 5, 18, 4, 17, 18, 5, 17, 5, 6, 16, 17, 6, 16, 6, 7, 16, 7, 8, 9, 16, 8, 15, 16, 9, 13, 14, 15, 10, 15, 9, 11, 13, 15, 11, 12, 13, 15, 10, 11], "vertices": [1, 129, 0.4, 9.89, 1, 1, 129, 1.94, 11.26, 1, 1, 129, 10.24, 7.97, 1, 2, 130, -12.69, 9.07, 0.00056, 129, 23.25, 7.27, 0.99944, 2, 130, -7.32, 8.53, 0.05571, 129, 28.64, 7.46, 0.94429, 2, 131, -4.39, 15.11, 0.45334, 130, 35.72, 15.62, 0.54666, 2, 132, -17.68, 12.91, 0.09795, 131, 21.76, 20.69, 0.90205, 2, 132, 0.14, 18.69, 0.7866, 131, 39.69, 15.27, 0.2134, 3, 133, -22.91, 3.05, 0.00017, 132, 10.41, 19.05, 0.98285, 131, 48.33, 9.7, 0.01698, 2, 133, -6.73, 12.9, 0.46245, 132, 28.56, 13.64, 0.53755, 2, 133, 6.44, 15.95, 0.97697, 132, 39.65, 5.9, 0.02303, 1, 133, 14.38, 13.32, 1, 1, 133, 28.45, 1.49, 1, 1, 133, 26.11, -4.73, 1, 1, 133, 30.83, -15.33, 1, 3, 133, 11.67, -11.81, 0.80478, 132, 22.54, -16.58, 0.19177, 131, 37.96, -26.48, 0.00345, 3, 133, -0.66, -20.23, 0.08101, 132, 8.04, -13.06, 0.61882, 131, 28.06, -15.32, 0.30017, 3, 132, -13.28, -22.83, 0.00053, 131, 4.98, -11.17, 0.68614, 130, 34.01, -12.23, 0.31333, 3, 131, -10.98, -17.3, 0.00138, 130, 16.93, -11.59, 0.99816, 129, 55.39, -9.2, 0.00046, 2, 130, 0.09, -15.24, 0.53932, 129, 39.2, -15.09, 0.46068, 2, 130, -10.69, -15.3, 0.10811, 129, 28.52, -16.61, 0.89189, 1, 129, 9.33, -9.02, 1, 1, 129, 1.28, -0.02, 1, 1, 129, -0.68, 7.15, 1], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 89, "height": 118}}, "toufa7": {"toufa7": {"type": "mesh", "uvs": [0.25069, 0, 0.28125, 0, 0.3061, 0.0412, 0.34769, 0.13459, 0.35669, 0.26299, 0.36642, 0.41262, 0.42046, 0.48541, 0.4745, 0.55819, 0.53769, 0.59422, 0.63955, 0.62987, 0.84862, 0.67601, 0.91677, 0.70603, 0.99983, 0.79743, 1, 0.81072, 1, 0.87656, 0.9787, 0.92066, 0.86508, 0.98337, 0.77482, 1, 0.73891, 1, 0.70659, 0.99647, 0.71177, 0.98618, 0.79682, 0.95261, 0.85205, 0.90837, 0.8405, 0.85259, 0.78428, 0.81856, 0.61988, 0.78524, 0.45548, 0.75193, 0.29261, 0.73132, 0.14753, 0.67803, 0.0419, 0.56136, 0, 0.46347, 0, 0.33464, 0.03106, 0.24289, 0.15775, 0.06793, 0.21553, 0.01037], "triangles": [32, 3, 4, 32, 33, 3, 33, 2, 3, 33, 34, 2, 34, 0, 2, 0, 1, 2, 5, 31, 4, 4, 31, 32, 31, 5, 30, 7, 29, 6, 7, 28, 29, 29, 5, 6, 29, 30, 5, 26, 8, 9, 26, 27, 8, 27, 7, 8, 27, 28, 7, 24, 25, 10, 25, 9, 10, 25, 26, 9, 13, 23, 12, 23, 24, 12, 24, 11, 12, 24, 10, 11, 16, 22, 15, 15, 22, 14, 22, 23, 14, 23, 13, 14, 16, 17, 21, 19, 20, 18, 21, 17, 20, 17, 18, 20, 21, 22, 16], "vertices": [1, 134, 0.76, 3.42, 1, 1, 134, 0.63, 6.93, 1, 1, 134, 9.09, 10.1, 1, 1, 134, 28.33, 15.58, 1, 3, 136, -24.66, 28.01, 5e-05, 135, 4.64, 17.6, 0.73387, 134, 54.99, 17.58, 0.26608, 3, 137, -17.14, 37.65, 0.00132, 136, 5.24, 19.31, 0.58076, 135, 35.69, 19.92, 0.41792, 3, 137, -3.51, 28.58, 0.09526, 136, 21.56, 20.46, 0.87928, 135, 50.58, 26.72, 0.02545, 3, 138, -14.84, 26.91, 0.00167, 137, 10.11, 19.51, 0.60488, 136, 37.89, 21.62, 0.39345, 3, 138, -6.14, 21.13, 0.07718, 137, 20.33, 17.36, 0.84574, 136, 47.28, 26.17, 0.07708, 3, 138, 6.89, 16.39, 0.60415, 137, 34.18, 17.78, 0.39522, 136, 58, 34.97, 0.00063, 2, 139, -9.03, 8.13, 0.17562, 138, 32.43, 12.14, 0.82438, 2, 139, 0.29, 11.83, 0.75672, 138, 41.41, 7.71, 0.24328, 2, 140, -8.4, 6.91, 0.12359, 139, 21.53, 10.69, 0.87641, 2, 140, -5.68, 7.43, 0.25541, 139, 23.94, 9.34, 0.74459, 1, 140, 7.79, 9.9, 1, 2, 141, -10.05, 2.7, 0.00638, 140, 17.26, 9.14, 0.99362, 1, 141, 8.03, 6.47, 1, 1, 141, 18.61, 3.69, 1, 1, 141, 22.06, 1.44, 1, 1, 141, 24.78, -1.21, 1, 1, 141, 23.11, -2.68, 1, 2, 141, 11.1, -3.18, 0.99995, 140, 27.56, -10.23, 5e-05, 3, 141, 0.75, -7.41, 0.18649, 140, 17.37, -5.65, 0.81214, 139, 33.13, -15.51, 0.00137, 3, 140, 6.19, -9.04, 0.60815, 139, 22.4, -10.91, 0.37236, 138, 39.35, -23.94, 0.0195, 3, 140, 0.4, -16.68, 0.07941, 139, 13.05, -13.01, 0.67118, 138, 31.52, -18.4, 0.24941, 3, 139, -2.35, -25.99, 0.04887, 138, 11.57, -15.67, 0.93557, 137, 50.39, -10.27, 0.01556, 2, 138, -8.38, -12.93, 0.14999, 137, 30.85, -15.11, 0.85001, 2, 137, 12.93, -22.04, 0.95754, 136, 65.52, -9.54, 0.04246, 2, 137, -7.1, -22.19, 0.44767, 136, 49.76, -21.9, 0.55233, 3, 137, -30.75, -8.88, 0.00061, 136, 22.91, -25.83, 0.9976, 135, 68.06, -16.17, 0.00179, 2, 136, 2.07, -24.02, 0.7413, 135, 47.9, -21.77, 0.2587, 3, 136, -23.38, -15.61, 0.01928, 135, 21.12, -22.81, 0.9748, 134, 71.36, -22.87, 0.00593, 2, 135, 1.91, -19.98, 0.66782, 134, 52.16, -19.99, 0.33218, 1, 134, 15.27, -6.75, 1, 1, 134, 3.06, -0.54, 1], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 6, 8, 8, 10, 10, 12, 12, 14, 48, 50, 50, 52, 48, 46, 46, 44, 44, 42, 42, 40], "width": 115, "height": 208}}, "toufa8": {"toufa8": {"type": "mesh", "uvs": [0.14794, 0, 0.21057, 0, 0.29183, 0.01161, 0.36287, 0.03415, 0.50801, 0.09557, 0.72009, 0.22599, 0.77365, 0.25644, 0.99958, 0.35255, 1, 0.40247, 1, 0.49108, 0.9501, 0.54301, 0.9002, 0.59493, 0.8123, 0.64192, 0.7244, 0.68891, 0.60049, 0.74347, 0.47658, 0.79802, 0.45022, 0.84484, 0.47618, 0.89394, 0.57254, 0.92528, 0.66891, 0.95663, 0.79681, 0.97597, 0.92687, 0.96129, 0.93969, 0.96775, 0.90891, 0.98053, 0.8363, 0.99314, 0.63723, 1, 0.4844, 1, 0.3253, 0.97808, 0.16321, 0.92597, 0.04036, 0.85614, 0, 0.78874, 0, 0.68315, 0, 0.57756, 0.04446, 0.44371, 0.08893, 0.30986, 0.15672, 0.23115, 0.22452, 0.15244, 0.24611, 0.1409, 0.14254, 0.05264, 0.12376, 0.0151], "triangles": [38, 3, 37, 1, 39, 0, 37, 3, 4, 3, 38, 2, 2, 38, 1, 1, 38, 39, 6, 34, 5, 34, 35, 5, 35, 36, 5, 36, 37, 5, 37, 4, 5, 6, 8, 34, 11, 12, 33, 10, 33, 34, 10, 11, 33, 34, 8, 10, 9, 10, 8, 8, 6, 7, 13, 14, 32, 12, 13, 32, 12, 32, 33, 32, 14, 31, 29, 30, 16, 16, 30, 15, 14, 15, 31, 15, 30, 31, 27, 28, 17, 28, 16, 17, 28, 29, 16, 19, 25, 18, 25, 26, 18, 18, 26, 17, 26, 27, 17, 25, 20, 24, 25, 19, 20, 24, 20, 23, 23, 21, 22, 23, 20, 21], "vertices": [1, 120, 3.85, 0.97, 1, 1, 120, 8.61, 7.11, 1, 1, 120, 17.46, 13, 1, 1, 120, 28.06, 15.93, 1, 2, 121, -8.13, 17.36, 0.39784, 120, 53.26, 19.17, 0.60216, 2, 122, -43.95, 16.91, 0.03636, 121, 34.94, 34.29, 0.96364, 2, 122, -36.71, 25.33, 0.13508, 121, 45.11, 38.74, 0.86492, 4, 124, -111.49, 75.44, 3e-05, 123, -78.54, 45.1, 0.00159, 122, -15.39, 58.76, 0.63741, 121, 78.82, 59.62, 0.36097, 4, 124, -97.07, 77.6, 0.00147, 123, -65.15, 50.87, 0.01207, 122, -1.17, 61.97, 0.74019, 121, 93.02, 56.36, 0.24627, 4, 124, -71.47, 81.35, 0.0161, 123, -41.35, 61.03, 0.07853, 122, 24.09, 67.59, 0.83329, 121, 118.22, 50.46, 0.07208, 4, 124, -55.57, 77.42, 0.04072, 123, -24.98, 61.29, 0.16246, 122, 40.23, 64.84, 0.77375, 121, 131.57, 40.99, 0.02307, 4, 124, -39.68, 73.49, 0.08728, 123, -8.61, 61.55, 0.28105, 122, 56.37, 62.09, 0.62875, 121, 144.93, 31.51, 0.00292, 3, 124, -24.52, 64.69, 0.17865, 123, 8.29, 56.91, 0.41107, 122, 72.13, 54.42, 0.41028, 4, 125, 3.39, 77.03, 0.00061, 124, -9.36, 55.9, 0.34657, 123, 25.19, 52.28, 0.46337, 122, 87.89, 46.76, 0.18945, 4, 125, 6.59, 55.13, 0.02492, 124, 8.62, 43, 0.66056, 123, 45.87, 44.4, 0.28074, 122, 106.78, 35.22, 0.03378, 5, 126, -5.79, 41.53, 0.00019, 125, 9.78, 33.23, 0.21967, 124, 26.61, 30.1, 0.74956, 123, 66.56, 36.52, 0.03023, 122, 125.66, 23.67, 0.00035, 4, 126, -3.55, 27.66, 0.03698, 125, 18.6, 22.28, 0.6077, 124, 40.61, 28.85, 0.35515, 123, 80.41, 38.88, 0.00017, 3, 126, 4.93, 15.66, 0.44786, 125, 31.91, 16.05, 0.51212, 124, 54.33, 34.11, 0.04003, 3, 126, 19.48, 11.81, 0.95388, 125, 46.46, 19.9, 0.0457, 124, 61.65, 47.26, 0.00042, 2, 127, 2.94, 7.4, 0.55992, 126, 34.04, 7.95, 0.44008, 1, 127, 18.7, 1.46, 1, 2, 127, 34.9, 5.46, 1, 126, 64.08, 19, 0, 2, 127, 36.45, 3.55, 1, 126, 66.28, 17.87, 0, 1, 127, 32.57, -0.11, 1, 1, 127, 23.5, -3.64, 1, 2, 127, -1.21, -5.2, 0.41256, 126, 35.28, -5.25, 0.58744, 1, 126, 17.79, -12.54, 1, 2, 126, -2.89, -14.22, 0.45007, 125, 39.89, -13.78, 0.54993, 2, 125, 15.56, -20.37, 0.98294, 124, 69.2, -2.94, 0.01706, 3, 125, -9.89, -19.94, 0.28488, 124, 51.23, -20.96, 0.71494, 123, 103.4, -6.57, 0.00018, 3, 125, -28.52, -11.86, 0.0021, 124, 32.48, -28.76, 0.95439, 123, 87.26, -18.9, 0.04351, 3, 124, 1.98, -33.23, 0.44853, 123, 58.91, -31, 0.54938, 122, 105.73, -41.29, 0.0021, 4, 124, -28.53, -37.69, 0.01828, 123, 30.55, -43.1, 0.84999, 122, 75.64, -47.98, 0.12832, 121, 114.57, -76.03, 0.00342, 3, 123, -7.56, -53.38, 0.28197, 122, 36.29, -51.08, 0.57799, 121, 77.77, -61.76, 0.14004, 3, 123, -45.67, -63.65, 0.01602, 122, -3.06, -54.17, 0.32239, 121, 40.97, -47.49, 0.66159, 4, 123, -70.11, -64.94, 0.0001, 122, -27.32, -50.95, 0.08124, 121, 20.5, -34.07, 0.89701, 120, 57.87, -39.52, 0.02165, 3, 122, -51.58, -47.73, 0.00365, 121, 0.04, -20.65, 0.57492, 120, 44.85, -18.79, 0.42143, 3, 122, -55.45, -45.85, 0.00103, 121, -2.63, -17.28, 0.38905, 120, 43.83, -14.61, 0.60992, 1, 120, 15.59, -8.97, 1, 1, 120, 5.5, -4.1, 1], "hull": 40, "edges": [0, 78, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 72, 74, 74, 76, 76, 78, 64, 66, 66, 68, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 22, 24, 24, 26, 18, 20, 20, 22, 68, 70, 70, 72, 60, 62, 62, 64], "width": 124, "height": 292}}, "toufa9": {"toufa9": {"type": "mesh", "uvs": [0, 0.02639, 0.01278, 0, 0.02885, 0, 0.06458, 0.02525, 0.1712, 0.08756, 0.29306, 0.19503, 0.3259, 0.27614, 0.35875, 0.35725, 0.40797, 0.40813, 0.4572, 0.45902, 0.59086, 0.50227, 0.6708, 0.50927, 0.76095, 0.47168, 0.82178, 0.44632, 0.84167, 0.44274, 0.8254, 0.47116, 0.79047, 0.52135, 0.86135, 0.55199, 0.93223, 0.58263, 0.96603, 0.63326, 0.99984, 0.68388, 1, 0.69508, 1, 0.74916, 0.96383, 0.8125, 0.86797, 0.89612, 0.74594, 0.91849, 0.63647, 1, 0.62773, 1, 0.62608, 0.95977, 0.68076, 0.87266, 0.73543, 0.78555, 0.70855, 0.70335, 0.64578, 0.65484, 0.51417, 0.66764, 0.38257, 0.68044, 0.20728, 0.63919, 0.11997, 0.57771, 0.05193, 0.47017, 0.02597, 0.3167, 0, 0.16324], "triangles": [5, 39, 4, 4, 39, 3, 39, 5, 38, 2, 3, 0, 0, 1, 2, 3, 39, 0, 7, 38, 6, 7, 37, 38, 38, 5, 6, 34, 35, 9, 35, 8, 9, 35, 36, 8, 36, 7, 8, 36, 37, 7, 33, 34, 9, 33, 10, 32, 33, 9, 10, 32, 10, 11, 31, 32, 16, 31, 16, 17, 16, 11, 12, 11, 16, 32, 16, 12, 15, 12, 13, 15, 15, 13, 14, 23, 30, 22, 22, 30, 21, 21, 30, 19, 18, 19, 17, 17, 19, 31, 31, 19, 30, 21, 19, 20, 24, 25, 30, 25, 29, 30, 24, 30, 23, 25, 26, 28, 26, 27, 28, 28, 29, 25], "vertices": [1, 143, 0.95, -3.86, 1, 1, 143, -2.22, -0.59, 1, 1, 143, -1.35, 1.49, 1, 1, 143, 4.29, 4.55, 1, 2, 144, -18.19, 16.29, 0.02396, 143, 19.2, 14.47, 0.97604, 2, 144, 4.94, 23.23, 0.69045, 143, 41.56, 23.59, 0.30955, 3, 145, -4.07, 26.13, 0.00625, 144, 18.47, 21.17, 0.94274, 143, 55.23, 22.84, 0.05101, 3, 145, 7.04, 18.12, 0.28631, 144, 32.01, 19.12, 0.71356, 143, 68.9, 22.09, 0.00013, 3, 146, -6.52, 20.8, 0.01682, 145, 17.3, 15.36, 0.74951, 144, 42.4, 21.36, 0.23367, 3, 146, 1.28, 13.57, 0.31377, 145, 27.56, 12.6, 0.6617, 144, 52.79, 23.6, 0.02453, 2, 146, 20.67, 8.95, 0.99898, 145, 46.88, 17.55, 0.00102, 2, 147, -5.39, 7.65, 0.33689, 146, 31.92, 9.16, 0.66311, 3, 148, -29.07, -15.93, 0.00823, 147, 2.56, 19.13, 0.96905, 146, 43.75, 16.58, 0.02271, 2, 148, -33.56, -7.65, 0.00022, 147, 7.93, 26.88, 0.99978, 2, 147, 10.06, 28.76, 1, 146, 54.43, 22.48, 0, 2, 148, -29.65, -6.93, 0.00222, 147, 10.34, 23.71, 0.99778, 3, 148, -21.41, -11.38, 0.06791, 147, 10.09, 14.35, 0.92835, 146, 48.78, 9.22, 0.00374, 2, 148, -17.09, -1.2, 0.54247, 147, 21.12, 15.09, 0.45753, 2, 148, -12.76, 8.97, 0.9818, 147, 32.15, 15.82, 0.0182, 1, 148, -4.98, 14.13, 1, 1, 148, 2.8, 19.3, 1, 1, 148, 4.57, 19.42, 1, 1, 148, 13.16, 19.89, 1, 2, 149, -11.33, 10.44, 0.07461, 148, 23.49, 15.38, 0.92539, 2, 149, 7.55, 11.08, 0.98511, 148, 37.5, 2.7, 0.01489, 1, 149, 22.51, 2.08, 1, 2, 150, 14.98, 1.72, 1, 149, 42.56, 1.18, 0, 2, 150, 15.89, 0.91, 1, 149, 43.45, 0.35, 0, 2, 150, 11.78, -4, 1, 149, 39.28, -4.5, 0, 4, 150, -3.17, -9.18, 0.909, 149, 24.25, -9.46, 0.0853, 148, 35.2, -23.67, 0.00325, 147, 24.67, -41.71, 0.00245, 4, 150, -18.13, -14.35, 0.09275, 149, 9.23, -14.43, 0.46331, 148, 20.96, -16.78, 0.26747, 147, 24.38, -25.89, 0.17647, 5, 150, -24.07, -26.59, 0.00534, 149, 3.11, -26.58, 0.05992, 148, 8.11, -21.25, 0.13356, 147, 14.6, -16.45, 0.76978, 146, 40.79, -20.87, 0.0314, 5, 150, -22.7, -38.2, 4e-05, 149, 4.32, -38.21, 0.00263, 148, 0.89, -30.45, 0.00295, 147, 3.13, -14.15, 0.50438, 146, 31.16, -14.24, 0.49, 3, 147, -11.82, -25.11, 0.00419, 146, 13.1, -18.43, 0.96312, 145, 52.94, -10.19, 0.03269, 2, 146, -4.96, -22.62, 0.45441, 145, 38.92, -22.32, 0.54559, 2, 146, -30.1, -18.99, 0.01037, 145, 14.99, -30.83, 0.98963, 1, 145, -0.62, -29.71, 1, 2, 145, -18.16, -21.03, 0.8167, 144, 27.5, -27.22, 0.1833, 3, 145, -34.99, -2.99, 0.12506, 144, 4.28, -18.87, 0.70256, 143, 44.92, -18.37, 0.17238, 2, 144, -18.93, -10.52, 0.00238, 143, 21.02, -12.28, 0.99762], "hull": 40, "edges": [0, 78, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 68, 70, 70, 72, 72, 74, 64, 66, 66, 68, 64, 62, 62, 60, 56, 58, 58, 60, 32, 34, 34, 36, 36, 38, 38, 40, 74, 76, 76, 78, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26], "width": 140, "height": 159}}, "toufa10": {"toufa10": {"type": "mesh", "uvs": [0.19653, 0, 0.19648, 0.02199, 0.23813, 0.11368, 0.2573, 0.20421, 0.27647, 0.29475, 0.30331, 0.42152, 0.35128, 0.52821, 0.44378, 0.59491, 0.52036, 0.61297, 0.60977, 0.58226, 0.68757, 0.55554, 0.76538, 0.52882, 0.88749, 0.55563, 0.95932, 0.63829, 1, 0.75147, 1, 0.77903, 0.98873, 0.83064, 0.96543, 0.88478, 0.91623, 0.94239, 0.86703, 1, 0.80228, 1, 0.79868, 0.99993, 0.76686, 0.97228, 0.73504, 0.94464, 0.71695, 0.90036, 0.73611, 0.88845, 0.78788, 0.92106, 0.83498, 0.88768, 0.85705, 0.81323, 0.81571, 0.74924, 0.73194, 0.73732, 0.62769, 0.80151, 0.52343, 0.86569, 0.38068, 0.89915, 0.28279, 0.89582, 0.17165, 0.84756, 0.09649, 0.76126, 0.0615, 0.69875, 0.01846, 0.53396, 0, 0.41311, 0, 0.32446, 0.0032, 0.22378, 0.04271, 0.1333, 0.09739, 0.04708, 0.14626, 0], "triangles": [4, 42, 3, 3, 42, 2, 41, 42, 40, 42, 43, 2, 43, 1, 2, 43, 44, 1, 1, 44, 0, 6, 38, 5, 5, 38, 4, 4, 39, 42, 4, 38, 39, 42, 39, 40, 34, 35, 6, 35, 36, 6, 36, 37, 6, 38, 6, 37, 32, 33, 7, 32, 7, 8, 7, 33, 34, 34, 6, 7, 32, 8, 31, 8, 9, 31, 31, 9, 30, 9, 10, 30, 29, 30, 11, 30, 10, 11, 13, 29, 12, 12, 29, 11, 16, 28, 15, 15, 28, 14, 14, 28, 13, 28, 29, 13, 18, 19, 27, 17, 18, 28, 18, 27, 28, 17, 28, 16, 27, 19, 20, 22, 26, 21, 27, 20, 26, 20, 21, 26, 22, 23, 26, 23, 25, 26, 23, 24, 25], "vertices": [1, 151, 10.86, 16.22, 1, 1, 151, 15.08, 15.59, 1, 2, 152, -27.34, 21.74, 0.00192, 151, 34.03, 22.26, 0.99808, 2, 152, -9.37, 23.8, 0.20457, 151, 52.03, 23.97, 0.79543, 3, 153, -21.35, 48.73, 0.00602, 152, 8.59, 25.85, 0.76424, 151, 70.03, 25.68, 0.22974, 4, 154, -4.18, 59.74, 0.01032, 153, 0.41, 35.79, 0.20376, 152, 33.74, 28.73, 0.78526, 151, 95.23, 28.09, 0.00066, 3, 154, 5.18, 38.38, 0.19037, 153, 22.71, 28.96, 0.61331, 152, 55.62, 36.8, 0.19632, 4, 155, -17.13, 29.73, 0.04534, 154, 25.01, 24.11, 0.76602, 153, 46.46, 34.68, 0.18247, 152, 71.06, 55.73, 0.00617, 3, 155, -2.48, 20.13, 0.46733, 154, 41.89, 19.49, 0.52256, 153, 60.97, 44.47, 0.01011, 2, 155, 18.33, 18.28, 0.99747, 154, 62.27, 24.12, 0.00253, 2, 156, -20.51, 4.82, 0.00706, 155, 36.45, 16.66, 0.99294, 2, 156, -5.43, 14.97, 0.50126, 155, 54.56, 15.05, 0.49874, 2, 157, -21.14, -1.02, 0.00198, 156, 22.23, 18.16, 0.99802, 2, 157, -2.89, 12.51, 0.8878, 156, 42.37, 7.65, 0.1122, 2, 158, -17.2, 9.93, 0.03996, 157, 20.17, 18.27, 0.96004, 2, 158, -12.6, 12.64, 0.16292, 157, 25.46, 17.47, 0.83708, 2, 158, -2.69, 15.55, 0.71276, 157, 34.99, 13.49, 0.28724, 1, 158, 9.01, 16.39, 1, 3, 160, -20.32, -26.94, 0, 159, -12.12, 5.39, 0.07433, 158, 24.23, 12.58, 0.92567, 3, 160, -21.85, -11.32, 7e-05, 159, 0.98, 14.04, 0.88602, 158, 39.46, 8.76, 0.11391, 2, 160, -12.57, -0.18, 0.00021, 159, 15.17, 11.03, 0.99979, 1, 159, 15.96, 10.85, 1, 2, 160, -3.35, 2.48, 0.21248, 159, 21.82, 4.12, 0.78752, 1, 160, 5.33, 4.52, 1, 2, 160, 14.52, 2.13, 0.99993, 159, 29.86, -11.85, 7e-05, 1, 160, 13.55, -2.64, 1, 3, 160, 1.26, -7.5, 0.17539, 159, 15.15, -4.62, 0.81363, 158, 35.27, -14.29, 0.01098, 4, 159, 3.48, -8.77, 0.39464, 158, 24.34, -8.49, 0.60075, 157, 40.82, -22.21, 0.0045, 156, 30.22, -46.83, 0.00011, 4, 158, 9.38, -11.57, 0.56247, 157, 27.27, -15.18, 0.36838, 156, 30.63, -31.57, 0.06296, 155, 53.27, -43.81, 0.00619, 4, 158, 3.39, -25.85, 0.02521, 157, 13.62, -22.49, 0.37773, 156, 18.09, -22.48, 0.45858, 155, 49.25, -28.86, 0.13847, 3, 157, 8.55, -40.71, 0.03525, 156, -0.51, -25.87, 0.17512, 155, 32.66, -19.78, 0.78963, 2, 155, 6.36, -22.73, 0.71051, 154, 63.48, -18.59, 0.28949, 2, 155, -19.94, -25.68, 0.03832, 154, 39.36, -29.48, 0.96168, 2, 154, 7.03, -33.86, 0.87639, 153, 78.67, -16.76, 0.12361, 2, 154, -14.81, -31.77, 0.44586, 153, 62.85, -31.95, 0.55414, 3, 154, -39.04, -20.8, 0.0593, 153, 38.73, -43.16, 0.94038, 152, 112.04, -10.89, 0.00032, 3, 154, -54.74, -2.99, 8e-05, 153, 14.98, -43.44, 0.93102, 152, 93.32, -25.5, 0.06889, 2, 153, 0.84, -40.54, 0.78038, 152, 80.31, -31.75, 0.21962, 3, 153, -28.73, -25.02, 0.12608, 152, 47.38, -37.31, 0.87129, 151, 107.61, -38.2, 0.00264, 3, 153, -48.35, -11.54, 0.00093, 152, 23.6, -38.47, 0.90589, 151, 83.81, -38.91, 0.09318, 2, 152, 6.54, -36.31, 0.65828, 151, 66.8, -36.43, 0.34172, 2, 152, -12.75, -33.15, 0.24808, 151, 47.57, -32.9, 0.75192, 2, 152, -29.05, -22.17, 0.0203, 151, 31.48, -21.61, 0.9797, 1, 151, 16.7, -7.07, 1, 1, 151, 9.24, 5.08, 1], "hull": 45, "edges": [0, 88, 0, 2, 2, 4, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 60, 62, 62, 64, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 48, 50, 48, 46, 42, 44, 44, 46, 34, 36, 36, 38, 16, 18, 18, 20, 20, 22, 8, 10, 4, 6, 6, 8], "width": 224, "height": 194}}, "toufa11": {"toufa11": {"type": "mesh", "uvs": [0.12873, 0, 0.24194, 0.04553, 0.34657, 0.15401, 0.4512, 0.26248, 0.53555, 0.34994, 0.59233, 0.37793, 0.66773, 0.37794, 0.74312, 0.37796, 0.85293, 0.42656, 0.91536, 0.49994, 0.92859, 0.55906, 0.94181, 0.61819, 0.90421, 0.67102, 0.85536, 0.73965, 0.81566, 0.79542, 0.83262, 0.85503, 0.87155, 0.90361, 0.92886, 0.90664, 0.97061, 0.87447, 0.97168, 0.83003, 0.9822, 0.8223, 1, 0.84283, 1, 0.90049, 0.96546, 0.94971, 0.87741, 1, 0.77434, 0.99999, 0.71138, 0.97891, 0.67339, 0.94273, 0.64125, 0.88162, 0.61749, 0.78169, 0.59448, 0.73191, 0.54006, 0.66651, 0.48097, 0.64791, 0.38504, 0.64157, 0.44153, 0.6076, 0.28585, 0.55765, 0.18298, 0.48677, 0.09268, 0.39047, 0.03727, 0.28739, 0.00405, 0.13725, 0.00424, 0.04859, 0.05559, 0], "triangles": [2, 38, 1, 1, 38, 0, 40, 41, 0, 0, 38, 39, 40, 0, 39, 36, 37, 3, 38, 2, 37, 37, 2, 3, 35, 3, 4, 35, 36, 3, 7, 31, 6, 6, 31, 5, 5, 31, 34, 33, 34, 32, 31, 32, 34, 34, 4, 5, 34, 35, 4, 12, 31, 8, 8, 31, 7, 12, 10, 11, 10, 8, 9, 10, 12, 8, 28, 29, 14, 13, 14, 30, 14, 29, 30, 13, 30, 12, 30, 31, 12, 25, 26, 15, 26, 27, 15, 27, 28, 15, 28, 14, 15, 25, 16, 24, 24, 16, 17, 16, 25, 15, 24, 17, 23, 22, 17, 18, 22, 23, 17, 18, 21, 22, 18, 19, 21, 19, 20, 21], "vertices": [2, 162, -48.61, 25.49, 0.00508, 161, 8.17, 17.52, 0.99492, 2, 162, -22.66, 37.65, 0.18068, 161, 31.83, 33.69, 0.81932, 3, 163, -23.51, 42.56, 0.06249, 162, 13.48, 37.99, 0.78786, 161, 67.44, 39.83, 0.14964, 3, 164, -10.76, 45.91, 0.06182, 163, 12.17, 36.8, 0.6008, 162, 49.62, 38.32, 0.33737, 3, 164, 13.21, 29.33, 0.61265, 163, 40.94, 32.16, 0.37257, 162, 78.76, 38.6, 0.01478, 4, 165, -36.23, -10.59, 0.0012, 164, 27.64, 25.57, 0.92691, 163, 55.58, 34.98, 0.07184, 162, 92.72, 43.85, 5e-05, 3, 165, -30.75, 6.1, 0.11778, 164, 44.77, 29.49, 0.88084, 163, 69.35, 45.9, 0.00138, 2, 165, -25.27, 22.79, 0.44252, 164, 61.89, 33.41, 0.55748, 3, 166, -56.55, 24.61, 0.0079, 165, -5.94, 43.38, 0.88901, 164, 89.5, 27.47, 0.10309, 3, 166, -39.28, 40.09, 0.08284, 165, 15.75, 51.57, 0.91277, 164, 107.71, 13.13, 0.00439, 2, 166, -24.92, 43.93, 0.19431, 165, 30.53, 49.96, 0.80569, 2, 166, -10.56, 47.77, 0.33254, 165, 45.31, 48.36, 0.66746, 3, 167, -11.53, 55.12, 0.00051, 166, 2.88, 39.7, 0.52905, 165, 54.93, 35.98, 0.47043, 4, 168, 5.07, 46.83, 0.00056, 167, -2.71, 36.77, 0.0416, 166, 20.34, 29.22, 0.8549, 165, 67.42, 19.9, 0.10295, 4, 168, -0.37, 31.2, 0.04348, 167, 4.46, 21.86, 0.33028, 166, 34.53, 20.71, 0.62423, 165, 77.57, 6.83, 0.00201, 3, 168, 7.2, 18.03, 0.41643, 167, 19.11, 17.86, 0.50248, 166, 48.96, 25.42, 0.08109, 4, 169, 0.14, 14.74, 0.07995, 168, 19.02, 8.8, 0.88923, 167, 34.01, 19.65, 0.03043, 166, 60.42, 35.11, 0.00039, 2, 169, 10.03, 5.74, 0.91411, 168, 32.12, 11.48, 0.08589, 2, 170, 4.06, 4.11, 0.7508, 169, 22.57, 5.74, 0.2492, 2, 170, 15, 3.86, 0.99982, 169, 29.66, 14.07, 0.00018, 2, 170, 16.9, 1.41, 0.99991, 169, 32.76, 14, 9e-05, 1, 170, 11.85, -2.74, 1, 2, 170, -2.34, -2.74, 0.29447, 169, 23.85, -3.54, 0.70553, 1, 169, 9.97, -7.86, 1, 2, 169, -13.76, -4.53, 0.00351, 168, 26.39, -13.78, 0.99649, 2, 168, 3.18, -19.91, 0.76511, 167, 43.03, -11.88, 0.23489, 2, 168, -12.33, -18.65, 0.24785, 167, 31.14, -21.92, 0.75215, 3, 168, -23.16, -12.3, 0.03806, 167, 18.99, -25.06, 0.95471, 166, 72.46, -10.49, 0.00723, 3, 167, 2.23, -23.93, 0.7773, 166, 57.84, -18.76, 0.22265, 164, 66.43, -92.66, 6e-05, 4, 167, -21.78, -16.29, 0.05223, 166, 33.58, -25.58, 0.88392, 165, 59.97, -35.98, 0.0283, 164, 55.54, -69.93, 0.03556, 4, 167, -35.06, -14.73, 0.00012, 166, 21.63, -31.58, 0.73062, 165, 46.66, -37.26, 0.13062, 164, 47.58, -59.2, 0.13865, 4, 166, 6.23, -45.09, 0.27755, 165, 27.42, -44.29, 0.21366, 164, 31.63, -46.35, 0.50645, 163, 90.15, -28.21, 0.00234, 4, 166, 2.39, -59.08, 0.1087, 165, 18.78, -55.95, 0.11839, 164, 17.19, -44.96, 0.73529, 163, 76.52, -33.18, 0.03762, 4, 166, 2, -81.48, 0.06295, 165, 10.33, -76.7, 0.07297, 164, -4.95, -48.43, 0.80099, 163, 58.04, -45.84, 0.06309, 4, 166, -7.03, -68.77, 0.05584, 165, 6.49, -61.59, 0.06608, 164, 6.01, -37.35, 0.75345, 163, 63.16, -31.12, 0.12463, 5, 166, -17.4, -105.64, 0.00026, 165, -16.49, -92.22, 0.00013, 164, -32.09, -33.47, 0.11396, 163, 27.11, -44.03, 0.79162, 162, 77.97, -38.83, 0.09403, 3, 164, -59.35, -21.83, 0.00152, 163, -2.51, -45.26, 0.5074, 162, 48.98, -45.04, 0.49108, 3, 163, -33.71, -39.78, 0.08558, 162, 17.3, -44.89, 0.84106, 161, 84.53, -41.36, 0.07337, 3, 163, -59.59, -27.93, 0.00063, 162, -10.2, -37.58, 0.47988, 161, 56.21, -38.55, 0.51949, 2, 162, -42.86, -18.68, 0.00329, 161, 20.93, -25.15, 0.99671, 1, 161, 2.59, -13.35, 1, 1, 161, -1.02, 3.17, 1], "hull": 42, "edges": [0, 82, 0, 2, 8, 10, 14, 16, 16, 18, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 48, 50, 28, 30, 30, 32, 32, 34, 34, 36, 38, 40, 36, 38, 26, 28, 22, 24, 24, 26, 18, 20, 20, 22, 10, 12, 12, 14, 6, 8, 2, 4, 4, 6], "width": 233, "height": 246}}, "toufa12": {"toufa12": {"type": "mesh", "uvs": [0.13897, 0, 0.22442, 0, 0.30485, 0.04784, 0.37349, 0.10399, 0.37334, 0.21985, 0.37975, 0.27253, 0.39645, 0.34302, 0.42119, 0.40803, 0.49816, 0.51277, 0.55855, 0.55479, 0.64086, 0.576, 0.72724, 0.56786, 0.86401, 0.52842, 0.93886, 0.57387, 0.96436, 0.59849, 1, 0.66232, 1, 0.73259, 0.99243, 0.83565, 0.96236, 0.89149, 0.93229, 0.94733, 0.88387, 0.98959, 0.83864, 1, 0.80265, 1, 0.66694, 0.98305, 0.62464, 0.96152, 0.65148, 0.96028, 0.73254, 0.93176, 0.76698, 0.8939, 0.77412, 0.83696, 0.75013, 0.78364, 0.64919, 0.74134, 0.57326, 0.73639, 0.38251, 0.69189, 0.26324, 0.64352, 0.13073, 0.55554, 0.04567, 0.46352, 0.01056, 0.32634, 0, 0.23414, 0, 0.15954, 0.05432, 0.0253, 0.10742, 0.00012], "triangles": [4, 36, 2, 2, 38, 0, 0, 38, 39, 39, 40, 0, 0, 1, 2, 2, 36, 37, 2, 37, 38, 4, 2, 3, 6, 35, 5, 4, 5, 36, 35, 6, 34, 35, 36, 5, 32, 33, 8, 33, 7, 8, 33, 34, 7, 34, 6, 7, 31, 10, 30, 32, 9, 31, 31, 9, 10, 32, 8, 9, 30, 11, 29, 29, 13, 14, 12, 29, 11, 12, 13, 29, 30, 10, 11, 17, 28, 16, 16, 28, 29, 16, 29, 15, 29, 14, 15, 20, 27, 19, 19, 27, 18, 27, 28, 18, 18, 28, 17, 20, 21, 27, 23, 26, 22, 27, 21, 22, 22, 26, 27, 24, 25, 23, 23, 25, 26], "vertices": [1, 171, 2.52, -23.81, 1, 1, 171, 0.44, 0.37, 1, 2, 172, -52.44, 39.64, 0.00561, 171, 13.21, 24.39, 0.99439, 2, 172, -31.85, 55.68, 0.06676, 171, 28.83, 45.3, 0.93324, 3, 173, -16.22, 76.42, 0.02868, 172, 3.35, 49.18, 0.42585, 171, 64.5, 48.32, 0.54546, 3, 173, -4.24, 65.25, 0.10615, 172, 19.69, 48.03, 0.63192, 171, 80.57, 51.53, 0.26193, 3, 173, 13.53, 51.79, 0.37137, 172, 41.97, 48.77, 0.57556, 171, 101.86, 58.12, 0.05307, 4, 174, -23, 54.6, 0.00402, 173, 31.94, 41.12, 0.73986, 172, 63, 52.05, 0.25262, 171, 121.27, 66.84, 0.0035, 3, 174, 7.53, 30.25, 0.43898, 173, 69.6, 30.77, 0.55625, 172, 98.78, 67.71, 0.00477, 3, 175, -27.23, 28.05, 0.00096, 174, 27.77, 22.99, 0.93082, 173, 91.07, 32.07, 0.06822, 2, 175, -3.26, 24.21, 0.31078, 174, 52.04, 23.72, 0.68922, 2, 175, 20.83, 29.51, 0.93046, 174, 74.7, 33.45, 0.06954, 2, 176, -46.48, 4.5, 0.02138, 175, 58.02, 46.05, 0.97862, 2, 176, -29.45, 23.45, 0.2622, 175, 80.74, 34.53, 0.7378, 2, 176, -20.86, 29.5, 0.44279, 175, 88.81, 27.8, 0.55721, 2, 176, 0.14, 36.59, 0.84561, 175, 101.12, 9.37, 0.15439, 3, 177, -31.38, 20.92, 0.03078, 176, 21.62, 33.39, 0.96584, 175, 103.6, -12.2, 0.00339, 2, 177, -0.74, 29.85, 0.64471, 176, 52.8, 26.56, 0.35529, 2, 177, 18.4, 27.77, 0.9603, 176, 68.6, 15.57, 0.0397, 2, 178, -24.77, 9.17, 0.03352, 177, 37.54, 25.68, 0.96648, 2, 178, -8.31, 18.6, 0.42882, 177, 54.53, 17.26, 0.57118, 2, 178, 4.93, 18.68, 0.84013, 177, 61.97, 6.3, 0.15987, 2, 178, 14.86, 16.27, 0.99111, 177, 65.48, -3.3, 0.00889, 1, 178, 51.06, 2.05, 1, 1, 178, 61.16, -7.25, 1, 1, 178, 53.66, -5.82, 1, 4, 178, 29.21, -8.94, 0.98052, 177, 52.53, -29.25, 0.01912, 176, 71.27, -50.82, 1e-05, 175, 35.17, -82.03, 0.00034, 4, 178, 16.94, -17.99, 0.60319, 177, 38.18, -24.08, 0.36904, 176, 61.15, -39.42, 0.01189, 175, 43.55, -69.29, 0.01589, 4, 178, 10.81, -34.6, 0.11099, 177, 20.96, -28.23, 0.59218, 176, 44.05, -34.82, 0.14581, 175, 43.56, -51.58, 0.15102, 4, 178, 13.53, -52.22, 0.00819, 177, 7.84, -40.29, 0.21589, 176, 26.75, -39.13, 0.22345, 175, 34.91, -35.99, 0.55248, 4, 177, 5.42, -71.71, 0.00964, 176, 9.59, -65.55, 0.01512, 175, 4.93, -26.28, 0.72183, 174, 69.57, -24.33, 0.25341, 4, 177, 11.4, -92.48, 3e-05, 176, 4.89, -86.65, 1e-05, 175, -16.67, -27.22, 0.12573, 174, 48.53, -29.32, 0.87423, 2, 174, -7.28, -32.39, 0.42952, 173, 80.68, -32.63, 0.57048, 2, 174, -44.07, -28.24, 0.00341, 173, 45.24, -43.33, 0.99659, 2, 173, -1.03, -47.17, 0.71251, 172, 92.94, -37.31, 0.28749, 3, 173, -37.87, -41.31, 0.17704, 172, 60.62, -55.94, 0.82219, 171, 147.49, -37.95, 0.00076, 3, 173, -73.03, -15.61, 0.00135, 172, 17.12, -58.1, 0.83048, 171, 106.11, -51.51, 0.16817, 2, 172, -11.44, -55.9, 0.46071, 171, 77.98, -56.94, 0.53929, 2, 172, -34.11, -51.74, 0.18427, 171, 55.01, -58.91, 0.81573, 2, 172, -72.13, -29.08, 0.00119, 171, 12.37, -47.1, 0.99881, 1, 171, 3.32, -32.74, 1], "hull": 41, "edges": [0, 80, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 34, 36, 36, 38, 58, 24], "width": 284, "height": 309}}, "toufa13": {"toufa13": {"type": "mesh", "uvs": [0.33398, 0, 0.35576, 2e-05, 0.60865, 0.04292, 0.71351, 0.07673, 0.80056, 0.13246, 0.89803, 0.22957, 0.99253, 0.36512, 1, 0.40179, 1, 0.47778, 0.96947, 0.59511, 0.89272, 0.69409, 0.78502, 0.76365, 0.75859, 0.79315, 0.71561, 0.85241, 0.69013, 0.93181, 0.71564, 1, 0.69536, 1, 0.68572, 0.99819, 0.64767, 0.97986, 0.55053, 0.85505, 0.52311, 0.77046, 0.52595, 0.64133, 0.52946, 0.48156, 0.49094, 0.37621, 0.47303, 0.34245, 0.35501, 0.23838, 0.29194, 0.20332, 0.0995, 0.12818, 0, 0.07926, 0, 0.06063, 0.09641, 0.01845, 0.1776, 0], "triangles": [28, 29, 30, 27, 30, 31, 28, 30, 27, 26, 31, 0, 27, 31, 26, 1, 26, 0, 25, 1, 2, 25, 26, 1, 24, 25, 2, 3, 23, 24, 2, 3, 24, 4, 23, 3, 22, 6, 7, 22, 23, 5, 5, 23, 4, 22, 5, 6, 8, 22, 7, 9, 22, 8, 10, 21, 22, 9, 10, 22, 11, 21, 10, 12, 20, 21, 11, 12, 21, 13, 20, 12, 19, 20, 13, 14, 19, 13, 18, 19, 14, 17, 18, 14, 15, 16, 14, 17, 14, 16], "vertices": [2, 180, -8.94, 24.97, 0.31951, 179, 65.47, 25.59, 0.68049, 2, 180, -5.46, 26.29, 0.42825, 179, 69.05, 26.63, 0.57175, 2, 181, -41.49, 3.53, 0.00022, 180, 37.69, 34.48, 0.99978, 2, 181, -30.18, 18.69, 0.0816, 180, 56.58, 35.22, 0.9184, 2, 181, -16.11, 29.73, 0.39399, 180, 74.02, 31.22, 0.60601, 3, 182, -58.66, 2.42, 0.00065, 181, 5.52, 40.17, 0.85878, 180, 95.74, 20.97, 0.14057, 3, 182, -42.6, 26.62, 0.09145, 181, 33.48, 47.99, 0.90723, 180, 119.4, 4.14, 0.00132, 2, 182, -37.07, 30.31, 0.13705, 181, 40.08, 47.17, 0.86295, 2, 182, -24.58, 35.51, 0.29906, 181, 52.93, 42.95, 0.70094, 2, 182, -3.3, 38.73, 0.68643, 181, 71.14, 31.47, 0.31357, 3, 183, -6.84, 46.55, 8e-05, 182, 18.02, 33.39, 0.96467, 181, 83.79, 13.5, 0.03525, 2, 183, 0.79, 25.71, 0.09935, 182, 36.53, 21.15, 0.90065, 2, 183, 4.81, 20.07, 0.27621, 182, 43.11, 19, 0.72379, 2, 183, 13.3, 10.41, 0.85599, 182, 55.68, 16.27, 0.14401, 1, 183, 25.98, 2.8, 1, 1, 183, 38.81, 4.14, 1, 1, 183, 37.98, 0.77, 1, 1, 183, 37.27, -0.75, 1, 1, 183, 32.55, -6.29, 1, 2, 183, 7.01, -17.12, 0.91238, 182, 66.97, -9.61, 0.08762, 3, 183, -8.73, -18.07, 0.33139, 182, 54.87, -19.72, 0.66576, 181, 76.97, -50.79, 0.00284, 4, 183, -30.93, -12.11, 0.0011, 182, 33.47, -28.12, 0.89018, 181, 55.29, -43.15, 0.10791, 180, 62.24, -70.12, 0.00081, 3, 182, 6.99, -38.5, 0.30477, 181, 28.45, -33.71, 0.58803, 180, 52.71, -43.32, 0.10721, 3, 182, -7.79, -51.79, 0.04323, 181, 8.58, -34.11, 0.42251, 180, 39.9, -28.12, 0.53425, 3, 182, -12.16, -56.93, 0.01516, 181, 1.92, -35.14, 0.24523, 180, 34.91, -23.59, 0.73961, 3, 181, -21.98, -48.54, 0.00143, 180, 9.47, -13.43, 0.95389, 179, 80.83, -14.13, 0.04468, 2, 180, -2.83, -11.42, 0.32403, 179, 68.73, -11.16, 0.67597, 1, 179, 33.39, -7.56, 1, 1, 179, 14.62, -3.98, 1, 1, 179, 13.69, -0.8, 1, 1, 179, 27.4, 11.04, 1, 2, 180, -33.94, 15.49, 2e-05, 179, 39.81, 18.09, 0.99998], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 40, 42, 42, 44], "width": 171, "height": 178}}, "toufa14": {"toufa14": {"type": "mesh", "uvs": [0.45528, 0, 0.5232, 0.00813, 0.74721, 0.08301, 0.88731, 0.15027, 0.94365, 0.20521, 0.99998, 0.26014, 0.99999, 0.30796, 1, 0.35577, 0.96879, 0.39261, 0.93758, 0.42945, 0.77479, 0.52422, 0.612, 0.61899, 0.60776, 0.68778, 0.64647, 0.73621, 0.70662, 0.77571, 0.80295, 0.75965, 0.85941, 0.73153, 0.88686, 0.68192, 0.91688, 0.70458, 0.91707, 0.77032, 0.84699, 0.83124, 0.71634, 0.87355, 0.47709, 0.92792, 0.37431, 0.98393, 0.2414, 1, 0.13663, 1, 0.05858, 0.98019, 0.01075, 0.95083, 0.03175, 0.95015, 0.09092, 0.95661, 0.15728, 0.94607, 0.17954, 0.90794, 0.1709, 0.84776, 0.10814, 0.76813, 0.04538, 0.68849, 0.04438, 0.55558, 0.12576, 0.44584, 0.31492, 0.34429, 0.3511, 0.26668, 0.21497, 0.09419, 0.30118, 0.03852, 0.41982, 0], "triangles": [2, 38, 40, 39, 40, 38, 38, 2, 3, 41, 0, 1, 1, 2, 41, 41, 2, 40, 4, 6, 38, 6, 4, 5, 8, 6, 7, 3, 4, 38, 38, 6, 8, 8, 37, 38, 9, 37, 8, 10, 37, 9, 11, 37, 10, 36, 37, 11, 35, 36, 11, 11, 34, 35, 12, 34, 11, 33, 34, 12, 13, 32, 33, 31, 32, 22, 21, 22, 32, 23, 31, 22, 30, 31, 23, 25, 29, 30, 24, 25, 30, 23, 24, 30, 26, 28, 29, 27, 28, 26, 26, 29, 25, 13, 33, 12, 13, 21, 32, 14, 21, 13, 20, 15, 19, 14, 15, 20, 21, 14, 20, 16, 17, 18, 16, 18, 19, 15, 16, 19], "vertices": [1, 184, 3.88, 10.45, 1, 1, 184, 9.15, 15.87, 1, 2, 185, -26.05, 16.79, 0.11577, 184, 35.78, 28.32, 0.88423, 2, 185, -11.85, 32.67, 0.55392, 184, 56.36, 33.81, 0.44608, 2, 185, 0.07, 39.37, 0.79868, 184, 70.01, 32.88, 0.20132, 3, 186, -56.19, 20.55, 0.00316, 185, 12, 46.07, 0.92788, 184, 83.66, 31.95, 0.06895, 3, 186, -47.27, 26.39, 0.01995, 185, 22.65, 46.62, 0.9564, 184, 92.86, 26.57, 0.02365, 3, 186, -38.35, 32.23, 0.05952, 185, 33.3, 47.17, 0.93664, 184, 102.07, 21.19, 0.00385, 4, 187, -36.77, 69.43, 0.00059, 186, -29.63, 33.91, 0.11859, 185, 41.67, 44.22, 0.88072, 184, 107.46, 14.14, 0.00011, 4, 191, 10.64, 89, 0, 187, -28.78, 65.57, 0.00416, 186, -20.91, 35.59, 0.2084, 185, 50.05, 41.28, 0.78744, 4, 191, 4.92, 62.11, 0.01064, 187, -8.74, 46.74, 0.10386, 186, 6.4, 32.45, 0.66841, 185, 72.06, 24.8, 0.21709, 4, 191, -0.79, 35.22, 0.17546, 187, 11.29, 27.92, 0.55199, 186, 33.71, 29.32, 0.27245, 185, 94.06, 8.33, 9e-05, 3, 191, 5.95, 21.43, 0.59122, 187, 26.57, 26.53, 0.3887, 186, 46.8, 37.33, 0.02008, 4, 192, 5.28, 18.15, 0.04006, 191, 14.68, 13.82, 0.87793, 187, 37.61, 30.05, 0.08187, 186, 53.55, 46.75, 0.00015, 3, 192, 6.22, 7.25, 0.57053, 191, 24.53, 9.05, 0.42758, 187, 46.79, 36, 0.00189, 3, 193, 0.3, 8.03, 0.08964, 192, 16.96, 4.87, 0.90896, 191, 32.07, 17.06, 0.0014, 2, 193, 6.95, 2.34, 0.99437, 192, 25.43, 7.03, 0.00563, 1, 193, 18.17, 0.07, 1, 1, 193, 13.33, -3.48, 1, 2, 193, -1.3, -4.42, 0.53047, 192, 26.22, -3.6, 0.46953, 1, 192, 12.67, -11.23, 1, 3, 192, -4.29, -11.9, 0.47471, 191, 35.62, -9.77, 0.46878, 188, 13.68, 42.12, 0.05651, 3, 191, 18.4, -32.53, 0.26381, 189, -18.01, 10.89, 0.02883, 188, 31.98, 20.21, 0.70737, 3, 191, 14.4, -48.76, 0.02691, 189, -1.93, 15.43, 0.58795, 188, 46.89, 12.66, 0.38514, 1, 189, 12.09, 10.71, 1, 2, 190, -0.77, 5.21, 0.46396, 189, 21.62, 4.61, 0.53604, 1, 190, 8, 1.51, 1, 2, 190, 13.69, -4.58, 0.99999, 189, 27.16, -11.95, 1e-05, 2, 190, 11.44, -4.92, 0.99999, 189, 25.17, -10.86, 1e-05, 2, 190, 4.96, -4.02, 0.92939, 189, 20.56, -6.2, 0.07061, 3, 190, -1.99, -6.96, 0.06396, 189, 13.26, -4.32, 0.91608, 188, 44.69, -12.16, 0.01996, 4, 189, 6.65, -10.18, 0.56716, 188, 35.85, -12, 0.43141, 187, 72.78, -22.6, 0.00142, 186, 113.21, 25.53, 1e-05, 4, 189, 0.2, -21.99, 0.05022, 188, 23.11, -16.32, 0.85972, 187, 59.33, -22.72, 0.08093, 186, 102.49, 17.4, 0.00912, 3, 188, 7.66, -27.39, 0.47213, 187, 41.19, -28.41, 0.42115, 186, 91.34, 2, 0.10673, 3, 188, -7.78, -38.46, 0.15402, 187, 23.06, -34.11, 0.44759, 186, 80.2, -13.4, 0.39839, 4, 188, -36.42, -46.11, 0.00605, 187, -6.54, -32.42, 0.02386, 186, 55.46, -29.72, 0.96809, 185, 83.08, -53.62, 0.002, 2, 186, 30.17, -35.77, 0.91522, 185, 58.19, -46.1, 0.08478, 3, 186, 0.03, -31.08, 0.32077, 185, 34.53, -26.85, 0.67091, 184, 62.52, -41.39, 0.00832, 3, 186, -16.59, -37.29, 0.02467, 185, 17.04, -23.84, 0.77595, 184, 49.55, -29.28, 0.19938, 2, 185, -20.62, -40.49, 0.01156, 184, 8.92, -22.56, 0.98844, 1, 184, 2.9, -8.26, 1, 1, 184, 1.95, 7.14, 1], "hull": 42, "edges": [0, 82, 0, 2, 2, 4, 4, 6, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 18, 20, 20, 22, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 16, 76, 64, 66, 66, 68, 64, 62, 62, 60, 60, 58, 54, 56, 58, 56], "width": 108, "height": 223}}, "toufa15": {"toufa15": {"type": "mesh", "uvs": [0.13141, 0.03463, 0.13147, 0.09119, 0.19196, 0.11929, 0.31545, 0.20559, 0.38399, 0.30412, 0.38399, 0.41582, 0.34847, 0.52378, 0.33917, 0.61245, 0.36546, 0.7351, 0.44383, 0.84022, 0.64166, 0.92928, 0.77669, 0.90852, 0.86819, 0.86101, 0.87074, 0.8802, 0.8368, 0.9174, 0.76632, 0.95264, 0.50554, 1, 0.33238, 1, 0.18212, 0.93699, 0.09579, 0.877, 0, 0.72105, 0, 0.68804, 0.02496, 0.60483, 0.12056, 0.47257, 0.11803, 0.42568, 0.02704, 0.20391, 0.04827, 0.12542, 0.10416, 0.03453], "triangles": [24, 25, 3, 2, 25, 26, 2, 26, 1, 3, 25, 2, 1, 27, 0, 1, 26, 27, 6, 23, 5, 23, 24, 5, 5, 24, 4, 4, 24, 3, 7, 21, 22, 8, 21, 7, 8, 20, 21, 22, 23, 7, 7, 23, 6, 17, 18, 9, 9, 18, 8, 19, 20, 8, 18, 19, 8, 10, 16, 9, 16, 17, 9, 16, 10, 15, 15, 11, 14, 15, 10, 11, 14, 11, 13, 11, 12, 13], "vertices": [1, 194, 1.28, 0.95, 1, 1, 194, 9.01, -1.23, 1, 1, 194, 14.48, 3.45, 1, 2, 195, -13, 9.29, 0.03718, 194, 29.59, 11.88, 0.96282, 2, 195, 1.36, 15.27, 0.75238, 194, 44.9, 14.61, 0.24762, 2, 196, -14.27, 12.44, 0.00103, 195, 17.19, 14.36, 0.99897, 2, 196, 1.31, 10.27, 0.63138, 195, 32.3, 9.97, 0.36862, 2, 197, -7.63, 16.83, 0.01452, 196, 13.93, 10.44, 0.98548, 3, 198, -7.48, 24.83, 0.00026, 197, 9.14, 11.46, 0.80593, 196, 31.06, 14.54, 0.19381, 2, 198, 4.26, 12.78, 0.38733, 197, 25.96, 11.83, 0.61267, 2, 199, 3.44, 5.41, 0.60723, 198, 26.66, 6.32, 0.39277, 1, 199, 16.79, 2.39, 1, 1, 199, 27.86, 4.63, 1, 1, 199, 26.93, 2.06, 1, 1, 199, 21.64, -1.29, 1, 1, 199, 13.2, -2.85, 1, 1, 198, 16.66, -7.18, 1, 2, 198, 0.24, -12.13, 0.77707, 197, 41.44, -8.1, 0.22293, 2, 198, -16.58, -7.86, 0.02189, 197, 26.84, -17.49, 0.97811, 2, 197, 15.42, -21.39, 0.98006, 196, 53.44, -10.31, 0.01994, 2, 197, -8.64, -20.11, 0.32896, 196, 32.2, -21.67, 0.67104, 2, 197, -12.84, -18.04, 0.21421, 196, 27.53, -22.08, 0.78579, 3, 197, -22.35, -10.6, 0.02036, 196, 15.55, -20.64, 0.97067, 195, 41.96, -22.66, 0.00897, 2, 196, -3.98, -12.84, 0.26159, 195, 23.75, -12.14, 0.73841, 3, 196, -10.6, -13.67, 0.0268, 195, 17.09, -12.01, 0.9684, 194, 54.35, -15.42, 0.0048, 2, 195, -14.86, -19.2, 0.02253, 194, 21.6, -15.53, 0.97747, 1, 194, 11.45, -10.47, 1, 1, 194, 0.53, -1.64, 1], "hull": 28, "edges": [0, 54, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 26, 28], "width": 99, "height": 142}}, "tun": {"tun": {"type": "mesh", "uvs": [0.64613, 0, 0.66353, 0, 0.66859, 0.01343, 0.72459, 0.10697, 0.79869, 0.24283, 0.81915, 0.28406, 0.84633, 0.29952, 0.89081, 0.39958, 0.91968, 0.48579, 0.98246, 0.73463, 1, 0.82325, 1, 1, 0.46914, 1, 0, 1, 0, 0.99644, 0.00338, 0.98932, 0.04454, 0.93867, 0.14799, 0.83091, 0.19725, 0.79245, 0.21848, 0.77695, 0.22606, 0.73542, 0.26397, 0.72087, 0.27834, 0.65528, 0.26658, 0.53359, 0.26388, 0.43192, 0.29236, 0.38391, 0.32388, 0.34402, 0.35541, 0.30414, 0.41083, 0.21521, 0.43671, 0.18226, 0.53833, 0.08046, 0.62416, 0.00838, 0.35548, 0.53461, 0.40513, 0.73052, 0.47436, 0.77234], "triangles": [12, 13, 16, 13, 15, 16, 16, 17, 12, 17, 18, 12, 18, 19, 12, 12, 19, 21, 19, 20, 21, 13, 14, 15, 12, 21, 33, 21, 22, 33, 12, 33, 34, 29, 34, 32, 22, 32, 33, 34, 33, 32, 22, 23, 32, 32, 24, 25, 25, 26, 32, 26, 27, 32, 32, 27, 28, 32, 23, 24, 11, 12, 9, 12, 34, 9, 34, 8, 9, 9, 10, 11, 4, 30, 3, 34, 4, 5, 7, 8, 5, 5, 6, 7, 34, 5, 8, 4, 34, 30, 3, 31, 2, 3, 30, 31, 31, 0, 2, 0, 1, 2, 32, 28, 29, 34, 29, 30], "vertices": [1, 5, -15.18, 99.32, 1, 1, 5, -9.39, 105.62, 1, 1, 5, -4.82, 104.8, 1, 1, 5, 33.93, 106.61, 1, 2, 5, 87.82, 106.61, 0.97974, 6, -114.41, 30.63, 0.02026, 2, 5, 103.49, 105.87, 0.94382, 6, -103.61, 42.02, 0.05618, 2, 5, 115.86, 112.66, 0.90283, 6, -100.66, 55.82, 0.09717, 2, 5, 152.18, 109.01, 0.73463, 6, -74.15, 80.91, 0.26537, 2, 5, 180.33, 102.43, 0.53915, 6, -50.78, 97.92, 0.46085, 2, 5, 254.73, 76.01, 0.08809, 6, 17.85, 136.95, 0.91191, 2, 5, 279.62, 64.86, 0.03271, 6, 42.56, 148.5, 0.96729, 1, 6, 93.83, 154.43, 1, 4, 5, 140.9, -162.38, 0.00683, 6, 123.85, -105.02, 0.27578, 2, 209, 37.63, 0.15523, 3, -21.72, 70.57, 0.56216, 1, 3, 187.01, -27.96, 1, 1, 3, 186.57, -28.9, 1, 1, 3, 184.17, -30.07, 1, 1, 3, 159.55, -34.8, 1, 2, 2, 128.51, -107.03, 0.00392, 3, 100.09, -41.53, 0.99608, 2, 2, 122.45, -81.02, 0.0555, 3, 73.38, -41.34, 0.9445, 2, 2, 120.14, -69.87, 0.12965, 3, 62, -40.97, 0.87035, 2, 2, 109.03, -63.75, 0.22716, 3, 53.46, -50.35, 0.77284, 2, 2, 108.66, -44.62, 0.47765, 3, 34.77, -46.23, 0.52235, 2, 2, 91.35, -33.8, 0.86321, 3, 20.2, -60.53, 0.13679, 2, 2, 55.38, -32.24, 0.99854, 3, 10.27, -95.13, 0.00146, 1, 2, 26.04, -27.5, 1, 1, 2, 15.17, -10.93, 1, 2, 5, -48.49, -85.4, 0.04488, 2, 6.92, 6.62, 0.95512, 2, 5, -46.57, -66.1, 0.25264, 2, -1.33, 24.18, 0.74736, 2, 5, -47.24, -28.45, 0.69797, 2, -21.2, 56.16, 0.30203, 2, 5, -45.71, -12.56, 0.83228, 2, -28.03, 70.58, 0.16772, 1, 5, -33.77, 44.36, 1, 1, 5, -20.7, 89.7, 1, 3, 5, 3.01, -111.61, 0.0302, 6, -4.71, -176.19, 0.00099, 2, 64.57, 10.52, 0.96881, 4, 5, 61.65, -132.33, 0.0411, 6, 49.31, -145.35, 0.03681, 2, 125.55, 22.8, 0.89989, 3, -26.83, -14.04, 0.0222, 4, 5, 93.69, -115.51, 0.1071, 6, 57.52, -110.11, 0.19964, 2, 144.44, 53.67, 0.53821, 3, -52.42, 11.55, 0.15506], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 54, 56, 56, 58, 58, 60, 60, 62, 50, 52, 52, 54, 52, 64, 64, 66, 66, 68, 22, 24, 24, 26, 68, 24], "width": 492, "height": 292}}, "waitao": {"waitao": {"type": "mesh", "uvs": [0.66167, 0, 0.77019, 0, 0.81034, 0.00023, 0.8699, 0.01484, 0.93037, 0.04563, 0.98323, 0.09871, 1, 0.14666, 1, 0.19374, 0.99996, 0.20584, 0.97927, 0.27292, 0.94798, 0.32477, 0.88292, 0.40637, 0.79742, 0.5571, 0.71193, 0.70783, 0.59827, 0.89258, 0.59412, 0.9117, 0.56134, 1, 0.54868, 1, 0.47267, 0.9747, 0.40173, 0.95575, 0.40103, 0.98996, 0.32255, 0.95888, 0.28911, 0.94876, 0.24499, 0.94219, 0.06012, 0.95742, 0.03499, 0.95736, 0, 0.89969, 0, 0.85572, 0.02587, 0.63543, 0.05857, 0.5277, 0.11028, 0.42516, 0.19965, 0.35318, 0.32134, 0.28488, 0.3947, 0.2322, 0.46558, 0.15594, 0.54206, 0.08547], "triangles": [12, 32, 33, 12, 33, 34, 12, 34, 35, 31, 13, 19, 13, 31, 32, 22, 29, 31, 29, 30, 31, 23, 24, 27, 26, 27, 24, 25, 26, 24, 23, 27, 28, 23, 28, 29, 29, 22, 23, 16, 17, 15, 17, 18, 15, 20, 21, 19, 15, 18, 14, 21, 22, 19, 14, 18, 19, 13, 14, 19, 31, 19, 22, 13, 32, 12, 11, 12, 0, 2, 10, 11, 0, 12, 35, 2, 11, 1, 0, 1, 11, 9, 10, 3, 3, 10, 2, 7, 9, 3, 8, 9, 7, 4, 7, 3, 4, 6, 7, 4, 5, 6], "vertices": [1, 19, 67.8, 43.45, 1, 1, 19, 109.62, 54.63, 1, 1, 19, 125.12, 58.68, 1, 1, 19, 149.59, 59.2, 1, 1, 19, 176.06, 53.6, 1, 1, 19, 201.89, 38.64, 1, 2, 19, 213.29, 21.93, 0.9989, 48, -34.05, 65.22, 0.0011, 2, 19, 218.13, 3.83, 0.95349, 48, -16.53, 71.87, 0.04651, 2, 19, 219.36, -0.83, 0.93062, 48, -12.03, 73.57, 0.06938, 2, 19, 218.28, -28.75, 0.67511, 48, 15.86, 75.33, 0.32489, 2, 19, 211.55, -51.91, 0.39146, 48, 39.59, 70.98, 0.60854, 3, 19, 194.85, -90, 0.06362, 48, 79.16, 58.24, 0.91579, 49, -47.63, 59.78, 0.02059, 2, 48, 147.36, 47.65, 0.25307, 49, 20.19, 47.03, 0.74693, 2, 49, 88.01, 34.29, 0.87825, 50, -11.04, 34.83, 0.12175, 1, 50, 72.29, 12.04, 1, 1, 50, 80.05, 12.59, 1, 4, 50, 117.46, 9.95, 0.99999, 54, 137.67, 209.14, 1e-05, 53, 135.64, 245.52, 0, 52, 72.92, 279.2, 0, 2, 50, 118.88, 5.1, 1, 52, 77.23, 276.56, 0, 4, 50, 117.77, -26.83, 0.93274, 54, 129.43, 173.29, 0.06296, 53, 141.46, 209.19, 0.0037, 52, 97.82, 252.12, 0.0006, 5, 49, 221.58, -50.65, 0.00026, 50, 118.51, -56.12, 0.75316, 54, 123.36, 144.63, 0.23114, 53, 146.58, 180.35, 0.01282, 52, 118.01, 230.9, 0.00262, 4, 50, 131.65, -52.55, 0.72248, 54, 136.97, 145.05, 0.26243, 53, 159.04, 185.85, 0.01296, 52, 125.36, 242.36, 0.00213, 5, 49, 233.03, -80.12, 0.00128, 50, 128.61, -86.08, 0.58844, 54, 126.22, 113.15, 0.38817, 53, 161.05, 152.24, 0.01832, 52, 145.59, 215.44, 0.00378, 5, 49, 233.57, -94.05, 0.00207, 50, 128.5, -100.01, 0.48729, 54, 122.89, 99.61, 0.48666, 53, 163.04, 138.45, 0.01985, 52, 154.86, 205.04, 0.00413, 5, 49, 236.82, -111.55, 0.00192, 50, 130.95, -117.64, 0.34897, 54, 121.18, 81.9, 0.62857, 53, 168.1, 121.39, 0.0173, 52, 168.5, 193.6, 0.00323, 2, 50, 157.56, -186.71, 0.01193, 54, 131.02, 8.54, 0.98807, 2, 50, 160.36, -196.34, 0.00433, 54, 131.52, -1.47, 0.99567, 1, 54, 109.32, -16.59, 1, 1, 54, 91.84, -17.49, 1, 2, 54, 3.75, -11.69, 0.87994, 53, 94.37, -9.43, 0.12006, 1, 53, 50, -15.72, 1, 2, 53, 4.29, -14.25, 0.90747, 52, 106.75, -9.92, 0.09253, 1, 52, 61.38, -15.7, 1, 2, 52, 5.77, -13.49, 0.78524, 51, 105.7, -14.4, 0.21476, 1, 51, 70.03, -9.46, 1, 1, 51, 28.63, -12, 1, 2, 19, 30.48, -1.74, 0.88756, 51, -12.81, -11.34, 0.11244], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 22, 24, 24, 26], "width": 399, "height": 398}}, "waitao1": {"waitao1": {"type": "mesh", "uvs": [0.84729, 0, 0.95552, 0.08263, 0.98753, 0.13791, 1, 0.18035, 1, 0.2127, 0.92479, 0.39357, 0.8622, 0.46395, 0.73144, 0.57195, 0.6438, 0.69093, 0.56038, 0.77042, 0.40124, 0.89503, 0.34654, 0.916, 0.23174, 0.92866, 0.03564, 1, 0, 1, 0, 0.98514, 0.00106, 0.97591, 0.13336, 0.90799, 0.18586, 0.8689, 0.30882, 0.68044, 0.45292, 0.50161, 0.4483, 0.46139, 0.47602, 0.3652, 0.50793, 0.29368, 0.59562, 0.19348, 0.68578, 0.1164, 0.76885, 0.06158, 0.762, 0.02026, 0.79761, 0], "triangles": [5, 25, 26, 5, 26, 28, 1, 5, 28, 1, 28, 0, 26, 27, 28, 4, 1, 2, 4, 5, 1, 2, 3, 4, 7, 24, 6, 5, 6, 25, 25, 6, 24, 20, 22, 7, 22, 23, 7, 23, 24, 7, 20, 21, 22, 8, 20, 7, 19, 20, 9, 9, 20, 8, 10, 11, 19, 11, 18, 19, 10, 19, 9, 12, 17, 18, 12, 13, 17, 13, 15, 16, 13, 16, 17, 13, 14, 15, 12, 18, 11], "vertices": [2, 61, 45.61, -6.98, 0.00642, 62, 21.74, -10.58, 0.99358, 3, 60, 52.92, -20.26, 0.00171, 61, 28.31, -23.34, 0.52395, 62, 2.05, -23.97, 0.47434, 3, 60, 43.44, -25.02, 0.03377, 61, 18.38, -27.08, 0.74359, 62, -8.35, -26.08, 0.22264, 3, 60, 36.42, -26.47, 0.08694, 61, 11.25, -27.79, 0.79917, 62, -15.5, -25.65, 0.11389, 3, 60, 31.28, -25.84, 0.14084, 61, 6.21, -26.63, 0.79071, 62, -20.29, -23.7, 0.06846, 1, 60, 4.21, -8.7, 1, 5, 56, 119.77, 21.96, 0, 57, 84.14, -15.63, 0, 58, 18, -57.22, 0.06792, 59, -40.95, -40.18, 0.00695, 60, -5.59, 4.02, 0.92514, 7, 56, 91.16, 14.71, 0, 57, 55.18, -9.91, 0, 58, 7.24, -29.74, 0.68246, 59, -30.86, -12.45, 0.02024, 60, -19.87, 29.85, 0.29718, 61, -38.87, 34.09, 9e-05, 62, -55.11, 43.43, 2e-05, 3, 57, 30.32, -11.16, 0.37562, 58, -7.2, -9.46, 0.60694, 60, -36.84, 48.06, 0.01744, 1, 57, 10.61, -8.66, 1, 1, 56, 15.94, -11.09, 1, 2, 55, 52.86, -10.14, 0.09198, 56, 5.4, -10.52, 0.90802, 1, 55, 32.23, -5.64, 1, 2, 55, -5.42, -5.54, 1, 61, -77.26, 173.48, 0, 2, 55, -11.63, -3.54, 1, 61, -75.81, 179.84, 0, 2, 55, -10.91, -1.28, 1, 61, -73.49, 179.31, 0, 2, 55, -10.27, 0.07, 1, 61, -72.09, 178.79, 0, 1, 55, 16.1, 3.01, 1, 5, 55, 27.16, 6.03, 0.99995, 56, -19.17, 7.32, 5e-05, 59, 25.08, 82.93, 0, 60, -55.04, 134.68, 0, 62, -61.66, 153.81, 0, 8, 55, 57.81, 27.86, 0.04299, 56, 12.86, 27.06, 0.79813, 57, -10.25, 34.83, 0.06138, 58, 9.72, 49.48, 0.00689, 59, 23.39, 45.34, 0.09059, 60, -27.81, 108.72, 0, 61, -38.56, 113.35, 1e-05, 62, -42.16, 121.63, 1e-05, 7, 56, 47.92, 43.95, 0.03782, 57, 28.66, 35.05, 0.03097, 58, 30.85, 16.81, 0.03072, 59, 17.64, 6.86, 0.90044, 60, -2.57, 79.1, 2e-05, 61, -16.54, 81.26, 2e-05, 62, -25.54, 86.45, 2e-05, 4, 56, 49.5, 50.24, 0.00073, 57, 32.79, 40.05, 0.00057, 58, 37.29, 16.03, 0.00039, 59, 21.95, 2.01, 0.99831, 6, 56, 59.89, 62.68, 0.00026, 57, 47.5, 46.84, 0.00021, 59, 26.4, -13.57, 0.90747, 60, 18.58, 72.28, 0.02589, 61, 3.79, 72.28, 0.03278, 62, -6.9, 74.33, 0.03339, 6, 56, 69.53, 71.17, 0.00024, 57, 59.86, 50.37, 0.00019, 59, 27.99, -26.32, 0.77145, 60, 29.24, 65.11, 0.05919, 61, 13.65, 64.04, 0.08162, 62, 1.51, 64.62, 0.08732, 7, 56, 90.36, 80.17, 0.00015, 57, 82.53, 49.56, 0.00011, 58, 72.08, -20.78, 0.00123, 59, 23.71, -48.6, 0.44819, 60, 43.23, 47.25, 0.08932, 61, 25.7, 44.82, 0.19347, 62, 10.34, 43.73, 0.26754, 6, 56, 110.24, 85.55, 6e-05, 57, 102.8, 45.9, 4e-05, 59, 16.99, -68.07, 0.16629, 60, 53.49, 29.39, 0.0352, 61, 34.04, 25.99, 0.18957, 62, 15.58, 23.81, 0.60884, 6, 56, 127.6, 88.11, 0, 57, 119.58, 40.77, 0, 59, 9.33, -83.86, 0.013, 60, 60.37, 13.24, 0.00033, 61, 39.21, 9.21, 0.00863, 62, 18, 6.43, 0.97803, 4, 56, 128.87, 94.72, 0, 57, 123.56, 46.19, 0, 59, 14.08, -88.63, 0.00056, 62, 24.6, 5.1, 0.99944, 1, 62, 25.16, -2.15, 1], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56], "width": 183, "height": 160}}, "waitao3": {"waitao3": {"type": "mesh", "uvs": [0.47436, 0.1212, 0.78673, 0.2307, 0.89512, 0.32595, 0.94116, 0.40541, 1, 0.59115, 1, 0.65382, 0.93483, 0.92854, 0.73629, 1, 0.27815, 1, 0, 0.88717, 0, 0, 0.09446, 0, 0.16199, 0.0117], "triangles": [3, 4, 5, 9, 10, 11, 3, 7, 2, 5, 7, 3, 8, 12, 0, 9, 11, 12, 8, 9, 12, 7, 0, 1, 7, 1, 2, 5, 6, 7, 8, 0, 7], "vertices": [2, 50, 91.31, -39.61, 0.75079, 54, 100.73, 167, 0.24921, 2, 50, 88.88, -12.32, 0.9075, 54, 104.7, 194.11, 0.0925, 2, 50, 90.73, -2.06, 0.96574, 54, 108.89, 203.66, 0.03426, 2, 50, 93.35, 2.83, 0.99364, 54, 112.57, 207.81, 0.00636, 1, 50, 100.66, 10.25, 1, 1, 50, 103.61, 11.12, 1, 1, 50, 118.1, 9.53, 1, 2, 50, 126.27, -5.86, 0.96799, 54, 142.57, 191.71, 0.03201, 2, 50, 137.38, -43.66, 0.77652, 54, 144.6, 152.36, 0.22349, 2, 50, 138.81, -68.17, 0.7252, 54, 140.31, 128.19, 0.2748, 2, 50, 97.1, -80.43, 0.7252, 54, 96.89, 125.96, 0.2748, 2, 50, 94.81, -72.63, 0.7252, 54, 96.48, 134.07, 0.2748, 2, 50, 93.73, -66.9, 0.7252, 54, 96.75, 139.9, 0.2748], "hull": 13, "edges": [2, 4, 4, 6, 6, 8, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 8, 10, 2, 0, 0, 24], "width": 86, "height": 49}}, "waitao4": {"waitao4": {"type": "mesh", "uvs": [0.44301, 0, 0.54086, 0, 0.98582, 0.13216, 1, 0.25291, 1, 1, 0.94509, 1, 0.14924, 0.72355, 0, 0.58409, 0, 0.46471, 0.10471, 0.06327], "triangles": [1, 3, 6, 3, 1, 2, 4, 5, 3, 0, 1, 6, 5, 6, 3, 6, 7, 0, 9, 0, 8, 0, 7, 8], "vertices": [2, 74, 0.86, 17.93, 0.6, 77, 57.92, -81.53, 0.4, 2, 74, -0.34, 23.28, 0.5, 77, 56.89, -76.15, 0.5, 1, 77, 59.97, -50.18, 1, 1, 77, 66.93, -48.03, 1, 2, 75, 53.66, 41.05, 0.2, 78, 70.19, -53.79, 0.8, 1, 78, 70.19, -56.87, 1, 1, 75, 24.92, -0.41, 1, 1, 75, 14.62, -6.23, 1, 1, 75, 7.72, -4.31, 1, 2, 75, -13.92, 7.79, 0.5725, 74, 8.73, 0.28, 0.4275], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 10, 12, 16, 18], "width": 56, "height": 60}}, "xiong": {"xiong": {"type": "mesh", "uvs": [0.98396, 0, 0.99944, 0, 1, 0.04651, 1, 0.11687, 0.97478, 0.14464, 0.84255, 0.2788, 0.7972, 0.2991, 0.72219, 0.34156, 0.62498, 0.41474, 0.57727, 0.46417, 0.49278, 0.60313, 0.4813, 0.64355, 0.45713, 0.78231, 0.44809, 0.92566, 0.29611, 0.90626, 0.23611, 0.9094, 0.16639, 0.93402, 0.14046, 0.94783, 0.12258, 1, 0.11586, 1, 0.10839, 0.9963, 0.0974, 0.97537, 0.09746, 0.89271, 0.10045, 0.87087, 0.00425, 0.67721, 0, 0.6038, 0, 0.56274, 0.01995, 0.5005, 0.07373, 0.43103, 0.2397, 0.33917, 0.32555, 0.32745, 0.37245, 0.30719, 0.46781, 0.24204, 0.50154, 0.2155, 0.59113, 0.11322, 0.64852, 0.06522, 0.83392, 0.03612, 0.965, 0.00625, 0.61003, 0.2779, 0.50604, 0.30716, 0.42994, 0.34278, 0.3602, 0.38858, 0.29425, 0.45346, 0.27396, 0.51071, 0.28537, 0.59467, 0.31581, 0.66337, 0.35766, 0.72189, 0.40585, 0.7486, 0.022, 0.62653, 0.05724, 0.70636, 0.08793, 0.75084, 0.12204, 0.78391, 0.03791, 0.56038, 0.32213, 0.50222, 0.41422, 0.30834], "triangles": [0, 1, 2, 2, 4, 37, 36, 37, 4, 0, 2, 37, 3, 4, 2, 38, 34, 35, 33, 34, 38, 5, 36, 4, 6, 35, 36, 6, 38, 35, 36, 5, 6, 39, 33, 38, 32, 33, 39, 39, 54, 32, 7, 38, 6, 8, 38, 7, 39, 38, 8, 41, 54, 40, 40, 54, 39, 42, 30, 41, 53, 42, 41, 43, 28, 42, 43, 42, 53, 44, 43, 53, 52, 43, 44, 53, 41, 10, 44, 53, 10, 42, 29, 30, 28, 29, 42, 52, 27, 28, 52, 28, 43, 26, 27, 52, 48, 25, 26, 52, 48, 26, 24, 25, 48, 49, 52, 44, 48, 52, 49, 24, 48, 49, 50, 49, 44, 51, 50, 45, 23, 50, 51, 24, 49, 50, 23, 24, 50, 15, 23, 51, 23, 16, 22, 15, 16, 23, 17, 22, 16, 21, 22, 17, 18, 19, 20, 20, 21, 18, 17, 18, 21, 11, 45, 10, 12, 47, 11, 13, 14, 12, 54, 31, 32, 9, 39, 8, 30, 31, 54, 40, 39, 9, 9, 41, 40, 10, 41, 9, 10, 45, 44, 46, 45, 11, 47, 46, 11, 51, 46, 15, 46, 51, 45, 14, 46, 47, 15, 46, 14, 12, 14, 47, 41, 30, 54, 45, 50, 44], "vertices": [4, 47, 226.47, -134.1, 0, 13, 182.35, -33.52, 0.00017, 23, -54.53, 18.14, 0, 19, 30.93, 6.68, 0.99983, 4, 47, 228.33, -138.66, 0, 13, 185.65, -37.17, 0.00035, 23, -58.9, 20.41, 1e-05, 19, 35.69, 7.95, 0.99964, 3, 13, 174.84, -47.19, 0.02093, 23, -52.27, 33.58, 0.00037, 19, 39.67, -6.25, 0.9787, 3, 13, 158.3, -62.16, 0.19193, 23, -42.01, 53.38, 0.00253, 19, 45.43, -27.79, 0.80554, 3, 13, 146.39, -62.11, 0.31377, 23, -30.84, 57.51, 0.00332, 19, 39.96, -38.37, 0.68291, 3, 13, 86.64, -59.47, 0.91129, 23, 26.07, 75.92, 0.0008, 19, 10.32, -90.32, 0.08791, 3, 13, 72.2, -53.09, 0.95956, 23, 41.83, 75, 0.00032, 19, -1.95, -100.26, 0.04012, 4, 12, 219.58, -57.54, 0.00597, 13, 46.21, -44.43, 0.99053, 23, 69.2, 75.97, 1e-05, 19, -21.51, -119.42, 0.00348, 2, 12, 186.43, -37.67, 0.36969, 13, 8.27, -37.07, 0.63031, 4, 12, 166.2, -29.53, 0.85578, 76, -62.61, 22.34, 0.00068, 47, 41.4, -69.87, 0, 13, -13.53, -36.33, 0.14354, 3, 12, 115.26, -21.28, 0.57561, 76, -14.86, 2.77, 0.42439, 47, -9.54, -61.62, 0, 2, 12, 102.02, -22.74, 0.00235, 76, -1.63, 1.19, 0.99765, 1, 77, 12.52, 0.94, 1, 2, 78, 26.63, 1.7, 1, 47, -109.58, -87.05, 0, 5, 12, 2.68, 0.37, 0.99412, 77, 60.78, -41.92, 0, 74, 2.48, 57.61, 0.0041, 73, 39.36, 57.04, 0.00177, 47, -122.13, -39.97, 0, 6, 12, -5.45, 17.66, 0.59046, 78, 21.47, -65.71, 0.00094, 77, 65.36, -60.47, 0, 75, 3.52, 42.61, 0.0028, 74, 7.64, 39.21, 0.28802, 73, 42.57, 38.2, 0.11778, 6, 12, -21.04, 35.24, 0.11874, 78, 29.28, -87.88, 0.00027, 77, 77.21, -80.77, 0, 75, 5.11, 19.16, 0.17143, 74, 20.12, 19.3, 0.68769, 73, 52.92, 17.1, 0.02187, 6, 12, -28.21, 41.23, 0.02548, 78, 33.66, -96.12, 7e-05, 77, 83.07, -88.04, 0, 75, 7.12, 10.04, 0.58001, 74, 26.2, 12.21, 0.3944, 73, 58.23, 9.42, 4e-05, 2, 75, 21.53, 0.14, 0.99976, 74, 43.58, 10.3, 0.00024, 5, 12, -46.48, 42.23, 0, 78, 50.19, -103.95, 0, 77, 100.78, -92.6, 0, 75, 20.96, -1.92, 0.99985, 74, 44.05, 8.21, 0.00015, 2, 75, 19.2, -3.9, 0.99992, 74, 43.43, 5.64, 8e-05, 1, 75, 11.87, -5.49, 1, 1, 74, 12.16, -4.96, 1, 2, 74, 5.19, -5.56, 0.87989, 73, 35.48, -6.07, 0.12011, 2, 73, -21.91, -43.62, 0.3053, 46, -40.64, -6.29, 0.6947, 2, 73, -44.86, -47.68, 0.16432, 46, -19.61, 3.74, 0.83568, 2, 73, -57.79, -49.2, 0.02894, 46, -7.55, 8.65, 0.97106, 1, 46, 13.11, 10.23, 1, 1, 46, 39.96, 2.7, 1, 3, 12, 162.37, 84.83, 0.23787, 46, 86.85, -35.18, 0.75725, 13, -56.17, 69.85, 0.00488, 4, 12, 176.12, 60.95, 0.60646, 46, 100.59, -59.06, 0.3, 13, -35.09, 52.1, 0.08967, 23, 179.13, 13.96, 0.00387, 4, 12, 187.7, 49.56, 0.71318, 46, 112.17, -70.45, 0.01557, 13, -20.33, 45.35, 0.25232, 23, 162.93, 15.12, 0.01893, 3, 12, 218.27, 29.28, 0.12391, 13, 15.33, 36.72, 0.70586, 23, 126.5, 10.74, 0.17024, 4, 12, 230.11, 22.52, 0.01805, 47, 105.31, -17.82, 0.01105, 13, 28.77, 34.41, 0.61328, 23, 113.11, 8.2, 0.35763, 2, 47, 146.08, -31.96, 0, 23, 72.9, -7.47, 1, 2, 47, 167.06, -43.12, 0, 23, 49.69, -12.59, 1, 3, 13, 141.85, -5.82, 0.85811, 23, -6.9, 6.36, 0.01421, 19, -12.2, -16.71, 0.12769, 2, 47, 222.36, -129.26, 0, 19, 25.62, 3.21, 1, 2, 47, 100.01, -57.23, 0.2, 13, 37.25, -4.44, 0.8, 4, 12, 203.74, 10.23, 0.06624, 47, 78.94, -30.11, 0.4, 13, 8.18, 13.85, 0.50583, 23, 125.21, 34.66, 0.02794, 5, 12, 184.15, 28.37, 0.28802, 46, 108.62, -91.64, 0.00052, 47, 59.35, -11.97, 0.6107, 13, -16.43, 24.22, 0.094, 23, 151.89, 33.55, 0.00676, 5, 12, 162.33, 43.43, 0.35703, 46, 86.81, -76.58, 0.01741, 47, 37.53, 3.09, 0.60136, 13, -42.07, 30.93, 0.02336, 23, 178.26, 36.23, 0.00084, 4, 12, 135.37, 55.09, 0.02134, 73, -103.13, 39.68, 0.00173, 46, 59.84, -64.92, 0.06969, 47, 10.57, 14.75, 0.90723, 4, 12, 116.13, 54.22, 0.01955, 73, -84.35, 35.39, 0.02746, 46, 40.6, -65.8, 0.10409, 47, -8.67, 13.88, 0.84889, 4, 12, 92.85, 40.81, 0.35427, 73, -58.34, 42.11, 0.10144, 46, 17.33, -79.2, 0.0762, 47, -31.95, 0.47, 0.46809, 4, 12, 76.34, 23.63, 0.60124, 73, -37.84, 54.26, 0.07952, 46, 0.81, -96.38, 0.01924, 47, -48.46, -16.71, 0.3, 4, 12, 64.19, 4.3, 0.78936, 73, -20.98, 69.65, 0.00978, 46, -11.34, -115.71, 0.00087, 47, -60.62, -36.04, 0.2, 4, 12, 62.13, -13.09, 0.53653, 77, 5.11, -17.09, 0.36743, 76, 35.04, -17.24, 0.09603, 47, -62.67, -53.43, 0, 4, 12, 51.89, 114.56, 4e-05, 73, -38.53, -39.89, 0.10591, 46, -23.64, -5.45, 0.8931, 47, -72.91, 74.22, 0.00095, 4, 12, 32.69, 94.63, 8e-05, 73, -14.7, -25.81, 0.59915, 46, -42.84, -25.39, 0.3969, 47, -92.12, 54.29, 0.00387, 3, 73, -1.84, -14.46, 0.70143, 46, -52.22, -39.75, 0.2977, 47, -101.49, 39.93, 0.00087, 2, 73, 7.31, -2.47, 0.8, 46, -57.83, -53.75, 0.2, 4, 12, 73.22, 117.78, 0.00015, 73, -59.94, -37.32, 0.02102, 46, -2.31, -2.23, 0.97658, 47, -51.58, 77.44, 0.00225, 3, 73, -88.81, 50.29, 0.00075, 46, 48.88, -78.97, 0.00251, 47, -0.4, 0.71, 0.99674, 4, 12, 192.37, 37.13, 0.472, 46, 116.84, -82.89, 0.2, 13, -11.69, 35.25, 0.30041, 23, 151.3, 21.55, 0.0276], "hull": 38, "edges": [0, 74, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 24, 52, 96, 96, 98, 98, 100, 100, 102, 60, 108], "width": 318, "height": 317}}, "yanbai": {"yanbai": {"type": "mesh", "uvs": [0.81356, 0, 0.90485, 0, 0.97512, 0.03973, 1, 0.12663, 1, 0.29006, 0.95689, 0.41661, 0.82701, 0.57649, 0.68895, 0.6557, 0.60203, 0.56893, 0.31712, 0.78432, 0.32031, 0.83696, 0.2954, 0.91665, 0.1989, 1, 0.11742, 1, 0.05136, 0.88617, 0, 0.73851, 0.00281, 0.68682, 0.18604, 0.59788, 0.34333, 0.76301, 0.61721, 0.55659, 0.61765, 0.32136, 0.67293, 0.14477], "triangles": [5, 0, 1, 4, 5, 1, 6, 21, 5, 20, 21, 6, 19, 8, 18, 14, 17, 12, 9, 11, 12, 17, 9, 12, 10, 11, 9, 9, 17, 18, 9, 18, 8, 14, 16, 17, 8, 19, 7, 7, 19, 6, 19, 20, 6, 0, 5, 21, 3, 4, 1, 1, 2, 3, 15, 16, 14, 14, 12, 13], "vertices": [2, 17, 41.51, 56.37, 0.26192, 18, 87.25, -69.13, 0.73808, 2, 17, 40.3, 44.02, 0.40265, 18, 86.04, -81.48, 0.59735, 2, 17, 36.25, 34.81, 0.49977, 18, 81.98, -90.69, 0.50023, 2, 17, 29.08, 32.11, 0.49425, 18, 74.82, -93.39, 0.50575, 2, 17, 16.23, 33.37, 0.39752, 18, 61.97, -92.13, 0.60248, 2, 17, 6.86, 40.18, 0.25298, 18, 52.59, -85.32, 0.74702, 2, 17, -3.99, 58.99, 0.43626, 18, 41.74, -66.5, 0.56374, 2, 17, -8.39, 78.29, 0.24397, 18, 37.35, -47.21, 0.75603, 2, 17, -0.41, 89.39, 0.17871, 18, 45.32, -36.11, 0.82129, 2, 17, -13.56, 129.61, 0.18467, 18, 32.17, 4.11, 0.81533, 2, 17, -17.75, 129.58, 0.19869, 18, 27.99, 4.09, 0.80131, 2, 17, -23.68, 133.57, 0.2599, 18, 22.05, 8.07, 0.7401, 2, 17, -28.95, 147.27, 0.41359, 18, 16.78, 21.78, 0.58641, 2, 17, -27.87, 158.3, 0.51982, 18, 17.86, 32.8, 0.48018, 2, 17, -18.05, 166.37, 0.5738, 18, 27.69, 40.87, 0.4262, 2, 17, -5.76, 172.18, 0.60553, 18, 39.98, 46.68, 0.39447, 2, 17, -1.73, 171.4, 0.58905, 18, 44.01, 45.9, 0.41095, 2, 17, 2.83, 145.92, 0.30849, 18, 48.57, 20.42, 0.69151, 2, 17, -12.24, 125.9, 0.14723, 18, 33.5, 0.4, 0.85277, 2, 17, 0.36, 87.24, 0.20096, 18, 46.09, -38.26, 0.79904, 2, 17, 18.85, 85.36, 0.28279, 18, 64.58, -40.13, 0.71721, 2, 17, 32, 76.52, 0.42346, 18, 77.73, -48.98, 0.57654], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 30, 32], "width": 136, "height": 79}}, "yaodai": {"yaodai": {"type": "mesh", "uvs": [0.1275, 0, 0.13558, 0, 0.17364, 0.01162, 0.15382, 0.02472, 0.19236, 0.18721, 0.3499, 0.3353, 0.74654, 0.57177, 0.89811, 0.68717, 1, 0.80646, 1, 0.82322, 0.98092, 0.88435, 0.95183, 1, 0.93027, 1, 0.79854, 0.8784, 0.51361, 0.67751, 0.25919, 0.54472, 0.22004, 0.53463, 0.17478, 0.5533, 0.13815, 0.51733, 0.08922, 0.44494, 0.09698, 0.40459, 0.013, 0.23961, 0, 0.15357, 0, 0.13149, 0.10386, 0.01925], "triangles": [19, 20, 18, 20, 4, 5, 24, 3, 4, 4, 20, 21, 23, 24, 4, 24, 0, 3, 0, 1, 3, 22, 4, 21, 4, 22, 23, 3, 1, 2, 15, 5, 14, 17, 18, 16, 15, 16, 5, 5, 16, 20, 16, 18, 20, 14, 6, 13, 14, 5, 6, 11, 12, 10, 12, 13, 10, 8, 9, 10, 10, 13, 7, 13, 6, 7, 10, 7, 8], "vertices": [1, 41, -3.33, 24.08, 1, 1, 41, -2.19, 26.05, 1, 1, 41, 5.46, 33.98, 1, 1, 41, 5.27, 27.66, 1, 2, 42, -18.45, 34.32, 0.02938, 41, 42.91, 18.45, 0.97062, 2, 42, 36.31, 23.78, 0.98954, 41, 94.41, 39.86, 0.01046, 2, 44, -8.91, 28.4, 0.24379, 43, 63.83, 28.51, 0.75621, 1, 44, 41.19, 26.99, 1, 1, 44, 79.68, 17.78, 1, 1, 44, 81.61, 14.47, 1, 1, 44, 84.02, -0.33, 1, 1, 44, 90.28, -27.33, 1, 1, 44, 85.05, -30.38, 1, 2, 44, 39.05, -24.93, 0.9996, 43, 111.13, -25.41, 0.0004, 2, 43, 18.79, -24.8, 0.91354, 42, 112.5, -25.73, 0.08646, 2, 42, 34.96, -30.51, 0.99797, 41, 123.22, -6.17, 0.00203, 2, 42, 24.08, -33.36, 0.96476, 41, 115.72, -14.55, 0.03524, 2, 42, 14.62, -42.87, 0.89394, 41, 113.07, -27.7, 0.10606, 2, 42, 1.73, -40.11, 0.77525, 41, 100.8, -32.51, 0.22475, 2, 42, -17.98, -31.43, 0.42215, 41, 79.57, -36.13, 0.57785, 2, 42, -20.16, -22.19, 0.24224, 41, 72.65, -29.63, 0.75776, 1, 41, 28.13, -31.2, 1, 1, 41, 9.24, -24.52, 1, 1, 41, 4.86, -22, 1, 1, 41, -2.83, 16.12, 1], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 28, 30], "width": 281, "height": 229}}, "yaodai1": {"yaodai1": {"type": "mesh", "uvs": [0.795, 0, 0.85516, 0, 0.87835, 0.00499, 0.89385, 0.01376, 1, 0.24715, 1, 0.28867, 0.98636, 0.31286, 0.90393, 0.28581, 0.87473, 0.28721, 0.71074, 0.37539, 0.67855, 0.39088, 0.40682, 0.65608, 0.38759, 0.6816, 0.33203, 0.77543, 0.28859, 0.88424, 0.12978, 1, 0.12468, 1, 0.12155, 0.9869, 0.10474, 0.95102, 0.06505, 0.95074, 0, 0.69245, 0, 0.6658, 0.01221, 0.57626, 0.02585, 0.55681, 0.16939, 0.41477, 0.24001, 0.41974, 0.28047, 0.37294, 0.28372, 0.33233, 0.39047, 0.2114, 0.42341, 0.18579, 0.66424, 0.00633], "triangles": [23, 20, 21, 23, 25, 20, 23, 21, 22, 19, 20, 13, 13, 18, 19, 14, 18, 13, 17, 18, 14, 15, 16, 17, 14, 15, 17, 10, 11, 28, 10, 28, 29, 26, 27, 28, 11, 26, 28, 25, 26, 11, 12, 25, 11, 13, 25, 12, 25, 13, 20, 24, 25, 23, 7, 3, 4, 3, 8, 1, 3, 1, 2, 8, 3, 7, 6, 7, 4, 5, 6, 4, 9, 30, 0, 10, 29, 30, 0, 1, 8, 9, 0, 8, 9, 10, 30], "vertices": [1, 9, 61.8, 31.72, 1, 1, 9, 75.96, 28.93, 1, 1, 9, 81.25, 26.96, 1, 1, 9, 84.59, 24.67, 1, 1, 9, 101.38, -21.92, 1, 1, 9, 99.92, -29.34, 1, 1, 9, 95.85, -33.03, 1, 1, 9, 77.4, -24.37, 1, 1, 9, 70.47, -23.27, 1, 2, 9, 28.75, -31.41, 0.89284, 8, 123.88, -41.07, 0.10716, 2, 9, 20.63, -32.68, 0.76149, 8, 116.07, -38.49, 0.23851, 2, 8, 34.98, -35.93, 0.99737, 10, -52.64, 44.76, 0.00263, 2, 8, 28.48, -36.71, 0.98694, 10, -46.09, 44.95, 0.01306, 2, 8, 7.43, -41.84, 0.82604, 10, -24.67, 48.15, 0.17396, 2, 8, -13.03, -50.92, 0.52911, 10, -3.47, 55.32, 0.47089, 2, 8, -56, -43.82, 0.10033, 10, 38.68, 44.35, 0.89967, 2, 8, -56.96, -43.06, 0.10022, 10, 39.56, 43.5, 0.89978, 2, 8, -56.07, -40.72, 0.09906, 10, 38.46, 41.26, 0.90094, 2, 8, -55.19, -33.1, 0.07206, 10, 36.89, 33.74, 0.92794, 2, 8, -62.63, -27.15, 0.0382, 10, 43.76, 27.15, 0.9618, 2, 8, -45.73, 19.41, 1e-05, 10, 22.7, -17.69, 0.99999, 2, 8, -42.73, 23.22, 0.00268, 10, 19.36, -21.2, 0.99732, 2, 8, -30.32, 34.18, 0.07775, 10, 6.01, -31, 0.92225, 2, 8, -25.56, 34.93, 0.11732, 10, 1.2, -31.31, 0.88268, 2, 8, 17.5, 33.86, 0.89483, 10, -41.58, -26.33, 0.10517, 2, 8, 30.23, 22.64, 0.98734, 10, -53.24, -14, 0.01266, 2, 8, 43.13, 23.3, 0.99996, 10, -66.15, -13.48, 4e-05, 1, 8, 48.33, 28.62, 1, 2, 9, -40.9, 12.73, 3e-05, 8, 82.08, 30, 0.99997, 2, 9, -32.24, 15.78, 0.01134, 8, 91.17, 28.76, 0.98866, 1, 9, 30.78, 36.65, 1], "hull": 31, "edges": [0, 60, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 240, "height": 182}}, "yiling": {"yiling": {"type": "mesh", "uvs": [0.5568, 0, 0.62807, 0, 0.72661, 0.04228, 0.78529, 0.03633, 0.93487, 0.04264, 0.99999, 0.07488, 1, 0.07985, 1, 0.13363, 0.98187, 0.16376, 0.80265, 0.16822, 0.72833, 0.18229, 0.6321, 0.22208, 0.60761, 0.233, 0.46183, 0.32099, 0.43341, 0.33145, 0.34096, 0.43202, 0.28052, 0.46707, 0.15649, 0.65635, 0.10226, 0.84578, 0.10412, 0.87496, 0.10411, 0.96276, 0.09428, 0.99044, 0.08131, 1, 0.03463, 1, 0, 0.97468, 0, 0.92502, 0.0614, 0.69041, 0.16166, 0.47471, 0.17326, 0.43218, 0.14786, 0.38875, 0.17685, 0.33738, 0.35504, 0.1923, 0.42979, 0.0804, 0.47586, 0.03849, 0.54182, 7e-05], "triangles": [11, 32, 33, 33, 1, 11, 1, 33, 0, 34, 0, 33, 11, 1, 2, 11, 2, 10, 10, 2, 9, 2, 3, 9, 9, 4, 8, 9, 3, 4, 7, 8, 6, 6, 8, 4, 6, 4, 5, 11, 12, 32, 13, 31, 12, 32, 12, 31, 15, 30, 14, 14, 31, 13, 15, 28, 30, 28, 29, 30, 30, 31, 14, 17, 27, 16, 27, 28, 16, 16, 28, 15, 18, 26, 17, 27, 17, 26, 22, 23, 21, 21, 23, 20, 20, 23, 24, 24, 25, 20, 20, 25, 19, 25, 18, 19, 25, 26, 18], "vertices": [1, 70, 2.11, -1.18, 1, 2, 63, 52.14, -40.03, 0.21216, 70, -5.72, 8.34, 0.78784, 2, 63, 36.82, -27.56, 0.74033, 70, -8.85, 27.84, 0.25967, 2, 63, 26.57, -27.4, 0.90258, 70, -16.38, 34.79, 0.09742, 2, 63, 1.23, -21.97, 0.99806, 70, -31.67, 55.72, 0.00194, 2, 63, -8.74, -12.73, 1, 70, -32.96, 69.25, 0, 2, 63, -8.56, -11.57, 1, 70, -32.05, 70, 0, 1, 63, -6.62, 0.97, 1, 1, 63, -2.44, 7.52, 1, 1, 63, 28.36, 3.82, 1, 2, 64, -7.37, 8.51, 0.0687, 63, 41.58, 5.14, 0.9313, 1, 64, 11.71, 7.41, 1, 1, 64, 16.67, 7.28, 1, 2, 65, 7.49, 10.26, 0.89469, 64, 49.11, 11.11, 0.10531, 2, 65, 12.85, 9, 0.99409, 64, 54.58, 10.53, 0.00591, 2, 66, -2.28, 21.13, 0.26567, 65, 40.34, 16.96, 0.73433, 2, 66, 8.97, 13.98, 0.62497, 65, 53.67, 16.61, 0.37503, 1, 67, 13.65, 7.07, 1, 2, 69, -2.41, 7.1, 0.24332, 68, 23.68, 6.85, 0.75668, 2, 69, 4.42, 7.99, 0.90113, 68, 30.41, 8.37, 0.09887, 1, 69, 25.07, 9.71, 1, 1, 69, 31.72, 8.56, 1, 1, 69, 34.16, 6.51, 1, 1, 69, 34.83, -1.53, 1, 1, 69, 29.37, -8, 1, 1, 69, 17.69, -8.97, 1, 1, 67, 25.24, -7.1, 1, 2, 66, 17.46, -4.83, 0.99963, 72, 55.62, -3.62, 0.00037, 2, 66, 7.32, -6.25, 0.67876, 72, 45.51, -5.21, 0.32124, 2, 66, -0.91, -13.78, 0.023, 72, 37.41, -12.87, 0.977, 1, 72, 24.3, -12.36, 1, 1, 71, 20.69, -0.39, 1, 1, 70, 30.73, -6.09, 1, 1, 70, 18.02, -6.22, 1, 1, 70, 3.77, -3.17, 1], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68], "width": 173, "height": 236}}, "yiling2": {"yiling2": {"type": "mesh", "uvs": [0.8569, 0, 0.87677, 0, 0.8961, 0.01363, 0.91568, 0.04509, 0.91589, 0.16855, 0.9868, 0.39703, 1, 0.41451, 1, 0.43417, 0.77682, 0.6969, 0.65319, 0.81616, 0.59691, 0.84696, 0.32618, 0.95123, 0.22746, 1, 0.18505, 1, 0.09093, 0.9112, 0.09024, 0.83303, 0.12979, 0.7064, 0.12343, 0.66468, 0.01129, 0.44741, 0, 0.40678, 0, 0.36501, 0.03411, 0.35996, 0.13465, 0.39147, 0.25436, 0.37999, 0.55929, 0.24615, 0.60166, 0.22141, 0.77895, 0.12579, 0.82429, 0.07713, 0.85076, 0.00198], "triangles": [10, 23, 9, 23, 24, 9, 9, 24, 8, 8, 5, 7, 4, 5, 26, 3, 4, 27, 3, 27, 2, 2, 27, 1, 0, 1, 28, 28, 1, 27, 26, 27, 4, 5, 25, 26, 24, 25, 8, 25, 5, 8, 5, 6, 7, 11, 12, 14, 12, 13, 14, 16, 11, 15, 23, 17, 22, 10, 11, 17, 10, 17, 23, 11, 14, 15, 11, 16, 17, 17, 18, 22, 18, 21, 22, 18, 19, 21, 19, 20, 21], "vertices": [1, 14, 81.27, -60.24, 1, 1, 14, 81.05, -63.3, 1, 1, 14, 78.89, -66.12, 1, 1, 14, 74.18, -68.8, 1, 1, 14, 56.57, -67.54, 1, 1, 14, 23.19, -76.05, 1, 1, 14, 20.55, -77.9, 1, 1, 14, 17.74, -77.69, 1, 1, 14, -17.22, -40.67, 1, 2, 13, 157.99, -38.96, 0.13491, 14, -32.84, -20.44, 0.86509, 2, 13, 148.91, -35.48, 0.30347, 14, -36.6, -11.47, 0.69653, 2, 13, 109.88, -14.57, 0.99395, 14, -48.43, 31.2, 0.00605, 1, 13, 94.51, -7.98, 1, 1, 13, 90.13, -3.13, 1, 1, 13, 89.82, 16.13, 1, 2, 13, 98.04, 23.71, 0.99901, 14, -28.92, 66.2, 0.00099, 2, 13, 115.55, 31.34, 0.96218, 14, -11.3, 58.8, 0.03782, 2, 13, 119.32, 36.07, 0.92384, 14, -5.28, 59.35, 0.07616, 2, 13, 130.77, 69.72, 0.80132, 14, 26.97, 74.3, 0.19868, 2, 13, 133.91, 74.91, 0.7998, 14, 32.89, 75.61, 0.2002, 2, 13, 138.34, 78.92, 0.799, 14, 38.85, 75.18, 0.201, 2, 13, 142.4, 75.51, 0.79732, 14, 39.18, 69.88, 0.20268, 2, 13, 149.45, 61, 0.75228, 14, 33.56, 54.77, 0.24772, 2, 13, 163.04, 48.44, 0.52703, 14, 33.85, 36.27, 0.47297, 1, 14, 49.51, -11.97, 1, 1, 14, 52.57, -18.73, 1, 1, 14, 64.21, -46.96, 1, 1, 14, 70.64, -54.43, 1, 1, 14, 81.06, -59.28, 1], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56], "width": 154, "height": 143}}, "yiling3": {"yiling3": {"type": "mesh", "uvs": [1, 0, 1, 0.39326, 0.89381, 0.53676, 0.76192, 0.63701, 0.29197, 0.93517, 0.19902, 1, 0.15298, 1, 0, 0.66436, 0, 0.55705, 0.16858, 0.34897, 0.6998, 0.06604, 0.87332, 0], "triangles": [11, 0, 1, 2, 11, 1, 10, 11, 2, 3, 10, 2, 9, 10, 3, 7, 8, 9, 4, 9, 3, 7, 9, 4, 6, 7, 4, 5, 6, 4], "vertices": [1, 14, 80.98, -64.27, 1, 1, 14, 45.68, -61.69, 1, 1, 14, 33.85, -46.34, 1, 1, 14, 26.16, -27.79, 1, 1, 14, 4.06, 37.91, 1, 1, 14, -0.83, 50.94, 1, 1, 14, -0.38, 57.19, 1, 1, 14, 31.27, 75.73, 1, 1, 14, 40.9, 75.03, 1, 1, 14, 57.91, 50.79, 1, 1, 14, 78.03, -23.12, 1, 1, 14, 82.24, -47.09, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 136, "height": 90}}, "youshou": {"youshou": {"type": "mesh", "uvs": [0.46605, 0, 0.46852, 2e-05, 0.54957, 0.03223, 0.60176, 0.05938, 0.67866, 0.11993, 0.89267, 0.32487, 0.89115, 0.36126, 0.85304, 0.41565, 0.85323, 0.41871, 0.96068, 0.46984, 0.98221, 0.48705, 1, 0.51749, 1, 0.5534, 0.98671, 0.58535, 0.94237, 0.61235, 0.87521, 0.63377, 0.85737, 0.63861, 0.83832, 0.66214, 0.79062, 0.689, 0.73658, 0.70415, 0.55684, 0.73405, 0.54669, 0.74854, 0.48881, 0.83426, 0.50708, 0.84878, 0.50711, 0.85694, 0.4888, 0.87447, 0.48016, 0.88885, 0.50975, 0.91069, 0.46898, 0.95855, 0.46927, 0.9683, 0.4515, 1, 0, 1, 0, 0.99714, 0.11059, 0.95299, 0.11227, 0.9456, 0.16748, 0.92269, 0.14927, 0.90714, 0.16171, 0.8799, 0.19802, 0.87404, 0.20349, 0.83082, 0.24274, 0.82172, 0.24814, 0.7217, 0.24033, 0.709, 0.1357, 0.67128, 0.09086, 0.63563, 0.09081, 0.60622, 0.12734, 0.57677, 0.11844, 0.57293, 0.0448, 0.55263, 0, 0.51176, 0, 0.49898, 0.01864, 0.47149, 0.01394, 0.45712, 0.01346, 0.4099, 0.02699, 0.31442, 0.05919, 0.2656, 0.09176, 0.22658, 0.10085, 0.22359, 0.09398, 0.19535, 0.13383, 0.12101, 0.18582, 0.07136, 0.26278, 0.03471, 0.3181, 0.01793, 0.40947, 0], "triangles": [59, 60, 4, 60, 3, 4, 60, 61, 3, 61, 2, 3, 61, 62, 2, 2, 0, 1, 63, 0, 2, 2, 62, 63, 15, 16, 8, 15, 12, 14, 13, 14, 12, 9, 12, 15, 9, 15, 8, 9, 11, 12, 7, 51, 55, 55, 6, 7, 8, 51, 7, 57, 55, 56, 9, 10, 11, 54, 55, 53, 55, 5, 6, 4, 5, 57, 57, 5, 55, 58, 59, 4, 4, 57, 58, 26, 28, 38, 38, 25, 26, 40, 38, 39, 25, 38, 40, 24, 22, 23, 24, 25, 22, 25, 40, 22, 22, 40, 21, 40, 41, 21, 21, 41, 20, 46, 19, 20, 20, 41, 42, 46, 20, 42, 18, 19, 46, 16, 17, 18, 42, 43, 46, 43, 44, 46, 16, 18, 46, 44, 45, 46, 8, 16, 46, 8, 46, 51, 47, 51, 46, 47, 48, 50, 51, 47, 50, 48, 49, 50, 53, 55, 51, 51, 52, 53, 31, 33, 30, 30, 33, 35, 33, 34, 35, 29, 30, 35, 28, 35, 38, 29, 35, 28, 31, 32, 33, 28, 26, 27, 35, 36, 38, 38, 36, 37], "vertices": [2, 20, -53.71, 17.89, 0, 19, 172.67, 54.93, 1, 2, 20, -53.6, 18.59, 0, 19, 173.36, 55.09, 1, 1, 19, 201.71, 38.81, 1, 2, 20, -6.37, 50.65, 0.07033, 19, 221.15, 23.88, 0.92967, 2, 20, 39.54, 66.63, 0.61928, 19, 253.6, -12.32, 0.38072, 1, 20, 193.16, 107.6, 1, 1, 20, 218.93, 103.67, 1, 2, 21, -51.04, 77.78, 0.00024, 20, 256.05, 87.64, 0.99976, 2, 21, -48.9, 78.25, 0.00061, 20, 258.23, 87.41, 0.99939, 2, 21, -18.72, 115.31, 0.06256, 20, 298.63, 112.95, 0.93744, 2, 21, -7.77, 123.68, 0.09467, 20, 311.67, 117.4, 0.90533, 2, 21, 12.67, 132.77, 0.16448, 20, 333.95, 119.51, 0.83552, 2, 21, 37.93, 137.61, 0.26594, 20, 359.43, 116.07, 0.73406, 2, 21, 61.11, 138.17, 0.3606, 20, 381.59, 109.23, 0.6394, 2, 21, 82.48, 129.35, 0.46234, 20, 399.04, 94.07, 0.53766, 2, 21, 101.16, 113.37, 0.60744, 20, 411.67, 72.98, 0.39256, 2, 21, 105.52, 109.01, 0.65836, 20, 414.41, 67.46, 0.34164, 2, 21, 123.09, 106.83, 0.76346, 20, 430.38, 59.8, 0.23654, 2, 21, 144.54, 97.04, 0.84811, 20, 447.61, 43.7, 0.15189, 2, 21, 158.1, 83.9, 0.8974, 20, 456.28, 26.93, 0.1026, 2, 21, 188.8, 37.44, 0.99239, 20, 470.6, -26.88, 0.00761, 2, 21, 199.53, 36.54, 0.99751, 20, 480.49, -31.15, 0.00249, 1, 21, 262.92, 31.82, 1, 2, 22, -53.24, 29.04, 0.00042, 21, 272.15, 38.9, 0.99958, 2, 22, -47.84, 31.24, 0.00628, 21, 277.89, 40.01, 0.99372, 2, 22, -34.23, 31.12, 0.05911, 21, 291.2, 37.23, 0.94089, 2, 22, -23.77, 32.71, 0.19831, 21, 301.78, 36.74, 0.80169, 2, 22, -12.47, 46.44, 0.46216, 21, 315.55, 47.99, 0.53784, 2, 22, 23.67, 48.55, 0.90301, 21, 351.39, 42.98, 0.09699, 2, 22, 30.1, 51.26, 0.94851, 21, 358.23, 44.37, 0.05149, 2, 22, 53.04, 55.11, 0.99948, 21, 381.48, 43.65, 0.00052, 1, 22, 101.7, -64.5, 1, 1, 22, 99.8, -65.28, 1, 2, 22, 58.6, -47.89, 0.99551, 21, 366.76, -58.44, 0.00449, 2, 22, 53.52, -49.44, 0.98903, 21, 361.46, -58.97, 0.01097, 2, 22, 32.37, -40.99, 0.87811, 21, 342.39, -46.54, 0.12189, 2, 22, 24.02, -50.01, 0.74128, 21, 332.43, -53.75, 0.25872, 2, 22, 4.61, -54.07, 0.54704, 21, 312.6, -53.92, 0.45296, 2, 22, -3.18, -46.03, 0.39588, 21, 306.53, -44.51, 0.60412, 2, 22, -32.44, -56.24, 0.04717, 21, 275.85, -48.79, 0.95283, 2, 22, -42.7, -48.29, 0.01501, 21, 267.34, -38.99, 0.98499, 1, 21, 196.71, -50.94, 1, 1, 21, 188.19, -54.84, 1, 1, 21, 167.3, -89.31, 1, 1, 21, 144.64, -106.7, 1, 1, 21, 123.96, -110.68, 1, 2, 21, 101.28, -104.38, 0.99811, 20, 342.53, -133.5, 0.00189, 2, 21, 99.06, -107.39, 0.99629, 20, 339.47, -135.66, 0.00371, 2, 21, 88.74, -130.81, 0.98248, 20, 322.23, -154.58, 0.01752, 2, 21, 62.41, -148.9, 0.94681, 20, 291.52, -163.35, 0.05319, 2, 21, 53.42, -150.62, 0.93163, 20, 282.45, -162.12, 0.06837, 2, 21, 33.09, -149.08, 0.86986, 20, 263.66, -154.2, 0.13014, 2, 21, 23.24, -152.34, 0.82256, 20, 253.29, -154.15, 0.17744, 2, 21, -9.95, -158.83, 0.64905, 20, 219.76, -149.75, 0.35095, 3, 21, -77.82, -167.88, 0.2874, 20, 152.53, -136.75, 0.71092, 19, 109.51, -194.99, 0.00168, 3, 21, -113.88, -165.41, 0.15571, 20, 119.13, -122.93, 0.83041, 19, 109.38, -158.84, 0.01388, 3, 21, -143.07, -161.51, 0.07755, 20, 92.7, -109.95, 0.87489, 19, 111.16, -129.45, 0.04756, 3, 21, -145.66, -159.36, 0.06979, 20, 90.92, -107.09, 0.8758, 19, 113.12, -126.71, 0.05441, 3, 21, -165.15, -165.09, 0.03595, 20, 70.62, -106.32, 0.85421, 19, 106, -107.68, 0.10984, 3, 21, -219.57, -163.9, 0.00354, 20, 19.4, -87.89, 0.57138, 19, 103.26, -53.32, 0.42508, 3, 21, -257.28, -155.98, 4e-05, 20, -13.83, -68.39, 0.1473, 19, 108.44, -15.13, 0.85267, 3, 21, -287.19, -139.3, 0, 20, -36.88, -43.05, 0.00011, 19, 122.93, 15.9, 0.99989, 3, 21, -301.97, -126.02, 0, 20, -46.67, -25.76, 6e-05, 19, 135.11, 31.6, 0.99994, 3, 21, -319.49, -102.76, 0, 20, -55.88, 1.86, 2e-05, 19, 157.04, 50.75, 0.99998], "hull": 64, "edges": [0, 126, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126], "width": 286, "height": 716}}, "zuoshou1": {"zuoshou1": {"type": "mesh", "uvs": [0.61026, 0, 0.88481, 0.12325, 0.97104, 0.21968, 1, 0.28243, 1, 0.30923, 0.74805, 0.54103, 0.56071, 0.58625, 0.53001, 0.68185, 0.46126, 0.89598, 0.29191, 1, 0.06818, 1, 0, 0.92527, 0, 0.6732, 0, 0.39252, 0, 0.31202, 0.41095, 0.00053, 0.4591, 0], "triangles": [5, 1, 2, 2, 3, 4, 8, 9, 11, 9, 10, 11, 11, 12, 8, 8, 12, 7, 7, 12, 6, 6, 12, 13, 6, 14, 1, 14, 15, 16, 5, 6, 1, 16, 1, 14, 5, 2, 4, 0, 1, 16, 6, 13, 14], "vertices": [2, 26, 8.74, -18.23, 0.92941, 25, 220.23, -19.39, 0.07059, 2, 26, 7.61, 7.62, 0.29264, 25, 218.27, 6.41, 0.70736, 2, 26, 12.94, 20.64, 0.30449, 25, 223.17, 19.59, 0.69551, 3, 26, 17.71, 27.59, 0.46178, 27, -31.82, 48.2, 0.00052, 25, 227.72, 26.69, 0.5377, 3, 26, 20.35, 29.87, 0.56998, 27, -28.42, 48.97, 0.00146, 25, 230.29, 29.05, 0.42855, 3, 26, 55.35, 35.42, 0.78117, 27, 5.08, 37.42, 0.21827, 28, -21.16, 38.69, 0.00056, 3, 26, 68.86, 28.76, 0.41431, 27, 13.87, 25.2, 0.51723, 28, -13.08, 25.98, 0.06847, 3, 26, 79.75, 35.15, 0.09872, 27, 26.5, 25.72, 0.50763, 28, -0.45, 25.78, 0.39365, 2, 27, 54.77, 26.89, 0.03979, 28, 27.85, 25.34, 0.96021, 2, 27, 70.72, 17.64, 0.00023, 28, 43.24, 15.2, 0.99977, 1, 28, 45.97, -1.13, 1, 1, 28, 37.21, -7.7, 1, 2, 27, 34.04, -12.79, 0.11123, 28, 4.89, -13.09, 0.88877, 3, 26, 76.87, -19.11, 0.44512, 27, -1.56, -20.83, 0.55472, 28, -31.1, -19.09, 0.00016, 2, 26, 68.95, -25.95, 0.74566, 27, -11.76, -23.13, 0.25434, 1, 26, 18.42, -29.35, 1, 1, 26, 16.04, -26.7, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 28, 30, 30, 32, 22, 24, 12, 14, 14, 16, 24, 26, 26, 28], "width": 74, "height": 130}}, "zuoshou2": {"zuoshou2": {"type": "mesh", "uvs": [0.62922, 0, 0.71658, 0.01733, 0.80391, 0.09282, 0.86067, 0.16539, 0.97231, 0.33241, 0.99996, 0.39666, 1, 0.41912, 1, 0.53706, 0.98555, 0.60321, 0.8938, 0.65617, 0.81274, 0.66226, 0.62967, 0.61761, 0.34355, 0.8212, 0.26002, 0.89825, 0.26808, 0.94821, 0.21649, 1, 0.17894, 1, 0.13162, 0.97758, 0.04955, 0.90653, 0.00826, 0.84507, 0, 0.81449, 0, 0.78896, 0.0129, 0.77038, 0.04238, 0.72898, 0.14535, 0.6438, 0.19927, 0.64833, 0.26418, 0.57378, 0.42169, 0.37108, 0.40861, 0.31215, 0.38957, 0.20339, 0.41634, 0.07934, 0.47085, 0.0323, 0.53135, 0], "triangles": [8, 9, 6, 0, 2, 27, 2, 0, 1, 3, 27, 2, 31, 32, 0, 31, 0, 28, 6, 4, 5, 7, 8, 6, 11, 3, 4, 10, 4, 6, 31, 28, 29, 30, 31, 29, 0, 27, 28, 10, 11, 4, 27, 3, 11, 6, 9, 10, 20, 21, 22, 11, 12, 26, 11, 26, 27, 25, 26, 12, 19, 20, 22, 13, 25, 12, 23, 13, 18, 19, 22, 23, 18, 19, 23, 24, 13, 23, 25, 13, 24, 17, 18, 13, 16, 17, 13, 13, 15, 16, 14, 15, 13], "vertices": [2, 25, -25.62, -43.13, 0.06493, 24, 209.57, -16.71, 0.93507, 1, 24, 212.52, 7.22, 1, 2, 25, -41.6, 6.74, 0.03575, 24, 229.23, 31.82, 0.96425, 2, 25, -39.4, 29.74, 0.15022, 24, 245.66, 48.07, 0.84978, 2, 25, -30.83, 78.89, 0.04014, 24, 283.71, 80.33, 0.95986, 2, 25, -24.71, 94.73, 0.05042, 24, 298.56, 88.59, 0.94958, 2, 25, -20.8, 98.35, 0.11169, 24, 303.87, 88.86, 0.88831, 2, 25, -0.23, 117.27, 0.28304, 24, 331.79, 90.22, 0.71696, 2, 25, 13.97, 124.99, 0.50486, 24, 347.64, 87.06, 0.49514, 2, 25, 40.1, 115.12, 0.81438, 24, 361.39, 62.74, 0.18562, 2, 25, 56.09, 99.88, 0.9665, 24, 363.91, 40.79, 0.0335, 1, 25, 82.02, 56.07, 1, 1, 25, 170.22, 31.46, 1, 2, 25, 199.04, 27.11, 0.90451, 26, -10.94, 28.93, 0.09549, 2, 25, 206.27, 36.74, 0.88872, 26, -3.4, 38.32, 0.11128, 2, 25, 224.8, 34.72, 0.81107, 26, 15.05, 35.71, 0.18893, 2, 25, 231.72, 27.2, 0.61484, 26, 21.72, 27.97, 0.38516, 2, 25, 236.52, 14.13, 0.51674, 26, 26.1, 14.75, 0.48326, 2, 25, 239.24, -13.7, 0.46932, 26, 27.92, -13.15, 0.53068, 2, 25, 236.13, -31.82, 0.53637, 26, 24.22, -31.17, 0.46363, 2, 25, 232.32, -38.39, 0.63299, 26, 20.2, -37.6, 0.36701, 2, 25, 227.86, -42.48, 0.73326, 26, 15.62, -41.55, 0.26674, 2, 25, 222.25, -42.88, 0.80484, 26, 9.99, -41.77, 0.19516, 2, 25, 209.6, -43.62, 0.92838, 26, -2.67, -42.1, 0.07162, 1, 25, 175.78, -36.68, 1, 1, 25, 166.64, -25.16, 1, 1, 25, 141.68, -24.13, 1, 2, 25, 77.32, -25.13, 0.98273, 24, 300.16, -68.81, 0.01727, 2, 25, 69.45, -37.2, 0.92643, 24, 286.39, -73.05, 0.07357, 2, 25, 53.99, -58.46, 0.77102, 24, 260.9, -79.47, 0.22898, 2, 25, 27.42, -73.01, 0.55955, 24, 231.18, -73.63, 0.44045, 2, 25, 9.18, -69.65, 0.42702, 24, 219.32, -59.37, 0.57298, 2, 25, -7.6, -62.72, 0.27611, 24, 210.87, -43.3, 0.72389], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 272, "height": 237}}, "zuoshou3": {"zuoshou3": {"type": "mesh", "uvs": [0.58819, 0, 0.69796, 0, 0.77208, 0.00941, 0.87959, 0.0617, 0.9617, 0.16516, 0.99998, 0.25096, 1, 0.25942, 1, 0.35629, 0.95531, 0.44213, 0.94206, 0.45064, 0.94363, 0.55832, 0.8863, 0.66745, 0.86245, 0.69687, 0.93676, 0.77839, 0.94583, 0.88537, 0.91629, 0.9359, 0.87097, 0.98119, 0.82036, 0.99763, 0.73777, 0.99755, 0.68529, 0.97889, 0.52761, 0.86605, 0.44004, 0.83314, 0.30766, 0.86214, 0.11537, 0.84089, 0.06105, 0.8052, 0.02797, 0.76919, 0, 0.71623, 0, 0.71301, 0.04187, 0.65661, 0.04599, 0.64485, 0.04532, 0.59013, 0.09942, 0.53426, 0.10948, 0.50207, 0.15395, 0.38833, 0.19599, 0.33165, 0.28392, 0.25154, 0.34438, 0.15194, 0.43148, 0.06869, 0.48562, 0.02995, 0.56057, 1e-05], "triangles": [22, 23, 24, 25, 28, 24, 24, 28, 22, 21, 22, 28, 6, 4, 5, 3, 4, 37, 4, 6, 9, 7, 8, 6, 6, 8, 9, 37, 38, 0, 38, 39, 0, 1, 37, 0, 29, 30, 31, 3, 1, 2, 3, 37, 1, 4, 36, 37, 4, 9, 36, 9, 34, 35, 9, 35, 36, 34, 9, 10, 10, 33, 34, 10, 32, 33, 11, 32, 10, 32, 21, 31, 11, 12, 32, 21, 29, 31, 26, 27, 28, 25, 26, 28, 29, 21, 28, 21, 32, 12, 20, 21, 12, 20, 12, 13, 20, 13, 14, 14, 19, 20, 15, 19, 14, 15, 18, 19, 15, 17, 18, 16, 17, 15], "vertices": [1, 24, -65.46, 38.91, 1, 1, 24, -66.87, 67.63, 1, 1, 24, -64.05, 87.21, 1, 1, 24, -44.53, 116.37, 1, 1, 24, -4.25, 139.87, 1, 1, 24, 29.55, 151.56, 1, 1, 24, 32.92, 151.73, 1, 1, 24, 71.63, 153.62, 1, 1, 24, 106.49, 143.6, 1, 1, 24, 110.06, 140.29, 1, 1, 24, 153.06, 142.8, 1, 1, 24, 197.39, 129.93, 1, 1, 24, 209.45, 124.26, 1, 1, 24, 241.07, 145.29, 1, 1, 24, 283.7, 149.75, 1, 2, 24, 304.26, 143.01, 0.88615, 25, -55.18, 140.19, 0.11385, 2, 24, 322.94, 132.03, 0.53817, 25, -33.81, 143.72, 0.46183, 2, 24, 330.15, 119.11, 0.50114, 25, -19.99, 138.41, 0.49886, 2, 24, 331.17, 97.49, 0.48494, 25, -5.37, 122.46, 0.51506, 2, 24, 324.39, 83.4, 0.56574, 25, -1.55, 107.29, 0.43426, 2, 24, 281.32, 39.93, 0.37875, 25, -6.8, 46.33, 0.62125, 2, 24, 269.29, 16.38, 0.17011, 25, -0.95, 20.53, 0.82989, 1, 25, 31.07, 2.86, 1, 2, 24, 276.53, -68.44, 0.20561, 25, 58.93, -39.97, 0.79439, 2, 24, 262.96, -83.35, 0.29879, 25, 58.06, -60.11, 0.70121, 2, 24, 249, -92.71, 0.38727, 25, 53.32, -76.24, 0.61273, 2, 24, 228.2, -101.06, 0.49383, 25, 42.69, -95.98, 0.50617, 2, 24, 226.91, -101.12, 0.49665, 25, 41.75, -96.85, 0.50335, 2, 24, 203.84, -91.26, 0.65368, 25, 17.72, -104.05, 0.34632, 2, 24, 199.09, -90.41, 0.695, 25, 13.52, -106.44, 0.305, 2, 24, 177.24, -91.65, 0.81249, 25, -2.47, -121.39, 0.18751, 2, 24, 154.23, -78.59, 0.91221, 25, -28.51, -126.09, 0.08779, 2, 24, 141.23, -76.58, 0.95066, 25, -39.77, -132.87, 0.04934, 2, 24, 95.23, -67.16, 0.99725, 25, -81.14, -155.1, 0.00275, 2, 24, 72.04, -57.26, 0.99995, 25, -105.28, -162.34, 5e-05, 1, 24, 38.92, -35.81, 1, 1, 24, -1.65, -21.93, 1, 1, 24, -36.02, -0.76, 1, 1, 24, -52.19, 12.65, 1, 1, 24, -65.11, 31.68, 1], "hull": 40, "edges": [0, 78, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78], "width": 262, "height": 400}}, "zuoshouzhi": {"zuoshouzhi": {"type": "mesh", "uvs": [0.61753, 0, 0.74543, 0.1062, 1, 0.21215, 1, 0.23132, 0.65187, 0.52035, 0.63402, 0.54356, 0.50044, 0.86836, 0.39553, 0.95743, 0.21666, 1, 0.10965, 1, 0.0982, 0.99993, 0, 0.95958, 0, 0.64656, 0.03223, 0.55079, 0.11561, 0.41202, 0.19898, 0.27325, 0.4472, 0.08467, 0.53785, 0], "triangles": [6, 12, 5, 6, 11, 12, 7, 11, 6, 9, 10, 11, 11, 8, 9, 7, 8, 11, 4, 15, 3, 14, 15, 4, 5, 14, 4, 13, 14, 5, 5, 12, 13, 1, 2, 3, 16, 17, 0, 1, 16, 0, 15, 1, 3, 1, 15, 16], "vertices": [2, 29, 26.95, -9.55, 1, 30, -36.61, 5.01, 0, 2, 29, 34.65, 2.74, 0.99982, 30, -24.75, 13.37, 0.00018, 2, 29, 38.97, 20.55, 0.93178, 30, -13.87, 28.11, 0.06822, 2, 29, 40.97, 21.76, 0.92215, 30, -11.56, 28.45, 0.07785, 3, 29, 80.33, 24.85, 0.01323, 30, 25.93, 16.07, 0.97878, 31, -15.22, 16.35, 0.00799, 3, 29, 83.22, 25.54, 0.00385, 30, 28.86, 15.58, 0.96649, 31, -12.3, 15.81, 0.02966, 1, 31, 27.88, 14.16, 1, 1, 31, 39.34, 10.26, 1, 1, 31, 45.67, 1.88, 1, 1, 31, 46.37, -3.53, 1, 1, 31, 46.44, -4.11, 1, 1, 31, 42.2, -9.71, 1, 2, 30, 46.04, -14.55, 0.27953, 31, 4.33, -14.63, 0.72047, 2, 30, 34.24, -14.64, 0.86142, 31, -7.47, -14.51, 0.13858, 1, 30, 16.87, -12.92, 1, 2, 29, 66.52, -10.53, 0.34124, 30, -0.5, -11.21, 0.65876, 2, 29, 40.29, -11.62, 1, 30, -25.12, -2.06, 0, 1, 29, 29.05, -13.02, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 26, 28, 28, 30], "width": 51, "height": 122}}, "zuoshouzhi1": {"zuoshouzhi1": {"type": "mesh", "uvs": [0.68562, 0, 0.87545, 0.05152, 0.92561, 0.08845, 0.95939, 0.12128, 0.95963, 0.23363, 0.90757, 0.34814, 0.90978, 0.34942, 0.76484, 0.49899, 0.7644, 0.56617, 0.78061, 0.62278, 0.94349, 0.7255, 0.94322, 0.76735, 0.92922, 0.80732, 0.88526, 0.82441, 0.82448, 0.84228, 0.75221, 0.82004, 0.73145, 0.86964, 0.67419, 0.88647, 0.56964, 0.88683, 0.49264, 0.83066, 0.4578, 0.77263, 0.4299, 0.72294, 0.43751, 0.77674, 0.45544, 0.83801, 0.45603, 0.96207, 0.37846, 1, 0.28443, 1, 0.23198, 0.96156, 0.19046, 0.87089, 0.17068, 0.76739, 0.15091, 0.66389, 0.19977, 0.57491, 0.21685, 0.52622, 0.12384, 0.44062, 0.11982, 0.38885, 0.14529, 0.31418, 0.47987, 0.0254, 0.57077, 0], "triangles": [32, 33, 34, 7, 32, 35, 32, 34, 35, 7, 35, 36, 4, 5, 0, 7, 5, 6, 7, 36, 5, 7, 21, 32, 8, 21, 7, 4, 2, 3, 21, 8, 9, 20, 21, 9, 10, 15, 9, 10, 11, 15, 15, 20, 9, 15, 11, 13, 12, 13, 11, 19, 20, 15, 14, 15, 13, 19, 15, 18, 16, 17, 15, 17, 18, 15, 31, 32, 21, 30, 31, 21, 29, 30, 21, 29, 21, 22, 28, 29, 22, 28, 22, 23, 27, 28, 23, 25, 27, 23, 25, 26, 27, 23, 24, 25, 1, 4, 0, 2, 4, 1, 37, 0, 36, 0, 5, 36], "vertices": [3, 26, 19.78, -11.12, 0.91164, 29, -57.45, 13.95, 0.00438, 25, 231.04, -11.94, 0.08398, 3, 26, 11.05, 12.18, 0.47725, 35, -43.55, -0.34, 0.08289, 25, 221.56, 11.07, 0.43986, 3, 26, 11.69, 20.88, 0.4107, 35, -40.29, 7.75, 0.16163, 25, 221.92, 19.79, 0.42767, 3, 26, 13.13, 27.61, 0.37991, 35, -36.88, 13.72, 0.23541, 25, 223.14, 26.56, 0.38468, 3, 26, 27.32, 39.88, 0.21852, 35, -19.63, 21.1, 0.62118, 25, 236.93, 39.28, 0.1603, 3, 26, 45.99, 47.52, 0.00985, 35, 0.47, 22.71, 0.91627, 25, 255.34, 47.52, 0.07388, 3, 26, 45.97, 47.86, 0.0097, 35, 0.56, 23.04, 0.91697, 25, 255.31, 47.86, 0.07333, 3, 37, -1.24, 39.15, 0.01477, 36, -5.18, 25.52, 0.07546, 35, 30.53, 16.43, 0.90977, 3, 37, 3.65, 29.05, 0.12905, 36, 5.57, 22.32, 0.31504, 35, 40.87, 20.78, 0.55591, 3, 37, 9.61, 21.44, 0.45419, 36, 15.21, 21.57, 0.34813, 35, 48.79, 26.32, 0.19768, 3, 37, 35.15, 14.87, 0.99536, 36, 37.3, 35.97, 0.0041, 35, 56.72, 51.47, 0.00054, 1, 37, 38.2, 8.59, 1, 1, 37, 39.6, 1.83, 1, 1, 37, 36, -3.11, 1, 1, 37, 30.61, -9.08, 1, 1, 37, 20.99, -9.67, 1, 1, 37, 22.35, -18.23, 1, 3, 33, 59.69, 43.79, 0.00094, 37, 17.27, -23.86, 0.99143, 36, 53.78, -3.38, 0.00764, 4, 34, 27.34, 28.48, 0.00119, 33, 59.45, 30.93, 0.01318, 37, 5.75, -29.58, 0.90368, 36, 50.22, -15.74, 0.08194, 4, 34, 16.92, 20.18, 0.01295, 33, 49.85, 21.68, 0.05855, 37, -6.89, -25.33, 0.65912, 36, 38.56, -22.19, 0.26938, 4, 34, 6.79, 17.06, 0.05153, 33, 40.06, 17.63, 0.15942, 37, -15, -18.52, 0.34067, 36, 28.05, -23.57, 0.44839, 4, 34, -1.86, 14.63, 0.22204, 33, 31.68, 14.39, 0.37881, 37, -21.74, -12.58, 0.06064, 36, 19.13, -24.53, 0.3385, 4, 34, 7.18, 14.5, 0.82153, 33, 40.69, 15.11, 0.11263, 37, -16.94, -20.23, 0.00484, 36, 28.01, -26.16, 0.061, 3, 34, 17.6, 15.49, 0.99234, 33, 50.97, 17.08, 0.00244, 36, 38.45, -26.92, 0.00522, 1, 34, 38.18, 13.12, 1, 1, 34, 43.35, 2.9, 1, 1, 34, 41.99, -8.58, 1, 1, 34, 34.85, -14.23, 1, 2, 34, 19.22, -17.53, 0.99701, 33, 55.7, -15.63, 0.00299, 2, 34, 1.77, -17.91, 0.63911, 33, 38.36, -17.66, 0.36089, 2, 34, -15.69, -18.29, 0.05601, 33, 21.03, -19.69, 0.94399, 3, 33, 6.31, -13.34, 0.91147, 32, 46.86, -9.6, 0.03822, 29, 55.63, 12.6, 0.05031, 3, 33, -1.77, -11.05, 0.38369, 32, 38.55, -10.81, 0.28785, 29, 47.59, 10.18, 0.32846, 3, 33, -16.33, -22.15, 0.00069, 32, 29.8, -26.89, 0.00115, 29, 41.29, -7.01, 0.99816, 3, 33, -24.98, -22.45, 0.00016, 32, 22.02, -30.69, 0.00026, 29, 34.16, -11.91, 0.99958, 3, 33, -37.37, -19.02, 1e-05, 32, 9.31, -32.63, 1e-05, 29, 21.87, -15.7, 0.99998, 2, 26, 39.51, -27.52, 0.88401, 29, -40.71, -5.5, 0.11599, 2, 26, 29, -21.82, 0.95652, 29, -50.13, 1.86, 0.04348], "hull": 38, "edges": [0, 74, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 34, 36, 48, 50, 50, 52, 52, 54, 54, 56, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 10, 12, 46, 48, 46, 44, 44, 42, 42, 40, 36, 38, 40, 38, 32, 34, 32, 30, 26, 28, 30, 28, 56, 58, 58, 60], "width": 123, "height": 167}}}}], "animations": {"idle": {"slots": {"biyan": {"rgba": [{"time": 4.1667, "color": "ffffff00"}, {"time": 4.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.4333, "color": "ffffffff"}, {"time": 4.5, "color": "ffffff00"}], "attachment": [{"time": 4.1667, "name": "biyan"}, {"time": 4.5}]}}, "bones": {"bone": {"translate": [{"curve": [0.444, 0, 0.889, 0, 0.444, 0, 0.889, -16.62]}, {"time": 1.3333, "y": -16.62, "curve": [1.778, 0, 2.222, 0, 1.778, -16.62, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 0, 3.111, 0, 3.556, -16.62]}, {"time": 4, "y": -16.62, "curve": [4.444, 0, 4.889, 0, 4.444, -16.62, 4.889, 0]}, {"time": 5.3333}]}, "bone2": {"scale": [{"curve": [0.444, 1, 0.889, 0.93, 0.444, 1, 0.889, 1]}, {"time": 1.3333, "x": 0.93, "curve": [1.778, 0.93, 2.222, 1, 1.778, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.111, 1, 3.556, 0.93, 3.111, 1, 3.556, 1]}, {"time": 4, "x": 0.93, "curve": [4.444, 0.93, 4.889, 1, 4.444, 1, 4.889, 1]}, {"time": 5.3333}]}, "bone4": {"rotate": [{"curve": [0.444, 0, 0.889, 4.1]}, {"time": 1.3333, "value": 4.1, "curve": [1.778, 4.1, 2.222, 0]}, {"time": 2.6667, "curve": [3.111, 0, 3.556, 4.1]}, {"time": 4, "value": 4.1, "curve": [4.444, 4.1, 4.889, 0]}, {"time": 5.3333}]}, "bone9": {"rotate": [{"curve": [0.447, 0, 0.895, 1.82]}, {"time": 1.3333, "value": 1.82, "curve": [1.784, 1.82, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 1.82]}, {"time": 4, "value": 1.82, "curve": [4.45, 1.82, 4.892, 0]}, {"time": 5.3333}], "scale": [{"curve": [0.447, 1, 0.895, 0.968, 0.447, 1, 0.895, 1]}, {"time": 1.3333, "x": 0.968, "curve": [1.784, 0.968, 2.222, 1, 1.784, 1, 2.222, 1]}, {"time": 2.6667, "curve": [3.114, 1, 3.561, 0.968, 3.114, 1, 3.561, 1]}, {"time": 4, "x": 0.968, "curve": [4.45, 0.968, 4.892, 1, 4.45, 1, 4.892, 1]}, {"time": 5.3333}]}, "bone10": {"rotate": [{"curve": [0.447, 0, 0.895, -1.29]}, {"time": 1.3333, "value": -1.29, "curve": [1.784, -1.29, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -1.29]}, {"time": 4, "value": -1.29, "curve": [4.45, -1.29, 4.892, 0]}, {"time": 5.3333}], "translate": [{"curve": [0.447, 0, 0.895, -3.11, 0.447, 0, 0.895, 0.2]}, {"time": 1.3333, "x": -3.11, "y": 0.2, "curve": [1.784, -3.11, 2.222, 0, 1.784, 0.2, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -3.11, 3.114, 0, 3.561, 0.2]}, {"time": 4, "x": -3.11, "y": 0.2, "curve": [4.45, -3.11, 4.892, 0, 4.45, 0.2, 4.892, 0]}, {"time": 5.3333}]}, "bone11": {"rotate": [{"curve": [0.447, 0, 0.895, -0.67]}, {"time": 1.3333, "value": -0.67, "curve": [1.784, -0.67, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -0.67]}, {"time": 4, "value": -0.67, "curve": [4.45, -0.67, 4.892, 0]}, {"time": 5.3333}], "translate": [{"curve": [0.447, 0, 0.895, -5.78, 0.447, 0, 0.895, -2.34]}, {"time": 1.3333, "x": -5.78, "y": -2.34, "curve": [1.784, -5.78, 2.222, 0, 1.784, -2.34, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -5.78, 3.114, 0, 3.561, -2.34]}, {"time": 4, "x": -5.78, "y": -2.34, "curve": [4.45, -5.78, 4.892, 0, 4.45, -2.34, 4.892, 0]}, {"time": 5.3333}]}, "bone12": {"rotate": [{"curve": [0.447, 0, 0.895, -0.64]}, {"time": 1.3333, "value": -0.64, "curve": [1.784, -0.64, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -0.64]}, {"time": 4, "value": -0.64, "curve": [4.45, -0.64, 4.892, 0]}, {"time": 5.3333}]}, "bone13": {"rotate": [{"curve": [0.447, 0, 0.895, -1.11]}, {"time": 1.3333, "value": -1.11, "curve": [1.784, -1.11, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -1.11]}, {"time": 4, "value": -1.11, "curve": [4.45, -1.11, 4.892, 0]}, {"time": 5.3333}]}, "bone16": {"translate": [{"curve": [0.447, 0, 0.895, 8.82, 0.447, 0, 0.895, -2.26]}, {"time": 1.3333, "x": 8.82, "y": -2.26, "curve": [1.784, 8.82, 2.222, 0, 1.784, -2.26, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 8.82, 3.114, 0, 3.561, -2.26]}, {"time": 4, "x": 8.82, "y": -2.26, "curve": [4.45, 8.82, 4.892, 0, 4.45, -2.26, 4.892, 0]}, {"time": 5.3333}]}, "bone17": {"rotate": [{"curve": [0.447, 0, 0.895, 0.83]}, {"time": 1.3333, "value": 0.83, "curve": [1.784, 0.83, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 0.83]}, {"time": 4, "value": 0.83, "curve": [4.45, 0.83, 4.892, 0]}, {"time": 5.3333}]}, "bone18": {"rotate": [{"curve": [0.447, 0, 0.895, 2.84]}, {"time": 1.3333, "value": 2.84, "curve": [1.784, 2.84, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.84]}, {"time": 4, "value": 2.84, "curve": [4.45, 2.84, 4.892, 0]}, {"time": 5.3333}]}, "bone19": {"rotate": [{"curve": [0.447, 0, 0.895, -5.64]}, {"time": 1.3333, "value": -5.64, "curve": [1.784, -5.64, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -5.64]}, {"time": 4, "value": -5.64, "curve": [4.45, -5.64, 4.892, 0]}, {"time": 5.3333}]}, "bone21": {"rotate": [{"curve": [0.447, 0, 0.895, -1.44]}, {"time": 1.3333, "value": -1.44, "curve": [1.784, -1.44, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -1.44]}, {"time": 4, "value": -1.44, "curve": [4.45, -1.44, 4.892, 0]}, {"time": 5.3333}]}, "bone22": {"rotate": [{"curve": [0.447, 0, 0.895, -0.66]}, {"time": 1.3333, "value": -0.66, "curve": [1.784, -0.66, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -0.66]}, {"time": 4, "value": -0.66, "curve": [4.45, -0.66, 4.892, 0]}, {"time": 5.3333}]}, "bone23": {"rotate": [{"curve": [0.447, 0, 0.895, 1.86]}, {"time": 1.3333, "value": 1.86, "curve": [1.784, 1.86, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 1.86]}, {"time": 4, "value": 1.86, "curve": [4.45, 1.86, 4.892, 0]}, {"time": 5.3333}]}, "bone24": {"rotate": [{"curve": [0.447, 0, 0.895, 8.51]}, {"time": 1.3333, "value": 8.51, "curve": [1.784, 8.51, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 8.51]}, {"time": 4, "value": 8.51, "curve": [4.45, 8.51, 4.892, 0]}, {"time": 5.3333}]}, "bone36": {"rotate": [{"curve": [0.447, 0, 0.895, -3.66]}, {"time": 1.3333, "value": -3.66, "curve": [1.784, -3.66, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -3.66]}, {"time": 4, "value": -3.66, "curve": [4.45, -3.66, 4.892, 0]}, {"time": 5.3333}]}, "bone37": {"rotate": [{"curve": [0.447, 0, 0.895, 1.62]}, {"time": 1.3333, "value": 1.62, "curve": [1.784, 1.62, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 1.62]}, {"time": 4, "value": 1.62, "curve": [4.45, 1.62, 4.892, 0]}, {"time": 5.3333}]}, "bone42": {"rotate": [{"value": -4.16}]}, "bone43": {"translate": [{"x": -0.52, "y": -0.21, "curve": [0.058, -0.23, 0.112, 0, 0.058, -0.09, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -10.92, 0.614, 0, 1.061, -4.5]}, {"time": 1.5, "x": -10.92, "y": -4.5, "curve": [1.895, -10.92, 2.278, -2.48, 1.895, -4.5, 2.278, -1.02]}, {"time": 2.6667, "x": -0.52, "y": -0.21, "curve": [2.724, -0.23, 2.779, 0, 2.724, -0.09, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -10.92, 3.281, 0, 3.728, -4.5]}, {"time": 4.1667, "x": -10.92, "y": -4.5, "curve": [4.561, -10.92, 4.949, -2.59, 4.561, -4.5, 4.949, -1.07]}, {"time": 5.3333, "x": -0.52, "y": -0.21}]}, "bone44": {"translate": [{"x": -0.74, "y": -0.3, "curve": [0.058, -0.32, 0.112, 0, 0.058, -0.13, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -15.47, 0.614, 0, 1.061, -6.38]}, {"time": 1.5, "x": -15.47, "y": -6.38, "curve": [1.895, -15.47, 2.278, -3.51, 1.895, -6.38, 2.278, -1.45]}, {"time": 2.6667, "x": -0.74, "y": -0.3, "curve": [2.724, -0.32, 2.779, 0, 2.724, -0.13, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -15.47, 3.281, 0, 3.728, -6.38]}, {"time": 4.1667, "x": -15.47, "y": -6.38, "curve": [4.561, -15.47, 4.949, -3.67, 4.561, -6.38, 4.949, -1.51]}, {"time": 5.3333, "x": -0.74, "y": -0.3}]}, "bone45": {"rotate": [{"curve": [0.447, 0, 0.895, 1.52]}, {"time": 1.3333, "value": 1.52, "curve": [1.784, 1.52, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 1.52]}, {"time": 4, "value": 1.52, "curve": [4.45, 1.52, 4.892, 0]}, {"time": 5.3333}]}, "bone46": {"rotate": [{"value": 0.07, "curve": [0.058, 0.03, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, 1.52]}, {"time": 1.5, "value": 1.52, "curve": [1.895, 1.52, 2.278, 0.34]}, {"time": 2.6667, "value": 0.07, "curve": [2.724, 0.03, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, 1.52]}, {"time": 4.1667, "value": 1.52, "curve": [4.561, 1.52, 4.949, 0.36]}, {"time": 5.3333, "value": 0.07}]}, "bone47": {"rotate": [{"value": 0.25, "curve": [0.113, 0.1, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 1.52]}, {"time": 1.6667, "value": 1.52, "curve": [2.004, 1.52, 2.333, 0.67]}, {"time": 2.6667, "value": 0.25, "curve": [2.779, 0.1, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 1.52]}, {"time": 4.3333, "value": 1.52, "curve": [4.671, 1.52, 5.004, 0.67]}, {"time": 5.3333, "value": 0.25}]}, "bone48": {"rotate": [{"curve": [0.447, 0, 0.895, 0.41]}, {"time": 1.3333, "value": 0.41, "curve": [1.784, 0.41, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 0.41]}, {"time": 4, "value": 0.41, "curve": [4.45, 0.41, 4.892, 0]}, {"time": 5.3333}]}, "bone49": {"rotate": [{"value": 0.06, "curve": [0.058, 0.02, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, 1.17]}, {"time": 1.5, "value": 1.17, "curve": [1.895, 1.17, 2.278, 0.27]}, {"time": 2.6667, "value": 0.06, "curve": [2.724, 0.02, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, 1.17]}, {"time": 4.1667, "value": 1.17, "curve": [4.561, 1.17, 4.949, 0.28]}, {"time": 5.3333, "value": 0.06}]}, "bone50": {"rotate": [{"value": 0.19, "curve": [0.113, 0.08, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 1.17]}, {"time": 1.6667, "value": 1.17, "curve": [2.004, 1.17, 2.333, 0.51]}, {"time": 2.6667, "value": 0.19, "curve": [2.779, 0.08, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 1.17]}, {"time": 4.3333, "value": 1.17, "curve": [4.671, 1.17, 5.004, 0.52]}, {"time": 5.3333, "value": 0.19}]}, "bone51": {"rotate": [{"value": 0.38, "curve": [0.167, 0.17, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, 1.17]}, {"time": 1.8333, "value": 1.17, "curve": [2.114, 1.17, 2.389, 0.72]}, {"time": 2.6667, "value": 0.38, "curve": [2.834, 0.17, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, 1.17]}, {"time": 4.5, "value": 1.17, "curve": [4.781, 1.17, 5.058, 0.72]}, {"time": 5.3333, "value": 0.38}]}, "bone54": {"rotate": [{"curve": [0.447, 0, 0.895, 4.62]}, {"time": 1.3333, "value": 4.62, "curve": [1.784, 4.62, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 4.62]}, {"time": 4, "value": 4.62, "curve": [4.45, 4.62, 4.892, 0]}, {"time": 5.3333}]}, "bone55": {"rotate": [{"value": 0.21, "curve": [0.058, 0.09, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, 4.35]}, {"time": 1.5, "value": 4.35, "curve": [1.895, 4.35, 2.278, 0.99]}, {"time": 2.6667, "value": 0.21, "curve": [2.724, 0.09, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, 4.35]}, {"time": 4.1667, "value": 4.35, "curve": [4.561, 4.35, 4.949, 1.03]}, {"time": 5.3333, "value": 0.21}]}, "bone56": {"rotate": [{"value": 0.55, "curve": [0.113, 0.23, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 3.43]}, {"time": 1.6667, "value": 3.43, "curve": [2.004, 3.43, 2.333, 1.51]}, {"time": 2.6667, "value": 0.55, "curve": [2.779, 0.23, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 3.43]}, {"time": 4.3333, "value": 3.43, "curve": [4.671, 3.43, 5.004, 1.52]}, {"time": 5.3333, "value": 0.55}]}, "bone57": {"rotate": [{"curve": [0.447, 0, 0.895, -2.5]}, {"time": 1.3333, "value": -2.5, "curve": [1.784, -2.5, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -2.5]}, {"time": 4, "value": -2.5, "curve": [4.45, -2.5, 4.892, 0]}, {"time": 5.3333}]}, "bone58": {"rotate": [{"value": -0.12, "curve": [0.058, -0.05, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -2.5]}, {"time": 1.5, "value": -2.5, "curve": [1.895, -2.5, 2.278, -0.57]}, {"time": 2.6667, "value": -0.12, "curve": [2.724, -0.05, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -2.5]}, {"time": 4.1667, "value": -2.5, "curve": [4.561, -2.5, 4.949, -0.59]}, {"time": 5.3333, "value": -0.12}]}, "bone59": {"rotate": [{"value": -0.4, "curve": [0.113, -0.17, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, -2.5]}, {"time": 1.6667, "value": -2.5, "curve": [2.004, -2.5, 2.333, -1.1]}, {"time": 2.6667, "value": -0.4, "curve": [2.779, -0.17, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, -2.5]}, {"time": 4.3333, "value": -2.5, "curve": [4.671, -2.5, 5.004, -1.11]}, {"time": 5.3333, "value": -0.4}]}, "bone63": {"rotate": [{"curve": [0.447, 0, 0.895, 3.81]}, {"time": 1.3333, "value": 3.81, "curve": [1.784, 3.81, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 3.81]}, {"time": 4, "value": 3.81, "curve": [4.45, 3.81, 4.892, 0]}, {"time": 5.3333}]}, "bone64": {"rotate": [{"value": 0.18, "curve": [0.058, 0.08, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, 3.81]}, {"time": 1.5, "value": 3.81, "curve": [1.895, 3.81, 2.278, 0.87]}, {"time": 2.6667, "value": 0.18, "curve": [2.724, 0.08, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, 3.81]}, {"time": 4.1667, "value": 3.81, "curve": [4.561, 3.81, 4.949, 0.9]}, {"time": 5.3333, "value": 0.18}]}, "bone65": {"rotate": [{"value": 0.62, "curve": [0.113, 0.26, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 3.81]}, {"time": 1.6667, "value": 3.81, "curve": [2.004, 3.81, 2.333, 1.67]}, {"time": 2.6667, "value": 0.62, "curve": [2.779, 0.26, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 3.81]}, {"time": 4.3333, "value": 3.81, "curve": [4.671, 3.81, 5.004, 1.69]}, {"time": 5.3333, "value": 0.62}]}, "bone66": {"rotate": [{"value": 1.22, "curve": [0.167, 0.55, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, 3.81]}, {"time": 1.8333, "value": 3.81, "curve": [2.114, 3.81, 2.389, 2.34]}, {"time": 2.6667, "value": 1.22, "curve": [2.834, 0.55, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, 3.81]}, {"time": 4.5, "value": 3.81, "curve": [4.781, 3.81, 5.058, 2.34]}, {"time": 5.3333, "value": 1.22}]}, "bone70": {"rotate": [{"curve": [0.447, 0, 0.895, -1.75]}, {"time": 1.3333, "value": -1.75, "curve": [1.784, -1.75, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -1.75]}, {"time": 4, "value": -1.75, "curve": [4.45, -1.75, 4.892, 0]}, {"time": 5.3333}]}, "bone71": {"rotate": [{"value": -0.08, "curve": [0.058, -0.04, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -1.75]}, {"time": 1.5, "value": -1.75, "curve": [1.895, -1.75, 2.278, -0.4]}, {"time": 2.6667, "value": -0.08, "curve": [2.724, -0.04, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -1.75]}, {"time": 4.1667, "value": -1.75, "curve": [4.561, -1.75, 4.949, -0.42]}, {"time": 5.3333, "value": -0.08}]}, "bone72": {"rotate": [{"value": -0.28, "curve": [0.113, -0.12, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, -1.75]}, {"time": 1.6667, "value": -1.75, "curve": [2.004, -1.75, 2.333, -0.77]}, {"time": 2.6667, "value": -0.28, "curve": [2.779, -0.12, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, -1.75]}, {"time": 4.3333, "value": -1.75, "curve": [4.671, -1.75, 5.004, -0.78]}, {"time": 5.3333, "value": -0.28}]}, "bone73": {"rotate": [{"curve": [0.447, 0, 0.895, 2.87]}, {"time": 1.3333, "value": 2.87, "curve": [1.784, 2.87, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.87]}, {"time": 4, "value": 2.87, "curve": [4.45, 2.87, 4.892, 0]}, {"time": 5.3333}]}, "bone74": {"rotate": [{"curve": [0.447, 0, 0.895, 2.87]}, {"time": 1.3333, "value": 2.87, "curve": [1.784, 2.87, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.87]}, {"time": 4, "value": 2.87, "curve": [4.45, 2.87, 4.892, 0]}, {"time": 5.3333}]}, "bone75": {"rotate": [{"curve": [0.447, 0, 0.895, 2.87]}, {"time": 1.3333, "value": 2.87, "curve": [1.784, 2.87, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.87]}, {"time": 4, "value": 2.87, "curve": [4.45, 2.87, 4.892, 0]}, {"time": 5.3333}]}, "bone83": {"rotate": [{"curve": [0.447, 0, 0.895, -3.17]}, {"time": 1.3333, "value": -3.17, "curve": [1.784, -3.17, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -3.17]}, {"time": 4, "value": -3.17, "curve": [4.45, -3.17, 4.892, 0]}, {"time": 5.3333}]}, "bone84": {"rotate": [{"value": -0.15, "curve": [0.058, -0.07, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -3.17]}, {"time": 1.5, "value": -3.17, "curve": [1.895, -3.17, 2.278, -0.72]}, {"time": 2.6667, "value": -0.15, "curve": [2.724, -0.07, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -3.17]}, {"time": 4.1667, "value": -3.17, "curve": [4.561, -3.17, 4.949, -0.75]}, {"time": 5.3333, "value": -0.15}]}, "bone85": {"rotate": [{"value": -0.51, "curve": [0.113, -0.22, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, -3.17]}, {"time": 1.6667, "value": -3.17, "curve": [2.004, -3.17, 2.333, -1.39]}, {"time": 2.6667, "value": -0.51, "curve": [2.779, -0.22, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, -3.17]}, {"time": 4.3333, "value": -3.17, "curve": [4.671, -3.17, 5.004, -1.41]}, {"time": 5.3333, "value": -0.51}]}, "bone86": {"rotate": [{"value": -1.02, "curve": [0.167, -0.46, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, -3.17]}, {"time": 1.8333, "value": -3.17, "curve": [2.114, -3.17, 2.389, -1.95]}, {"time": 2.6667, "value": -1.02, "curve": [2.834, -0.46, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, -3.17]}, {"time": 4.5, "value": -3.17, "curve": [4.781, -3.17, 5.058, -1.95]}, {"time": 5.3333, "value": -1.02}]}, "bone87": {"rotate": [{"value": -1.14, "curve": [0.225, -0.57, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, -2.26]}, {"time": 2, "value": -2.26, "curve": [2.225, -2.26, 2.444, -1.7]}, {"time": 2.6667, "value": -1.14, "curve": [2.892, -0.57, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, -2.26]}, {"time": 4.6667, "value": -2.26, "curve": [4.892, -2.26, 5.115, -1.71]}, {"time": 5.3333, "value": -1.14}]}, "bone88": {"rotate": [{"value": -1.55, "curve": [0.28, -0.89, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, -2.26]}, {"time": 2.1667, "value": -2.26, "curve": [2.335, -2.26, 2.5, -1.95]}, {"time": 2.6667, "value": -1.55, "curve": [2.947, -0.89, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, -2.26]}, {"time": 4.8333, "value": -2.26, "curve": [5.001, -2.26, 5.168, -1.95]}, {"time": 5.3333, "value": -1.55}]}, "bone89": {"rotate": [{"value": -13.99, "curve": [0.338, -9.32, 0.669, 0]}, {"time": 1, "curve": [1.447, 0, 1.895, -16.61]}, {"time": 2.3333, "value": -16.61, "curve": [2.446, -16.61, 2.556, -15.53]}, {"time": 2.6667, "value": -13.99, "curve": [3.004, -9.32, 3.336, 0]}, {"time": 3.6667, "curve": [4.114, 0, 4.561, -16.61]}, {"time": 5, "value": -16.61, "curve": [5.113, -16.61, 5.225, -15.55]}, {"time": 5.3333, "value": -13.99}]}, "bone90": {"rotate": [{"value": -15.84, "curve": [0.396, -12.63, 0.781, 0]}, {"time": 1.1667, "curve": [1.614, 0, 2.061, -16.61]}, {"time": 2.5, "value": -16.61, "curve": [2.557, -16.61, 2.611, -16.29]}, {"time": 2.6667, "value": -15.84, "curve": [3.062, -12.63, 3.448, 0]}, {"time": 3.8333, "curve": [4.281, 0, 4.728, -16.61]}, {"time": 5.1667, "value": -16.61, "curve": [5.224, -16.61, 5.281, -16.31]}, {"time": 5.3333, "value": -15.84}]}, "bone91": {"rotate": [{"value": -16.61, "curve": [0.45, -16.61, 0.892, 0]}, {"time": 1.3333, "curve": [1.781, 0, 2.222, -16.61]}, {"time": 2.6667, "value": -16.61, "curve": [3.117, -16.61, 3.559, 0]}, {"time": 4, "curve": [4.447, 0, 4.895, -16.61]}, {"time": 5.3333, "value": -16.61}]}, "bone92": {"rotate": [{"value": -0.1, "curve": [0.058, -0.04, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -2.12]}, {"time": 1.5, "value": -2.12, "curve": [1.895, -2.12, 2.278, -0.48]}, {"time": 2.6667, "value": -0.1, "curve": [2.724, -0.04, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -2.12]}, {"time": 4.1667, "value": -2.12, "curve": [4.561, -2.12, 4.949, -0.5]}, {"time": 5.3333, "value": -0.1}]}, "bone93": {"rotate": [{"value": -0.34, "curve": [0.113, -0.14, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, -2.12]}, {"time": 1.6667, "value": -2.12, "curve": [2.004, -2.12, 2.333, -0.93]}, {"time": 2.6667, "value": -0.34, "curve": [2.779, -0.14, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, -2.12]}, {"time": 4.3333, "value": -2.12, "curve": [4.671, -2.12, 5.004, -0.94]}, {"time": 5.3333, "value": -0.34}]}, "bone94": {"rotate": [{"value": -0.68, "curve": [0.167, -0.3, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, -2.12]}, {"time": 1.8333, "value": -2.12, "curve": [2.114, -2.12, 2.389, -1.3]}, {"time": 2.6667, "value": -0.68, "curve": [2.834, -0.3, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, -2.12]}, {"time": 4.5, "value": -2.12, "curve": [4.781, -2.12, 5.058, -1.3]}, {"time": 5.3333, "value": -0.68}]}, "bone95": {"rotate": [{"value": -2.63, "curve": [0.225, -1.32, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, -5.22]}, {"time": 2, "value": -5.22, "curve": [2.225, -5.22, 2.444, -3.92]}, {"time": 2.6667, "value": -2.63, "curve": [2.892, -1.32, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, -5.22]}, {"time": 4.6667, "value": -5.22, "curve": [4.892, -5.22, 5.115, -3.93]}, {"time": 5.3333, "value": -2.63}]}, "bone96": {"rotate": [{"value": -3.58, "curve": [0.28, -2.05, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, -5.22]}, {"time": 2.1667, "value": -5.22, "curve": [2.335, -5.22, 2.5, -4.49]}, {"time": 2.6667, "value": -3.58, "curve": [2.947, -2.05, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, -5.22]}, {"time": 4.8333, "value": -5.22, "curve": [5.001, -5.22, 5.168, -4.49]}, {"time": 5.3333, "value": -3.58}]}, "bone97": {"rotate": [{"value": -4.4, "curve": [0.338, -2.93, 0.669, 0]}, {"time": 1, "curve": [1.447, 0, 1.895, -5.22]}, {"time": 2.3333, "value": -5.22, "curve": [2.446, -5.22, 2.556, -4.88]}, {"time": 2.6667, "value": -4.4, "curve": [3.004, -2.93, 3.336, 0]}, {"time": 3.6667, "curve": [4.114, 0, 4.561, -5.22]}, {"time": 5, "value": -5.22, "curve": [5.113, -5.22, 5.225, -4.89]}, {"time": 5.3333, "value": -4.4}]}, "bone98": {"rotate": [{"curve": [0.447, 0, 0.895, -2.03]}, {"time": 1.3333, "value": -2.03, "curve": [1.784, -2.03, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -2.03]}, {"time": 4, "value": -2.03, "curve": [4.45, -2.03, 4.892, 0]}, {"time": 5.3333}]}, "bone99": {"rotate": [{"value": -0.1, "curve": [0.058, -0.04, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -2.03]}, {"time": 1.5, "value": -2.03, "curve": [1.895, -2.03, 2.278, -0.46]}, {"time": 2.6667, "value": -0.1, "curve": [2.724, -0.04, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -2.03]}, {"time": 4.1667, "value": -2.03, "curve": [4.561, -2.03, 4.949, -0.48]}, {"time": 5.3333, "value": -0.1}]}, "bone100": {"rotate": [{"value": -0.33, "curve": [0.113, -0.14, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, -2.03]}, {"time": 1.6667, "value": -2.03, "curve": [2.004, -2.03, 2.333, -0.89]}, {"time": 2.6667, "value": -0.33, "curve": [2.779, -0.14, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, -2.03]}, {"time": 4.3333, "value": -2.03, "curve": [4.671, -2.03, 5.004, -0.9]}, {"time": 5.3333, "value": -0.33}]}, "bone101": {"rotate": [{"value": -0.65, "curve": [0.167, -0.29, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, -2.03]}, {"time": 1.8333, "value": -2.03, "curve": [2.114, -2.03, 2.389, -1.25]}, {"time": 2.6667, "value": -0.65, "curve": [2.834, -0.29, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, -2.03]}, {"time": 4.5, "value": -2.03, "curve": [4.781, -2.03, 5.058, -1.25]}, {"time": 5.3333, "value": -0.65}]}, "bone102": {"rotate": [{"value": -1.02, "curve": [0.225, -0.52, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, -2.03]}, {"time": 2, "value": -2.03, "curve": [2.225, -2.03, 2.444, -1.53]}, {"time": 2.6667, "value": -1.02, "curve": [2.892, -0.52, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, -2.03]}, {"time": 4.6667, "value": -2.03, "curve": [4.892, -2.03, 5.115, -1.53]}, {"time": 5.3333, "value": -1.02}]}, "bone103": {"rotate": [{"value": -0.02, "curve": [0.058, -0.01, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -0.52]}, {"time": 1.5, "value": -0.52, "curve": [1.895, -0.52, 2.278, -0.12]}, {"time": 2.6667, "value": -0.02, "curve": [2.724, -0.01, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -0.52]}, {"time": 4.1667, "value": -0.52, "curve": [4.561, -0.52, 4.949, -0.12]}, {"time": 5.3333, "value": -0.02}]}, "bone104": {"rotate": [{"value": -0.08, "curve": [0.113, -0.04, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, -0.52]}, {"time": 1.6667, "value": -0.52, "curve": [2.004, -0.52, 2.333, -0.23]}, {"time": 2.6667, "value": -0.08, "curve": [2.779, -0.04, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, -0.52]}, {"time": 4.3333, "value": -0.52, "curve": [4.671, -0.52, 5.004, -0.23]}, {"time": 5.3333, "value": -0.08}]}, "bone105": {"rotate": [{"value": -0.17, "curve": [0.167, -0.07, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, -0.52]}, {"time": 1.8333, "value": -0.52, "curve": [2.114, -0.52, 2.389, -0.32]}, {"time": 2.6667, "value": -0.17, "curve": [2.834, -0.07, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, -0.52]}, {"time": 4.5, "value": -0.52, "curve": [4.781, -0.52, 5.058, -0.32]}, {"time": 5.3333, "value": -0.17}]}, "bone106": {"rotate": [{"value": -0.26, "curve": [0.225, -0.13, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, -0.52]}, {"time": 2, "value": -0.52, "curve": [2.225, -0.52, 2.444, -0.39]}, {"time": 2.6667, "value": -0.26, "curve": [2.892, -0.13, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, -0.52]}, {"time": 4.6667, "value": -0.52, "curve": [4.892, -0.52, 5.115, -0.39]}, {"time": 5.3333, "value": -0.26}]}, "bone107": {"rotate": [{"curve": [0.447, 0, 0.895, -0.58]}, {"time": 1.3333, "value": -0.58, "curve": [1.784, -0.58, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -0.58]}, {"time": 4, "value": -0.58, "curve": [4.45, -0.58, 4.892, 0]}, {"time": 5.3333}]}, "bone108": {"rotate": [{"value": -0.03, "curve": [0.058, -0.01, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -0.58]}, {"time": 1.5, "value": -0.58, "curve": [1.895, -0.58, 2.278, -0.13]}, {"time": 2.6667, "value": -0.03, "curve": [2.724, -0.01, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -0.58]}, {"time": 4.1667, "value": -0.58, "curve": [4.561, -0.58, 4.949, -0.14]}, {"time": 5.3333, "value": -0.03}]}, "bone109": {"rotate": [{"value": -0.09, "curve": [0.113, -0.04, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, -0.58]}, {"time": 1.6667, "value": -0.58, "curve": [2.004, -0.58, 2.333, -0.26]}, {"time": 2.6667, "value": -0.09, "curve": [2.779, -0.04, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, -0.58]}, {"time": 4.3333, "value": -0.58, "curve": [4.671, -0.58, 5.004, -0.26]}, {"time": 5.3333, "value": -0.09}]}, "bone110": {"rotate": [{"value": -0.19, "curve": [0.167, -0.08, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, -0.58]}, {"time": 1.8333, "value": -0.58, "curve": [2.114, -0.58, 2.389, -0.36]}, {"time": 2.6667, "value": -0.19, "curve": [2.834, -0.08, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, -0.58]}, {"time": 4.5, "value": -0.58, "curve": [4.781, -0.58, 5.058, -0.36]}, {"time": 5.3333, "value": -0.19}]}, "bone111": {"rotate": [{"value": -0.29, "curve": [0.225, -0.15, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, -0.58]}, {"time": 2, "value": -0.58, "curve": [2.225, -0.58, 2.444, -0.44]}, {"time": 2.6667, "value": -0.29, "curve": [2.892, -0.15, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, -0.58]}, {"time": 4.6667, "value": -0.58, "curve": [4.892, -0.58, 5.115, -0.44]}, {"time": 5.3333, "value": -0.29}]}, "bone112": {"rotate": [{"curve": [0.447, 0, 0.895, 4.16]}, {"time": 1.3333, "value": 4.16, "curve": [1.784, 4.16, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 4.16]}, {"time": 4, "value": 4.16, "curve": [4.45, 4.16, 4.892, 0]}, {"time": 5.3333}]}, "bone113": {"rotate": [{"value": 0.2, "curve": [0.058, 0.09, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, 4.16]}, {"time": 1.5, "value": 4.16, "curve": [1.895, 4.16, 2.278, 0.94]}, {"time": 2.6667, "value": 0.2, "curve": [2.724, 0.09, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, 4.16]}, {"time": 4.1667, "value": 4.16, "curve": [4.561, 4.16, 4.949, 0.99]}, {"time": 5.3333, "value": 0.2}]}, "bone114": {"rotate": [{"value": 1, "curve": [0.113, 0.42, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 6.2]}, {"time": 1.6667, "value": 6.2, "curve": [2.004, 6.2, 2.333, 2.72]}, {"time": 2.6667, "value": 1, "curve": [2.779, 0.42, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 6.2]}, {"time": 4.3333, "value": 6.2, "curve": [4.671, 6.2, 5.004, 2.75]}, {"time": 5.3333, "value": 1}]}, "bone115": {"rotate": [{"value": 2.73, "curve": [0.167, 1.23, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, 8.52]}, {"time": 1.8333, "value": 8.52, "curve": [2.114, 8.52, 2.389, 5.23]}, {"time": 2.6667, "value": 2.73, "curve": [2.834, 1.23, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, 8.52]}, {"time": 4.5, "value": 8.52, "curve": [4.781, 8.52, 5.058, 5.23]}, {"time": 5.3333, "value": 2.73}]}, "bone116": {"rotate": [{"value": 5.47, "curve": [0.225, 2.76, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, 10.86]}, {"time": 2, "value": 10.86, "curve": [2.225, 10.86, 2.444, 8.15]}, {"time": 2.6667, "value": 5.47, "curve": [2.892, 2.76, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, 10.86]}, {"time": 4.6667, "value": 10.86, "curve": [4.892, 10.86, 5.115, 8.19]}, {"time": 5.3333, "value": 5.47}]}, "bone117": {"rotate": [{"curve": [0.447, 0, 0.895, 2.31]}, {"time": 1.3333, "value": 2.31, "curve": [1.784, 2.31, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.31]}, {"time": 4, "value": 2.31, "curve": [4.45, 2.31, 4.892, 0]}, {"time": 5.3333}]}, "bone118": {"rotate": [{"value": 0.11, "curve": [0.058, 0.05, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, 2.31]}, {"time": 1.5, "value": 2.31, "curve": [1.895, 2.31, 2.278, 0.52]}, {"time": 2.6667, "value": 0.11, "curve": [2.724, 0.05, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, 2.31]}, {"time": 4.1667, "value": 2.31, "curve": [4.561, 2.31, 4.949, 0.55]}, {"time": 5.3333, "value": 0.11}]}, "bone119": {"rotate": [{"value": 0.7, "curve": [0.113, 0.29, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 4.32]}, {"time": 1.6667, "value": 4.32, "curve": [2.004, 4.32, 2.333, 1.9]}, {"time": 2.6667, "value": 0.7, "curve": [2.779, 0.29, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 4.32]}, {"time": 4.3333, "value": 4.32, "curve": [4.671, 4.32, 5.004, 1.91]}, {"time": 5.3333, "value": 0.7}]}, "bone120": {"rotate": [{"value": 1.78, "curve": [0.167, 0.8, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, 5.54]}, {"time": 1.8333, "value": 5.54, "curve": [2.114, 5.54, 2.389, 3.4]}, {"time": 2.6667, "value": 1.78, "curve": [2.834, 0.8, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, 5.54]}, {"time": 4.5, "value": 5.54, "curve": [4.781, 5.54, 5.058, 3.4]}, {"time": 5.3333, "value": 1.78}]}, "bone121": {"rotate": [{"value": 3.52, "curve": [0.225, 1.77, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, 6.99]}, {"time": 2, "value": 6.99, "curve": [2.225, 6.99, 2.444, 5.24]}, {"time": 2.6667, "value": 3.52, "curve": [2.892, 1.77, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, 6.99]}, {"time": 4.6667, "value": 6.99, "curve": [4.892, 6.99, 5.115, 5.26]}, {"time": 5.3333, "value": 3.52}]}, "bone122": {"rotate": [{"value": 4.79, "curve": [0.28, 2.74, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, 6.99]}, {"time": 2.1667, "value": 6.99, "curve": [2.335, 6.99, 2.5, 6.01]}, {"time": 2.6667, "value": 4.79, "curve": [2.947, 2.74, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, 6.99]}, {"time": 4.8333, "value": 6.99, "curve": [5.001, 6.99, 5.168, 6.01]}, {"time": 5.3333, "value": 4.79}]}, "bone123": {"rotate": [{"value": 5.88, "curve": [0.338, 3.92, 0.669, 0]}, {"time": 1, "curve": [1.447, 0, 1.895, 6.99]}, {"time": 2.3333, "value": 6.99, "curve": [2.446, 6.99, 2.556, 6.53]}, {"time": 2.6667, "value": 5.88, "curve": [3.004, 3.92, 3.336, 0]}, {"time": 3.6667, "curve": [4.114, 0, 4.561, 6.99]}, {"time": 5, "value": 6.99, "curve": [5.113, 6.99, 5.225, 6.54]}, {"time": 5.3333, "value": 5.88}]}, "bone124": {"rotate": [{"value": 6.66, "curve": [0.396, 5.31, 0.781, 0]}, {"time": 1.1667, "curve": [1.614, 0, 2.061, 6.99]}, {"time": 2.5, "value": 6.99, "curve": [2.557, 6.99, 2.611, 6.85]}, {"time": 2.6667, "value": 6.66, "curve": [3.062, 5.31, 3.448, 0]}, {"time": 3.8333, "curve": [4.281, 0, 4.728, 6.99]}, {"time": 5.1667, "value": 6.99, "curve": [5.224, 6.99, 5.281, 6.86]}, {"time": 5.3333, "value": 6.66}]}, "bone125": {"rotate": [{"curve": [0.447, 0, 0.895, 4.48]}, {"time": 1.3333, "value": 4.48, "curve": [1.784, 4.48, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 4.48]}, {"time": 4, "value": 4.48, "curve": [4.45, 4.48, 4.892, 0]}, {"time": 5.3333}]}, "bone126": {"rotate": [{"value": 0.21, "curve": [0.058, 0.09, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, 4.48]}, {"time": 1.5, "value": 4.48, "curve": [1.895, 4.48, 2.278, 1.02]}, {"time": 2.6667, "value": 0.21, "curve": [2.724, 0.09, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, 4.48]}, {"time": 4.1667, "value": 4.48, "curve": [4.561, 4.48, 4.949, 1.06]}, {"time": 5.3333, "value": 0.21}]}, "bone127": {"rotate": [{"value": 0.72, "curve": [0.113, 0.3, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 4.48]}, {"time": 1.6667, "value": 4.48, "curve": [2.004, 4.48, 2.333, 1.97]}, {"time": 2.6667, "value": 0.72, "curve": [2.779, 0.3, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 4.48]}, {"time": 4.3333, "value": 4.48, "curve": [4.671, 4.48, 5.004, 1.98]}, {"time": 5.3333, "value": 0.72}]}, "bone128": {"rotate": [{"value": 2.96, "curve": [0.167, 1.33, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, 9.24]}, {"time": 1.8333, "value": 9.24, "curve": [2.114, 9.24, 2.389, 5.68]}, {"time": 2.6667, "value": 2.96, "curve": [2.834, 1.33, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, 9.24]}, {"time": 4.5, "value": 9.24, "curve": [4.781, 9.24, 5.058, 5.67]}, {"time": 5.3333, "value": 2.96}]}, "bone129": {"rotate": [{"value": 5.7, "curve": [0.225, 2.87, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, 11.31]}, {"time": 2, "value": 11.31, "curve": [2.225, 11.31, 2.444, 8.49]}, {"time": 2.6667, "value": 5.7, "curve": [2.892, 2.87, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, 11.31]}, {"time": 4.6667, "value": 11.31, "curve": [4.892, 11.31, 5.115, 8.52]}, {"time": 5.3333, "value": 5.7}]}, "bone130": {"rotate": [{"value": 10.5, "curve": [0.28, 6.01, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, 15.32]}, {"time": 2.1667, "value": 15.32, "curve": [2.335, 15.32, 2.5, 13.18]}, {"time": 2.6667, "value": 10.5, "curve": [2.947, 6.01, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, 15.32]}, {"time": 4.8333, "value": 15.32, "curve": [5.001, 15.32, 5.168, 13.18]}, {"time": 5.3333, "value": 10.5}]}, "bone131": {"rotate": [{"curve": [0.447, 0, 0.895, 2.97]}, {"time": 1.3333, "value": 2.97, "curve": [1.784, 2.97, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.97]}, {"time": 4, "value": 2.97, "curve": [4.45, 2.97, 4.892, 0]}, {"time": 5.3333}]}, "bone132": {"rotate": [{"curve": [0.447, 0, 0.895, 2.97]}, {"time": 1.3333, "value": 2.97, "curve": [1.784, 2.97, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.97]}, {"time": 4, "value": 2.97, "curve": [4.45, 2.97, 4.892, 0]}, {"time": 5.3333}]}, "bone133": {"rotate": [{"curve": [0.447, 0, 0.895, 2.97]}, {"time": 1.3333, "value": 2.97, "curve": [1.784, 2.97, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.97]}, {"time": 4, "value": 2.97, "curve": [4.45, 2.97, 4.892, 0]}, {"time": 5.3333}]}, "bone134": {"rotate": [{"curve": [0.447, 0, 0.895, 2.97]}, {"time": 1.3333, "value": 2.97, "curve": [1.784, 2.97, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.97]}, {"time": 4, "value": 2.97, "curve": [4.45, 2.97, 4.892, 0]}, {"time": 5.3333}]}, "bone135": {"rotate": [{"curve": [0.447, 0, 0.895, 2.97]}, {"time": 1.3333, "value": 2.97, "curve": [1.784, 2.97, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 2.97]}, {"time": 4, "value": 2.97, "curve": [4.45, 2.97, 4.892, 0]}, {"time": 5.3333}]}, "bone136": {"rotate": [{"curve": [0.447, 0, 0.895, 9.54]}, {"time": 1.3333, "value": 9.54, "curve": [1.784, 9.54, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 9.54]}, {"time": 4, "value": 9.54, "curve": [4.45, 9.54, 4.892, 0]}, {"time": 5.3333}]}, "bone137": {"rotate": [{"curve": [0.447, 0, 0.895, 9.54]}, {"time": 1.3333, "value": 9.54, "curve": [1.784, 9.54, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 9.54]}, {"time": 4, "value": 9.54, "curve": [4.45, 9.54, 4.892, 0]}, {"time": 5.3333}]}, "bone138": {"rotate": [{"curve": [0.447, 0, 0.895, 9.54]}, {"time": 1.3333, "value": 9.54, "curve": [1.784, 9.54, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 9.54]}, {"time": 4, "value": 9.54, "curve": [4.45, 9.54, 4.892, 0]}, {"time": 5.3333}]}, "bone139": {"rotate": [{"curve": [0.447, 0, 0.895, 3.42]}, {"time": 1.3333, "value": 3.42, "curve": [1.784, 3.42, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 3.42]}, {"time": 4, "value": 3.42, "curve": [4.45, 3.42, 4.892, 0]}, {"time": 5.3333}]}, "bone140": {"rotate": [{"value": 0.16, "curve": [0.058, 0.07, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, 3.42]}, {"time": 1.5, "value": 3.42, "curve": [1.895, 3.42, 2.278, 0.78]}, {"time": 2.6667, "value": 0.16, "curve": [2.724, 0.07, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, 3.42]}, {"time": 4.1667, "value": 3.42, "curve": [4.561, 3.42, 4.949, 0.81]}, {"time": 5.3333, "value": 0.16}]}, "bone141": {"rotate": [{"value": 0.55, "curve": [0.113, 0.23, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 3.42]}, {"time": 1.6667, "value": 3.42, "curve": [2.004, 3.42, 2.333, 1.5]}, {"time": 2.6667, "value": 0.55, "curve": [2.779, 0.23, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 3.42]}, {"time": 4.3333, "value": 3.42, "curve": [4.671, 3.42, 5.004, 1.51]}, {"time": 5.3333, "value": 0.55}]}, "bone142": {"rotate": [{"value": 1.1, "curve": [0.167, 0.49, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, 3.42]}, {"time": 1.8333, "value": 3.42, "curve": [2.114, 3.42, 2.389, 2.1]}, {"time": 2.6667, "value": 1.1, "curve": [2.834, 0.49, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, 3.42]}, {"time": 4.5, "value": 3.42, "curve": [4.781, 3.42, 5.058, 2.1]}, {"time": 5.3333, "value": 1.1}]}, "bone143": {"rotate": [{"value": 1.97, "curve": [0.225, 0.99, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, 3.91]}, {"time": 2, "value": 3.91, "curve": [2.225, 3.91, 2.444, 2.93]}, {"time": 2.6667, "value": 1.97, "curve": [2.892, 0.99, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, 3.91]}, {"time": 4.6667, "value": 3.91, "curve": [4.892, 3.91, 5.115, 2.95]}, {"time": 5.3333, "value": 1.97}]}, "bone144": {"rotate": [{"value": 7.59, "curve": [0.28, 4.34, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, 11.07]}, {"time": 2.1667, "value": 11.07, "curve": [2.335, 11.07, 2.5, 9.52]}, {"time": 2.6667, "value": 7.59, "curve": [2.947, 4.34, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, 11.07]}, {"time": 4.8333, "value": 11.07, "curve": [5.001, 11.07, 5.168, 9.52]}, {"time": 5.3333, "value": 7.59}]}, "bone145": {"rotate": [{"value": 9.33, "curve": [0.338, 6.21, 0.669, 0]}, {"time": 1, "curve": [1.447, 0, 1.895, 11.07]}, {"time": 2.3333, "value": 11.07, "curve": [2.446, 11.07, 2.556, 10.35]}, {"time": 2.6667, "value": 9.33, "curve": [3.004, 6.21, 3.336, 0]}, {"time": 3.6667, "curve": [4.114, 0, 4.561, 11.07]}, {"time": 5, "value": 11.07, "curve": [5.113, 11.07, 5.225, 10.37]}, {"time": 5.3333, "value": 9.33}]}, "bone146": {"rotate": [{"value": 10.56, "curve": [0.396, 8.42, 0.781, 0]}, {"time": 1.1667, "curve": [1.614, 0, 2.061, 11.07]}, {"time": 2.5, "value": 11.07, "curve": [2.557, 11.07, 2.611, 10.86]}, {"time": 2.6667, "value": 10.56, "curve": [3.062, 8.42, 3.448, 0]}, {"time": 3.8333, "curve": [4.281, 0, 4.728, 11.07]}, {"time": 5.1667, "value": 11.07, "curve": [5.224, 11.07, 5.281, 10.87]}, {"time": 5.3333, "value": 10.56}]}, "bone147": {"rotate": [{"value": 11.07, "curve": [0.45, 11.07, 0.892, 0]}, {"time": 1.3333, "curve": [1.781, 0, 2.222, 11.07]}, {"time": 2.6667, "value": 11.07, "curve": [3.117, 11.07, 3.559, 0]}, {"time": 4, "curve": [4.447, 0, 4.895, 11.07]}, {"time": 5.3333, "value": 11.07}]}, "bone148": {"rotate": [{"value": 1.02, "curve": [0.113, 0.43, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 6.32]}, {"time": 1.6667, "value": 6.32, "curve": [2.004, 6.32, 2.333, 2.78]}, {"time": 2.6667, "value": 1.02, "curve": [2.779, 0.43, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 6.32]}, {"time": 4.3333, "value": 6.32, "curve": [4.671, 6.32, 5.004, 2.8]}, {"time": 5.3333, "value": 1.02}]}, "bone149": {"rotate": [{"value": 1.05, "curve": [0.167, 0.47, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, 3.28]}, {"time": 1.8333, "value": 3.28, "curve": [2.114, 3.28, 2.389, 2.02]}, {"time": 2.6667, "value": 1.05, "curve": [2.834, 0.47, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, 3.28]}, {"time": 4.5, "value": 3.28, "curve": [4.781, 3.28, 5.058, 2.02]}, {"time": 5.3333, "value": 1.05}]}, "bone150": {"rotate": [{"value": 1.65, "curve": [0.225, 0.83, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, 3.28]}, {"time": 2, "value": 3.28, "curve": [2.225, 3.28, 2.444, 2.47]}, {"time": 2.6667, "value": 1.65, "curve": [2.892, 0.83, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, 3.28]}, {"time": 4.6667, "value": 3.28, "curve": [4.892, 3.28, 5.115, 2.48]}, {"time": 5.3333, "value": 1.65}]}, "bone151": {"rotate": [{"value": 2.25, "curve": [0.28, 1.29, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, 3.28]}, {"time": 2.1667, "value": 3.28, "curve": [2.335, 3.28, 2.5, 2.82]}, {"time": 2.6667, "value": 2.25, "curve": [2.947, 1.29, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, 3.28]}, {"time": 4.8333, "value": 3.28, "curve": [5.001, 3.28, 5.168, 2.83]}, {"time": 5.3333, "value": 2.25}]}, "bone152": {"rotate": [{"value": 2.77, "curve": [0.338, 1.84, 0.669, 0]}, {"time": 1, "curve": [1.447, 0, 1.895, 3.28]}, {"time": 2.3333, "value": 3.28, "curve": [2.446, 3.28, 2.556, 3.07]}, {"time": 2.6667, "value": 2.77, "curve": [3.004, 1.84, 3.336, 0]}, {"time": 3.6667, "curve": [4.114, 0, 4.561, 3.28]}, {"time": 5, "value": 3.28, "curve": [5.113, 3.28, 5.225, 3.08]}, {"time": 5.3333, "value": 2.77}]}, "bone153": {"rotate": [{"value": 7.8, "curve": [0.396, 6.22, 0.781, 0]}, {"time": 1.1667, "curve": [1.614, 0, 2.061, 8.18]}, {"time": 2.5, "value": 8.18, "curve": [2.557, 8.18, 2.611, 8.02]}, {"time": 2.6667, "value": 7.8, "curve": [3.062, 6.22, 3.448, 0]}, {"time": 3.8333, "curve": [4.281, 0, 4.728, 8.18]}, {"time": 5.1667, "value": 8.18, "curve": [5.224, 8.18, 5.281, 8.03]}, {"time": 5.3333, "value": 7.8}]}, "bone154": {"rotate": [{"value": 9.37, "curve": [0.45, 9.37, 0.892, 0]}, {"time": 1.3333, "curve": [1.781, 0, 2.222, 9.37]}, {"time": 2.6667, "value": 9.37, "curve": [3.117, 9.37, 3.559, 0]}, {"time": 4, "curve": [4.447, 0, 4.895, 9.37]}, {"time": 5.3333, "value": 9.37}]}, "bone155": {"rotate": [{"value": 9.88, "curve": [0.046, 10.06, 0.089, 10.17]}, {"time": 0.1333, "value": 10.17, "curve": [0.584, 10.17, 1.025, 0]}, {"time": 1.4667, "curve": [1.869, 0, 2.267, 8.27]}, {"time": 2.6667, "value": 9.88, "curve": [2.712, 10.06, 2.756, 10.17]}, {"time": 2.8, "value": 10.17, "curve": [3.25, 10.17, 3.692, 0]}, {"time": 4.1333, "curve": [4.536, 0, 4.939, 8.23]}, {"time": 5.3333, "value": 9.88}]}, "bone156": {"rotate": [{"value": 9.09, "curve": [0.091, 9.74, 0.179, 10.17]}, {"time": 0.2667, "value": 10.17, "curve": [0.717, 10.17, 1.159, 0]}, {"time": 1.6, "curve": [1.958, 0, 2.311, 6.54]}, {"time": 2.6667, "value": 9.09, "curve": [2.758, 9.74, 2.846, 10.17]}, {"time": 2.9333, "value": 10.17, "curve": [3.384, 10.17, 3.825, 0]}, {"time": 4.2667, "curve": [4.625, 0, 4.983, 6.49]}, {"time": 5.3333, "value": 9.09}]}, "bone157": {"rotate": [{"value": 7.94, "curve": [0.136, 9.22, 0.268, 10.17]}, {"time": 0.4, "value": 10.17, "curve": [0.85, 10.17, 1.292, 0]}, {"time": 1.7333, "curve": [2.047, 0, 2.356, 5.01]}, {"time": 2.6667, "value": 7.94, "curve": [2.803, 9.22, 2.935, 10.17]}, {"time": 3.0667, "value": 10.17, "curve": [3.517, 10.17, 3.959, 0]}, {"time": 4.4, "curve": [4.713, 0, 5.026, 4.95]}, {"time": 5.3333, "value": 7.94}]}, "bone158": {"rotate": [{"curve": [0.447, 0, 0.895, 8.33]}, {"time": 1.3333, "value": 8.33, "curve": [1.784, 8.33, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 8.33]}, {"time": 4, "value": 8.33, "curve": [4.45, 8.33, 4.892, 0]}, {"time": 5.3333}]}, "bone159": {"rotate": [{"curve": [0.447, 0, 0.895, 3.72]}, {"time": 1.3333, "value": 3.72, "curve": [1.784, 3.72, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 3.72]}, {"time": 4, "value": 3.72, "curve": [4.45, 3.72, 4.892, 0]}, {"time": 5.3333}]}, "bone160": {"rotate": [{"curve": [0.447, 0, 0.895, 3.72]}, {"time": 1.3333, "value": 3.72, "curve": [1.784, 3.72, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 3.72]}, {"time": 4, "value": 3.72, "curve": [4.45, 3.72, 4.892, 0]}, {"time": 5.3333}]}, "bone161": {"rotate": [{"curve": [0.447, 0, 0.895, 3.72]}, {"time": 1.3333, "value": 3.72, "curve": [1.784, 3.72, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 3.72]}, {"time": 4, "value": 3.72, "curve": [4.45, 3.72, 4.892, 0]}, {"time": 5.3333}]}, "bone162": {"rotate": [{"curve": [0.447, 0, 0.895, 8.43]}, {"time": 1.3333, "value": 8.43, "curve": [1.784, 8.43, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 8.43]}, {"time": 4, "value": 8.43, "curve": [4.45, 8.43, 4.892, 0]}, {"time": 5.3333}]}, "bone163": {"rotate": [{"curve": [0.447, 0, 0.895, 8.43]}, {"time": 1.3333, "value": 8.43, "curve": [1.784, 8.43, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 8.43]}, {"time": 4, "value": 8.43, "curve": [4.45, 8.43, 4.892, 0]}, {"time": 5.3333}]}, "bone164": {"rotate": [{"curve": [0.447, 0, 0.895, 8.43]}, {"time": 1.3333, "value": 8.43, "curve": [1.784, 8.43, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 8.43]}, {"time": 4, "value": 8.43, "curve": [4.45, 8.43, 4.892, 0]}, {"time": 5.3333}]}, "bone165": {"rotate": [{"curve": [0.447, 0, 0.895, 8.43]}, {"time": 1.3333, "value": 8.43, "curve": [1.784, 8.43, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 8.43]}, {"time": 4, "value": 8.43, "curve": [4.45, 8.43, 4.892, 0]}, {"time": 5.3333}]}, "bone166": {"rotate": [{"curve": [0.447, 0, 0.895, 10.06]}, {"time": 1.3333, "value": 10.06, "curve": [1.784, 10.06, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 10.06]}, {"time": 4, "value": 10.06, "curve": [4.45, 10.06, 4.892, 0]}, {"time": 5.3333}]}, "bone167": {"rotate": [{"curve": [0.447, 0, 0.895, 10.06]}, {"time": 1.3333, "value": 10.06, "curve": [1.784, 10.06, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 10.06]}, {"time": 4, "value": 10.06, "curve": [4.45, 10.06, 4.892, 0]}, {"time": 5.3333}]}, "bone168": {"rotate": [{"curve": [0.447, 0, 0.895, 3.59]}, {"time": 1.3333, "value": 3.59, "curve": [1.784, 3.59, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 3.59]}, {"time": 4, "value": 3.59, "curve": [4.45, 3.59, 4.892, 0]}, {"time": 5.3333}]}, "bone169": {"rotate": [{"value": 0.3, "curve": [0.058, 0.13, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, 6.29]}, {"time": 1.5, "value": 6.29, "curve": [1.895, 6.29, 2.278, 1.43]}, {"time": 2.6667, "value": 0.3, "curve": [2.724, 0.13, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, 6.29]}, {"time": 4.1667, "value": 6.29, "curve": [4.561, 6.29, 4.949, 1.49]}, {"time": 5.3333, "value": 0.3}]}, "bone170": {"rotate": [{"value": 0.58, "curve": [0.113, 0.24, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 3.59]}, {"time": 1.6667, "value": 3.59, "curve": [2.004, 3.59, 2.333, 1.58]}, {"time": 2.6667, "value": 0.58, "curve": [2.779, 0.24, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 3.59]}, {"time": 4.3333, "value": 3.59, "curve": [4.671, 3.59, 5.004, 1.59]}, {"time": 5.3333, "value": 0.58}]}, "bone171": {"rotate": [{"value": 0.89, "curve": [0.167, 0.4, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, 2.76]}, {"time": 1.8333, "value": 2.76, "curve": [2.114, 2.76, 2.389, 1.7]}, {"time": 2.6667, "value": 0.89, "curve": [2.834, 0.4, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, 2.76]}, {"time": 4.5, "value": 2.76, "curve": [4.781, 2.76, 5.058, 1.7]}, {"time": 5.3333, "value": 0.89}]}, "bone172": {"rotate": [{"value": 4.17, "curve": [0.225, 2.1, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, 8.28]}, {"time": 2, "value": 8.28, "curve": [2.225, 8.28, 2.444, 6.22]}, {"time": 2.6667, "value": 4.17, "curve": [2.892, 2.1, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, 8.28]}, {"time": 4.6667, "value": 8.28, "curve": [4.892, 8.28, 5.115, 6.24]}, {"time": 5.3333, "value": 4.17}]}, "bone173": {"rotate": [{"value": 5.68, "curve": [0.28, 3.25, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, 8.28]}, {"time": 2.1667, "value": 8.28, "curve": [2.335, 8.28, 2.5, 7.12]}, {"time": 2.6667, "value": 5.68, "curve": [2.947, 3.25, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, 8.28]}, {"time": 4.8333, "value": 8.28, "curve": [5.001, 8.28, 5.168, 7.12]}, {"time": 5.3333, "value": 5.68}]}, "bone174": {"rotate": [{"value": 6.98, "curve": [0.338, 4.65, 0.669, 0]}, {"time": 1, "curve": [1.447, 0, 1.895, 8.28]}, {"time": 2.3333, "value": 8.28, "curve": [2.446, 8.28, 2.556, 7.74]}, {"time": 2.6667, "value": 6.98, "curve": [3.004, 4.65, 3.336, 0]}, {"time": 3.6667, "curve": [4.114, 0, 4.561, 8.28]}, {"time": 5, "value": 8.28, "curve": [5.113, 8.28, 5.225, 7.75]}, {"time": 5.3333, "value": 6.98}]}, "bone175": {"rotate": [{"value": 7.9, "curve": [0.396, 6.3, 0.781, 0]}, {"time": 1.1667, "curve": [1.614, 0, 2.061, 8.28]}, {"time": 2.5, "value": 8.28, "curve": [2.557, 8.28, 2.611, 8.13]}, {"time": 2.6667, "value": 7.9, "curve": [3.062, 6.3, 3.448, 0]}, {"time": 3.8333, "curve": [4.281, 0, 4.728, 8.28]}, {"time": 5.1667, "value": 8.28, "curve": [5.224, 8.28, 5.281, 8.13]}, {"time": 5.3333, "value": 7.9}]}, "bone176": {"rotate": [{"value": 0.92, "curve": [0.113, 0.39, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, 5.66]}, {"time": 1.6667, "value": 5.66, "curve": [2.004, 5.66, 2.333, 2.49]}, {"time": 2.6667, "value": 0.92, "curve": [2.779, 0.39, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, 5.66]}, {"time": 4.3333, "value": 5.66, "curve": [4.671, 5.66, 5.004, 2.51]}, {"time": 5.3333, "value": 0.92}], "translate": [{"x": -0.26, "y": -5.02, "curve": [0.113, -0.11, 0.223, 0, 0.113, -2.11, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, -1.6, 0.781, 0, 1.228, -31.05]}, {"time": 1.6667, "x": -1.6, "y": -31.05, "curve": [2.004, -1.6, 2.333, -0.7, 2.004, -31.05, 2.333, -13.64]}, {"time": 2.6667, "x": -0.26, "y": -5.02, "curve": [2.779, -0.11, 2.89, 0, 2.779, -2.11, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, -1.6, 3.447, 0, 3.895, -31.05]}, {"time": 4.3333, "x": -1.6, "y": -31.05, "curve": [4.671, -1.6, 5.004, -0.71, 4.671, -31.05, 5.004, -13.75]}, {"time": 5.3333, "x": -0.26, "y": -5.02}]}, "bone177": {"rotate": [{"value": 1.82, "curve": [0.167, 0.82, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, 5.66]}, {"time": 1.8333, "value": 5.66, "curve": [2.114, 5.66, 2.389, 3.48]}, {"time": 2.6667, "value": 1.82, "curve": [2.834, 0.82, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, 5.66]}, {"time": 4.5, "value": 5.66, "curve": [4.781, 5.66, 5.058, 3.48]}, {"time": 5.3333, "value": 1.82}]}, "bone178": {"rotate": [{"value": 2.85, "curve": [0.225, 1.44, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, 5.66]}, {"time": 2, "value": 5.66, "curve": [2.225, 5.66, 2.444, 4.25]}, {"time": 2.6667, "value": 2.85, "curve": [2.892, 1.44, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, 5.66]}, {"time": 4.6667, "value": 5.66, "curve": [4.892, 5.66, 5.115, 4.27]}, {"time": 5.3333, "value": 2.85}]}, "bone179": {"rotate": [{"value": 11.86, "curve": [0.28, 6.79, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, 17.3]}, {"time": 2.1667, "value": 17.3, "curve": [2.335, 17.3, 2.5, 14.88]}, {"time": 2.6667, "value": 11.86, "curve": [2.947, 6.79, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, 17.3]}, {"time": 4.8333, "value": 17.3, "curve": [5.001, 17.3, 5.168, 14.88]}, {"time": 5.3333, "value": 11.86}]}, "bone180": {"rotate": [{"value": 14.57, "curve": [0.338, 9.71, 0.669, 0]}, {"time": 1, "curve": [1.447, 0, 1.895, 17.3]}, {"time": 2.3333, "value": 17.3, "curve": [2.446, 17.3, 2.556, 16.17]}, {"time": 2.6667, "value": 14.57, "curve": [3.004, 9.71, 3.336, 0]}, {"time": 3.6667, "curve": [4.114, 0, 4.561, 17.3]}, {"time": 5, "value": 17.3, "curve": [5.113, 17.3, 5.225, 16.2]}, {"time": 5.3333, "value": 14.57}]}, "bone181": {"rotate": [{"value": 3.35, "curve": [0.28, 1.92, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, 4.89]}, {"time": 2.1667, "value": 4.89, "curve": [2.335, 4.89, 2.5, 4.2]}, {"time": 2.6667, "value": 3.35, "curve": [2.947, 1.92, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, 4.89]}, {"time": 4.8333, "value": 4.89, "curve": [5.001, 4.89, 5.168, 4.2]}, {"time": 5.3333, "value": 3.35}]}, "bone182": {"rotate": [{"value": 4.12, "curve": [0.338, 2.74, 0.669, 0]}, {"time": 1, "curve": [1.447, 0, 1.895, 4.89]}, {"time": 2.3333, "value": 4.89, "curve": [2.446, 4.89, 2.556, 4.57]}, {"time": 2.6667, "value": 4.12, "curve": [3.004, 2.74, 3.336, 0]}, {"time": 3.6667, "curve": [4.114, 0, 4.561, 4.89]}, {"time": 5, "value": 4.89, "curve": [5.113, 4.89, 5.225, 4.57]}, {"time": 5.3333, "value": 4.12}]}, "bone183": {"rotate": [{"value": 4.66, "curve": [0.396, 3.72, 0.781, 0]}, {"time": 1.1667, "curve": [1.614, 0, 2.061, 4.89]}, {"time": 2.5, "value": 4.89, "curve": [2.557, 4.89, 2.611, 4.79]}, {"time": 2.6667, "value": 4.66, "curve": [3.062, 3.72, 3.448, 0]}, {"time": 3.8333, "curve": [4.281, 0, 4.728, 4.89]}, {"time": 5.1667, "value": 4.89, "curve": [5.224, 4.89, 5.281, 4.8]}, {"time": 5.3333, "value": 4.66}]}, "bone184": {"rotate": [{"value": 4.89, "curve": [0.45, 4.89, 0.892, 0]}, {"time": 1.3333, "curve": [1.781, 0, 2.222, 4.89]}, {"time": 2.6667, "value": 4.89, "curve": [3.117, 4.89, 3.559, 0]}, {"time": 4, "curve": [4.447, 0, 4.895, 4.89]}, {"time": 5.3333, "value": 4.89}]}, "bone185": {"rotate": [{"value": 15.08, "curve": [0.058, 15.5, 0.113, 15.84]}, {"time": 0.1667, "value": 15.84, "curve": [0.617, 15.84, 1.059, 0]}, {"time": 1.5, "curve": [1.892, 0, 2.278, 12.26]}, {"time": 2.6667, "value": 15.08, "curve": [2.725, 15.5, 2.779, 15.84]}, {"time": 2.8333, "value": 15.84, "curve": [3.284, 15.84, 3.725, 0]}, {"time": 4.1667, "curve": [4.559, 0, 4.951, 12.07]}, {"time": 5.3333, "value": 15.08}]}, "bone186": {"rotate": [{"value": 13.27, "curve": [0.114, 14.75, 0.224, 15.84]}, {"time": 0.3333, "value": 15.84, "curve": [0.784, 15.84, 1.225, 0]}, {"time": 1.6667, "curve": [2.002, 0, 2.333, 8.91]}, {"time": 2.6667, "value": 13.27, "curve": [2.78, 14.75, 2.89, 15.84]}, {"time": 3, "value": 15.84, "curve": [3.45, 15.84, 3.892, 0]}, {"time": 4.3333, "curve": [4.669, 0, 5.004, 8.81]}, {"time": 5.3333, "value": 13.27}]}, "bone187": {"rotate": [{"value": 10.75, "curve": [0.168, 13.55, 0.335, 15.84]}, {"time": 0.5, "value": 15.84, "curve": [0.95, 15.84, 1.392, 0]}, {"time": 1.8333, "curve": [2.112, 0, 2.389, 6.13]}, {"time": 2.6667, "value": 10.75, "curve": [2.835, 13.55, 3.001, 15.84]}, {"time": 3.1667, "value": 15.84, "curve": [3.617, 15.84, 4.059, 0]}, {"time": 4.5, "curve": [4.779, 0, 5.058, 6.11]}, {"time": 5.3333, "value": 10.75}]}, "bone188": {"rotate": [{"value": 12, "curve": [0.058, 12.34, 0.113, 12.61]}, {"time": 0.1667, "value": 12.61, "curve": [0.617, 12.61, 1.059, 0]}, {"time": 1.5, "curve": [1.892, 0, 2.278, 9.76]}, {"time": 2.6667, "value": 12, "curve": [2.725, 12.34, 2.779, 12.61]}, {"time": 2.8333, "value": 12.61, "curve": [3.284, 12.61, 3.725, 0]}, {"time": 4.1667, "curve": [4.559, 0, 4.951, 9.61]}, {"time": 5.3333, "value": 12}]}, "bone189": {"rotate": [{"value": 10.56, "curve": [0.114, 11.74, 0.224, 12.61]}, {"time": 0.3333, "value": 12.61, "curve": [0.784, 12.61, 1.225, 0]}, {"time": 1.6667, "curve": [2.002, 0, 2.333, 7.09]}, {"time": 2.6667, "value": 10.56, "curve": [2.78, 11.74, 2.89, 12.61]}, {"time": 3, "value": 12.61, "curve": [3.45, 12.61, 3.892, 0]}, {"time": 4.3333, "curve": [4.669, 0, 5.004, 7.01]}, {"time": 5.3333, "value": 10.56}]}, "bone190": {"rotate": [{"value": 8.55, "curve": [0.168, 10.78, 0.335, 12.61]}, {"time": 0.5, "value": 12.61, "curve": [0.95, 12.61, 1.392, 0]}, {"time": 1.8333, "curve": [2.112, 0, 2.389, 4.88]}, {"time": 2.6667, "value": 8.55, "curve": [2.835, 10.78, 3.001, 12.61]}, {"time": 3.1667, "value": 12.61, "curve": [3.617, 12.61, 4.059, 0]}, {"time": 4.5, "curve": [4.779, 0, 5.058, 4.86]}, {"time": 5.3333, "value": 8.55}]}, "bone191": {"rotate": [{"curve": [0.447, 0, 0.895, -8.18]}, {"time": 1.3333, "value": -8.18, "curve": [1.784, -8.18, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, -8.18]}, {"time": 4, "value": -8.18, "curve": [4.45, -8.18, 4.892, 0]}, {"time": 5.3333}], "translate": [{"curve": [0.447, 0, 0.895, 11.73, 0.447, 0, 0.895, 7.74]}, {"time": 1.3333, "x": 11.73, "y": 7.74, "curve": [1.784, 11.73, 2.222, 0, 1.784, 7.74, 2.222, 0]}, {"time": 2.6667, "curve": [3.114, 0, 3.561, 11.73, 3.114, 0, 3.561, 7.74]}, {"time": 4, "x": 11.73, "y": 7.74, "curve": [4.45, 11.73, 4.892, 0, 4.45, 7.74, 4.892, 0]}, {"time": 5.3333}]}, "bone192": {"rotate": [{"value": -0.39, "curve": [0.058, -0.17, 0.112, 0]}, {"time": 0.1667, "curve": [0.614, 0, 1.061, -8.18]}, {"time": 1.5, "value": -8.18, "curve": [1.895, -8.18, 2.278, -1.86]}, {"time": 2.6667, "value": -0.39, "curve": [2.724, -0.17, 2.779, 0]}, {"time": 2.8333, "curve": [3.281, 0, 3.728, -8.18]}, {"time": 4.1667, "value": -8.18, "curve": [4.561, -8.18, 4.949, -1.94]}, {"time": 5.3333, "value": -0.39}]}, "bone193": {"rotate": [{"value": -1.32, "curve": [0.113, -0.56, 0.223, 0]}, {"time": 0.3333, "curve": [0.781, 0, 1.228, -8.18]}, {"time": 1.6667, "value": -8.18, "curve": [2.004, -8.18, 2.333, -3.59]}, {"time": 2.6667, "value": -1.32, "curve": [2.779, -0.56, 2.89, 0]}, {"time": 3, "curve": [3.447, 0, 3.895, -8.18]}, {"time": 4.3333, "value": -8.18, "curve": [4.671, -8.18, 5.004, -3.62]}, {"time": 5.3333, "value": -1.32}]}, "bone194": {"rotate": [{"value": -4.4, "curve": [0.167, -1.97, 0.334, 0]}, {"time": 0.5, "curve": [0.947, 0, 1.395, -13.71]}, {"time": 1.8333, "value": -13.71, "curve": [2.114, -13.71, 2.389, -8.42]}, {"time": 2.6667, "value": -4.4, "curve": [2.834, -1.97, 3, 0]}, {"time": 3.1667, "curve": [3.614, 0, 4.061, -13.71]}, {"time": 4.5, "value": -13.71, "curve": [4.781, -13.71, 5.058, -8.41]}, {"time": 5.3333, "value": -4.4}]}, "bone195": {"rotate": [{"value": -6.9, "curve": [0.225, -3.48, 0.446, 0]}, {"time": 0.6667, "curve": [1.114, 0, 1.561, -13.71]}, {"time": 2, "value": -13.71, "curve": [2.225, -13.71, 2.444, -10.28]}, {"time": 2.6667, "value": -6.9, "curve": [2.892, -3.48, 3.113, 0]}, {"time": 3.3333, "curve": [3.781, 0, 4.228, -13.71]}, {"time": 4.6667, "value": -13.71, "curve": [4.892, -13.71, 5.115, -10.33]}, {"time": 5.3333, "value": -6.9}]}, "bone196": {"rotate": [{"value": -9.39, "curve": [0.28, -5.37, 0.557, 0]}, {"time": 0.8333, "curve": [1.281, 0, 1.728, -13.71]}, {"time": 2.1667, "value": -13.71, "curve": [2.335, -13.71, 2.5, -11.79]}, {"time": 2.6667, "value": -9.39, "curve": [2.947, -5.37, 3.223, 0]}, {"time": 3.5, "curve": [3.947, 0, 4.395, -13.71]}, {"time": 4.8333, "value": -13.71, "curve": [5.001, -13.71, 5.168, -11.79]}, {"time": 5.3333, "value": -9.39}]}}, "physics": {"": {"inertia": [{}], "strength": [{}]}}, "attachments": {"default": {"biyan": {"biyan": {"deform": [{"time": 4.1667, "offset": 12, "vertices": [24.42725, 12.44539, 24.42957, 12.44584, 36.24646, 14.66441, 36.24823, 14.66493, 40.17181, 18.08708, 40.1731, 18.08743, 32.72076, 21.39104, 32.72144, 21.39149, 18.75421, 5.92871, 18.75488, 5.929, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.68481, 1.39046, 5.68488, 1.39031, 23.02344, -10.49359, 23.0238, -10.49427, 27.51971, -2.47209, 27.51953, -2.47253, 27.16949, -0.39084, 27.16901, -0.39116, 14.04932, -3.60413, 14.04871, -3.60437, 8.4754, 0.64795, 8.47546, 0.64788]}, {"time": 4.3, "offset": 12, "vertices": [0.13257, -0.00774, 0.13257], "curve": "stepped"}, {"time": 4.3667, "offset": 12, "vertices": [0.13257, -0.00774, 0.13257]}, {"time": 4.5, "offset": 12, "vertices": [24.42725, 12.44539, 24.42957, 12.44584, 36.24646, 14.66441, 36.24823, 14.66493, 40.17181, 18.08708, 40.1731, 18.08743, 32.72076, 21.39104, 32.72144, 21.39149, 18.75421, 5.92871, 18.75488, 5.929, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.68481, 1.39046, 5.68488, 1.39031, 23.02344, -10.49359, 23.0238, -10.49427, 27.51971, -2.47209, 27.51953, -2.47253, 27.16949, -0.39084, 27.16901, -0.39116, 14.04932, -3.60413, 14.04871, -3.60437, 8.4754, 0.64795, 8.47546, 0.64788]}]}}, "toufa15": {"toufa15": {"deform": [{"offset": 2, "vertices": [-5.35254, 11.50103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.69214, -1.54462, -7.75635, 1.18021, -14.49377, 6.67501, -6.18652, 5.24612]}]}}}}}, "xiuxian": {"slots": {"biyan": {"rgba": [{"time": 5.8333, "color": "ffffff00"}, {"time": 5.9, "color": "ffffffff", "curve": "stepped"}, {"time": 6.1, "color": "ffffffff"}, {"time": 6.1667, "color": "ffffff00"}], "attachment": [{"time": 5.8333, "name": "biyan"}, {"time": 6.1667}]}, "daoguang2": {"attachment": [{}, {"time": 0.6667, "name": "1/a_03"}, {"time": 0.7, "name": "1/a_04"}, {"time": 0.7333, "name": "1/a_01"}, {"time": 0.7667, "name": "1/a_02"}, {"time": 0.8, "name": "1/a_03"}, {"time": 0.8333, "name": "1/a_04"}, {"time": 0.8667, "name": "1/a_01"}, {"time": 0.9, "name": "1/a_02"}, {"time": 0.9333, "name": "1/a_03"}, {"time": 0.9667, "name": "1/a_04"}, {"time": 1, "name": "1/a_01"}, {"time": 1.0333, "name": "1/a_02"}, {"time": 1.0667, "name": "1/a_03"}, {"time": 1.1, "name": "1/a_04"}, {"time": 1.1333, "name": "1/a_01"}, {"time": 1.1667, "name": "1/a_02"}, {"time": 1.2, "name": "1/a_03"}, {"time": 1.2333}]}, "daoguang3": {"rgba": [{"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffd2"}, {"time": 1.0333, "color": "ffffffe8"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "1/kirino_zhuan_X_0"}, {"time": 1.1667}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.3333, "value": -0.25, "curve": [0.578, 0.6, 0.383, -2.9]}, {"time": 0.8333, "value": -2.19, "curve": [1, -1.93, 1.167, -1.22]}, {"time": 1.3333, "value": -2.43, "curve": [1.532, -3.87, 1.5, 1.49]}, {"time": 1.6667}], "translate": [{}, {"time": 0.3333, "x": 2.22, "curve": [0.578, -0.78, 0.383, 10.13, 0.5, 0, 0.667, 0]}, {"time": 0.8333, "x": 7.66, "curve": [1, 6.75, 1.167, 3.86, 1, 0, 1.167, 0]}, {"time": 1.3333, "x": 7.66, "curve": [1.532, 12.22, 1.5, -4.69, 1.444, 0, 1.556, 0]}, {"time": 1.6667, "curve": [2.111, 12.51, 2.556, 0, 2.111, 0, 2.556, -16.62]}, {"time": 3, "y": -16.62, "curve": [3.444, 0, 3.889, 0, 3.444, -16.62, 3.889, 0]}, {"time": 4.3333, "curve": [4.778, 0, 5.222, 0, 4.778, 0, 5.222, -16.62]}, {"time": 5.6667, "y": -16.62, "curve": [6.111, 0, 6.556, 0, 6.111, -16.62, 6.556, 0]}, {"time": 7}]}, "bone2": {"scale": [{"time": 1.6667, "curve": [2.111, 1, 2.556, 0.93, 2.111, 1, 2.556, 1]}, {"time": 3, "x": 0.93, "curve": [3.444, 0.93, 3.889, 1, 3.444, 1, 3.889, 1]}, {"time": 4.3333, "curve": [4.778, 1, 5.222, 0.93, 4.778, 1, 5.222, 1]}, {"time": 5.6667, "x": 0.93, "curve": [6.111, 0.93, 6.556, 1, 6.111, 1, 6.556, 1]}, {"time": 7}]}, "bone3": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 1.38, "curve": "stepped"}, {"time": 1.3333, "value": 1.38, "curve": [1.532, 2.19, 1.5, -0.84]}, {"time": 1.6667, "curve": [1.971, 1.54, 2.544, -1.58]}, {"time": 3, "value": -1.58}, {"time": 4.3333}]}, "bone4": {"rotate": [{"time": 1.6667, "curve": [2.111, 0, 2.556, 4.1]}, {"time": 3, "value": 4.1, "curve": [3.444, 4.1, 3.889, 0]}, {"time": 4.3333, "curve": [4.778, 0, 5.222, 4.1]}, {"time": 5.6667, "value": 4.1, "curve": [6.111, 4.1, 6.556, 0]}, {"time": 7}]}, "bone5": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": -0.39, "curve": "stepped"}, {"time": 1.3333, "value": -0.39, "curve": [1.532, -0.63, 1.5, 0.24]}, {"time": 1.6667, "curve": [2.122, -0.66, 2.544, -8.59]}, {"time": 3, "value": -8.59}, {"time": 4.3333}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "value": -0.36, "curve": [0.578, 0.05, 0.383, -1.39]}, {"time": 0.8333, "value": -1.05, "curve": [1, -0.92, 1.167, -0.53]}, {"time": 1.3333, "value": -1.05, "curve": [1.532, -1.67, 1.5, 0.64]}, {"time": 1.6667, "curve": [2.111, -1.71, 2.556, 1.82]}, {"time": 3, "value": 1.82, "curve": [3.45, 1.82, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 1.82]}, {"time": 5.6667, "value": 1.82, "curve": [6.117, 1.82, 6.556, 0]}, {"time": 7}], "scale": [{"time": 1.6667, "curve": [2.111, 1, 2.556, 0.968, 2.111, 1, 2.556, 1]}, {"time": 3, "x": 0.968, "curve": [3.45, 0.968, 3.889, 1, 3.45, 1, 3.889, 1]}, {"time": 4.3333, "curve": [4.781, 1, 5.228, 0.968, 4.781, 1, 5.228, 1]}, {"time": 5.6667, "x": 0.968, "curve": [6.117, 0.968, 6.556, 1, 6.117, 1, 6.556, 1]}, {"time": 7}]}, "bone10": {"rotate": [{}, {"time": 0.3333, "value": -0.48, "curve": [0.578, 0.14, 0.383, -2.11]}, {"time": 0.8333, "value": -1.59, "curve": [1, -1.4, 1.167, -0.8]}, {"time": 1.3333, "value": -1.59, "curve": [1.532, -2.54, 1.5, 0.98]}, {"time": 1.6667, "curve": [1.887, -1.29, 2.556, -1.29]}, {"time": 3, "value": -1.29, "curve": [3.45, -1.29, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -1.29]}, {"time": 5.6667, "value": -1.29, "curve": [6.117, -1.29, 6.556, 0]}, {"time": 7}], "translate": [{"time": 1.6667, "curve": [2.111, 0, 2.556, -3.11, 2.111, 0, 2.556, 0.2]}, {"time": 3, "x": -3.11, "y": 0.2, "curve": [3.45, -3.11, 3.889, 0, 3.45, 0.2, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -3.11, 4.781, 0, 5.228, 0.2]}, {"time": 5.6667, "x": -3.11, "y": 0.2, "curve": [6.117, -3.11, 6.556, 0, 6.117, 0.2, 6.556, 0]}, {"time": 7}]}, "bone11": {"rotate": [{}, {"time": 0.3333, "value": -0.34, "curve": [0.578, 0.28, 0.383, -2.07]}, {"time": 0.8333, "value": -1.57, "curve": [1, -1.38, 1.167, -0.79]}, {"time": 1.3333, "value": -1.57, "curve": [1.532, -2.5, 1.5, 0.96]}, {"time": 1.6667, "curve": [1.784, -0.67, 2.556, -0.67]}, {"time": 3, "value": -0.67, "curve": [3.45, -0.67, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -0.67]}, {"time": 5.6667, "value": -0.67, "curve": [6.117, -0.67, 6.556, 0]}, {"time": 7}], "translate": [{"time": 1.6667, "curve": [2.111, 0, 2.556, -5.78, 2.111, 0, 2.556, -2.34]}, {"time": 3, "x": -5.78, "y": -2.34, "curve": [3.45, -5.78, 3.889, 0, 3.45, -2.34, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -5.78, 4.781, 0, 5.228, -2.34]}, {"time": 5.6667, "x": -5.78, "y": -2.34, "curve": [6.117, -5.78, 6.556, 0, 6.117, -2.34, 6.556, 0]}, {"time": 7}]}, "bone12": {"rotate": [{}, {"time": 0.3333, "value": -1.24, "curve": [0.578, 0.5, 0.383, -5.88]}, {"time": 0.8333, "value": -4.45, "curve": [1, -3.92, 1.167, -2.23]}, {"time": 1.3333, "value": -4.45, "curve": [1.532, -7.09, 1.5, 2.72]}, {"time": 1.6667, "curve": [1.706, -0.64, 2.556, -0.64]}, {"time": 3, "value": -0.64, "curve": [3.45, -0.64, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -0.64]}, {"time": 5.6667, "value": -0.64, "curve": [6.117, -0.64, 6.556, 0]}, {"time": 7}]}, "bone13": {"rotate": [{}, {"time": 0.3333, "value": -1.65, "curve": [0.578, 1.82, 0.383, -11.72]}, {"time": 0.8333, "value": -8.86, "curve": [1, -7.8, 1.167, -4.47]}, {"time": 1.3333, "value": -8.86, "curve": [1.532, -14.13, 1.5, 5.43]}, {"time": 1.6667, "curve": [1.701, -1.11, 2.556, -1.11]}, {"time": 3, "value": -1.11, "curve": [3.45, -1.11, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -1.11]}, {"time": 5.6667, "value": -1.11, "curve": [6.117, -1.11, 6.556, 0]}, {"time": 7}]}, "bone16": {"translate": [{}, {"time": 0.3333, "x": -0.02, "y": 16.07, "curve": [0.578, -1.54, 0.383, 5.14, 0.578, 6.11, 0.383, 33.65]}, {"time": 0.8333, "x": 3.89, "y": 25.44, "curve": [1, 3.42, 1.167, 1.95, 1, 22.41, 1.172, 13.21]}, {"time": 1.3333, "x": 3.89, "y": 25.44, "curve": [1.532, 6.2, 1.5, -2.38, 1.532, 40.58, 1.5, -15.59]}, {"time": 1.6667, "curve": [2.111, 6.35, 2.556, 8.82, 1.691, 2.26, 2.556, -2.26]}, {"time": 3, "x": 8.82, "y": -2.26, "curve": [3.45, 8.82, 3.889, 0, 3.45, -2.26, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 8.82, 4.781, 0, 5.228, -2.26]}, {"time": 5.6667, "x": 8.82, "y": -2.26, "curve": [6.117, 8.82, 6.556, 0, 6.117, -2.26, 6.556, 0]}, {"time": 7}]}, "bone17": {"rotate": [{"time": 1.6667, "curve": [2.111, 0, 2.556, 0.83]}, {"time": 3, "value": 0.83, "curve": [3.45, 0.83, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 0.83]}, {"time": 5.6667, "value": 0.83, "curve": [6.117, 0.83, 6.556, 0]}, {"time": 7}]}, "bone18": {"rotate": [{"time": 1.6667, "curve": [2.111, 0, 2.556, 2.84]}, {"time": 3, "value": 2.84, "curve": [3.45, 2.84, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.84]}, {"time": 5.6667, "value": 2.84, "curve": [6.117, 2.84, 6.556, 0]}, {"time": 7}]}, "bone19": {"rotate": [{"time": 1.6667, "curve": [2.111, 0, 2.556, -5.64]}, {"time": 3, "value": -5.64, "curve": [3.45, -5.64, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -5.64]}, {"time": 5.6667, "value": -5.64, "curve": [6.117, -5.64, 6.556, 0]}, {"time": 7}]}, "bone21": {"rotate": [{"time": 0.3333, "curve": [0.578, 0.43, 0.383, -1.45]}, {"time": 0.8333, "value": -1.1, "curve": [1, -0.96, 1.167, -0.55]}, {"time": 1.3333, "value": -1.1, "curve": [1.532, -1.75, 1.5, 0.67]}, {"time": 1.6667, "curve": [2.025, -1.44, 2.556, -1.44]}, {"time": 3, "value": -1.44, "curve": [3.45, -1.44, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -1.44]}, {"time": 5.6667, "value": -1.44, "curve": [6.117, -1.44, 6.556, 0]}, {"time": 7}]}, "bone22": {"rotate": [{"time": 0.3333, "curve": [0.578, 14.13, 0.383, -47.76]}, {"time": 0.8333, "value": -36.12, "curve": [1, -31.8, 1.178, -19.31]}, {"time": 1.3333, "value": -36.12, "curve": [1.532, -57.61, 1.5, 22.12]}, {"time": 1.6667, "curve": [1.672, -0.66, 2.556, -0.66]}, {"time": 3, "value": -0.66, "curve": [3.45, -0.66, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -0.66]}, {"time": 5.6667, "value": -0.66, "curve": [6.117, -0.66, 6.556, 0]}, {"time": 7}]}, "bone23": {"rotate": [{"time": 0.3333, "curve": [0.578, 7.21, 0.383, -24.37]}, {"time": 0.8333, "value": -18.43, "curve": [1, -16.23, 1.17, -9.42]}, {"time": 1.3333, "value": -18.43, "curve": [1.532, -29.4, 1.5, 11.29]}, {"time": 1.6667, "curve": [1.694, -1.86, 2.556, 1.86]}, {"time": 3, "value": 1.86, "curve": [3.45, 1.86, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 1.86]}, {"time": 5.6667, "value": 1.86, "curve": [6.117, 1.86, 6.556, 0]}, {"time": 7}]}, "bone24": {"rotate": [{"time": 0.3333, "curve": [0.578, 20.29, 0.383, -68.56]}, {"time": 0.8333, "value": -51.85, "curve": [1, -45.66, 1.187, -29.22]}, {"time": 1.3333, "value": -51.85, "curve": [1.532, -82.7, 1.5, 31.76]}, {"time": 1.6667, "curve": [1.711, -8.51, 2.556, 8.51]}, {"time": 3, "value": 8.51, "curve": [3.45, 8.51, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 8.51]}, {"time": 5.6667, "value": 8.51, "curve": [6.117, 8.51, 6.556, 0]}, {"time": 7}]}, "bone25": {"rotate": [{"time": 0.3333, "curve": [0.578, 7.02, 0.383, -54.03]}, {"time": 0.8333, "value": -48.25, "curve": [1, -46.11, 1.17, -9.15]}, {"time": 1.3333, "value": -17.93, "curve": [1.532, -28.59, 1.5, 10.98]}, {"time": 1.6667}]}, "bone27": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": -18.79}, {"time": 1.3333}]}, "bone28": {"rotate": [{"time": 0.3333, "curve": [0.578, 4.1, 0.383, -13.87]}, {"time": 0.8333, "value": -10.49, "curve": [1, -9.23, 1.168, -5.29]}, {"time": 1.3333, "value": -10.49, "curve": [1.532, -16.73, 1.5, 6.42]}, {"time": 1.6667}]}, "bone30": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": -8.16}, {"time": 1.3333}]}, "bone31": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": -17.08}, {"time": 1.3333}]}, "bone33": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": -1.1}, {"time": 1.2, "value": 2.4, "curve": "stepped"}, {"time": 1.3333, "value": 2.4, "curve": [1.532, 3.83, 1.5, -1.47]}, {"time": 1.6667}]}, "bone34": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": -17.25}, {"time": 1.3333}]}, "bone35": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": -8}, {"time": 1.3333}]}, "bone36": {"rotate": [{"time": 0.3333, "curve": [0.378, 0, 0.423, -4.09]}, {"time": 0.4667, "value": -1.81, "curve": [0.523, 1, 0.578, 10.29]}, {"time": 0.6333, "value": 10.29}, {"time": 1, "value": -687.94}, {"time": 1.1667, "value": -1081.61}, {"time": 1.3, "value": -1056.12}, {"time": 1.5667, "value": -1081.49}, {"time": 1.6, "value": -1081.8}, {"time": 1.6667, "value": -1081.49}, {"time": 3, "value": -1084.07}, {"time": 4.3333, "value": -1079.03}, {"time": 5.6667, "value": -1084.07}, {"time": 6.9667, "value": -1079.7}, {"time": 7, "value": -1079.03}], "translate": [{"time": 1.5667}, {"time": 1.6, "x": 25.07, "y": -35.43}, {"time": 1.6333, "x": 25.77, "y": -21.88}, {"time": 1.6667}]}, "bone37": {"rotate": [{"time": 0.3333}, {"time": 0.4333, "value": -2.66}, {"time": 0.6667, "value": -2.97, "curve": "stepped"}, {"time": 1.3333, "value": -2.97, "curve": [1.416, -3.71, 1.491, -1.39]}, {"time": 1.5667, "value": -0.33, "curve": [1.589, -0.02, 1.611, 8.47]}, {"time": 1.6333, "value": 8.46, "curve": [1.644, 8.46, 1.656, 0.12]}, {"time": 1.6667, "curve": [1.815, -1.62, 2.556, 1.62]}, {"time": 3, "value": 1.62, "curve": [3.45, 1.62, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 1.62]}, {"time": 5.6667, "value": 1.62, "curve": [6.117, 1.62, 6.556, 0]}, {"time": 7}], "translate": [{"time": 0.3333}, {"time": 0.4333, "x": -1.71, "y": 15.76}, {"time": 0.6667}]}, "bone42": {"rotate": [{"value": -4.16, "curve": "stepped"}, {"time": 0.3333, "value": -4.16}, {"time": 0.8333, "value": 2.42, "curve": "stepped"}, {"time": 1.3333, "value": 2.42, "curve": [1.532, 6.33, 1.5, -8.19]}, {"time": 1.6667, "value": -4.16, "curve": [2.028, 4.58, 2.544, 4.79]}, {"time": 3, "value": 4.79}, {"time": 4.3333, "value": -4.16}]}, "bone43": {"translate": [{"x": -0.52, "y": -0.21, "curve": "stepped"}, {"time": 0.3333, "x": -0.52, "y": -0.21}, {"time": 0.8333, "x": 2.78, "y": 1.47}, {"time": 1.3333, "x": -0.52, "y": -0.21, "curve": [1.423, -0.52, 1.512, -3.26, 1.423, -0.21, 1.512, 6.66]}, {"time": 1.6, "x": -3.26, "y": 6.66, "curve": [1.623, -3.26, 1.645, -0.52, 1.623, 6.66, 1.645, -0.21]}, {"time": 1.6667, "x": -0.52, "y": -0.21, "curve": [1.722, -0.52, 1.778, 3.72, 1.722, -0.21, 1.778, -10.48]}, {"time": 1.8333, "x": 3.52, "y": -10.56, "curve": [1.945, 3.12, 2.057, -7.72, 1.945, -10.72, 2.057, -6.45]}, {"time": 2.1667, "x": -9.1, "y": -5.74, "curve": [2.467, -12.82, 2.756, -9.07, 2.467, -3.82, 2.756, -3.74]}, {"time": 3, "x": -10.39, "y": -4.28, "curve": [3.058, -10.68, 3.113, -10.92, 3.058, -4.4, 3.113, -4.5]}, {"time": 3.1667, "x": -10.92, "y": -4.5, "curve": [3.561, -10.92, 3.944, -2.48, 3.561, -4.5, 3.944, -1.02]}, {"time": 4.3333, "x": -0.52, "y": -0.21, "curve": [4.391, -0.23, 4.446, 0, 4.391, -0.09, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -10.92, 4.947, 0, 5.395, -4.5]}, {"time": 5.8333, "x": -10.92, "y": -4.5, "curve": [6.228, -10.92, 6.611, -0.52, 6.228, -4.5, 6.611, -0.21]}, {"time": 7, "x": -0.52, "y": -0.21}]}, "bone44": {"translate": [{"x": -0.74, "y": -0.3, "curve": "stepped"}, {"time": 0.3333, "x": -0.74, "y": -0.3}, {"time": 0.8333, "x": 4.21, "y": 2.22}, {"time": 1.3333, "x": -0.74, "y": -0.3, "curve": [1.423, -0.74, 1.512, -3.48, 1.423, -0.3, 1.512, 6.57]}, {"time": 1.6, "x": -3.48, "y": 6.57, "curve": [1.623, -3.48, 1.778, 3.44, 1.623, 6.57, 1.778, -10.59]}, {"time": 1.8333, "x": 3.16, "y": -10.71, "curve": [1.945, 2.59, 2.057, -8.56, 1.945, -10.94, 2.057, -6.8]}, {"time": 2.1667, "x": -10.32, "y": -6.25, "curve": [2.467, -15.07, 2.756, -12.85, 2.467, -4.75, 2.756, -5.3]}, {"time": 3, "x": -14.73, "y": -6.07, "curve": [3.058, -15.14, 3.113, -15.47, 3.058, -6.24, 3.113, -6.38]}, {"time": 3.1667, "x": -15.47, "y": -6.38, "curve": [3.561, -15.47, 3.944, -3.51, 3.561, -6.38, 3.944, -1.45]}, {"time": 4.3333, "x": -0.74, "y": -0.3, "curve": [4.391, -0.32, 4.446, 0, 4.391, -0.13, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -15.47, 4.947, 0, 5.395, -6.38]}, {"time": 5.8333, "x": -15.47, "y": -6.38, "curve": [6.228, -15.47, 6.611, -0.74, 6.228, -6.38, 6.611, -0.3]}, {"time": 7, "x": -0.74, "y": -0.3}]}, "bone45": {"rotate": [{"time": 1.3333}, {"time": 3, "value": 1.52, "curve": [3.45, 1.52, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 1.52]}, {"time": 5.6667, "value": 1.52, "curve": [6.117, 1.52, 6.556, 0]}, {"time": 7}]}, "bone46": {"rotate": [{"value": 0.07, "curve": "stepped"}, {"time": 1.3333, "value": 0.07}, {"time": 3, "value": 1.44, "curve": [3.058, 1.48, 3.113, 1.52]}, {"time": 3.1667, "value": 1.52, "curve": [3.561, 1.52, 3.944, 0.34]}, {"time": 4.3333, "value": 0.07, "curve": [4.391, 0.03, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, 1.52]}, {"time": 5.8333, "value": 1.52, "curve": [6.228, 1.52, 6.611, 0.07]}, {"time": 7, "value": 0.07}]}, "bone47": {"rotate": [{"value": 0.25, "curve": "stepped"}, {"time": 1.3333, "value": 0.25}, {"time": 3, "value": 1.27, "curve": [3.114, 1.41, 3.224, 1.52]}, {"time": 3.3333, "value": 1.52, "curve": [3.671, 1.52, 4, 0.67]}, {"time": 4.3333, "value": 0.25, "curve": [4.446, 0.1, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 1.52]}, {"time": 6, "value": 1.52, "curve": [6.338, 1.52, 6.667, 0.25]}, {"time": 7, "value": 0.25}]}, "bone48": {"rotate": [{"time": 1.3333}, {"time": 3, "value": 0.41, "curve": [3.45, 0.41, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 0.41]}, {"time": 5.6667, "value": 0.41, "curve": [6.117, 0.41, 6.556, 0]}, {"time": 7}]}, "bone49": {"rotate": [{"value": 0.06, "curve": "stepped"}, {"time": 1.3333, "value": 0.06}, {"time": 3, "value": 1.11, "curve": [3.058, 1.15, 3.113, 1.17]}, {"time": 3.1667, "value": 1.17, "curve": [3.561, 1.17, 3.944, 0.27]}, {"time": 4.3333, "value": 0.06, "curve": [4.391, 0.02, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, 1.17]}, {"time": 5.8333, "value": 1.17, "curve": [6.228, 1.17, 6.611, 0.06]}, {"time": 7, "value": 0.06}]}, "bone50": {"rotate": [{"value": 0.19, "curve": "stepped"}, {"time": 1.3333, "value": 0.19}, {"time": 3, "value": 0.98, "curve": [3.114, 1.09, 3.224, 1.17]}, {"time": 3.3333, "value": 1.17, "curve": [3.671, 1.17, 4, 0.51]}, {"time": 4.3333, "value": 0.19, "curve": [4.446, 0.08, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 1.17]}, {"time": 6, "value": 1.17, "curve": [6.338, 1.17, 6.667, 0.19]}, {"time": 7, "value": 0.19}]}, "bone51": {"rotate": [{"value": 0.38, "curve": "stepped"}, {"time": 1.3333, "value": 0.38}, {"time": 3, "value": 0.79, "curve": [3.168, 1, 3.335, 1.17]}, {"time": 3.5, "value": 1.17, "curve": [3.781, 1.17, 4.056, 0.72]}, {"time": 4.3333, "value": 0.38, "curve": [4.501, 0.17, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, 1.17]}, {"time": 6.1667, "value": 1.17, "curve": [6.447, 1.17, 6.722, 0.38]}, {"time": 7, "value": 0.38}]}, "bone54": {"rotate": [{"time": 1.3333}, {"time": 3, "value": 4.62, "curve": [3.45, 4.62, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 4.62]}, {"time": 5.6667, "value": 4.62, "curve": [6.117, 4.62, 6.556, 0]}, {"time": 7}]}, "bone55": {"rotate": [{"value": 0.21, "curve": "stepped"}, {"time": 1.3333, "value": 0.21}, {"time": 3, "value": 4.14, "curve": [3.058, 4.26, 3.113, 4.35]}, {"time": 3.1667, "value": 4.35, "curve": [3.561, 4.35, 3.944, 0.99]}, {"time": 4.3333, "value": 0.21, "curve": [4.391, 0.09, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, 4.35]}, {"time": 5.8333, "value": 4.35, "curve": [6.228, 4.35, 6.611, 0.21]}, {"time": 7, "value": 0.21}]}, "bone56": {"rotate": [{"value": 0.55, "curve": "stepped"}, {"time": 1.3333, "value": 0.55}, {"time": 3, "value": 2.87, "curve": [3.114, 3.19, 3.224, 3.43]}, {"time": 3.3333, "value": 3.43, "curve": [3.671, 3.43, 4, 1.51]}, {"time": 4.3333, "value": 0.55, "curve": [4.446, 0.23, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 3.43]}, {"time": 6, "value": 3.43, "curve": [6.338, 3.43, 6.667, 0.55]}, {"time": 7, "value": 0.55}]}, "bone57": {"rotate": [{"time": 1.6667, "curve": [2.111, 0, 2.556, -2.5]}, {"time": 3, "value": -2.5, "curve": [3.45, -2.5, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -2.5]}, {"time": 5.6667, "value": -2.5, "curve": [6.117, -2.5, 6.556, 0]}, {"time": 7}]}, "bone58": {"rotate": [{"value": -0.12, "curve": "stepped"}, {"time": 1.6667, "value": -0.12, "curve": [2.131, -0.12, 2.62, -1.91]}, {"time": 3, "value": -2.38, "curve": [3.058, -2.45, 3.113, -2.5]}, {"time": 3.1667, "value": -2.5, "curve": [3.561, -2.5, 3.944, -0.57]}, {"time": 4.3333, "value": -0.12, "curve": [4.391, -0.05, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -2.5]}, {"time": 5.8333, "value": -2.5, "curve": [6.228, -2.5, 6.611, -0.12]}, {"time": 7, "value": -0.12}]}, "bone59": {"rotate": [{"value": -0.4, "curve": "stepped"}, {"time": 1.6667, "value": -0.4, "curve": [2.129, -0.4, 2.674, -1.4]}, {"time": 3, "value": -2.1, "curve": [3.114, -2.33, 3.224, -2.5]}, {"time": 3.3333, "value": -2.5, "curve": [3.671, -2.5, 4, -1.1]}, {"time": 4.3333, "value": -0.4, "curve": [4.446, -0.17, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, -2.5]}, {"time": 6, "value": -2.5, "curve": [6.338, -2.5, 6.667, -0.4]}, {"time": 7, "value": -0.4}]}, "bone63": {"rotate": [{"time": 1.3333}, {"time": 3, "value": 3.81, "curve": [3.45, 3.81, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 3.81]}, {"time": 5.6667, "value": 3.81, "curve": [6.117, 3.81, 6.556, 0]}, {"time": 7}]}, "bone64": {"rotate": [{"value": 0.18, "curve": "stepped"}, {"time": 1.3333, "value": 0.18}, {"time": 3, "value": 3.63, "curve": [3.058, 3.73, 3.113, 3.81]}, {"time": 3.1667, "value": 3.81, "curve": [3.561, 3.81, 3.944, 0.87]}, {"time": 4.3333, "value": 0.18, "curve": [4.391, 0.08, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, 3.81]}, {"time": 5.8333, "value": 3.81, "curve": [6.228, 3.81, 6.611, 0.18]}, {"time": 7, "value": 0.18}]}, "bone65": {"rotate": [{"value": 0.62, "curve": "stepped"}, {"time": 1.3333, "value": 0.62}, {"time": 3, "value": 3.19, "curve": [3.114, 3.55, 3.224, 3.81]}, {"time": 3.3333, "value": 3.81, "curve": [3.671, 3.81, 4, 1.67]}, {"time": 4.3333, "value": 0.62, "curve": [4.446, 0.26, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 3.81]}, {"time": 6, "value": 3.81, "curve": [6.338, 3.81, 6.667, 0.62]}, {"time": 7, "value": 0.62}]}, "bone66": {"rotate": [{"value": 1.22, "curve": "stepped"}, {"time": 1.3333, "value": 1.22}, {"time": 3, "value": 2.59, "curve": [3.168, 3.26, 3.335, 3.81]}, {"time": 3.5, "value": 3.81, "curve": [3.781, 3.81, 4.056, 2.34]}, {"time": 4.3333, "value": 1.22, "curve": [4.501, 0.55, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, 3.81]}, {"time": 6.1667, "value": 3.81, "curve": [6.447, 3.81, 6.722, 1.22]}, {"time": 7, "value": 1.22}]}, "bone70": {"rotate": [{"time": 1.3333}, {"time": 3, "value": -1.75, "curve": [3.45, -1.75, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -1.75]}, {"time": 5.6667, "value": -1.75, "curve": [6.117, -1.75, 6.556, 0]}, {"time": 7}]}, "bone71": {"rotate": [{"value": -0.08, "curve": "stepped"}, {"time": 1.3333, "value": -0.08}, {"time": 3, "value": -1.67, "curve": [3.058, -1.72, 3.113, -1.75]}, {"time": 3.1667, "value": -1.75, "curve": [3.561, -1.75, 3.944, -0.4]}, {"time": 4.3333, "value": -0.08, "curve": [4.391, -0.04, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -1.75]}, {"time": 5.8333, "value": -1.75, "curve": [6.228, -1.75, 6.611, -0.08]}, {"time": 7, "value": -0.08}]}, "bone72": {"rotate": [{"value": -0.28, "curve": "stepped"}, {"time": 1.3333, "value": -0.28}, {"time": 3, "value": -1.47, "curve": [3.114, -1.63, 3.224, -1.75]}, {"time": 3.3333, "value": -1.75, "curve": [3.671, -1.75, 4, -0.77]}, {"time": 4.3333, "value": -0.28, "curve": [4.446, -0.12, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, -1.75]}, {"time": 6, "value": -1.75, "curve": [6.338, -1.75, 6.667, -0.28]}, {"time": 7, "value": -0.28}]}, "bone73": {"rotate": [{"time": 1.3333}, {"time": 3, "value": 2.87, "curve": [3.45, 2.87, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.87]}, {"time": 5.6667, "value": 2.87, "curve": [6.117, 2.87, 6.556, 0]}, {"time": 7}]}, "bone74": {"rotate": [{"time": 1.3333}, {"time": 3, "value": 2.87, "curve": [3.45, 2.87, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.87]}, {"time": 5.6667, "value": 2.87, "curve": [6.117, 2.87, 6.556, 0]}, {"time": 7}]}, "bone75": {"rotate": [{"time": 1.3333}, {"time": 3, "value": 2.87, "curve": [3.45, 2.87, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.87]}, {"time": 5.6667, "value": 2.87, "curve": [6.117, 2.87, 6.556, 0]}, {"time": 7}]}, "bone81": {"translate": [{}, {"time": 0.3333, "x": -6.25, "y": 18.43, "curve": [0.578, -2.65, 0.383, -12.17, 0.578, 9.94, 0.383, 28.69]}, {"time": 0.8333, "x": -9.2, "y": 21.69, "curve": [1, -8.1, 1.167, -4.64, 1, 19.1, 1.171, 11.16]}, {"time": 1.3333, "x": -9.2, "y": 21.69, "curve": [1.532, -14.68, 1.5, 5.64, 1.532, 34.6, 1.5, -13.29]}, {"time": 1.6667}]}, "bone82": {"translate": [{}, {"time": 0.3333, "x": -4.35, "y": 9.29, "curve": [0.578, -1.21, 0.383, -10.63, 0.578, 5.56, 0.383, 12.6]}, {"time": 0.8333, "x": -8.04, "y": 9.53, "curve": [1, -7.08, 1.167, -4.05, 1, 8.39, 1.168, 4.81]}, {"time": 1.3333, "x": -8.04, "y": 9.53, "curve": [1.532, -12.82, 1.5, 4.92, 1.532, 15.2, 1.5, -5.84]}, {"time": 1.6667}]}, "bone83": {"rotate": [{"time": 1.3333}, {"time": 3, "value": -3.17, "curve": [3.45, -3.17, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -3.17]}, {"time": 5.6667, "value": -3.17, "curve": [6.117, -3.17, 6.556, 0]}, {"time": 7}]}, "bone84": {"rotate": [{"value": -0.15, "curve": "stepped"}, {"time": 1.3333, "value": -0.15}, {"time": 3, "value": -3.02, "curve": [3.058, -3.1, 3.113, -3.17]}, {"time": 3.1667, "value": -3.17, "curve": [3.561, -3.17, 3.944, -0.72]}, {"time": 4.3333, "value": -0.15, "curve": [4.391, -0.07, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -3.17]}, {"time": 5.8333, "value": -3.17, "curve": [6.228, -3.17, 6.611, -0.15]}, {"time": 7, "value": -0.15}]}, "bone85": {"rotate": [{"value": -0.51, "curve": "stepped"}, {"time": 1.3333, "value": -0.51}, {"time": 3, "value": -2.66, "curve": [3.114, -2.95, 3.224, -3.17]}, {"time": 3.3333, "value": -3.17, "curve": [3.671, -3.17, 4, -1.39]}, {"time": 4.3333, "value": -0.51, "curve": [4.446, -0.22, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, -3.17]}, {"time": 6, "value": -3.17, "curve": [6.338, -3.17, 6.667, -0.51]}, {"time": 7, "value": -0.51}]}, "bone86": {"rotate": [{"value": -1.02, "curve": "stepped"}, {"time": 1.3333, "value": -1.02}, {"time": 3, "value": -2.15, "curve": [3.168, -2.71, 3.335, -3.17]}, {"time": 3.5, "value": -3.17, "curve": [3.781, -3.17, 4.056, -1.95]}, {"time": 4.3333, "value": -1.02, "curve": [4.501, -0.46, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, -3.17]}, {"time": 6.1667, "value": -3.17, "curve": [6.447, -3.17, 6.722, -1.02]}, {"time": 7, "value": -1.02}]}, "bone87": {"rotate": [{"value": -1.14, "curve": "stepped"}, {"time": 1.3333, "value": -1.14}, {"time": 3, "value": -1.12, "curve": [3.226, -1.69, 3.447, -2.26]}, {"time": 3.6667, "value": -2.26, "curve": [3.892, -2.26, 4.111, -1.7]}, {"time": 4.3333, "value": -1.14, "curve": [4.559, -0.57, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, -2.26]}, {"time": 6.3333, "value": -2.26, "curve": [6.559, -2.26, 6.778, -1.14]}, {"time": 7, "value": -1.14}]}, "bone88": {"rotate": [{"value": -1.55, "curve": "stepped"}, {"time": 1.3333, "value": -1.55}, {"time": 3, "value": -0.71, "curve": [3.28, -1.38, 3.559, -2.26]}, {"time": 3.8333, "value": -2.26, "curve": [4.001, -2.26, 4.167, -1.95]}, {"time": 4.3333, "value": -1.55, "curve": [4.613, -0.89, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, -2.26]}, {"time": 6.5, "value": -2.26, "curve": [6.668, -2.26, 6.833, -1.55]}, {"time": 7, "value": -1.55}]}, "bone89": {"rotate": [{"value": -13.99, "curve": "stepped"}, {"time": 1.3333, "value": -13.99}, {"time": 3, "value": -2.63, "curve": [3.337, -7.3, 3.671, -16.61]}, {"time": 4, "value": -16.61, "curve": [4.113, -16.61, 4.222, -15.53]}, {"time": 4.3333, "value": -13.99, "curve": [4.671, -9.32, 5.002, 0]}, {"time": 5.3333, "curve": [5.781, 0, 6.228, -16.61]}, {"time": 6.6667, "value": -16.61, "curve": [6.779, -16.61, 6.889, -13.99]}, {"time": 7, "value": -13.99}]}, "bone90": {"rotate": [{"value": -15.84, "curve": "stepped"}, {"time": 1.3333, "value": -15.84}, {"time": 3, "value": -0.77, "curve": [3.394, -3.98, 3.784, -16.61]}, {"time": 4.1667, "value": -16.61, "curve": [4.224, -16.61, 4.278, -16.29]}, {"time": 4.3333, "value": -15.84, "curve": [4.729, -12.63, 5.114, 0]}, {"time": 5.5, "curve": [5.947, 0, 6.395, -16.61]}, {"time": 6.8333, "value": -16.61, "curve": [6.89, -16.61, 6.944, -15.84]}, {"time": 7, "value": -15.84}]}, "bone91": {"rotate": [{"value": -16.61, "curve": "stepped"}, {"time": 1.3333, "value": -16.61}, {"time": 3, "curve": [3.447, 0, 3.889, -16.61]}, {"time": 4.3333, "value": -16.61, "curve": [4.784, -16.61, 5.225, 0]}, {"time": 5.6667, "curve": [6.114, 0, 6.556, -16.61]}, {"time": 7, "value": -16.61}]}, "bone92": {"rotate": [{"value": -0.1, "curve": "stepped"}, {"time": 1.3333, "value": -0.1}, {"time": 3, "value": -2.02, "curve": [3.058, -2.07, 3.113, -2.12]}, {"time": 3.1667, "value": -2.12, "curve": [3.561, -2.12, 3.944, -0.48]}, {"time": 4.3333, "value": -0.1, "curve": [4.391, -0.04, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -2.12]}, {"time": 5.8333, "value": -2.12, "curve": [6.228, -2.12, 6.611, -0.1]}, {"time": 7, "value": -0.1}]}, "bone93": {"rotate": [{"value": -0.34, "curve": "stepped"}, {"time": 1.3333, "value": -0.34}, {"time": 3, "value": -1.77, "curve": [3.114, -1.97, 3.224, -2.12]}, {"time": 3.3333, "value": -2.12, "curve": [3.671, -2.12, 4, -0.93]}, {"time": 4.3333, "value": -0.34, "curve": [4.446, -0.14, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, -2.12]}, {"time": 6, "value": -2.12, "curve": [6.338, -2.12, 6.667, -0.34]}, {"time": 7, "value": -0.34}]}, "bone94": {"rotate": [{"value": -0.68, "curve": "stepped"}, {"time": 1.3333, "value": -0.68}, {"time": 3, "value": -1.44, "curve": [3.168, -1.81, 3.335, -2.12]}, {"time": 3.5, "value": -2.12, "curve": [3.781, -2.12, 4.056, -1.3]}, {"time": 4.3333, "value": -0.68, "curve": [4.501, -0.3, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, -2.12]}, {"time": 6.1667, "value": -2.12, "curve": [6.447, -2.12, 6.722, -0.68]}, {"time": 7, "value": -0.68}]}, "bone95": {"rotate": [{"value": -2.63, "curve": "stepped"}, {"time": 1.3333, "value": -2.63}, {"time": 3, "value": -2.59, "curve": [3.226, -3.9, 3.447, -5.22]}, {"time": 3.6667, "value": -5.22, "curve": [3.892, -5.22, 4.111, -3.92]}, {"time": 4.3333, "value": -2.63, "curve": [4.559, -1.32, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, -5.22]}, {"time": 6.3333, "value": -5.22, "curve": [6.559, -5.22, 6.778, -2.63]}, {"time": 7, "value": -2.63}]}, "bone96": {"rotate": [{"value": -3.58, "curve": "stepped"}, {"time": 1.3333, "value": -3.58}, {"time": 3, "value": -1.64, "curve": [3.28, -3.17, 3.559, -5.22]}, {"time": 3.8333, "value": -5.22, "curve": [4.001, -5.22, 4.167, -4.49]}, {"time": 4.3333, "value": -3.58, "curve": [4.613, -2.05, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, -5.22]}, {"time": 6.5, "value": -5.22, "curve": [6.668, -5.22, 6.833, -3.58]}, {"time": 7, "value": -3.58}]}, "bone97": {"rotate": [{"value": -4.4, "curve": "stepped"}, {"time": 1.3333, "value": -4.4}, {"time": 3, "value": -0.83, "curve": [3.337, -2.29, 3.671, -5.22]}, {"time": 4, "value": -5.22, "curve": [4.113, -5.22, 4.222, -4.88]}, {"time": 4.3333, "value": -4.4, "curve": [4.671, -2.93, 5.002, 0]}, {"time": 5.3333, "curve": [5.781, 0, 6.228, -5.22]}, {"time": 6.6667, "value": -5.22, "curve": [6.779, -5.22, 6.889, -4.4]}, {"time": 7, "value": -4.4}]}, "bone98": {"rotate": [{"time": 1.3333}, {"time": 1.6667, "value": 10.28}, {"time": 3, "value": -2.03, "curve": [3.45, -2.03, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -2.03]}, {"time": 5.6667, "value": -2.03, "curve": [6.117, -2.03, 6.556, 0]}, {"time": 7}]}, "bone99": {"rotate": [{"value": -0.1, "curve": "stepped"}, {"time": 1.3333, "value": -0.1}, {"time": 1.6667, "value": 10.22}, {"time": 3, "value": -1.94, "curve": [3.058, -1.99, 3.113, -2.03]}, {"time": 3.1667, "value": -2.03, "curve": [3.561, -2.03, 3.944, -0.46]}, {"time": 4.3333, "value": -0.1, "curve": [4.391, -0.04, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -2.03]}, {"time": 5.8333, "value": -2.03, "curve": [6.228, -2.03, 6.611, -0.1]}, {"time": 7, "value": -0.1}]}, "bone100": {"rotate": [{"value": -0.33, "curve": "stepped"}, {"time": 1.3333, "value": -0.33}, {"time": 1.6667, "value": 10.08}, {"time": 3, "value": -1.7, "curve": [3.114, -1.89, 3.224, -2.03]}, {"time": 3.3333, "value": -2.03, "curve": [3.671, -2.03, 4, -0.89]}, {"time": 4.3333, "value": -0.33, "curve": [4.446, -0.14, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, -2.03]}, {"time": 6, "value": -2.03, "curve": [6.338, -2.03, 6.667, -0.33]}, {"time": 7, "value": -0.33}]}, "bone101": {"rotate": [{"value": -0.65, "curve": "stepped"}, {"time": 1.3333, "value": -0.65}, {"time": 1.6667, "value": -8.41}, {"time": 3, "value": -1.38, "curve": [3.168, -1.74, 3.335, -2.03]}, {"time": 3.5, "value": -2.03, "curve": [3.781, -2.03, 4.056, -1.25]}, {"time": 4.3333, "value": -0.65, "curve": [4.501, -0.29, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, -2.03]}, {"time": 6.1667, "value": -2.03, "curve": [6.447, -2.03, 6.722, -0.65]}, {"time": 7, "value": -0.65}]}, "bone102": {"rotate": [{"value": -1.02, "curve": "stepped"}, {"time": 1.3333, "value": -1.02}, {"time": 1.6667, "value": -8.63}, {"time": 3, "value": -1.01, "curve": [3.226, -1.52, 3.447, -2.03]}, {"time": 3.6667, "value": -2.03, "curve": [3.892, -2.03, 4.111, -1.53]}, {"time": 4.3333, "value": -1.02, "curve": [4.559, -0.52, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, -2.03]}, {"time": 6.3333, "value": -2.03, "curve": [6.559, -2.03, 6.778, -1.02]}, {"time": 7, "value": -1.02}]}, "bone103": {"rotate": [{"value": -0.02, "curve": "stepped"}, {"time": 0.3333, "value": -0.02}, {"time": 0.6667, "value": -8.36}, {"time": 0.8333, "value": -0.02, "curve": "stepped"}, {"time": 1.3333, "value": -0.02}, {"time": 1.6667, "value": 10.12}, {"time": 3, "value": -0.49, "curve": [3.058, -0.5, 3.113, -0.52]}, {"time": 3.1667, "value": -0.52, "curve": [3.561, -0.52, 3.944, -0.12]}, {"time": 4.3333, "value": -0.02, "curve": [4.391, -0.01, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -0.52]}, {"time": 5.8333, "value": -0.52, "curve": [6.228, -0.52, 6.611, -0.02]}, {"time": 7, "value": -0.02}]}, "bone104": {"rotate": [{"value": -0.08, "curve": "stepped"}, {"time": 0.3333, "value": -0.08}, {"time": 0.6667, "value": -8.42}, {"time": 0.8333, "value": -0.08, "curve": "stepped"}, {"time": 1.3333, "value": -0.08}, {"time": 1.6667, "value": 10.09}, {"time": 3, "value": -0.43, "curve": [3.114, -0.48, 3.224, -0.52]}, {"time": 3.3333, "value": -0.52, "curve": [3.671, -0.52, 4, -0.23]}, {"time": 4.3333, "value": -0.08, "curve": [4.446, -0.04, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, -0.52]}, {"time": 6, "value": -0.52, "curve": [6.338, -0.52, 6.667, -0.08]}, {"time": 7, "value": -0.08}]}, "bone105": {"rotate": [{"value": -0.17, "curve": "stepped"}, {"time": 0.3333, "value": -0.17}, {"time": 0.6667, "value": 3.65}, {"time": 0.8333, "value": -0.17, "curve": "stepped"}, {"time": 1.3333, "value": -0.17}, {"time": 1.6667, "value": 10.04}, {"time": 3, "value": -0.35, "curve": [3.168, -0.44, 3.335, -0.52]}, {"time": 3.5, "value": -0.52, "curve": [3.781, -0.52, 4.056, -0.32]}, {"time": 4.3333, "value": -0.17, "curve": [4.501, -0.07, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, -0.52]}, {"time": 6.1667, "value": -0.52, "curve": [6.447, -0.52, 6.722, -0.17]}, {"time": 7, "value": -0.17}]}, "bone106": {"rotate": [{"value": -0.26, "curve": "stepped"}, {"time": 0.3333, "value": -0.26}, {"time": 0.6667, "value": 3.56}, {"time": 0.8333, "value": -0.26, "curve": "stepped"}, {"time": 1.3333, "value": -0.26}, {"time": 1.6667, "value": 9.98}, {"time": 3, "value": -0.26, "curve": [3.226, -0.38, 3.447, -0.52]}, {"time": 3.6667, "value": -0.52, "curve": [3.892, -0.52, 4.111, -0.39]}, {"time": 4.3333, "value": -0.26, "curve": [4.559, -0.13, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, -0.52]}, {"time": 6.3333, "value": -0.52, "curve": [6.559, -0.52, 6.778, -0.26]}, {"time": 7, "value": -0.26}]}, "bone107": {"rotate": [{"time": 1.6667, "curve": [2.111, 0, 2.556, -0.58]}, {"time": 3, "value": -0.58, "curve": [3.45, -0.58, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -0.58]}, {"time": 5.6667, "value": -0.58, "curve": [6.117, -0.58, 6.556, 0]}, {"time": 7}]}, "bone108": {"rotate": [{"value": -0.03, "curve": "stepped"}, {"time": 1.6667, "value": -0.03, "curve": [2.131, -0.03, 2.62, -0.44]}, {"time": 3, "value": -0.55, "curve": [3.058, -0.57, 3.113, -0.58]}, {"time": 3.1667, "value": -0.58, "curve": [3.561, -0.58, 3.944, -0.13]}, {"time": 4.3333, "value": -0.03, "curve": [4.391, -0.01, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -0.58]}, {"time": 5.8333, "value": -0.58, "curve": [6.228, -0.58, 6.611, -0.03]}, {"time": 7, "value": -0.03}]}, "bone109": {"rotate": [{"value": -0.09, "curve": "stepped"}, {"time": 1.6667, "value": -0.09, "curve": [2.129, -0.09, 2.674, -0.33]}, {"time": 3, "value": -0.49, "curve": [3.114, -0.54, 3.224, -0.58]}, {"time": 3.3333, "value": -0.58, "curve": [3.671, -0.58, 4, -0.26]}, {"time": 4.3333, "value": -0.09, "curve": [4.446, -0.04, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, -0.58]}, {"time": 6, "value": -0.58, "curve": [6.338, -0.58, 6.667, -0.09]}, {"time": 7, "value": -0.09}]}, "bone110": {"rotate": [{"value": -0.19, "curve": "stepped"}, {"time": 1.6667, "value": -0.19, "curve": [2.127, -0.19, 2.728, -0.23]}, {"time": 3, "value": -0.4, "curve": [3.168, -0.5, 3.335, -0.58]}, {"time": 3.5, "value": -0.58, "curve": [3.781, -0.58, 4.056, -0.36]}, {"time": 4.3333, "value": -0.19, "curve": [4.501, -0.08, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, -0.58]}, {"time": 6.1667, "value": -0.58, "curve": [6.447, -0.58, 6.722, -0.19]}, {"time": 7, "value": -0.19}]}, "bone111": {"rotate": [{"value": -0.29, "curve": "stepped"}, {"time": 1.6667, "value": -0.29, "curve": [2.126, -0.29, 2.784, -0.15]}, {"time": 3, "value": -0.29, "curve": [3.226, -0.43, 3.447, -0.58]}, {"time": 3.6667, "value": -0.58, "curve": [3.892, -0.58, 4.111, -0.44]}, {"time": 4.3333, "value": -0.29, "curve": [4.559, -0.15, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, -0.58]}, {"time": 6.3333, "value": -0.58, "curve": [6.559, -0.58, 6.778, -0.29]}, {"time": 7, "value": -0.29}]}, "bone112": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 4.16, "curve": [3.45, 4.16, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 4.16]}, {"time": 5.6667, "value": 4.16, "curve": [6.117, 4.16, 6.556, 0]}, {"time": 7}]}, "bone113": {"rotate": [{"value": 0.2, "curve": "stepped"}, {"time": 0.3333, "value": 0.2}, {"time": 0.8333, "value": 3.63}, {"time": 1.3333, "value": 0.2}, {"time": 3, "value": 3.96, "curve": [3.058, 4.07, 3.113, 4.16]}, {"time": 3.1667, "value": 4.16, "curve": [3.561, 4.16, 3.944, 0.94]}, {"time": 4.3333, "value": 0.2, "curve": [4.391, 0.09, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, 4.16]}, {"time": 5.8333, "value": 4.16, "curve": [6.228, 4.16, 6.611, 0.2]}, {"time": 7, "value": 0.2}]}, "bone114": {"rotate": [{"value": 1, "curve": "stepped"}, {"time": 0.3333, "value": 1}, {"time": 0.8333, "value": 4.44}, {"time": 1.3333, "value": 1}, {"time": 3, "value": 5.19, "curve": [3.114, 5.78, 3.224, 6.2]}, {"time": 3.3333, "value": 6.2, "curve": [3.671, 6.2, 4, 2.72]}, {"time": 4.3333, "value": 1, "curve": [4.446, 0.42, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 6.2]}, {"time": 6, "value": 6.2, "curve": [6.338, 6.2, 6.667, 1]}, {"time": 7, "value": 1}]}, "bone115": {"rotate": [{"value": 2.73, "curve": "stepped"}, {"time": 0.3333, "value": 2.73}, {"time": 0.8333, "value": 6.17}, {"time": 1.3333, "value": 2.73}, {"time": 3, "value": 5.78, "curve": [3.168, 7.29, 3.335, 8.52]}, {"time": 3.5, "value": 8.52, "curve": [3.781, 8.52, 4.056, 5.23]}, {"time": 4.3333, "value": 2.73, "curve": [4.501, 1.23, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, 8.52]}, {"time": 6.1667, "value": 8.52, "curve": [6.447, 8.52, 6.722, 2.73]}, {"time": 7, "value": 2.73}]}, "bone116": {"rotate": [{"value": 5.47, "curve": "stepped"}, {"time": 0.3333, "value": 5.47}, {"time": 0.8333, "value": 8.91}, {"time": 1.3333, "value": 5.47}, {"time": 3, "value": 5.39, "curve": [3.226, 8.11, 3.447, 10.86]}, {"time": 3.6667, "value": 10.86, "curve": [3.892, 10.86, 4.111, 8.15]}, {"time": 4.3333, "value": 5.47, "curve": [4.559, 2.76, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, 10.86]}, {"time": 6.3333, "value": 10.86, "curve": [6.559, 10.86, 6.778, 5.47]}, {"time": 7, "value": 5.47}]}, "bone117": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 27.44}, {"time": 1.3333}, {"time": 3, "value": 2.31, "curve": [3.45, 2.31, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.31]}, {"time": 5.6667, "value": 2.31, "curve": [6.117, 2.31, 6.556, 0]}, {"time": 7}]}, "bone118": {"rotate": [{"value": 0.11, "curve": "stepped"}, {"time": 0.3333, "value": 0.11}, {"time": 0.8333, "value": 3.55}, {"time": 1.3333, "value": 0.11}, {"time": 3, "value": 2.2, "curve": [3.058, 2.26, 3.113, 2.31]}, {"time": 3.1667, "value": 2.31, "curve": [3.561, 2.31, 3.944, 0.52]}, {"time": 4.3333, "value": 0.11, "curve": [4.391, 0.05, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, 2.31]}, {"time": 5.8333, "value": 2.31, "curve": [6.228, 2.31, 6.611, 0.11]}, {"time": 7, "value": 0.11}]}, "bone119": {"rotate": [{"value": 0.7, "curve": "stepped"}, {"time": 0.3333, "value": 0.7}, {"time": 0.8333, "value": 4.14}, {"time": 1.3333, "value": 0.7}, {"time": 3, "value": 3.62, "curve": [3.114, 4.02, 3.224, 4.32]}, {"time": 3.3333, "value": 4.32, "curve": [3.671, 4.32, 4, 1.9]}, {"time": 4.3333, "value": 0.7, "curve": [4.446, 0.29, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 4.32]}, {"time": 6, "value": 4.32, "curve": [6.338, 4.32, 6.667, 0.7]}, {"time": 7, "value": 0.7}]}, "bone120": {"rotate": [{"value": 1.78, "curve": "stepped"}, {"time": 0.3333, "value": 1.78}, {"time": 0.8333, "value": 5.22}, {"time": 1.3333, "value": 1.78}, {"time": 3, "value": 3.76, "curve": [3.168, 4.74, 3.335, 5.54]}, {"time": 3.5, "value": 5.54, "curve": [3.781, 5.54, 4.056, 3.4]}, {"time": 4.3333, "value": 1.78, "curve": [4.501, 0.8, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, 5.54]}, {"time": 6.1667, "value": 5.54, "curve": [6.447, 5.54, 6.722, 1.78]}, {"time": 7, "value": 1.78}]}, "bone121": {"rotate": [{"value": 3.52, "curve": "stepped"}, {"time": 0.3333, "value": 3.52}, {"time": 0.8333, "value": 6.96}, {"time": 1.3333, "value": 3.52}, {"time": 3, "value": 3.47, "curve": [3.226, 5.21, 3.447, 6.99]}, {"time": 3.6667, "value": 6.99, "curve": [3.892, 6.99, 4.111, 5.24]}, {"time": 4.3333, "value": 3.52, "curve": [4.559, 1.77, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, 6.99]}, {"time": 6.3333, "value": 6.99, "curve": [6.559, 6.99, 6.778, 3.52]}, {"time": 7, "value": 3.52}]}, "bone122": {"rotate": [{"value": 4.79, "curve": "stepped"}, {"time": 0.3333, "value": 4.79}, {"time": 0.8333, "value": 8.22}, {"time": 1.3333, "value": 4.79}, {"time": 3, "value": 2.2, "curve": [3.28, 4.25, 3.559, 6.99]}, {"time": 3.8333, "value": 6.99, "curve": [4.001, 6.99, 4.167, 6.01]}, {"time": 4.3333, "value": 4.79, "curve": [4.613, 2.74, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, 6.99]}, {"time": 6.5, "value": 6.99, "curve": [6.668, 6.99, 6.833, 4.79]}, {"time": 7, "value": 4.79}]}, "bone123": {"rotate": [{"value": 5.88, "curve": "stepped"}, {"time": 0.3333, "value": 5.88}, {"time": 0.8333, "value": 9.32}, {"time": 1.3333, "value": 5.88}, {"time": 3, "value": 1.11, "curve": [3.337, 3.07, 3.671, 6.99]}, {"time": 4, "value": 6.99, "curve": [4.113, 6.99, 4.222, 6.53]}, {"time": 4.3333, "value": 5.88, "curve": [4.671, 3.92, 5.002, 0]}, {"time": 5.3333, "curve": [5.781, 0, 6.228, 6.99]}, {"time": 6.6667, "value": 6.99, "curve": [6.779, 6.99, 6.889, 5.88]}, {"time": 7, "value": 5.88}]}, "bone124": {"rotate": [{"value": 6.66, "curve": "stepped"}, {"time": 0.3333, "value": 6.66}, {"time": 0.8333, "value": 10.1}, {"time": 1.3333, "value": 6.66}, {"time": 3, "value": 0.32, "curve": [3.394, 1.67, 3.784, 6.99]}, {"time": 4.1667, "value": 6.99, "curve": [4.224, 6.99, 4.278, 6.85]}, {"time": 4.3333, "value": 6.66, "curve": [4.729, 5.31, 5.114, 0]}, {"time": 5.5, "curve": [5.947, 0, 6.395, 6.99]}, {"time": 6.8333, "value": 6.99, "curve": [6.89, 6.99, 6.944, 6.66]}, {"time": 7, "value": 6.66}]}, "bone125": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 14.92}, {"time": 1.3333}, {"time": 3, "value": 4.48, "curve": [3.45, 4.48, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 4.48]}, {"time": 5.6667, "value": 4.48, "curve": [6.117, 4.48, 6.556, 0]}, {"time": 7}]}, "bone126": {"rotate": [{"value": 0.21, "curve": "stepped"}, {"time": 0.3333, "value": 0.21}, {"time": 0.8333, "value": 3.65}, {"time": 1.3333, "value": 0.21}, {"time": 3, "value": 4.26, "curve": [3.058, 4.38, 3.113, 4.48]}, {"time": 3.1667, "value": 4.48, "curve": [3.561, 4.48, 3.944, 1.02]}, {"time": 4.3333, "value": 0.21, "curve": [4.391, 0.09, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, 4.48]}, {"time": 5.8333, "value": 4.48, "curve": [6.228, 4.48, 6.611, 0.21]}, {"time": 7, "value": 0.21}]}, "bone127": {"rotate": [{"value": 0.72, "curve": "stepped"}, {"time": 0.3333, "value": 0.72}, {"time": 0.8333, "value": 4.16}, {"time": 1.3333, "value": 0.72}, {"time": 3, "value": 3.75, "curve": [3.114, 4.17, 3.224, 4.48]}, {"time": 3.3333, "value": 4.48, "curve": [3.671, 4.48, 4, 1.97]}, {"time": 4.3333, "value": 0.72, "curve": [4.446, 0.3, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 4.48]}, {"time": 6, "value": 4.48, "curve": [6.338, 4.48, 6.667, 0.72]}, {"time": 7, "value": 0.72}]}, "bone128": {"rotate": [{"value": 2.96, "curve": "stepped"}, {"time": 0.3333, "value": 2.96}, {"time": 0.8333, "value": 6.4}, {"time": 1.3333, "value": 2.96}, {"time": 3, "value": 6.27, "curve": [3.168, 7.9, 3.335, 9.24]}, {"time": 3.5, "value": 9.24, "curve": [3.781, 9.24, 4.056, 5.68]}, {"time": 4.3333, "value": 2.96, "curve": [4.501, 1.33, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, 9.24]}, {"time": 6.1667, "value": 9.24, "curve": [6.447, 9.24, 6.722, 2.96]}, {"time": 7, "value": 2.96}]}, "bone129": {"rotate": [{"value": 5.7, "curve": "stepped"}, {"time": 0.3333, "value": 5.7}, {"time": 0.8333, "value": 9.13}, {"time": 1.3333, "value": 5.7}, {"time": 3, "value": 5.61, "curve": [3.226, 8.44, 3.447, 11.31]}, {"time": 3.6667, "value": 11.31, "curve": [3.892, 11.31, 4.111, 8.49]}, {"time": 4.3333, "value": 5.7, "curve": [4.559, 2.87, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, 11.31]}, {"time": 6.3333, "value": 11.31, "curve": [6.559, 11.31, 6.778, 5.7]}, {"time": 7, "value": 5.7}]}, "bone130": {"rotate": [{"value": 10.5, "curve": "stepped"}, {"time": 0.3333, "value": 10.5}, {"time": 0.8333, "value": 13.94}, {"time": 1.3333, "value": 10.5}, {"time": 3, "value": 4.83, "curve": [3.28, 9.32, 3.559, 15.32]}, {"time": 3.8333, "value": 15.32, "curve": [4.001, 15.32, 4.167, 13.18]}, {"time": 4.3333, "value": 10.5, "curve": [4.613, 6.01, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, 15.32]}, {"time": 6.5, "value": 15.32, "curve": [6.668, 15.32, 6.833, 10.5]}, {"time": 7, "value": 10.5}]}, "bone131": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 29.14}, {"time": 1.3333}, {"time": 3, "value": 2.97, "curve": [3.45, 2.97, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.97]}, {"time": 5.6667, "value": 2.97, "curve": [6.117, 2.97, 6.556, 0]}, {"time": 7}]}, "bone132": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 2.97, "curve": [3.45, 2.97, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.97]}, {"time": 5.6667, "value": 2.97, "curve": [6.117, 2.97, 6.556, 0]}, {"time": 7}]}, "bone133": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 2.97, "curve": [3.45, 2.97, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.97]}, {"time": 5.6667, "value": 2.97, "curve": [6.117, 2.97, 6.556, 0]}, {"time": 7}]}, "bone134": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 2.97, "curve": [3.45, 2.97, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.97]}, {"time": 5.6667, "value": 2.97, "curve": [6.117, 2.97, 6.556, 0]}, {"time": 7}]}, "bone135": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 2.97, "curve": [3.45, 2.97, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 2.97]}, {"time": 5.6667, "value": 2.97, "curve": [6.117, 2.97, 6.556, 0]}, {"time": 7}]}, "bone136": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 9.54, "curve": [3.45, 9.54, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 9.54]}, {"time": 5.6667, "value": 9.54, "curve": [6.117, 9.54, 6.556, 0]}, {"time": 7}]}, "bone137": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 9.54, "curve": [3.45, 9.54, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 9.54]}, {"time": 5.6667, "value": 9.54, "curve": [6.117, 9.54, 6.556, 0]}, {"time": 7}]}, "bone138": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 9.54, "curve": [3.45, 9.54, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 9.54]}, {"time": 5.6667, "value": 9.54, "curve": [6.117, 9.54, 6.556, 0]}, {"time": 7}]}, "bone139": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 3.42, "curve": [3.45, 3.42, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 3.42]}, {"time": 5.6667, "value": 3.42, "curve": [6.117, 3.42, 6.556, 0]}, {"time": 7}]}, "bone140": {"rotate": [{"value": 0.16, "curve": "stepped"}, {"time": 0.3333, "value": 0.16}, {"time": 0.8333, "value": 3.6}, {"time": 1.3333, "value": 0.16}, {"time": 3, "value": 3.25, "curve": [3.058, 3.34, 3.113, 3.42]}, {"time": 3.1667, "value": 3.42, "curve": [3.561, 3.42, 3.944, 0.78]}, {"time": 4.3333, "value": 0.16, "curve": [4.391, 0.07, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, 3.42]}, {"time": 5.8333, "value": 3.42, "curve": [6.228, 3.42, 6.611, 0.16]}, {"time": 7, "value": 0.16}]}, "bone141": {"rotate": [{"value": 0.55, "curve": "stepped"}, {"time": 0.3333, "value": 0.55}, {"time": 0.8333, "value": 3.99}, {"time": 1.3333, "value": 0.55}, {"time": 3, "value": 2.86, "curve": [3.114, 3.18, 3.224, 3.42]}, {"time": 3.3333, "value": 3.42, "curve": [3.671, 3.42, 4, 1.5]}, {"time": 4.3333, "value": 0.55, "curve": [4.446, 0.23, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 3.42]}, {"time": 6, "value": 3.42, "curve": [6.338, 3.42, 6.667, 0.55]}, {"time": 7, "value": 0.55}]}, "bone142": {"rotate": [{"value": 1.1, "curve": "stepped"}, {"time": 0.3333, "value": 1.1}, {"time": 0.8333, "value": 4.53}, {"time": 1.3333, "value": 1.1}, {"time": 3, "value": 2.32, "curve": [3.168, 2.92, 3.335, 3.42]}, {"time": 3.5, "value": 3.42, "curve": [3.781, 3.42, 4.056, 2.1]}, {"time": 4.3333, "value": 1.1, "curve": [4.501, 0.49, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, 3.42]}, {"time": 6.1667, "value": 3.42, "curve": [6.447, 3.42, 6.722, 1.1]}, {"time": 7, "value": 1.1}]}, "bone143": {"rotate": [{"value": 1.97, "curve": "stepped"}, {"time": 0.3333, "value": 1.97}, {"time": 0.8333, "value": 5.41}, {"time": 1.3333, "value": 1.97}, {"time": 3, "value": 1.94, "curve": [3.226, 2.92, 3.447, 3.91]}, {"time": 3.6667, "value": 3.91, "curve": [3.892, 3.91, 4.111, 2.93]}, {"time": 4.3333, "value": 1.97, "curve": [4.559, 0.99, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, 3.91]}, {"time": 6.3333, "value": 3.91, "curve": [6.559, 3.91, 6.778, 1.97]}, {"time": 7, "value": 1.97}]}, "bone144": {"rotate": [{"value": 7.59, "curve": "stepped"}, {"time": 0.3333, "value": 7.59}, {"time": 0.8333, "value": 11.03}, {"time": 1.3333, "value": 7.59}, {"time": 3, "value": 3.49, "curve": [3.28, 6.74, 3.559, 11.07]}, {"time": 3.8333, "value": 11.07, "curve": [4.001, 11.07, 4.167, 9.52]}, {"time": 4.3333, "value": 7.59, "curve": [4.613, 4.34, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, 11.07]}, {"time": 6.5, "value": 11.07, "curve": [6.668, 11.07, 6.833, 7.59]}, {"time": 7, "value": 7.59}]}, "bone145": {"rotate": [{"value": 9.33, "curve": "stepped"}, {"time": 0.3333, "value": 9.33}, {"time": 0.8333, "value": 12.76}, {"time": 1.3333, "value": 9.33}, {"time": 3, "value": 1.75, "curve": [3.337, 4.87, 3.671, 11.07]}, {"time": 4, "value": 11.07, "curve": [4.113, 11.07, 4.222, 10.35]}, {"time": 4.3333, "value": 9.33, "curve": [4.671, 6.21, 5.002, 0]}, {"time": 5.3333, "curve": [5.781, 0, 6.228, 11.07]}, {"time": 6.6667, "value": 11.07, "curve": [6.779, 11.07, 6.889, 9.33]}, {"time": 7, "value": 9.33}]}, "bone146": {"rotate": [{"value": 10.56, "curve": "stepped"}, {"time": 0.3333, "value": 10.56}, {"time": 0.8333, "value": 14}, {"time": 1.3333, "value": 10.56}, {"time": 3, "value": 0.51, "curve": [3.394, 2.65, 3.784, 11.07]}, {"time": 4.1667, "value": 11.07, "curve": [4.224, 11.07, 4.278, 10.86]}, {"time": 4.3333, "value": 10.56, "curve": [4.729, 8.42, 5.114, 0]}, {"time": 5.5, "curve": [5.947, 0, 6.395, 11.07]}, {"time": 6.8333, "value": 11.07, "curve": [6.89, 11.07, 6.944, 10.56]}, {"time": 7, "value": 10.56}]}, "bone147": {"rotate": [{"value": 11.07, "curve": "stepped"}, {"time": 0.3333, "value": 11.07}, {"time": 0.8333, "value": 14.51}, {"time": 1.3333, "value": 11.07}, {"time": 3, "curve": [3.447, 0, 3.889, 11.07]}, {"time": 4.3333, "value": 11.07, "curve": [4.784, 11.07, 5.225, 0]}, {"time": 5.6667, "curve": [6.114, 0, 6.556, 11.07]}, {"time": 7, "value": 11.07}]}, "bone148": {"rotate": [{"value": 1.02, "curve": "stepped"}, {"time": 0.3333, "value": 1.02}, {"time": 0.8333, "value": 4.46}, {"time": 1.3333, "value": 1.02}, {"time": 3, "value": 5.29, "curve": [3.114, 5.89, 3.224, 6.32]}, {"time": 3.3333, "value": 6.32, "curve": [3.671, 6.32, 4, 2.78]}, {"time": 4.3333, "value": 1.02, "curve": [4.446, 0.43, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 6.32]}, {"time": 6, "value": 6.32, "curve": [6.338, 6.32, 6.667, 1.02]}, {"time": 7, "value": 1.02}]}, "bone149": {"rotate": [{"value": 1.05, "curve": "stepped"}, {"time": 0.3333, "value": 1.05}, {"time": 0.8333, "value": 4.49}, {"time": 1.3333, "value": 1.05}, {"time": 3, "value": 2.23, "curve": [3.168, 2.81, 3.335, 3.28]}, {"time": 3.5, "value": 3.28, "curve": [3.781, 3.28, 4.056, 2.02]}, {"time": 4.3333, "value": 1.05, "curve": [4.501, 0.47, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, 3.28]}, {"time": 6.1667, "value": 3.28, "curve": [6.447, 3.28, 6.722, 1.05]}, {"time": 7, "value": 1.05}]}, "bone150": {"rotate": [{"value": 1.65, "curve": "stepped"}, {"time": 0.3333, "value": 1.65}, {"time": 0.8333, "value": 5.09}, {"time": 1.3333, "value": 1.65}, {"time": 3, "value": 1.63, "curve": [3.226, 2.45, 3.447, 3.28]}, {"time": 3.6667, "value": 3.28, "curve": [3.892, 3.28, 4.111, 2.47]}, {"time": 4.3333, "value": 1.65, "curve": [4.559, 0.83, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, 3.28]}, {"time": 6.3333, "value": 3.28, "curve": [6.559, 3.28, 6.778, 1.65]}, {"time": 7, "value": 1.65}]}, "bone151": {"rotate": [{"value": 2.25, "curve": "stepped"}, {"time": 0.3333, "value": 2.25}, {"time": 0.8333, "value": 5.69}, {"time": 1.3333, "value": 2.25}, {"time": 3, "value": 1.04, "curve": [3.28, 2, 3.559, 3.28]}, {"time": 3.8333, "value": 3.28, "curve": [4.001, 3.28, 4.167, 2.82]}, {"time": 4.3333, "value": 2.25, "curve": [4.613, 1.29, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, 3.28]}, {"time": 6.5, "value": 3.28, "curve": [6.668, 3.28, 6.833, 2.25]}, {"time": 7, "value": 2.25}]}, "bone152": {"rotate": [{"value": 2.77, "curve": "stepped"}, {"time": 0.3333, "value": 2.77}, {"time": 0.8333, "value": 6.2}, {"time": 1.3333, "value": 2.77}, {"time": 3, "value": 0.52, "curve": [3.337, 1.44, 3.671, 3.28]}, {"time": 4, "value": 3.28, "curve": [4.113, 3.28, 4.222, 3.07]}, {"time": 4.3333, "value": 2.77, "curve": [4.671, 1.84, 5.002, 0]}, {"time": 5.3333, "curve": [5.781, 0, 6.228, 3.28]}, {"time": 6.6667, "value": 3.28, "curve": [6.779, 3.28, 6.889, 2.77]}, {"time": 7, "value": 2.77}]}, "bone153": {"rotate": [{"value": 7.8, "curve": "stepped"}, {"time": 0.3333, "value": 7.8}, {"time": 0.8333, "value": 11.24}, {"time": 1.3333, "value": 7.8}, {"time": 3, "value": 0.38, "curve": [3.394, 1.96, 3.784, 8.18]}, {"time": 4.1667, "value": 8.18, "curve": [4.224, 8.18, 4.278, 8.02]}, {"time": 4.3333, "value": 7.8, "curve": [4.729, 6.22, 5.114, 0]}, {"time": 5.5, "curve": [5.947, 0, 6.395, 8.18]}, {"time": 6.8333, "value": 8.18, "curve": [6.89, 8.18, 6.944, 7.8]}, {"time": 7, "value": 7.8}]}, "bone154": {"rotate": [{"value": 9.37, "curve": "stepped"}, {"time": 0.3333, "value": 9.37}, {"time": 0.8333, "value": 12.8}, {"time": 1.3333, "value": 9.37}, {"time": 3, "curve": [3.447, 0, 3.889, 9.37]}, {"time": 4.3333, "value": 9.37, "curve": [4.784, 9.37, 5.225, 0]}, {"time": 5.6667, "curve": [6.114, 0, 6.556, 9.37]}, {"time": 7, "value": 9.37}]}, "bone155": {"rotate": [{"value": 9.88, "curve": "stepped"}, {"time": 0.3333, "value": 9.88}, {"time": 0.8333, "value": 13.31}, {"time": 1.3333, "value": 9.88}, {"time": 3, "value": 0.29, "curve": [3.045, 0.11, 3.089, 0]}, {"time": 3.1333, "curve": [3.536, 0, 3.933, 8.27]}, {"time": 4.3333, "value": 9.88, "curve": [4.379, 10.06, 4.423, 10.17]}, {"time": 4.4667, "value": 10.17, "curve": [4.917, 10.17, 5.359, 0]}, {"time": 5.8, "curve": [6.203, 0, 6.6, 9.88]}, {"time": 7, "value": 9.88}]}, "bone156": {"rotate": [{"value": 9.09, "curve": "stepped"}, {"time": 0.3333, "value": 9.09}, {"time": 0.8333, "value": 12.53}, {"time": 1.3333, "value": 9.09}, {"time": 3, "value": 1.07, "curve": [3.09, 0.42, 3.178, 0]}, {"time": 3.2667, "curve": [3.625, 0, 3.978, 6.54]}, {"time": 4.3333, "value": 9.09, "curve": [4.424, 9.74, 4.512, 10.17]}, {"time": 4.6, "value": 10.17, "curve": [5.05, 10.17, 5.492, 0]}, {"time": 5.9333, "curve": [6.291, 0, 6.644, 9.09]}, {"time": 7, "value": 9.09}]}, "bone157": {"rotate": [{"value": 7.94, "curve": "stepped"}, {"time": 0.3333, "value": 7.94}, {"time": 0.8333, "value": 11.38}, {"time": 1.3333, "value": 7.94}, {"time": 3, "value": 2.22, "curve": [3.135, 0.94, 3.268, 0]}, {"time": 3.4, "curve": [3.713, 0, 4.022, 5.01]}, {"time": 4.3333, "value": 7.94, "curve": [4.469, 9.22, 4.602, 10.17]}, {"time": 4.7333, "value": 10.17, "curve": [5.184, 10.17, 5.625, 0]}, {"time": 6.0667, "curve": [6.38, 0, 6.689, 7.94]}, {"time": 7, "value": 7.94}]}, "bone158": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 8.33, "curve": [3.45, 8.33, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 8.33]}, {"time": 5.6667, "value": 8.33, "curve": [6.117, 8.33, 6.556, 0]}, {"time": 7}]}, "bone159": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 3.72, "curve": [3.45, 3.72, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 3.72]}, {"time": 5.6667, "value": 3.72, "curve": [6.117, 3.72, 6.556, 0]}, {"time": 7}]}, "bone160": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 3.72, "curve": [3.45, 3.72, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 3.72]}, {"time": 5.6667, "value": 3.72, "curve": [6.117, 3.72, 6.556, 0]}, {"time": 7}]}, "bone161": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 3.72, "curve": [3.45, 3.72, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 3.72]}, {"time": 5.6667, "value": 3.72, "curve": [6.117, 3.72, 6.556, 0]}, {"time": 7}]}, "bone162": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 8.43, "curve": [3.45, 8.43, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 8.43]}, {"time": 5.6667, "value": 8.43, "curve": [6.117, 8.43, 6.556, 0]}, {"time": 7}]}, "bone163": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 8.43, "curve": [3.45, 8.43, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 8.43]}, {"time": 5.6667, "value": 8.43, "curve": [6.117, 8.43, 6.556, 0]}, {"time": 7}]}, "bone164": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 8.43, "curve": [3.45, 8.43, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 8.43]}, {"time": 5.6667, "value": 8.43, "curve": [6.117, 8.43, 6.556, 0]}, {"time": 7}]}, "bone165": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 8.43, "curve": [3.45, 8.43, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 8.43]}, {"time": 5.6667, "value": 8.43, "curve": [6.117, 8.43, 6.556, 0]}, {"time": 7}]}, "bone166": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 10.06, "curve": [3.45, 10.06, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 10.06]}, {"time": 5.6667, "value": 10.06, "curve": [6.117, 10.06, 6.556, 0]}, {"time": 7}]}, "bone167": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.42}, {"time": 3, "value": 10.06, "curve": [3.45, 10.06, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 10.06]}, {"time": 5.6667, "value": 10.06, "curve": [6.117, 10.06, 6.556, 0]}, {"time": 7}]}, "bone168": {"rotate": [{"time": 0.3333}, {"time": 0.8333, "value": 3.44}, {"time": 1.3333}, {"time": 3, "value": 3.59, "curve": [3.45, 3.59, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, 3.59]}, {"time": 5.6667, "value": 3.59, "curve": [6.117, 3.59, 6.556, 0]}, {"time": 7}]}, "bone169": {"rotate": [{"value": 0.3, "curve": "stepped"}, {"time": 0.3333, "value": 0.3}, {"time": 0.8333, "value": 3.74}, {"time": 1.3333, "value": 0.3}, {"time": 3, "value": 5.99, "curve": [3.058, 6.16, 3.113, 6.29]}, {"time": 3.1667, "value": 6.29, "curve": [3.561, 6.29, 3.944, 1.43]}, {"time": 4.3333, "value": 0.3, "curve": [4.391, 0.13, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, 6.29]}, {"time": 5.8333, "value": 6.29, "curve": [6.228, 6.29, 6.611, 0.3]}, {"time": 7, "value": 0.3}]}, "bone170": {"rotate": [{"value": 0.58, "curve": "stepped"}, {"time": 0.3333, "value": 0.58}, {"time": 0.8333, "value": 4.02}, {"time": 1.3333, "value": 0.58}, {"time": 3, "value": 3.01, "curve": [3.114, 3.34, 3.224, 3.59]}, {"time": 3.3333, "value": 3.59, "curve": [3.671, 3.59, 4, 1.58]}, {"time": 4.3333, "value": 0.58, "curve": [4.446, 0.24, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 3.59]}, {"time": 6, "value": 3.59, "curve": [6.338, 3.59, 6.667, 0.58]}, {"time": 7, "value": 0.58}]}, "bone171": {"rotate": [{"value": 0.89, "curve": "stepped"}, {"time": 0.3333, "value": 0.89}, {"time": 0.8333, "value": 4.32}, {"time": 1.3333, "value": 0.89}, {"time": 3, "value": 1.88, "curve": [3.168, 2.36, 3.335, 2.76]}, {"time": 3.5, "value": 2.76, "curve": [3.781, 2.76, 4.056, 1.7]}, {"time": 4.3333, "value": 0.89, "curve": [4.501, 0.4, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, 2.76]}, {"time": 6.1667, "value": 2.76, "curve": [6.447, 2.76, 6.722, 0.89]}, {"time": 7, "value": 0.89}]}, "bone172": {"rotate": [{"value": 4.17, "curve": "stepped"}, {"time": 0.3333, "value": 4.17}, {"time": 0.8333, "value": 7.61}, {"time": 1.3333, "value": 4.17}, {"time": 3, "value": 4.11, "curve": [3.226, 6.18, 3.447, 8.28]}, {"time": 3.6667, "value": 8.28, "curve": [3.892, 8.28, 4.111, 6.22]}, {"time": 4.3333, "value": 4.17, "curve": [4.559, 2.1, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, 8.28]}, {"time": 6.3333, "value": 8.28, "curve": [6.559, 8.28, 6.778, 4.17]}, {"time": 7, "value": 4.17}]}, "bone173": {"rotate": [{"value": 5.68, "curve": "stepped"}, {"time": 0.3333, "value": 5.68}, {"time": 0.8333, "value": 9.11}, {"time": 1.3333, "value": 5.68}, {"time": 3, "value": 2.61, "curve": [3.28, 5.04, 3.559, 8.28]}, {"time": 3.8333, "value": 8.28, "curve": [4.001, 8.28, 4.167, 7.12]}, {"time": 4.3333, "value": 5.68, "curve": [4.613, 3.25, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, 8.28]}, {"time": 6.5, "value": 8.28, "curve": [6.668, 8.28, 6.833, 5.68]}, {"time": 7, "value": 5.68}]}, "bone174": {"rotate": [{"value": 6.98, "curve": "stepped"}, {"time": 0.3333, "value": 6.98}, {"time": 0.8333, "value": 10.41}, {"time": 1.3333, "value": 6.98}, {"time": 3, "value": 1.31, "curve": [3.337, 3.64, 3.671, 8.28]}, {"time": 4, "value": 8.28, "curve": [4.113, 8.28, 4.222, 7.74]}, {"time": 4.3333, "value": 6.98, "curve": [4.671, 4.65, 5.002, 0]}, {"time": 5.3333, "curve": [5.781, 0, 6.228, 8.28]}, {"time": 6.6667, "value": 8.28, "curve": [6.779, 8.28, 6.889, 6.98]}, {"time": 7, "value": 6.98}]}, "bone175": {"rotate": [{"value": 7.9, "curve": "stepped"}, {"time": 0.3333, "value": 7.9}, {"time": 0.8333, "value": 11.34}, {"time": 1.3333, "value": 7.9}, {"time": 3, "value": 0.38, "curve": [3.394, 1.98, 3.784, 8.28]}, {"time": 4.1667, "value": 8.28, "curve": [4.224, 8.28, 4.278, 8.13]}, {"time": 4.3333, "value": 7.9, "curve": [4.729, 6.3, 5.114, 0]}, {"time": 5.5, "curve": [5.947, 0, 6.395, 8.28]}, {"time": 6.8333, "value": 8.28, "curve": [6.89, 8.28, 6.944, 7.9]}, {"time": 7, "value": 7.9}]}, "bone176": {"rotate": [{"value": 0.92, "curve": "stepped"}, {"time": 0.3333, "value": 0.92}, {"time": 0.8333, "value": 4.35}, {"time": 1.3333, "value": 0.92}, {"time": 3, "value": 4.74, "curve": [3.114, 5.28, 3.224, 5.66]}, {"time": 3.3333, "value": 5.66, "curve": [3.671, 5.66, 4, 2.49]}, {"time": 4.3333, "value": 0.92, "curve": [4.446, 0.39, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, 5.66]}, {"time": 6, "value": 5.66, "curve": [6.338, 5.66, 6.667, 0.92]}, {"time": 7, "value": 0.92}], "translate": [{"x": -0.26, "y": -5.02, "curve": "stepped"}, {"time": 1.3333, "x": -0.26, "y": -5.02}, {"time": 3, "x": -1.34, "y": -26, "curve": [3.114, -1.49, 3.224, -1.6, 3.114, -28.91, 3.224, -31.05]}, {"time": 3.3333, "x": -1.6, "y": -31.05, "curve": [3.671, -1.6, 4, -0.7, 3.671, -31.05, 4, -13.64]}, {"time": 4.3333, "x": -0.26, "y": -5.02, "curve": [4.446, -0.11, 4.556, 0, 4.446, -2.11, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, -1.6, 5.114, 0, 5.561, -31.05]}, {"time": 6, "x": -1.6, "y": -31.05, "curve": [6.338, -1.6, 6.667, -0.26, 6.338, -31.05, 6.667, -5.02]}, {"time": 7, "x": -0.26, "y": -5.02}]}, "bone177": {"rotate": [{"value": 1.82, "curve": "stepped"}, {"time": 0.3333, "value": 1.82}, {"time": 0.8333, "value": 5.25}, {"time": 1.3333, "value": 1.82}, {"time": 3, "value": 3.84, "curve": [3.168, 4.85, 3.335, 5.66]}, {"time": 3.5, "value": 5.66, "curve": [3.781, 5.66, 4.056, 3.48]}, {"time": 4.3333, "value": 1.82, "curve": [4.501, 0.82, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, 5.66]}, {"time": 6.1667, "value": 5.66, "curve": [6.447, 5.66, 6.722, 1.82]}, {"time": 7, "value": 1.82}]}, "bone178": {"rotate": [{"value": 2.85, "curve": "stepped"}, {"time": 0.3333, "value": 2.85}, {"time": 0.8333, "value": 6.29}, {"time": 1.3333, "value": 2.85}, {"time": 3, "value": 2.81, "curve": [3.226, 4.23, 3.447, 5.66]}, {"time": 3.6667, "value": 5.66, "curve": [3.892, 5.66, 4.111, 4.25]}, {"time": 4.3333, "value": 2.85, "curve": [4.559, 1.44, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, 5.66]}, {"time": 6.3333, "value": 5.66, "curve": [6.559, 5.66, 6.778, 2.85]}, {"time": 7, "value": 2.85}]}, "bone179": {"rotate": [{"value": 11.86, "curve": "stepped"}, {"time": 0.3333, "value": 11.86}, {"time": 0.8333, "value": 15.29}, {"time": 1.3333, "value": 11.86}, {"time": 3, "value": 5.45, "curve": [3.28, 10.52, 3.559, 17.3]}, {"time": 3.8333, "value": 17.3, "curve": [4.001, 17.3, 4.167, 14.88]}, {"time": 4.3333, "value": 11.86, "curve": [4.613, 6.79, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, 17.3]}, {"time": 6.5, "value": 17.3, "curve": [6.668, 17.3, 6.833, 11.86]}, {"time": 7, "value": 11.86}]}, "bone180": {"rotate": [{"value": 14.57, "curve": "stepped"}, {"time": 0.3333, "value": 14.57}, {"time": 0.8333, "value": 18.01}, {"time": 1.3333, "value": 14.57}, {"time": 3, "value": 2.74, "curve": [3.337, 7.6, 3.671, 17.3]}, {"time": 4, "value": 17.3, "curve": [4.113, 17.3, 4.222, 16.17]}, {"time": 4.3333, "value": 14.57, "curve": [4.671, 9.71, 5.002, 0]}, {"time": 5.3333, "curve": [5.781, 0, 6.228, 17.3]}, {"time": 6.6667, "value": 17.3, "curve": [6.779, 17.3, 6.889, 14.57]}, {"time": 7, "value": 14.57}]}, "bone181": {"rotate": [{"value": 3.35, "curve": "stepped"}, {"time": 0.3333, "value": 3.35}, {"time": 0.8333, "value": 6.79}, {"time": 1.3333, "value": 3.35}, {"time": 3, "value": 1.54, "curve": [3.28, 2.97, 3.559, 4.89]}, {"time": 3.8333, "value": 4.89, "curve": [4.001, 4.89, 4.167, 4.2]}, {"time": 4.3333, "value": 3.35, "curve": [4.613, 1.92, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, 4.89]}, {"time": 6.5, "value": 4.89, "curve": [6.668, 4.89, 6.833, 3.35]}, {"time": 7, "value": 3.35}]}, "bone182": {"rotate": [{"value": 4.12, "curve": "stepped"}, {"time": 0.3333, "value": 4.12}, {"time": 0.8333, "value": 7.55}, {"time": 1.3333, "value": 4.12}, {"time": 3, "value": 0.77, "curve": [3.337, 2.15, 3.671, 4.89]}, {"time": 4, "value": 4.89, "curve": [4.113, 4.89, 4.222, 4.57]}, {"time": 4.3333, "value": 4.12, "curve": [4.671, 2.74, 5.002, 0]}, {"time": 5.3333, "curve": [5.781, 0, 6.228, 4.89]}, {"time": 6.6667, "value": 4.89, "curve": [6.779, 4.89, 6.889, 4.12]}, {"time": 7, "value": 4.12}]}, "bone183": {"rotate": [{"value": 4.66, "curve": "stepped"}, {"time": 0.3333, "value": 4.66}, {"time": 0.8333, "value": 8.1}, {"time": 1.3333, "value": 4.66}, {"time": 3, "value": 0.23, "curve": [3.394, 1.17, 3.784, 4.89]}, {"time": 4.1667, "value": 4.89, "curve": [4.224, 4.89, 4.278, 4.79]}, {"time": 4.3333, "value": 4.66, "curve": [4.729, 3.72, 5.114, 0]}, {"time": 5.5, "curve": [5.947, 0, 6.395, 4.89]}, {"time": 6.8333, "value": 4.89, "curve": [6.89, 4.89, 6.944, 4.66]}, {"time": 7, "value": 4.66}]}, "bone184": {"rotate": [{"value": 4.89, "curve": "stepped"}, {"time": 0.3333, "value": 4.89}, {"time": 0.8333, "value": 8.32}, {"time": 1.3333, "value": 4.89}, {"time": 3, "curve": [3.447, 0, 3.889, 4.89]}, {"time": 4.3333, "value": 4.89, "curve": [4.784, 4.89, 5.225, 0]}, {"time": 5.6667, "curve": [6.114, 0, 6.556, 4.89]}, {"time": 7, "value": 4.89}]}, "bone185": {"rotate": [{"value": 15.08, "curve": "stepped"}, {"time": 0.3333, "value": 15.08}, {"time": 0.8333, "value": 18.51}, {"time": 1.3333, "value": 15.08}, {"time": 3, "value": 0.75, "curve": [3.058, 0.33, 3.112, 0]}, {"time": 3.1667, "curve": [3.559, 0, 3.944, 12.26]}, {"time": 4.3333, "value": 15.08, "curve": [4.392, 15.5, 4.446, 15.84]}, {"time": 4.5, "value": 15.84, "curve": [4.95, 15.84, 5.392, 0]}, {"time": 5.8333, "curve": [6.226, 0, 6.611, 15.08]}, {"time": 7, "value": 15.08}]}, "bone186": {"rotate": [{"value": 13.27, "curve": "stepped"}, {"time": 0.3333, "value": 13.27}, {"time": 0.8333, "value": 16.7}, {"time": 1.3333, "value": 13.27}, {"time": 3, "value": 2.56, "curve": [3.113, 1.08, 3.223, 0]}, {"time": 3.3333, "curve": [3.669, 0, 4, 8.91]}, {"time": 4.3333, "value": 13.27, "curve": [4.447, 14.75, 4.557, 15.84]}, {"time": 4.6667, "value": 15.84, "curve": [5.117, 15.84, 5.559, 0]}, {"time": 6, "curve": [6.336, 0, 6.667, 13.27]}, {"time": 7, "value": 13.27}]}, "bone187": {"rotate": [{"value": 10.75, "curve": "stepped"}, {"time": 0.3333, "value": 10.75}, {"time": 0.8333, "value": 14.18}, {"time": 1.3333, "value": 10.75}, {"time": 3, "value": 5.08, "curve": [3.167, 2.28, 3.334, 0]}, {"time": 3.5, "curve": [3.779, 0, 4.056, 6.13]}, {"time": 4.3333, "value": 10.75, "curve": [4.502, 13.55, 4.668, 15.84]}, {"time": 4.8333, "value": 15.84, "curve": [5.284, 15.84, 5.725, 0]}, {"time": 6.1667, "curve": [6.446, 0, 6.722, 10.75]}, {"time": 7, "value": 10.75}]}, "bone188": {"rotate": [{"value": 12, "curve": "stepped"}, {"time": 0.3333, "value": 12}, {"time": 0.8333, "value": 15.44}, {"time": 1.3333, "value": 12}, {"time": 3, "value": 0.6, "curve": [3.058, 0.26, 3.112, 0]}, {"time": 3.1667, "curve": [3.559, 0, 3.944, 9.76]}, {"time": 4.3333, "value": 12, "curve": [4.392, 12.34, 4.446, 12.61]}, {"time": 4.5, "value": 12.61, "curve": [4.95, 12.61, 5.392, 0]}, {"time": 5.8333, "curve": [6.226, 0, 6.611, 12]}, {"time": 7, "value": 12}]}, "bone189": {"rotate": [{"value": 10.56, "curve": "stepped"}, {"time": 0.3333, "value": 10.56}, {"time": 0.8333, "value": 13.99}, {"time": 1.3333, "value": 10.56}, {"time": 3, "value": 2.04, "curve": [3.113, 0.86, 3.223, 0]}, {"time": 3.3333, "curve": [3.669, 0, 4, 7.09]}, {"time": 4.3333, "value": 10.56, "curve": [4.447, 11.74, 4.557, 12.61]}, {"time": 4.6667, "value": 12.61, "curve": [5.117, 12.61, 5.559, 0]}, {"time": 6, "curve": [6.336, 0, 6.667, 10.56]}, {"time": 7, "value": 10.56}]}, "bone190": {"rotate": [{"value": 8.55, "curve": "stepped"}, {"time": 0.3333, "value": 8.55}, {"time": 0.8333, "value": 11.99}, {"time": 1.3333, "value": 8.55}, {"time": 3, "value": 4.05, "curve": [3.167, 1.82, 3.334, 0]}, {"time": 3.5, "curve": [3.779, 0, 4.056, 4.88]}, {"time": 4.3333, "value": 8.55, "curve": [4.502, 10.78, 4.668, 12.61]}, {"time": 4.8333, "value": 12.61, "curve": [5.284, 12.61, 5.725, 0]}, {"time": 6.1667, "curve": [6.446, 0, 6.722, 8.55]}, {"time": 7, "value": 8.55}]}, "bone191": {"rotate": [{"time": 1.3333}, {"time": 3, "value": -8.18, "curve": [3.45, -8.18, 3.889, 0]}, {"time": 4.3333, "curve": [4.781, 0, 5.228, -8.18]}, {"time": 5.6667, "value": -8.18, "curve": [6.117, -8.18, 6.556, 0]}, {"time": 7}], "translate": [{"time": 1.3333}, {"time": 3, "x": 11.73, "y": 7.74, "curve": [3.45, 11.73, 3.889, 0, 3.45, 7.74, 3.889, 0]}, {"time": 4.3333, "curve": [4.445, 0, 4.557, -10.15, 4.445, 0, 4.557, -7.03]}, {"time": 4.6667, "x": -9.05, "y": -6.3, "curve": [5.004, -5.75, 5.338, 11.73, 5.004, -4.13, 5.338, 7.74]}, {"time": 5.6667, "x": 11.73, "y": 7.74, "curve": [6.117, 11.73, 6.556, 0, 6.117, 7.74, 6.556, 0]}, {"time": 7}]}, "bone192": {"rotate": [{"value": -0.39, "curve": "stepped"}, {"time": 1.3333, "value": -0.39}, {"time": 3, "value": -7.79, "curve": [3.058, -8.01, 3.113, -8.18]}, {"time": 3.1667, "value": -8.18, "curve": [3.561, -8.18, 3.944, -1.86]}, {"time": 4.3333, "value": -0.39, "curve": [4.391, -0.17, 4.446, 0]}, {"time": 4.5, "curve": [4.947, 0, 5.395, -8.18]}, {"time": 5.8333, "value": -8.18, "curve": [6.228, -8.18, 6.611, -0.39]}, {"time": 7, "value": -0.39}]}, "bone193": {"rotate": [{"value": -1.32, "curve": "stepped"}, {"time": 1.3333, "value": -1.32}, {"time": 3, "value": -6.85, "curve": [3.114, -7.62, 3.224, -8.18]}, {"time": 3.3333, "value": -8.18, "curve": [3.671, -8.18, 4, -3.59]}, {"time": 4.3333, "value": -1.32, "curve": [4.446, -0.56, 4.556, 0]}, {"time": 4.6667, "curve": [5.114, 0, 5.561, -8.18]}, {"time": 6, "value": -8.18, "curve": [6.338, -8.18, 6.667, -1.32]}, {"time": 7, "value": -1.32}]}, "bone194": {"rotate": [{"value": -4.4, "curve": "stepped"}, {"time": 1.3333, "value": -4.4}, {"time": 3, "value": -9.3, "curve": [3.168, -11.72, 3.335, -13.71]}, {"time": 3.5, "value": -13.71, "curve": [3.781, -13.71, 4.056, -8.42]}, {"time": 4.3333, "value": -4.4, "curve": [4.501, -1.97, 4.667, 0]}, {"time": 4.8333, "curve": [5.281, 0, 5.728, -13.71]}, {"time": 6.1667, "value": -13.71, "curve": [6.447, -13.71, 6.722, -4.4]}, {"time": 7, "value": -4.4}]}, "bone195": {"rotate": [{"value": -6.9, "curve": "stepped"}, {"time": 1.3333, "value": -6.9}, {"time": 3, "value": -6.8, "curve": [3.226, -10.23, 3.447, -13.71]}, {"time": 3.6667, "value": -13.71, "curve": [3.892, -13.71, 4.111, -10.28]}, {"time": 4.3333, "value": -6.9, "curve": [4.559, -3.48, 4.779, 0]}, {"time": 5, "curve": [5.447, 0, 5.895, -13.71]}, {"time": 6.3333, "value": -13.71, "curve": [6.559, -13.71, 6.778, -6.9]}, {"time": 7, "value": -6.9}]}, "bone196": {"rotate": [{"value": -9.39, "curve": "stepped"}, {"time": 1.3333, "value": -9.39}, {"time": 3, "value": -4.32, "curve": [3.28, -8.34, 3.559, -13.71]}, {"time": 3.8333, "value": -13.71, "curve": [4.001, -13.71, 4.167, -11.79]}, {"time": 4.3333, "value": -9.39, "curve": [4.613, -5.37, 4.89, 0]}, {"time": 5.1667, "curve": [5.614, 0, 6.061, -13.71]}, {"time": 6.5, "value": -13.71, "curve": [6.668, -13.71, 6.833, -9.39]}, {"time": 7, "value": -9.39}]}, "bone198": {"rotate": [{"time": 1.1667}, {"time": 1.2, "value": -199.49}], "scale": [{"time": 0.6667, "x": -3.75, "y": 3.75}]}, "bone199": {"rotate": [{"time": 0.6667, "value": -71.51}, {"time": 0.8333, "value": -385.78}, {"time": 1, "value": -710.02}, {"time": 1.1, "value": -912.4}], "scale": [{"time": 0.6667, "x": -2, "y": 2}]}}, "physics": {"bone100": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone101": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone102": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone103": {"inertia": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.7333, "value": 0.1}, {"time": 7}], "strength": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.7333, "value": 10}, {"time": 7}], "mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone104": {"inertia": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.7, "value": 0.1, "curve": "stepped"}, {"time": 4, "value": 0.1}, {"time": 4.3333}], "strength": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.7, "value": 10, "curve": "stepped"}, {"time": 4, "value": 10}, {"time": 4.3333}], "mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone105": {"inertia": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.7, "value": 0.1, "curve": "stepped"}, {"time": 4, "value": 0.1}, {"time": 4.3333}], "strength": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.7, "value": 10, "curve": "stepped"}, {"time": 4, "value": 10}, {"time": 4.3333}], "mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone106": {"inertia": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.7, "value": 0.1, "curve": "stepped"}, {"time": 4, "value": 0.1}, {"time": 4.3333}], "strength": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.7, "value": 10, "curve": "stepped"}, {"time": 4, "value": 10}, {"time": 4.3333}], "mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone112": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone113": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone114": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone115": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone116": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone117": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone118": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone119": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone120": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone121": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone122": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone123": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone124": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone125": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone126": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone127": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone128": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone129": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone130": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone131": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone132": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone133": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone134": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone135": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone136": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone137": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone138": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone139": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone140": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone141": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone142": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone143": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone144": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone145": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone146": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone147": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone148": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone149": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone150": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone151": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone152": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone153": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone154": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone155": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone156": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone157": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone158": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone159": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone160": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone161": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone162": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone163": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone164": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone165": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone166": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone167": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone168": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone169": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone170": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone171": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone172": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone173": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone174": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone175": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone176": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone177": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone178": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone179": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone180": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone181": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone182": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone183": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone184": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone185": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone186": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone187": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone188": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone189": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone190": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone192": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone193": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone194": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone195": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone196": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone43": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone44": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone45": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone46": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone48": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone49": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone50": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone51": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone54": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone55": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone56": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone63": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone64": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone65": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone66": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone70": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone71": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone72": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone73": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone74": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone75": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone76": {"mix": [{"time": 1.3, "value": 1}, {"time": 3}]}, "bone83": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone84": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone85": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone86": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone87": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone88": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone89": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone90": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone91": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone92": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone93": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone94": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone95": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone96": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone97": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone98": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "bone99": {"mix": [{"time": 4.3333, "value": 1}, {"time": 7}]}, "": {"inertia": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.6667, "value": 0.3}, {"time": 2.5333, "value": 0.2, "curve": [2.838, 0.185, 3.152, 0]}, {"time": 3.4333}], "strength": [{"curve": "stepped"}, {"time": 0.3333}, {"time": 0.6667, "value": 30}, {"time": 2.5333, "value": 20, "curve": [2.838, 18.5048, 3.152, 0]}, {"time": 3.4333}], "mix": [{}, {"time": 0.3333, "value": 1, "curve": "stepped"}, {"time": 2.5333, "value": 1}, {"time": 2.6667}, {"time": 3, "value": 1, "curve": "stepped"}, {"time": 4.3333, "value": 1}, {"time": 7}]}}, "attachments": {"default": {"biyan": {"biyan": {"deform": [{"time": 5.8333, "offset": 12, "vertices": [24.42725, 12.44539, 24.42957, 12.44584, 36.24646, 14.66441, 36.24823, 14.66493, 40.17181, 18.08708, 40.1731, 18.08743, 32.72076, 21.39104, 32.72144, 21.39149, 18.75421, 5.92871, 18.75488, 5.929, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.68481, 1.39046, 5.68488, 1.39031, 23.02344, -10.49359, 23.0238, -10.49427, 27.51971, -2.47209, 27.51953, -2.47253, 27.16949, -0.39084, 27.16901, -0.39116, 14.04932, -3.60413, 14.04871, -3.60437, 8.4754, 0.64795, 8.47546, 0.64788]}, {"time": 5.9667, "offset": 12, "vertices": [0.13257, -0.00774, 0.13257], "curve": "stepped"}, {"time": 6.0333, "offset": 12, "vertices": [0.13257, -0.00774, 0.13257]}, {"time": 6.1667, "offset": 12, "vertices": [24.42725, 12.44539, 24.42957, 12.44584, 36.24646, 14.66441, 36.24823, 14.66493, 40.17181, 18.08708, 40.1731, 18.08743, 32.72076, 21.39104, 32.72144, 21.39149, 18.75421, 5.92871, 18.75488, 5.929, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.68481, 1.39046, 5.68488, 1.39031, 23.02344, -10.49359, 23.0238, -10.49427, 27.51971, -2.47209, 27.51953, -2.47253, 27.16949, -0.39084, 27.16901, -0.39116, 14.04932, -3.60413, 14.04871, -3.60437, 8.4754, 0.64795, 8.47546, 0.64788]}]}}, "toufa15": {"toufa15": {"deform": [{"offset": 2, "vertices": [-5.35254, 11.50103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.69214, -1.54462, -7.75635, 1.18021, -14.49377, 6.67501, -6.18652, 5.24612]}]}}}}, "drawOrder": [{"time": 1.0667, "offsets": [{"slot": "dao", "offset": 2}]}, {"time": 1.1667}]}}}