using System;
using System.Collections.Generic;
using Build;
using Game.Hotfix.Config;
using Roledata;
using UnityEngine;

namespace Game.Hotfix
{
    public class SoldierData
    {
        private readonly List<SoldierModule> _soldierList = new();
        private readonly Dictionary<uint,uint> soldierDictionary = new();
        private readonly List<SoldierModule> _hospitalsList = new();

        public List<SoldierModule> SoldierList => _soldierList;
        public List<SoldierModule> HospitalsList => _hospitalsList;
        
        public SoldierData()
        {
        }

        public void Init(RoleSoldier soldiers,RoleSoldier hospitals)
        {
            if (soldiers == null || hospitals == null )
            {
                return;
            }
            // 全等级兵
            foreach (Build.Soldier roleSoldier in soldiers.Soldiers)
            {
                _soldierList.Add(new SoldierModule(roleSoldier));
                soldierDictionary[roleSoldier.Id] = roleSoldier.Num;
            }  
            // 医院治疗兵
            foreach (Build.Soldier roleSoldier in hospitals.Soldiers)
            {
                _hospitalsList.Add(new SoldierModule(roleSoldier));
            }
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushSoldierChange, message =>
            {
                var data = (RoleSoldier)message;
                OnSoldierChange(data);
            });  
            
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushHospitalsChange, message =>
            {
                var data = (RoleSoldier)message;
                OnHospitalsChange(data);
            });
        }
        
        private void OnHospitalsChange(RoleSoldier data)
        {
            bool isAdd = false;
            if (data.Soldiers.Count == 0)
            {
                _hospitalsList.Clear();
                GameEntry.LogicData.BuildingData.ChangeAllHospitalState(new HospitalSoldierParams(HospitalQueueChangeState.Remove));
                return;
            }

            Dictionary<int,int> addDictionary = new Dictionary<int, int>();
            for (int i = 0; i < data.Soldiers.Count; i++)
            {
                Build.Soldier dataSoldier = data.Soldiers[i];
                //Debug.LogError($"OnHospitalsChange:{dataSoldier.Id},{dataSoldier.Num}");
                bool exists = _hospitalsList.Exists(h => h.Id == dataSoldier.Id);
                if (!exists)
                {
                    SoldierModule soldierModule = new SoldierModule(dataSoldier);
                    _hospitalsList.Add(soldierModule);
                    isAdd = true;
                    addDictionary[(int)soldierModule.Id] = (int)soldierModule.Num;
                }
                else
                {
                    for (int j = _hospitalsList.Count - 1; j >= 0; j--)
                    {
                        if (_hospitalsList[j].Id == dataSoldier.Id)
                        {
                            if (dataSoldier.Num > 0)
                            {
                                _hospitalsList[j].Num = dataSoldier.Num;
                                addDictionary[(int)dataSoldier.Id] = (int)dataSoldier.Num;
                            }
                            else
                            {
                                _hospitalsList.RemoveAt(j);
                            }
                        }
                    }
                    //_hospitalsList.RemoveAll(h => h.Id == dataSoldier.Id && dataSoldier.Num == 0);
                }
            }

            var soldierTreatQueue = GameEntry.LogicData.QueueData.GetHospitalQueue();
            if (soldierTreatQueue == null && isAdd)
            {
                GameEntry.LogicData.BuildingData.ChangeAllHospitalState(new HospitalSoldierParams(HospitalQueueChangeState.Add));
            }
        }

        private void OnSoldierChange(RoleSoldier data)
        {
            for (int i = 0; i < data.Soldiers.Count; i++)
            {
                Build.Soldier dataSoldier = data.Soldiers[i];
                int changeNum = 0;
                //Debug.LogError($"OnSoldierChange:{dataSoldier.Id},{dataSoldier.Num}");
                bool exists = _soldierList.Exists(h => h.Id == dataSoldier.Id);
                if (!exists)
                {
                    SoldierModule soldierModule = new SoldierModule(dataSoldier);
                    _soldierList.Add(soldierModule);
                    changeNum = (int)dataSoldier.Num;
                }
                else
                {
                    for (int j = _soldierList.Count - 1; j >= 0; j--)
                    {
                        if (_soldierList[j].Id == dataSoldier.Id)
                        {
                            changeNum = (int)dataSoldier.Num - (int)_soldierList[j].Num;
                            _soldierList[j].Num = dataSoldier.Num;
                        }
                    }

                    //soldierDictionary[dataSoldier.Id] = dataSoldier.Num;
                } 
                soldierDictionary[dataSoldier.Id] = dataSoldier.Num;
                if (changeNum != 0)
                {
                    GameEntry.LogicData.BuildingData.ChangeAllGroundState(SoldierQueueChangeState.SoldierChange, (int)dataSoldier.Id,changeNum);
                }
            }
        }

        public int GetTotalSoldierCount()
        {
            int totalCount = 0;
            foreach (SoldierModule soldierModule in _soldierList)
            {
                totalCount += (int)soldierModule.Num;
            }
            return totalCount;
        } 
        
        public int GetTotalHospitalCount()
        {
            int totalCount = 0;
            foreach (SoldierModule soldierModule in _hospitalsList)
            {
                totalCount += (int)soldierModule.Num;
            }
            return totalCount;
        }

        public bool GetIsSoldierFull()
        {
            int totalSoldierCount = GetTotalSoldierCount();
            int capacity = GameEntry.LogicData.BuildingData.GetAllBuildingGroundCapacity();
            return totalSoldierCount >= capacity;
        }

        public void GetTotalTreatCost(int soldierLevel,int soldierNum,out int totalFoodCost,out int totalIronCost)
        {
            
            totalFoodCost = 0;
            totalIronCost = 0;
            build_soldier buildSoldierCfg = GameEntry.LDLTable.GetTableById<build_soldier>(soldierLevel);
            List<cost> treatCost = buildSoldierCfg.treat_cost;

            foreach (cost resCost in treatCost)
            {
                // 食物
                if (resCost.item_id == itemid.itemid_2)
                {
                    totalFoodCost += soldierNum * (int)resCost.num;
                }
                else if (resCost.item_id == itemid.itemid_3)
                {
                    totalIronCost += soldierNum * (int)resCost.num;
                }
            }
        }
        
        public int GetTotalTreatTime(int soldierLevel,int soldierNum)
        {
            build_soldier buildSoldierCfg = GameEntry.LDLTable.GetTableById<build_soldier>(soldierLevel);
            int treatTime = buildSoldierCfg.treat_time;

            return treatTime * soldierNum;
        }

        public bool GetIsShowHospitalHud()
        {
            var queue = GameEntry.LogicData.QueueData.GetHospitalQueue();
            bool isHave = GetTotalHospitalCount() > 0;
            return isHave || queue != null;
        }

        public string GetSoldierHeadById(int soldieId)
        {
            var soldierConfig = Game.GameEntry.LDLTable.GetTableById<build_soldier>(soldieId);
            if (soldierConfig != null && soldierConfig.picture != String.Empty)
            {
                return AssetUtility.GetAssetRootDirectory(soldierConfig.picture);
            }
            return "";
        }
        
        public string GetSoldierDisplayPath(int soldieId)
        {
            var soldierConfig = Game.GameEntry.LDLTable.GetTableById<build_soldier>(soldieId);
            if (soldierConfig != null && soldierConfig.model != String.Empty)
            {
                return AssetUtility.GetAssetRootDirectory(soldierConfig.model);
            }
            return "";
        }
        
        public string GetSoldierQualityBg(quality qualityLevel)
        {
            var t = qualityLevel;
            return qualityLevel switch
            {
                quality.quality_blue => "Sprite/ui_public/pingzhikuang_zhuangbei_blue.png",
                quality.quality_purple => "Sprite/ui_public/pingzhikuang_purple.png",
                quality.quality_orange => "Sprite/ui_public/pingzhikuang1_orange.png",
                quality.quality_red => "Sprite/ui_public/pingzhikuang1_red.png",
                _ => "Sprite/ui_public/pingzhikuang_zhuangbei_green.png",
            };
        }

        public int GetSoldierNumById(int id)
        {
            uint soldierNum;
            bool isExit = soldierDictionary.TryGetValue((uint)id, out soldierNum);
            return (int)soldierNum;
        }

        public List<SoldierModule> GetSoldierList()
        {
            var soldierModules = new List<SoldierModule>();
            for (var i = 0; i < _soldierList.Count; i++)
            {
                var soldierModule = _soldierList[i];
                if (soldierModule.Num != 0)
                {
                    soldierModules.Add(soldierModule);
                }
            }
            soldierModules.Sort(((a, b) =>
            {
                return a.Id.CompareTo(b.Id);
            }));

            return soldierModules;
        }
    }
}