using System;
using System.Collections.Generic;
using System.Text;
using Recruit;
using UnityEngine;
using UnityEngine.UI;
using Game.Hotfix.Config;
using UnityEngine.Events;

namespace Game.Hotfix
{
    public class RecruitData
    {
        private Dictionary<UInt32, RecruitPool> MsgDictionary;

        private StringBuilder sb = new StringBuilder();
        //赛季招募id
        private int recruitId_season;
        public int RecruitId_Season => recruitId_season;
        
        //英雄招募id
        private int recruitId_hero;
        public int RecruitId_Hero => recruitId_hero;
        
        //幸存者招募id
        private int recruitId_survivor;
        public int RecruitId_Survivor => recruitId_survivor;

        public void Init()
        {
            MsgDictionary = new Dictionary<UInt32, RecruitPool>();
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.RecruitPoolChange, message =>
            {
                var data = (RecruitPoolChange)message;
                OnRecruitPoolChange(data);
            });
            recruitId_season = 3011;
            recruitId_hero = 1001;
            recruitId_survivor = 2001;
            BindRedDotLogic();
        }
        #region 招募红点逻辑

        public void BindRedDotLogic()
        {
            var rootFlag = EnumRed.Recruit.ToString();
            //招募根节点
            RedPointManager.Instance.AddNodeTo(EnumRed.Root.ToString(), rootFlag);
            
            //赛季招募
            RedPointManager.Instance.AddNodeTo(rootFlag, EnumRed.Recruit_Season.ToString(), () =>
            {
                if (CheckSingleRedDot(recruittype.recruittype_season, recruitId_survivor))
                {
                    return 1;
                }
                return 0;
            });
            
            //英雄招募
            RedPointManager.Instance.AddNodeTo(rootFlag, EnumRed.Recruit_Hero.ToString(), () =>
            {
                if (CheckSingleRedDot(recruittype.recruittype_hero, recruitId_hero))
                {
                    return 1;
                }
                return 0;
            });
            
            //幸存者招募
            RedPointManager.Instance.AddNodeTo(EnumRed.Recruit.ToString(), EnumRed.Recruit_Survivor.ToString(), () =>
            {
                if (CheckSingleRedDot(recruittype.recruittype_survivor, recruitId_survivor))
                {
                    return 1;
                }
                return 0;
            });
        }

        private bool CheckSingleRedDot(recruittype type,int recruitId)
        {
            //是否可免费招募
            var isFree = IsCanFree(recruitId);
            if (isFree)
            {
                return true;
            }
            //招募券 > 0
            var itemId = GetIconIdByType(type);
            var ownCount = GameEntry.LogicData.BagData.GetAmountById(itemId);
            if (ownCount > 0)
            {
                return true;
            }
            //是否可领取心愿英雄
            var isOk = IsCanReceiveWishHero(recruitId);
            if (isOk)
            {
                return true;
            }
            return false;
        }

        public void DirtyAllDot()
        {
            RedPointManager.Instance.Dirty(EnumRed.Recruit_Hero.ToString());
            RedPointManager.Instance.Dirty(EnumRed.Recruit_Season.ToString());
            RedPointManager.Instance.Dirty(EnumRed.Recruit_Survivor.ToString());
        }
        
        #endregion
        
        #region 抽卡导表配置
        
        public void GetItemData(int id,UnityAction<item_config> callback)
        {
            if (!GameEntry.LDLTable.HaseTable<item_config>()) return;
            item_config data = GameEntry.LDLTable.GetTableById<item_config>(id);
            if (data!=null)
            {
                callback?.Invoke(data);
            }
        }
        
        public string GetCardQualityBg(quality quality)
        {
            var qualityId = (int)quality;
            if (qualityId >= 2 && qualityId <= 5)
            {
                return $"Sprite/ui_jianzhu_jiuguan/zhaomu_tongyong_ka_{qualityId-1}.png";
            }
            else
            {
                return "Sprite/ui_jianzhu_jiuguan/zhaomu_tongyong_ka_1.png";
            }
        }
        
        public string GetCardQualityBorder(quality quality)
        {
            var qualityId = (int)quality;
            if (qualityId >= 2 && qualityId <= 5)
            {
                return $"Sprite/ui_jianzhu_jiuguan/zhaomu_tongyong_ka_{qualityId-1}_1.png";
            }
            else
            {
                return "Sprite/ui_jianzhu_jiuguan/zhaomu_tongyong_ka_1_1.png";
            }
        }
        
        //获取指定品质对应的颜色码
        public string GetHexByQuality(quality quality)
        {
            var hex = "#FFFFFF";
            switch (quality)
            {
                case quality.quality_nil:
                case quality.quality_white:
                    hex = "#FFFFFF";
                    break;
                case quality.quality_green:
                    hex = "#91FFC7";
                    break;
                case quality.quality_blue:
                    hex = "#86F5FF";
                    break;
                case quality.quality_purple:
                    hex = "#FCAAFF";
                    break;
                case quality.quality_orange:
                    hex = "#FFE159";
                    break;
                case quality.quality_red:
                    hex = "#FFFFFF";
                    break;
                default:
                    hex = "#FFFFFF";
                    break;
            }
            return hex;
        }

        //获取预览英雄id列表
        public List<recruit_drops> GetPreviewHeroList(int recruitId)
        {
            var list = new List<recruit_drops>();
            if (ToolScriptExtend.GetConfigById<recruit_config>(recruitId, out var recruitConfig))
            {
                foreach (var info in recruitConfig.drop)
                {
                    var config = GameEntry.LDLTable.GetTable<recruit_drops>();
                    foreach (var drops in config)
                    {
                        var rewardId = drops.drop_reward.item_id;
                        var heroData = GameEntry.LogicData.RecruitData.GetHeroDataById(rewardId);
                        if (heroData != null)
                        {
                            if (drops.preview_time > 0 && info.drop_group_id == drops.drop_group_id && drops.on_time > drops.preview_time)
                            {
                                if (NeedPreview(recruitId,drops.preview_time,drops.on_time))
                                {
                                    list.Add(drops);
                                }
                            }
                        }
                    }
                }
            }
            return list;
        }
        
        //获取配置心愿英雄id列表
        public List<itemid> GetWishHeroList()
        {
            var result = new List<itemid>();
            if (GameEntry.LDLTable.HaseTable<recruit_config>())
            {
                var data = GameEntry.LDLTable.GetTableById<recruit_config>(1001);
                if (data!=null&&data.pray_list !=null)
                {
                    foreach(var str in data.pray_list)
                    {
                        result.Add(str);
                    }
                }
            }
            return result;
        }

        
        //获取英雄配置数据
        public hero_config GetHeroDataById(itemid heroId)
        {
            var itemConfig = ToolScriptExtend.GetItemConfig(heroId);
            if (itemConfig == null) return null;
            if (itemConfig.item_type != itemtype.itemtype_hero) return null;
            
            if (ToolScriptExtend.GetConfigById<hero_config>((int)heroId,out var heroConfig))
            {
                return heroConfig;
            }
            return null;
        }

        //获取心愿配置次数
        public int GetWishConfigCount(int id)
        {
            if (!GameEntry.LDLTable.HaseTable<Config.recruit_config>()) return 200;
            var data = GameEntry.LDLTable.GetTableById<Config.recruit_config>(id);
            return data?.pray_times ?? 200;
        }
        
        //获取去指定卡池保底配置次数
        public int GetGuaranteeCount(int id)
        {
            if (!GameEntry.LDLTable.HaseTable<Config.recruit_config>()) return 50;
            var data = GameEntry.LDLTable.GetTableById<Config.recruit_config>(id);
            return data != null ? data.guarantee_times : 50;
        }
        
        //概率掉落列表
        public List<drop> GetRatioDropList(int id,out int ratioSum)
        {
            ratioSum = 0;
            if (!GameEntry.LDLTable.HaseTable<Config.recruit_config>()) return new List<drop>();
            var data = GameEntry.LDLTable.GetTableById<Config.recruit_config>(id);
            foreach (var info in data.drop)
            {
                ratioSum += info.precent;
            }
            return data.drop;
        }

        //获取免费招募刷新CD
        public int GetFreeCD()
        {
            //TODO  读取酒馆相关buff接口
            return 86400;
        }
        
        //获取解锁掉落id列表
        public List<int> GetDropIdList(int typeId,out int weightSum)
        {
            weightSum = 0;
            var reslult = new List<int>();
            if (GameEntry.LDLTable.HaseTable<Config.recruit_drops>())
            {
                var data = GameEntry.LDLTable.GetTable<Config.recruit_drops>();
                foreach (var cell in data)
                {
                    if (cell.drop_group_id == typeId && CheckCellUnlock(cell.id))
                    {
                        reslult.Add(cell.id);
                        weightSum += cell.weight;
                    }
                }
            };
            return reslult;
        }

        //判断掉落物品是否解锁（即是否加入卡池）
        public bool CheckCellUnlock(int dropId)
        {
            var serviceDays = GameEntry.LDLNet.GetOpenServiceDays();
            if (ToolScriptExtend.GetConfigById<recruit_drops>(dropId, out var result))
            {
                if (serviceDays >= result.on_time)
                {
                    return true;
                }
            }
            return false;
        }

        public bool ChangeHeroToPiece(itemid rewardId,out itemid pieceId,out int unitCount)
        {
            pieceId = itemid.itemid_nil;
            unitCount = 1;//单位数量
            
            if (ToolScriptExtend.GetConfigById<item_config>((int)rewardId, out var itemConfig))
            {
                if (itemConfig.item_type == itemtype.itemtype_hero)
                {
                    //如果拥有该英雄转成10个英雄碎片
                    if (ToolScriptExtend.GetConfigById<hero_config>((int)rewardId, out var heroData))
                    {
                        pieceId = heroData.piece;
                        unitCount *= 10;
                        return true;
                    }
                }
            }
            return false;
        }
        
        #endregion
        
        
        #region 抽卡协议

        /// <summary>
        /// 招募初始化数据请求
        /// </summary>
        public void C2SRecruitPoolsInfo(Action action = null)
        {
            ProtoLog(true, "招募初始化");
            GameEntry.LDLNet.Send(Protocol.MessageID.RecruitPoolsInfo,new RecruitPoolsInfoReq(), (message) =>
            {
                var resp = (RecruitPoolsInfoResp)message;
                foreach (var data in resp.List)
                {
                    MsgDictionary[data.Id] = data;
                }
                ProtoLog(false, "招募初始化", resp.ToString());
                action?.Invoke();
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIRecruitForm,2);
            });
        }
        
        /// <summary>
        /// 协议请求：请求抽卡
        /// </summary>
        /// <param name="id">招募池id</param>
        /// <param name="numType">招募次数</param>
        /// <param name="action"></param>
        public void C2SRecruitReq(int id,recruitnumtype numType,Action<RecruitResp> action = null)
        {
            var req = new RecruitReq();
            var times = (int)numType;
            if (times == 0)
            {
                times = 1;
            }

            req.Id = (uint)id;
            req.Times = (uint)times;
            ProtoLog(true, "招募", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.Recruit, req, (message) =>
            {
                var resp = (RecruitResp)message;
                ProtoLog(false, "招募", resp);
                action?.Invoke(resp);
            });
        }
        
        /// <summary>
        /// 协议请求：设置心愿英雄
        /// </summary>
        /// <param name="id"></param>
        /// <param name="heroId"></param>
        /// <param name="action"></param>
        public void C2SRecruitSetWish(int id,itemid heroId,Action<RecruitSetWishResp> action = null)
        {
            var req = new RecruitSetWishReq();
            req.Id = (uint)id;
            req.ChooseHero = (uint)heroId;
            ProtoLog(true, "招募(设置心愿英雄)", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.RecruitSetWish, req, (message) =>
            {
                Debug.Log(message);
                // var resp = (RecruitSetWishResp)message;
                ProtoLog(false, "招募协议(设置心愿英雄)", null);
                action?.Invoke(null);
            });
        }
        
        /// <summary>
        /// 协议请求：领取心愿英雄
        /// </summary>
        /// <param name="id"></param>
        /// <param name="action"></param>
        public void C2SRecruitDrawWish(int id,Action<RecruitDrawWishResp> action = null)
        {
            var req = new RecruitDrawWishReq();
            req.Id = (uint)id;
            ProtoLog(true, "招募(领取心愿英雄)", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.RecruitDrawWish, req, (message) =>
            {
                var resp = (RecruitDrawWishResp)message;
                ProtoLog(false, "招募协议(领取心愿英雄)", resp.ToString());
                action?.Invoke(resp);
            });
        }

        //招募池数据变更推送(新增或修改)回调
        private void OnRecruitPoolChange(RecruitPoolChange resp)
        {
            if (resp.List.Count <= 0)
            {
                Debug.Log("推送数据为空！");
                return;
            }
            ProtoLog(false, "招募池数据变更推送(新增或修改)协议", resp.ToString());
            foreach (var data in resp.List)
            {
                MsgDictionary[(uint)data.Id] = data;
            }
            GameEntry.UI.RefreshUIForm(EnumUIForm.UIRecruitForm,2);
            
            DirtyAllDot();
        }
        
        /// <summary>
        /// 协议请求：更新已读预告时间戳 请求
        /// </summary>
        /// <param name="id"></param>
        /// <param name="action"></param>
        public void C2SRecruitUpdatePreviewReq(int id,Action<RecruitUpdatePreviewResp> action = null)
        {
            var req = new RecruitUpdatePreviewReq();
            req.Id = (uint)id;
            ProtoLog(true, "招募(更新已读预告时间戳)", req.ToString());
            GameEntry.LDLNet.Send(Protocol.MessageID.RecruitUpdatePreview, req, (message) =>
            {
                // var resp = (RecruitUpdatePreviewResp)message;
                ProtoLog(false, "招募协议(更新已读预告时间戳)", null);
                action?.Invoke(null);
            });
        }
        
        //协议返回数据打印
        private void ProtoLog(bool isRequest,string protoName,string protoDetail = null)
        {
            ColorLog.ProtoLog(isRequest,protoName,protoDetail);
        }
        
        //协议返回数据打印
        private void ProtoLog(bool isRequest,string protoName,object data)
        {
            ColorLog.ProtoLog(isRequest,protoName,data);
        }
        
        #endregion
        
        //设置按钮显隐
        public void SetBtnTxt(Button btn,recruitnumtype numType,recruittype type)
        {
            var root = btn.transform;
            var descTxt = root.Find("desc").GetComponent<UIText>();
            var numTxt = root.Find("num").GetComponent<UIText>();
            var icon = root.Find("icon").GetComponent<UIImage>();
            var flag = (int)numType;
            if (flag == 0)
            {
                flag = 1;//特殊处理免费招募次数
            }
            
            //招募{0}次
            var str = ToolScriptExtend.GetLang(1100101);
            descTxt.text = string.Format(str,flag);
            numTxt.text = flag.ToString();
            
            var itemId = GetIconIdByType(type);
            SetSprite(icon,itemId);
        }

        //设置顶部钻石资源栏信息显示
        public void SetTopDiamondView(GameObject obj,bool onlyRefresh = false)
        {
            BindResView(itemid.itemid_6, obj,onlyRefresh);
        }
        //设置顶部招募券资源栏信息显示
        public void SetRecruitTicketView(recruittype type,GameObject obj,bool onlyRefresh = false)
        {
            var itemId = GetIconIdByType(type);
            BindResView(itemId, obj);
        }
        
        private void BindResView(itemid itemId,GameObject obj,bool onlyRefresh = false)
        {
            if (!onlyRefresh)
            {
                var img = obj.transform.Find("icon").GetComponent<UIImage>();
                SetSprite(img,itemId);
            }
            var txt = obj.transform.Find("num").GetComponent<UIText>();
            var ownCount = GameEntry.LogicData.BagData.GetAmountById(itemId);
            txt.text = ToolScriptExtend.FormatNumberWithUnit(ownCount);
        }
        
        private void SetSprite(UIImage img,itemid itemId)
        {
            if (GameEntry.LDLTable.HaseTable<Game.Hotfix.Config.item_config>())
            {
                var data = GameEntry.LDLTable.GetTableById<Game.Hotfix.Config.item_config>(itemId);
                if (data.icon == null)
                {
                    return;
                }
                img.SetImage(data.icon);
            }
        }
        
        public itemid GetIconIdByType(recruittype type)
        {
            itemid itemId = itemid.itemid_1010091;
            switch (type)
            {
                case recruittype.recruittype_hero:
                    itemId = itemid.itemid_1010091;
                    break;
                case recruittype.recruittype_survivor:
                    itemId = itemid.itemid_1010092;
                    break;
                case recruittype.recruittype_season:
                    itemId = itemid.itemid_1010032;
                    break;
                default:
                    break;
            }
            return itemId;
        }
        
        //获取心愿英雄id,如果没有选则返回-1
        public itemid GetWishHero()
        {
            if (MsgDictionary.TryGetValue((uint)recruitId_hero, out var value))
            {
                return (itemid)value.ChooseHero;
            }
            return itemid.itemid_nil;
        }
        
        //是否已经选择心愿英雄
        public bool HasSelectWishHero()
        {
            var curId = GetWishHero();
            var list = GetWishHeroList();
            foreach (var heroId in list)
            {
                if (curId == heroId)
                {
                    return true;
                }
            }
            return false;
        }
        
        //指定卡池的当前保底次数
        public int GetGuardTimes(int id)
        {
            if (MsgDictionary.TryGetValue((uint)id, out var value))
            {
                return (int)value.GuardTimes;
            }
            return 0;
        }
        
        //指定卡池的当前心愿英雄领取进度
        public int GetWishTimes(int id)
        {
            if (MsgDictionary.TryGetValue((uint)id, out var value))
            {
                return (int)value.Wish;
            }
            return 0;
        }
        
        public int GetFreeTimeInterval(int id)
        {
            if (MsgDictionary.TryGetValue((uint)id, out var value))
            {
                var cd = GetFreeCD();
                return (int)value.LastFreeTime+cd - (int)TimeComponent.Now;
            }
            return 0;
        }
        
        //指定卡池是否可免费招募
        public bool IsCanFree(int id)
        {
            if (MsgDictionary.TryGetValue((uint)id, out var value))
            {
                var timestamp = (int)value.LastFreeTime+GetFreeCD();
                if (timestamp == 0)
                {
                    return true;
                }
                else
                {
                    var time = (int)TimeComponent.Now;
                    return time > timestamp;
                }
            }
            return false;
        }

        //格式化时间字符串
        public string FormatTime(int seconds)
        {
            TimeSpan span = new TimeSpan(0,0,seconds);
            if (span.Days > 0)
            {
                sb.Clear();
                sb.Append(span.Days);
                sb.Append("d ");
                sb.Append(span.ToString(@"hh\:mm\:ss"));
                return sb.ToString();
            }
            else
            {
                return span.ToString(@"hh\:mm\:ss");
            }
        }

        //获取上次查看英雄预告的时间戳
        private int GetLastCheckPreview(int id)
        {
            if (MsgDictionary.TryGetValue((uint)id, out var value))
            {
                return (int)value.ReadPreviewTime;
            }
            return 0;
        }
        
        //判断玩家预览
        private bool NeedPreview(int id,int preDay,int onDay)
        {
            var time = GameEntry.LDLNet.GetOpenServiceTimestamp();
            var curTime = (int)TimeComponent.Now;
            var preTimestamp = time + preDay* 86400;
            var openTimestamp = time + onDay * 86400;
            if (curTime >= preTimestamp && curTime < openTimestamp)
            {
                var lastCheck = GetLastCheckPreview(id);
                if (lastCheck == 0)
                {
                    return true;
                }
                else
                {
                    return lastCheck < preTimestamp;
                }
            }
            return false;
        }
        
        //判断是否可以领取心愿英雄
        public bool IsCanReceiveWishHero(int recruitId)
        {
            if (!CheckWishUnlock()) return false;
            var value1 = GetWishTimes(recruitId);
            var value2 = GetWishConfigCount(recruitId);
            return value1 >= value2;
        }

        //判断心愿英雄模块是否解锁
        public bool CheckWishUnlock()
        {
            if (ToolScriptExtend.GetConfigById<recruit_config>(recruitId_hero, out var recruitConfig))
            {
                return ToolScriptExtend.GetDemandUnlock(recruitConfig.pray_demand);
            }
            return false;
        }
    }
}
