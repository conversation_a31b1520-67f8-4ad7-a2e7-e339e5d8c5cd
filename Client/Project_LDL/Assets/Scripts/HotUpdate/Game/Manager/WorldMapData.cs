using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Game.Hotfix.Config;
using GameFramework.Resource;
using UnityEngine;
using Wmcamera;

namespace Game.Hotfix
{
    public class WorldMapData
    {
        public int Season => m_Season;
        
        public Vector2Int HomePos = new Vector2Int(70, 70);

        private WorldMapStaticData m_WorldMapStaticData;

        private Dictionary<Vector2Int, WorldMapGridData> m_GridDataDic;

        private WorldMapTroopData m_TroopData;
        
        private Dictionary<Vector3Int, Vector2Int> m_MapSettingStaminaDes;

        /// <summary>
        /// 赛季
        /// </summary>
        private int m_Season = 0;
        
        public void Init()
        {
            InitMapSetting();

            m_TroopData = new WorldMapTroopData();
            
            m_GridDataDic = new Dictionary<Vector2Int, WorldMapGridData>();
            
            if (m_WorldMapStaticData == null)
            {
                LoadData();
            }

            InitCityData();
            // InitElementTestData();
            // InitTownTestData();
        }

        /// <summary>
        /// 处理 map_setting 关于element 赛季|大类型|小类型|体力|描述 的解析
        /// </summary>
        private void InitMapSetting()
        {
            m_MapSettingStaminaDes = new Dictionary<Vector3Int, Vector2Int>();
            for (int i = 701; i <= 704; i++)
            {
                var cfg = Game.GameEntry.LDLTable.GetTableById<map_setting>(i);
                if (cfg != null && cfg.value.Count>=5)
                {
                    if (int.TryParse(cfg.value[0], out var x) &&
                        int.TryParse(cfg.value[1], out var y) &&
                        int.TryParse(cfg.value[2], out var z) &&
                        int.TryParse(cfg.value[3], out var a) &&
                        int.TryParse(cfg.value[4], out var b)
                       )
                    {
                        m_MapSettingStaminaDes.Add(new Vector3Int(x, y, z), new Vector2Int(a, b));
                    }
                }
            }
        }
        
        private void InitCityData()
        {
            var mapCityS0s = GameEntry.LDLTable.GetTable<map_city_s0>();
            if (mapCityS0s != null)
            {
                foreach (var rowData in mapCityS0s)
                {
                    var x = rowData.city_location.x;
                    var y = rowData.city_location.y;
                    var gridPos = MapGridUtils.GetGrid(x, y, GameDefine.WorldMapDataGridSize);

                    var gridData = GetGridDataByPos(gridPos);
                    gridData?.AddCity(x, y, rowData.id);
                }
            }
        }

        private void InitElementTestData()
        {
            var table = GameEntry.LDLTable.GetTable<map_element>();
            var max = table.Count;
            HashSet<Vector2Int> set = new HashSet<Vector2Int>();
            for (int i = 0; i < 3000; i++)
            {
                var rIndex = Random.Range(0, max - 1);
                if (table[rIndex] != null)
                {
                    var t = table[rIndex];
                    var posX = Random.Range(0, 999);
                    var posY = Random.Range(0, 999);

                    if (set.Contains(new Vector2Int(posX, posY)))
                        continue;
                    set.Add(new Vector2Int(posX, posY));
                    
                    var gridPos = MapGridUtils.GetGrid(posX, posY, GameDefine.WorldMapDataGridSize);
                    var gridData = GetGridDataByPos(gridPos);

                    var config = GameEntry.LDLTable.GetTableById<map_element>(t.id);
                    if (config.big_type == (map_element_big_type)WorldMapElementType.Gather)
                    {
                        gridData?.AddMine(posX, posY, t.id);
                    }
                    else if (config.big_type == (map_element_big_type)WorldMapElementType.Zombies)
                    {
                        gridData?.AddMonster(posX, posY, t.id);
                    }
                    else if (config.big_type == (map_element_big_type)WorldMapElementType.DoomElite)
                    {
                        gridData?.AddForts(posX, posY, t.id);
                    }
                }
            }
        }

        private void InitTownTestData()
        {
            for (int i = 0; i < 3000; i++)
            {
                var posX = Random.Range(0, 999);
                var posY = Random.Range(0, 999);
                var gridPos = MapGridUtils.GetGrid(posX, posY, GameDefine.WorldMapDataGridSize);

                ulong playerId = (ulong)i;
                uint buildingId = 101;
                int level = Random.Range(0, 40);
                
                var gridData = GetGridDataByPos(gridPos);
                gridData?.AddTown(playerId, buildingId, level, (uint)posX, (uint)posY);
            }
            
            //添加玩家主城
            var homeGridPos = MapGridUtils.GetGrid(HomePos.x, HomePos.y, GameDefine.WorldMapDataGridSize);
            var homeGridData = GetGridDataByPos(homeGridPos);

            GridTownData homeGridTownData = GetMyTownData();
            homeGridData?.AddTown(homeGridTownData);
        }

        public GridTownData GetMyTownData()
        {
            GridTownData data = new GridTownData();
            data.PlayerId = GameEntry.LogicData.UserData.uuid;
            data.BuildingId = 101;
            data.Level = Random.Range(0, 40);
            data.PosX = (uint)HomePos.x;
            data.PosY = (uint)HomePos.y;
            return data;
        }
        
        public WorldMapGridData GetGridDataByPos(int x,int y)
        {
            return GetGridDataByPos(new Vector2Int(x, y));
        }
        
        public WorldMapGridData GetGridDataByPos(Vector2Int pos)
        {
            if (!m_GridDataDic.ContainsKey(pos))
            {
                m_GridDataDic.Add(pos, WorldMapGridData.Create(pos.x, pos.y));
            }
            
            return m_GridDataDic.TryGetValue(pos,out var data) ? data : null;
        }
        
        public void GotoWorldMap()
        {
            var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
            if (tCurrentProcedure is ProcedureMain procedure)
            {
                procedure.GotoBattle((int)SceneDefine.WorldMapScene);
            }
        }

        public void GoBackMainCity()
        {
            var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
            if (tCurrentProcedure is ProcedureWorldMap procedure)
            {
                procedure.GotoMainCity();
            }
        }

        private void LoadData()
        {
            var path = AssetUtility.GetWorldMapDataAsset("WorldMapData");
            GameEntry.Resource.LoadAsset(path, new LoadAssetCallbacks(
                (string assetName, object asset, float duration, object userData) =>
                {
                    if (asset is WorldMapStaticData data)
                    {
                        m_WorldMapStaticData = data;
                    }
                    // Debug.LogError("AA");
                }, (
                    string assetName, LoadResourceStatus status, string errorMessage,
                    object userData) =>
                {
                    Debug.LogErrorFormat("Can not load WorldMapData from '{0}' with error message '{1}'.", assetName,
                        errorMessage);
                }));
        }

        public List<Vector3Int> GetTreeList(Vector2Int bigGrid)
        {
            if (m_WorldMapStaticData != null && m_WorldMapStaticData.TreeDic!=null)
            {
                var treeDic = m_WorldMapStaticData.TreeDic;
                if (treeDic.TryGetValue(bigGrid,out var treeList))
                {
                    return treeList;
                }
            }
            return null;
        }

        public ushort GetAreaId(Vector2Int pos)
        {
            var ed = m_WorldMapStaticData.areaData[(pos.x + pos.y * 1000)];
            return ed;
        }

        public void ClearAllData()
        {
            HashSet<Vector2Int> dirtySet = new HashSet<Vector2Int>();
            foreach (var data in m_GridDataDic)
            {
                dirtySet.Add(data.Key);
                data.Value.Clear();
            }
            
            if (dirtySet.Count > 0)
                GameEntry.Event.Fire(this, OnWorldMapGridDataDirtyArgs.Create(dirtySet));
        }

        public void ClearAllDataExceptTown()
        {
            HashSet<Vector2Int> dirtySet = new HashSet<Vector2Int>();
            foreach (var data in m_GridDataDic)
            {
                dirtySet.Add(data.Key);
                data.Value.ClearElement();
            }
            
            if (dirtySet.Count > 0)
                GameEntry.Event.Fire(this, OnWorldMapGridDataDirtyArgs.Create(dirtySet));
        }
        
        public void ProcessLogicalCameraData(ushort error, CameraMoveResp resp)
        {
            if (error > 0) return;

            HashSet<Vector2Int> dirtySet = new HashSet<Vector2Int>();
            
            //删除格子数据
            int gridX;
            int gridY;
            foreach (var deleteGridId in resp.DeleteGrids)
            {
                MapGridUtils.LogicGridId2Pos((int)deleteGridId, out gridX, out gridY);
                var gridData = GetGridDataByPos(gridX,gridY);
                gridData.Clear();

                dirtySet.Add(new Vector2Int(gridX, gridY));
            }
            
            //增加town
            foreach (var town in resp.Towns)
            {
                var gridPos = MapGridUtils.GetGrid(town.X, town.Y, GameDefine.WorldMapDataGridSize);
                var gridData = GetGridDataByPos(gridPos);
                gridData?.AddTown(town.Id, town.BuildNo, (int)town.BuildLevel, town.X, town.Y);
                
                dirtySet.Add(gridPos);
            }
            
            //增加monster
            foreach (var longId in resp.Monsters)
            {
                ParseId2Element(longId, out var tempId, out var tempX, out var tempY);
                if(tempId>0)
                {
                    var gridPos = MapGridUtils.GetGrid(tempX, tempY, GameDefine.WorldMapDataGridSize);
                    var gridData = GetGridDataByPos(gridPos);
                    gridData?.AddMonster((int)tempX, (int)tempY, (int)tempId);
                    
                    dirtySet.Add(gridPos);
                }
            }
            
            //增加精英怪
            foreach (var longId in resp.Forts)
            {
                ParseId2Element(longId, out var tempId, out var tempX, out var tempY);
                if(tempId>0)
                {
                    var gridPos = MapGridUtils.GetGrid(tempX, tempY, GameDefine.WorldMapDataGridSize);
                    var gridData = GetGridDataByPos(gridPos);
                    gridData?.AddForts((int)tempX, (int)tempY, (int)tempId);
                    
                    dirtySet.Add(gridPos);
                }
            }
            
            //增加矿
            foreach (var mine in resp.Mines)
            {
                ParseId2Element(mine.Id, out var tempId, out var tempX, out var tempY);
                if(tempId>0)
                {
                    var gridPos = MapGridUtils.GetGrid(tempX, tempY, GameDefine.WorldMapDataGridSize);
                    var gridData = GetGridDataByPos(gridPos);
                    gridData?.AddMine((int)tempX, (int)tempY, (int)tempId);
                    
                    dirtySet.Add(gridPos);
                }
            }


            if (dirtySet.Count > 0)
                GameEntry.Event.Fire(this, OnWorldMapGridDataDirtyArgs.Create(dirtySet));
        }

        private void ParseId2Element(ulong id,out uint code,out uint x,out uint y)
        {
            code = (uint)(id / 1000000UL);
            x = (uint)((id - code * 1000000UL) / 1000);
            y = (uint)(id - code * 1000000UL - x * 1000);
        }

        private ulong ParseElement2Id(uint code,uint x,uint y)
        {
            return code * 1000000UL + x * 1000 + y;
        }

        /// <summary>
        /// 获取全局配置 element 体力消耗
        /// </summary>
        /// <param name="type"></param>
        /// <param name="subType"></param>
        /// <returns></returns>
        public int GetMapSettingStamina(WorldMapElementType type,int subType)
        {
            return GetMapSettingStamina((int)type, subType);
        }
        
        public int GetMapSettingStamina(int type,int subType)
        {
            var key = new Vector3Int(m_Season, type, subType);
            if(m_MapSettingStaminaDes.TryGetValue(key, out var value))
            {
                return value.x;
            }
            return 0;
        }

        /// <summary>
        /// 获取全局配置 element 描述
        /// </summary>
        /// <param name="type"></param>
        /// <param name="subType"></param>
        /// <returns></returns>
        public string GetMapSettingDes(WorldMapElementType type,int subType)
        {
            return GetMapSettingDes((int)type, subType);
        }
        
        public string GetMapSettingDes(int type,int subType)
        {
            var key = new Vector3Int(m_Season, (int)type, subType);
            if(m_MapSettingStaminaDes.TryGetValue(key, out var value))
            {
                return ToolScriptExtend.GetLang(value.y);
            }
            return string.Empty;
        }
        
        #region Troop

        public void GetInShowTroopList(out List<ulong> list)
        {
            list = m_TroopData.GetAllTroopIds().ToList();
        }

        public TroopData GetTroopData(ulong id)
        {
            return m_TroopData.GetTroopData(id);
        }
        
        #endregion
        
    }
}