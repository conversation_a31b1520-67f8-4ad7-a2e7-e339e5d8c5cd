using System.Collections.Generic;
using System.Linq;
using Fight;
using Roledata;
using Team;
using UnityEngine;

namespace Game.Hotfix
{
    public class TeamData
    {
        private Dictionary<TeamType,List<FormationHero>> m_Teams;
        
        public void Init(RoleTeam roleTeam)
        {
            if (roleTeam == null)
                return;

            foreach (var team in roleTeam.Teams)
            {
                m_Teams
            }
            
        }
        
        
        
    }
}
