using System;
using System.Collections.Generic;
using System.Linq;
using Fight;
using Roledata;
using Team;
using UnityEngine;

namespace Game.Hotfix
{
    public class TeamData
    {
        private Dictionary<TeamType,List<FormationHero>> m_Teams;
        
        public void Init(RoleTeam roleTeam)
        {
            m_Teams = new Dictionary<TeamType, List<FormationHero>>();
            
            if (roleTeam == null)
                return;

            foreach (var team in roleTeam.Teams)
            {
                if(m_Teams.TryAdd(team.TeamType,team.Heroes.ToList()))
                {
                    
                }
            }
        }
        
        public List<FormationHero> GetTeam(TeamType teamType)
        {
            if (m_Teams.TryGetValue(teamType, out var team))
            {
                return team;
            }

            return null;
        }

        public void TeamModify(FormationTeam formationTeam, Action callback)
        {
            var list = new List<FormationTeam>();
            list.Add(formationTeam);
            TeamModify(list, callback);
        }
        
        public void TeamModify(List<FormationTeam> formationTeams,Action callback)
        {
            TeamModifyReq req = new TeamModifyReq();
            
            req.Teams.AddRange(formationTeams);
            
            GameEntry.LDLNet.Send(Protocol.MessageID.TeamModify, req, (message) =>
            {
                if (message is TeamModifyResp)
                {
                    foreach (var team in formationTeams)
                    {
                        m_Teams[team.TeamType] = team.Heroes.ToList();
                    }
                    
                    callback?.Invoke();
                }
            });
        }
        
    }
}
