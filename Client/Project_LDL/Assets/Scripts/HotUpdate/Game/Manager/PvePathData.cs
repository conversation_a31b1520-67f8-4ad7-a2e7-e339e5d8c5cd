using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using Innercity;
using dungeon = PbGameconfig.dungeon;

namespace Game.Hotfix
{
    public class PvePathData : IEnumerable<PvePathModule>
    {
        public int CurStepDisplay => m_CurStepDisplay;
        public bool IsFinish => m_EndModule?.Id == m_CurStep;

        /// <summary>
        /// 显示上得目标
        /// </summary>
        public PvePathModule CurPathModuleDisplay => GetPvePathModuleById(CurStepDisplay);
        /// <summary>
        /// 逻辑上得目标
        /// </summary>
        public PvePathModule CurPathModule => GetPvePathModuleById(m_CurStep);


        private LinkedList<PvePathModule> m_LinkedList;
        private int m_CurStep;
        private int m_CurStepDisplay;
        private PvePathModule m_EndModule;

        public PvePathData()
        {
            m_LinkedList = new LinkedList<PvePathModule>();
        }

        public void Init(int? curStep)
        {
            m_CurStep = curStep ?? 1;
            m_CurStepDisplay = m_CurStep;

            CreatePvePathModule(1, 1);
            SetDisplayData(m_LinkedList.First);
        }

        private void CreatePvePathModule(int id, int beginIndex)
        {
            HashSet<int> showSet = new HashSet<int>();

            innercity_path innercityCfg = GameEntry.LDLTable.GetTableById<innercity_path>(id);
            if (innercityCfg != null)
            {
                bool canShowGround = id >= m_CurStepDisplay;

                //增加showSet
                bool canShowObj = false;
                if (id >= m_CurStepDisplay + 1)
                {
                    if (innercityCfg.is_always_show)
                    {
                        canShowObj = true;
                    }
                    else if (showSet.Contains(id))
                    {
                        canShowObj = true;
                    }
                    else if (id == m_CurStepDisplay + 1)
                    {
                        //下一个 一定显示
                        canShowObj = true;
                    }
                }

                for (int i = 0; i < innercityCfg.arrive_show.Count; i++)
                {
                    if (!showSet.Contains(innercityCfg.arrive_show[i]))
                        showSet.Add(innercityCfg.arrive_show[i]);
                }

                if (innercityCfg.is_destination)
                {
                    if (m_CurStepDisplay > beginIndex && m_CurStepDisplay < id)
                    {
                        //将之前的设置为
                        LinkedListNode<PvePathModule>? temp = null;
                        for (int i = 0; i < id - beginIndex; i++)
                        {
                            if (temp == null)
                                temp = m_LinkedList.Last;
                            else
                                temp = temp.Previous;

                            if (temp == null)
                                break;
                            temp.Value?.ShowGround(true);
                        }
                    }
                }

                PvePathModule pvePathModule = new PvePathModule(innercityCfg,canShowGround,canShowObj);

                if (innercityCfg.is_destination)
                {
                    pvePathModule.SetBeginIndex(beginIndex);
                    beginIndex = id;
                }


                m_LinkedList.AddLast(pvePathModule);
                if (pvePathModule.HasNext())
                {
                    CreatePvePathModule(pvePathModule.GetNext(), beginIndex);
                }
                else
                {
                    m_EndModule = pvePathModule;
                }
            }
        }


        public void SetDisplayData(LinkedListNode<PvePathModule> node)
        {
            if (node.Previous != null)
            {
                node.Value.DirFrom = node.Previous.Value.DirTo;
            }

            if (node.Next != null)
            {
                node.Value.DirTo = (node.Next.Value.GetPosition() - node.Value.GetPosition()).normalized;
                SetDisplayData(node.Next);
            }
        }

        public PvePathModule GetPvePathModuleById(int id)
        {
            return m_LinkedList.FirstOrDefault(item => item.Id == id);
        }

        public bool CanTriggerEvent(PvePathModule pvePathModule,bool silence)
        {
            
            //TODO判断是不是在移动中

            if (!pvePathModule.CanTriggerEvent())
            {
                if(!silence)
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = "建筑物等级不够",
                    });
                return false;
            }
            return true;
        }

        public bool TryTriggerEvent(PvePathModule pvePathModule)
        {
            if (!CanTriggerEvent(pvePathModule, false))
            {
                return false;
            }

            var config = pvePathModule.GetConfig();
            if (config.event_type.types == ground_eventtype.ground_eventtype_1)
            {
                int dungeonId = config.event_type.value;
                if(dungeonId)
                Config.dungeon dungeonCfg = GameEntry.LDLTable.GetTableById<Config.dungeon>(dungeonId);
                if (dungeonCfg!=null)
                {
                    GameEntry.LogicData.Battle5v5Data.GoBattleDungeon(dungeonCfg,pvePathModule);    
                }
                
            }
            else if (config.event_type.types == ground_eventtype.ground_eventtype_2)
            {
                TriggerEventFinish(pvePathModule);
            }
            else if (config.event_type.types == ground_eventtype.ground_eventtype_3)
            {
                //剧情？？
                TriggerEventFinish(pvePathModule);
            }
            else if (config.event_type.types == ground_eventtype.ground_eventtype_nil)
            {
                TriggerEventFinish(pvePathModule);
            }
            else
            {
                TriggerEventFinish(pvePathModule);
            }

            return true;
        }

        public void TriggerEventFinish(PvePathModule pvePathModule)
        {
            InnerCityOccupyGridReq req = new InnerCityOccupyGridReq();
            req.Id = pvePathModule.Id;
            GameEntry.LDLNet.Send(Protocol.MessageID.InnerCityOccupyGrid, req, (message) =>
            {
                InnerCityOccupyGridResp resp = (InnerCityOccupyGridResp)message;
                if (resp != null && resp.Id != 0)
                {
                    //奖励
                    if (resp.Rewards != null)
                    {
                        pvePathModule.CacheRewards = resp.Rewards;
                        if (pvePathModule.GetEventType() != ground_eventtype.ground_eventtype_1)
                        {
                            List<reward> rewards = resp.Rewards.Select(item => new reward() { item_id = (itemid)item.Code, num = item.Amount }).ToList();
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);
                        }
                    }
                    
                    var curModule = CurPathModuleDisplay;
                    m_CurStep = pvePathModule.Id;
                    
                    curModule.OnLeaveLogic();
                    pvePathModule.OnArriveLogic();
                    
                    GameEntry.Event.Fire(OnPvePathTriggeredEventArgs.EventId,
                        OnPvePathTriggeredEventArgs.Create(curModule,pvePathModule));    
                }
            });
        }
        
        public void OnAgentArrive(PvePathModule pvePathModule)
        {
            var config = pvePathModule.GetConfig();
            if (config.event_type.types == ground_eventtype.ground_eventtype_1)
            {
                //关卡奖励 在 关卡表
                if (pvePathModule.CacheRewards != null && pvePathModule.CacheRewards.Count > 0)
                {
                    List<reward> rewards = new List<reward>();
                    for (int i = 0; i < pvePathModule.CacheRewards.Count; i++)
                    {
                        var reward = pvePathModule.CacheRewards[i];
                        rewards.Add(new reward() { item_id = (itemid)reward.Code, num = reward.Amount });
                    }
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);
                }
                // var dungeonCfg = Game.GameEntry.LDLTable.GetTableById<Config.dungeon>(config.event_type.value);
                // if (dungeonCfg != null && dungeonCfg.reward?.Count>0)
                // {
                //     GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, dungeonCfg.reward);
                // }
            }
        }

        public List<PvePathModule> GetIncludeBackGrounds(PvePathModule pvePathModule)
        {
            var list = new List<PvePathModule>();
            if (pvePathModule.IsDestination() && pvePathModule.BeginIndex != null)
            {
                var beginIndex = pvePathModule.BeginIndex.Value;
                for (int i = 0; i < pvePathModule.Id-beginIndex; i++)
                {
                    var m = GetPvePathModuleById(beginIndex + i);
                    list.Add(m);
                }
            }
            return list;
        }

        public void OnDisplayArrive(PvePathModule nPvePathModule)
        {
            m_CurStepDisplay = nPvePathModule.Id;
            
            //显示下一个
            var nextId = nPvePathModule.GetNext();
            if (nextId > 0)
            {
                var nextModule = GetPvePathModuleById(nextId);
                if (!nextModule.InShowObj)
                {
                    nextModule.ShowObj(true);
                }
            }

            //显示打开列表里面的
            var showList = nPvePathModule.GetArriveShow();
            for (int i = 0; i < showList.Count; i++)
            {
                var tempModule = GetPvePathModuleById(showList[i]);
                if (tempModule != null && !tempModule.InShowObj)
                {
                    tempModule.ShowObj(true);
                }
            }

        }
        
        #region 其他

        public IEnumerator<PvePathModule> GetEnumerator()
        {
            foreach (PvePathModule item in m_LinkedList)
            {
                yield return item;
            }
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        #endregion
    }
}