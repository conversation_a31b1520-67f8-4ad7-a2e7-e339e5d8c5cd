using System;
using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class HeroData
    {
        public const int SKILL_COUNT = 4;

        private Dictionary<itemid, HeroModule> heroModuleDic = new();
        private Dictionary<hero_services, List<itemid>> heroServicesDic = new();
        private Dictionary<itemid, bool> honorItemDic;
        private Dictionary<itemid, bool> newHeroDic = new();
        private List<itemid> newHeroList = new();

        public void Init(Roledata.RoleHero roleHero)
        {
            InitConfig();
            InitRedPoint();

            if (roleHero == null)
                return;

            var heroList = roleHero.Heroes;
            OnHeroUpdate(heroList);
        }

        private void InitConfig()
        {
            heroServicesDic.TryAdd(hero_services.hero_services_all, new());

            List<hero_config> config = GameEntry.LDLTable.GetTable<hero_config>();
            foreach (var item in config)
            {
                heroModuleDic.TryAdd(item.id, new(item.id));
                heroServicesDic.TryAdd(item.services, new());

                List<itemid> list;
                heroServicesDic.TryGetValue(item.services, out list);
                list?.Add(item.id);

                heroServicesDic.TryGetValue(hero_services.hero_services_all, out list);
                list?.Add(item.id);
            }
        }

        private void InitRedPoint()
        {
            RedPointManager.Instance.AddNodeTo(EnumRed.Root.ToString(), EnumRed.Hero.ToString(), () =>
            {
                var list = GetHeroModuleList(hero_services.hero_services_all);
                for (int i = 0; i < list.Count; i++)
                {
                    var heroVo = list[i];
                    if (GetSingleHeroRed(heroVo.id) || heroVo.GetHeroStarRed())
                    {
                        return 1;
                    }
                }
                return 0;
            });
        }

        public bool GetSingleHeroRed(itemid heroId)
        {
            var heroVo = GetHeroModule(heroId);
            if (heroVo != null)
            {
                return heroVo.IsCombind || heroVo.GetHeroUpgradeRed();
            }
            return false;
        }

        #region 调用接口

        public List<itemid> GetHeroIdList(hero_services services)
        {
            heroServicesDic.TryGetValue(services, out List<itemid> list);
            return list ?? new();
        }

        public List<HeroModule> GetHeroModuleList(hero_services services, bool isSort = false, Func<HeroModule, bool> filter = null)
        {
            List<HeroModule> heroVoList = new();
            var list = GetHeroIdList(services);
            foreach (var heroId in list)
            {
                var heroVo = GetHeroModule(heroId);
                if (heroVo != null && heroVo.IsShow)
                {
                    if (filter != null)
                    {
                        if (filter.Invoke(heroVo)) heroVoList.Add(heroVo);
                    }
                    else
                    {
                        heroVoList.Add(heroVo);
                    }
                }
            }

            if (isSort)
            {
                heroVoList.Sort((a, b) =>
                {
                    var isEqualCombind = a.IsCombind == b.IsCombind;
                    var isEqualActive = a.IsActive == b.IsActive;
                    var isEqualNew = GetHeroNew(a.id) == GetHeroNew(b.id);
                    var isEqualNewIndex = GetHeroNewIndex(a.id) == GetHeroNewIndex(b.id);
                    var isEqualQuality = a.Quality == b.Quality;
                    var isCompare = (a.IsCombind && !b.IsCombind)
                                || (isEqualCombind && a.IsActive && !b.IsActive)
                                || (isEqualCombind && isEqualActive && GetHeroNew(a.id) && !GetHeroNew(b.id))
                                || (isEqualCombind && isEqualActive && isEqualNew && GetHeroNewIndex(a.id) > GetHeroNewIndex(b.id))
                                || (isEqualCombind && isEqualActive && isEqualNew && isEqualNewIndex && a.Quality > b.Quality)
                                || (isEqualCombind && isEqualActive && isEqualNew && isEqualNewIndex && isEqualQuality && a.power > b.power);
                    return isCompare ? -1 : 1;
                });
            }
            return heroVoList;
        }

        public List<HeroModule> GetHeroHonorList(hero_services services, bool isSort = false, Dictionary<hero_services, bool> redDic = null)
        {
            List<HeroModule> heroVoList = new();
            var list = GetHeroIdList(hero_services.hero_services_all);
            foreach (var heroId in list)
            {
                var heroVo = GetHeroModule(heroId);
                if (heroVo != null && heroVo.IsActive && heroVo.IsShow && heroVo.starLv >= heroVo.MaxStarLv)
                {
                    if (heroVo.Services == services)
                        heroVoList.Add(heroVo);

                    if (redDic != null)
                    {
                        redDic.TryGetValue(heroVo.Services, out bool isShowRed);
                        if (!isShowRed && heroVo.GetHeroHonorRed())
                            redDic[heroVo.Services] = true;
                    }
                }
            }

            if (isSort)
            {
                heroVoList.Sort((a, b) =>
                {
                    var isEqualQuality = a.Quality == b.Quality;
                    var isEqualHonor = a.honorLv == b.honorLv;
                    var isCompare = a.Quality > b.Quality || (isEqualQuality && a.honorLv > b.honorLv)
                                    || (isEqualQuality && isEqualHonor && a.power > b.power);
                    return isCompare ? -1 : 1;
                });
            }
            return heroVoList;
        }

        public HeroModule GetHeroModule(itemid heroId)
        {
            HeroModule heroModule;
            heroModuleDic.TryGetValue(heroId, out heroModule);
            return heroModule;
        }

        public bool IsHeroActive(itemid heroId)
        {
            var heroVo = GetHeroModule(heroId);
            if (heroVo != null)
                return heroVo.IsActive;
            return false;
        }

        public attributes GetHeroHonorAddAttr()
        {
            attributes attr = null;
            var heroList = GetHeroHonorList(hero_services.hero_services_all);
            for (int i = 0; i < heroList.Count; i++)
            {
                var heroVo = heroList[i];
                var config = heroVo.GetHeroHonorConfig(heroVo.honorLv);
                if (config != null && config.add_attributes != null)
                {
                    var attrInfo = config.add_attributes;
                    attr ??= new()
                    {
                        attributes_target = attrInfo.attributes_target,
                        attributes_type = attrInfo.attributes_type,
                        value_type = attrInfo.value_type,
                        value = 0,
                    };
                    attr.value += attrInfo.value;
                }
            }
            return attr;
        }

        public void OpenHeroSingleForm(itemid heroId, int panelIndex = 0)
        {
            var heroVo = GetHeroModule(heroId);
            if (heroVo == null)
                return;

            List<HeroModule> heroList = new() { heroVo };
            GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroDevelopForm, new UIHeroDevelopFormParams()
            {
                HeroList = heroList,
                HeroIndex = 0,
                PanelIndex = panelIndex,
            });
        }

        public string GetHeroSkillDesc(skill_config skillConfig, int skillLv = 1)
        {
            var desc = "";
            if (skillConfig != null)
            {
                desc = ToolScriptExtend.GetLang(skillConfig.skill_desc);
                var strList = skillConfig.skill_effect;
                for (int i = 0; i < strList.Count; i++)
                {
                    var effectConfig = GameEntry.LDLTable.GetTableById<skill_effect>(int.Parse(strList[i]));
                    if (effectConfig != null)
                    {
                        float value = effectConfig.effect_value + effectConfig.effect_value_lv_factor * (skillLv - 1);
                        var replaceStr = "{" + i + "}";
                        desc = desc.Replace(replaceStr, string.Format("<color=#5ce785>{0}</color>", (value / 100).ToString("0.00") + "%"));
                    }
                }
            }
            return desc;
        }

        /// <summary>
        /// 根据兵种获取荣誉墙红点
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public bool GetHonorServicesRed(hero_services services)
        {
            Dictionary<hero_services, bool> redDic = new();
            GetHeroHonorList(services, false, redDic);
            redDic.TryGetValue(services, out bool isShowRed);
            return isShowRed;
        }

        /// <summary>
        /// 获取荣誉墙升级材料
        /// </summary>
        /// <returns></returns>
        public Dictionary<itemid, bool> GetHonorItemDic()
        {
            if (honorItemDic == null)
            {
                honorItemDic = new();
                var config = GameEntry.LDLTable.GetTable<hero_promotion>();
                for (int i = 0; i < config.Count; i++)
                {
                    var dic = config[i].promotion_cost;
                    honorItemDic.TryAdd(dic.item_id, true);
                }
            }
            return honorItemDic;
        }

        /// <summary>
        /// 获取新英雄状态
        /// </summary>
        /// <param name="heroId"></param>
        /// <returns></returns>
        public bool GetHeroNew(itemid heroId)
        {
            newHeroDic.TryGetValue(heroId, out bool newState);
            return newState;
        }

        /// <summary>
        /// 获取新英雄获得顺序
        /// </summary>
        /// <param name="heroId"></param>
        /// <returns></returns>
        public int GetHeroNewIndex(itemid heroId)
        {
            return newHeroList.IndexOf(heroId);
        }

        /// <summary>
        /// 设置新英雄状态
        /// </summary>
        /// <param name="heroId"></param>
        /// <param name="newState"></param>
        public void SetHeroNew(itemid heroId, bool newState)
        {
            if (!newHeroDic.TryAdd(heroId, newState))
            {
                newHeroDic[heroId] = newState;
            }

            if (newHeroList.Contains(heroId))
            {
                if (!newState) newHeroList.Remove(heroId);
            }
            else
            {
                if (newState) newHeroList.Add(heroId);
            }
        }

        /// <summary>
        /// 清除新英雄状态
        /// </summary>
        public void ClearHeroNew()
        {
            newHeroDic.Clear();
            newHeroList.Clear();
        }

        public string GetPositionImgPath(hero_position position)
        {
            return position switch
            {
                hero_position.hero_position_1 => "Sprite/ui_public/icon_dingwei_fangyu.png",
                hero_position.hero_position_2 => "Sprite/ui_public/icon_dingwei_shanghai.png",
                hero_position.hero_position_3 => "Sprite/ui_public/icon_dingwei_zhiliao.png",
                _ => "Sprite/ui_public/icon_dingwei_fangyu.png",
            };
        }

        public string GetServicesImgPath(hero_services services)
        {
            return services switch
            {
                hero_services.hero_services_tank => "Sprite/ui_public/icon_junzhong_tanke.png",
                hero_services.hero_services_missile => "Sprite/ui_public/icon_junzhong_daodanche.png",
                hero_services.hero_services_aircraft => "Sprite/ui_public/icon_junzhong_feiji.png",
                _ => "Sprite/ui_public/icon_junzhong_tanke.png",
            };
        }

        public string GetQualityImgPath(int _quality)
        {
            var t = (Config.quality)_quality;
            return t switch
            {
                Config.quality.quality_blue => "Sprite/ui_public/icon_pinzhi_SR.png",
                Config.quality.quality_purple => "Sprite/ui_public/icon_pinzhi_SSR.png",
                Config.quality.quality_orange => "Sprite/ui_public/icon_pinzhi_UR.png",
                _ => "Sprite/ui_public/icon_pinzhi_SR.png",
            };
        }

        public string GetSkillTypeName(skilltpye _type)
        {
            return _type switch
            {
                skilltpye.skilltype_1 => ToolScriptExtend.GetLang(711309),
                skilltpye.skilltype_2 => ToolScriptExtend.GetLang(711310),
                skilltpye.skilltype_3 => ToolScriptExtend.GetLang(711311),
                _ => ToolScriptExtend.GetLang(711312),
            };
        }

        public string GetSkillTypeDesc(skilltpye _type)
        {
            return _type switch
            {
                skilltpye.skilltype_1 => ToolScriptExtend.GetLang(711319),
                skilltpye.skilltype_2 => ToolScriptExtend.GetLang(711320),
                skilltpye.skilltype_3 => ToolScriptExtend.GetLang(711321),
                _ => ToolScriptExtend.GetLang(711322),
            };
        }

        public string GetSkillLabelName(skilllabel _type)
        {
            return _type switch
            {
                skilllabel.skilllabel_1 => ToolScriptExtend.GetLang(711313),
                skilllabel.skilllabel_2 => ToolScriptExtend.GetLang(711314),
                skilllabel.skilllabel_3 => ToolScriptExtend.GetLang(711316),
                skilllabel.skilllabel_4 => ToolScriptExtend.GetLang(711318),
                skilllabel.skilllabel_5 => ToolScriptExtend.GetLang(711315),
                _ => ToolScriptExtend.GetLang(711317),
            };
        }

        public string GetSkillLabelImg(skilllabel _type)
        {
            return _type switch
            {
                skilllabel.skilllabel_1 => "Sprite/ui_hero/herojineng_icon_wuli.png",
                skilllabel.skilllabel_2 => "Sprite/ui_hero/herojineng_icon_wuli.png",
                skilllabel.skilllabel_3 => "Sprite/ui_hero/herojineng_icon_wuli.png",
                skilllabel.skilllabel_4 => "Sprite/ui_hero/herojineng_icon_wuli.png",
                skilllabel.skilllabel_5 => "Sprite/ui_hero/herojineng_icon_wuli.png",
                _ => "Sprite/ui_hero/herojineng_icon_nengliang.png",
            };
        }

        public string GetSkillLabelDesc(skilllabel _type)
        {
            return _type switch
            {
                skilllabel.skilllabel_1 => ToolScriptExtend.GetLang(711323),
                skilllabel.skilllabel_2 => ToolScriptExtend.GetLang(711325),
                skilllabel.skilllabel_3 => ToolScriptExtend.GetLang(711324),
                skilllabel.skilllabel_4 => ToolScriptExtend.GetLang(711328),
                skilllabel.skilllabel_5 => ToolScriptExtend.GetLang(711326),
                _ => ToolScriptExtend.GetLang(711327),
            };
        }

        #endregion

        #region 协议处理

        public void OnHeroUpdate(Google.Protobuf.Collections.RepeatedField<Hero.Hero> heroList)
        {
            if (heroList != null)
            {
                itemid heroId;
                HeroModule heroVo;
                foreach (var item in heroList)
                {
                    heroId = (itemid)item.Id;
                    heroVo = GetHeroModule(heroId);
                    if (heroVo != null)
                    {
                        heroVo.SetData(item);
                    }
                    else
                    {
                        heroVo = new HeroModule(heroId);
                        heroModuleDic.TryAdd(heroId, heroVo);
                        heroServicesDic.TryAdd(heroVo.Services, new());

                        heroServicesDic.TryGetValue(heroVo.Services, out List<itemid> list);
                        list?.Add(heroId);

                        heroServicesDic.TryGetValue((int)hero_services.hero_services_all, out list);
                        list?.Add(heroId);
                    }
                }
            }
        }

        public void OnHeroChanges(Hero.HeroChanges heroChanges)
        {
            var current = heroChanges.Current; // 新增or修改
            if (current != null)
            {
                OnHeroUpdate(current);
            }

            var delete = heroChanges.Delete; // 删除
            if (delete != null)
            {
                foreach (var heroId in delete)
                {
                    var heroVo = GetHeroModule((itemid)heroId);
                    if (heroVo != null)
                    {
                        heroVo.ClearData();
                    }
                }
            }

            ColorLog.Pink("推送英雄变化");
            GameEntry.Event.Fire(HeroChangeEventArgs.EventId, HeroChangeEventArgs.Create());
            RedPointManager.Instance.Dirty(EnumRed.Hero.ToString());
        }

        public void OnReqHeroUpgradeLevel(itemid id, Action<Hero.HeroUpgradeLevelResp> action = null)
        {
            var req = new Hero.HeroUpgradeLevelReq
            {
                Id = (PbGameconfig.itemid)id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.HeroUpgradeLevel, req, (message) =>
            {
                var resp = (Hero.HeroUpgradeLevelResp)message;
                action?.Invoke(resp);
            });
        }

        public void OnReqHeroUpgradeStar(itemid id, int operate_type, Action<Hero.HeroUpgradeStarResp> action = null)
        {
            var req = new Hero.HeroUpgradeStarReq
            {
                Id = (PbGameconfig.itemid)id,
                OperateType = (uint)operate_type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.HeroUpgradeStar, req, (message) =>
            {
                var resp = (Hero.HeroUpgradeStarResp)message;
                action?.Invoke(resp);
            });
        }

        public void OnReqHeroUpgradeSkill(itemid id, int skill_id, Action<Hero.HeroUpgradeSkillResp> action = null)
        {
            var req = new Hero.HeroUpgradeSkillReq
            {
                Id = (PbGameconfig.itemid)id,
                SkillId = (uint)skill_id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.HeroUpgradeSkill, req, (message) =>
            {
                var resp = (Hero.HeroUpgradeSkillResp)message;
                action?.Invoke(resp);
            });
        }

        public void OnReqHeroMapShow(itemid id, int operate_type, Action<Hero.HeroMapShowResp> action = null)
        {
            var req = new Hero.HeroMapShowReq
            {
                Id = (PbGameconfig.itemid)id,
                OperateType = (uint)operate_type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.HeroMapShow, req, (message) =>
            {
                var resp = (Hero.HeroMapShowResp)message;
                action?.Invoke(resp);
            });
        }

        public void OnReqHeroUpgradeHonorLevel(itemid id, int operate_type, Action<Hero.HeroUpgradeHonorLevelResp> action = null)
        {
            var req = new Hero.HeroUpgradeHonorLevelReq
            {
                Id = (PbGameconfig.itemid)id,
                OperateType = (uint)operate_type
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.HeroUpgradeHonorLevel, req, (message) =>
            {
                var resp = (Hero.HeroUpgradeHonorLevelResp)message;
                action?.Invoke(resp);
            });
        }

        public void OnReqHeroSynthetic(itemid id, Action<Hero.HeroSyntheticResp> action = null)
        {
            var req = new Hero.HeroSyntheticReq
            {
                Id = (PbGameconfig.itemid)id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.HeroSynthetic, req, (message) =>
            {
                var resp = (Hero.HeroSyntheticResp)message;
                action?.Invoke(resp);
            });
        }

        public void OnReqHeroQueryAttr(itemid hero_id, Action<Hero.HeroQueryAttrBuildResp> action = null)
        {
            var req = new Hero.HeroQueryAttrBuildReq
            {
                HeroId = (PbGameconfig.itemid)hero_id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.HeroQueryAttrBuild, req, (message) =>
            {
                var resp = (Hero.HeroQueryAttrBuildResp)message;
                action?.Invoke(resp);
            });
        }

        #endregion
    }
}
