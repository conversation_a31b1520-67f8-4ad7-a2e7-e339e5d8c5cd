using System;
using System.Collections.Generic;
using UnityEngine;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    /// <summary>
    /// 机器人管理类，用于创建和管理游戏中的AI机器人
    /// </summary>
    public class RobotManager
    {
        /// <summary>
        /// 根据配置ID获取机器人数据
        /// </summary>
        /// <param name="robotId">机器人配置ID</param>
        /// <returns>机器人数据</returns>
        public static RobotData GetRobotById(int id)
        {
            // 从配置表获取机器人类型数据
            arena_robot_type robotConfig = Game.GameEntry.LDLTable.GetTableById<arena_robot_type>(id);
            if (robotConfig == null)
            {
                Debug.LogError($"找不到ID为{id}的机器人配置");
                return null;
            }
            
            // 创建机器人数据
            RobotData robot = new RobotData
            {
                RoleId = 0, // 使用配置ID生成唯一角色ID
                Name = ToolScriptExtend.GetLang(robotConfig.name), // 从语言表获取名称
                Level = robotConfig.level,
                Power = robotConfig.power,
                HeadPath = robotConfig.picture, // 使用配置的头像ID
                IsRobot = true,
                TeamId = robotConfig.team // 使用配置的队伍ID
            };
            
            return robot;
        }
        
        /// <summary>
        /// 获取指定排名范围内的机器人配置
        /// </summary>
        /// <param name="minRank">最小排名（包含）</param>
        /// <param name="maxRank">最大排名（包含）</param>
        /// <returns>排名范围内的机器人配置列表</returns>
        public static List<arena_novice_robot> GetRobotsByRankRange(int minRank, int maxRank)
        {
            // 获取所有排名配置
            List<arena_novice_robot> allRankConfigs = Game.GameEntry.LDLTable.GetTable<arena_novice_robot>();
            if (allRankConfigs == null || allRankConfigs.Count == 0)
            {
                Debug.LogError("找不到排名机器人配置");
                return new List<arena_novice_robot>();
            }
            
            // 筛选指定排名范围内的配置
            List<arena_novice_robot> filteredConfigs = new List<arena_novice_robot>();
            foreach (var config in allRankConfigs)
            {
                if (config.rank >= minRank && config.rank <= maxRank)
                {
                    filteredConfigs.Add(config);
                }
            }
            
            // 按排名排序
            filteredConfigs.Sort((a, b) => a.rank.CompareTo(b.rank));
            
            return filteredConfigs;
        }
        
        /// <summary>
        /// 根据排名范围创建机器人数据列表
        /// </summary>
        /// <param name="minRank">最小排名（包含）</param>
        /// <param name="maxRank">最大排名（包含）</param>
        /// <returns>排名范围内的机器人数据列表</returns>
        public static List<RobotData> CreateRobotsByRankRange(int minRank, int maxRank)
        {
            List<RobotData> robots = new List<RobotData>();
            
            // 获取指定排名范围内的机器人配置
            List<arena_novice_robot> rankConfigs = GetRobotsByRankRange(minRank, maxRank);
            
            // 创建机器人
            foreach (var config in rankConfigs)
            {
                RobotData robot = GetRobotById(config.id);
                if (robot != null)
                {
                    robots.Add(robot);
                }
            }
            
            return robots;
        }

        /// <summary>
        /// 根据排名获取机器人数据
        /// </summary>
        /// <param name="rank">排名</param>
        /// <returns>机器人数据，如果找不到则返回null</returns>
        public static RobotData GetRobotByRank(int rank)
        {
            // 获取所有排名配置
            List<arena_novice_robot> allRankConfigs = Game.GameEntry.LDLTable.GetTable<arena_novice_robot>();
            if (allRankConfigs == null || allRankConfigs.Count == 0)
            {
                Debug.LogError($"找不到排名机器人配置");
                return null;
            }
            
            // 查找指定排名的配置
            arena_novice_robot targetConfig = null;
            foreach (var config in allRankConfigs)
            {
                if (config.rank == rank)
                {
                    targetConfig = config;
                    break;
                }
            }
            
            if (targetConfig == null)
            {
                Debug.LogError($"找不到排名为{rank}的机器人配置");
                return null;
            }
            
            // 通过机器人ID获取机器人数据
            return GetRobotById(targetConfig.id);
        }
    }
    
    /// <summary>
    /// 机器人数据类
    /// </summary>
    public class RobotData
    {
        // 基本信息
        public ulong RoleId { get; set; }
        public string Name { get; set; }
        public int Level { get; set; }
        public int Power { get; set; }
        public string HeadPath { get; set; }
        public bool IsRobot { get; set; }
        public int TeamId { get; set; } // 队伍ID
    }
}



