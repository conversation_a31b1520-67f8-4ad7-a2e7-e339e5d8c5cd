using System;
using System.Collections.Generic;
using UnityEngine;
using Peak;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    /// <summary>
    /// 巅峰竞技场数据管理类
    /// </summary>
    public class PeakRankData
    {
        // private static PeakRankData _instance;
        // public static PeakRankData Instance
        // {
        //     get
        //     {
        //         if (_instance == null)
        //         {
        //             _instance = new PeakRankData();
        //         }
        //         return _instance;
        //     }
        // }

        // 个人信息
        private PeakPersonInfoResp _personInfo;
        // 排名数据
        private PeakRankResp _rankInfo;
        // 排名区间数据
        private PeakRankIntervalResp _rankIntervalInfo;
        // 滑动数据
        private List<ArenaScrollData> _competitorList = new List<ArenaScrollData>();

        public PeakPersonInfoResp PersonInfo => _personInfo;
        public PeakRankResp RankInfo => _rankInfo;
        public PeakRankIntervalResp RankIntervalInfo => _rankIntervalInfo;
        public List<ArenaScrollData> CompetitorList => _competitorList;
        public PeakReportsResp ReportInfo { get; set; }

        /// <summary>
        /// 初始化
        /// </summary>
        public void Init()
        {
            _personInfo = null;
            _rankInfo = null;
            _rankIntervalInfo = null;
            _competitorList.Clear();

            // 先检查竞技场是否开放
            ArenaManager.Instance.RequestArenaData(2, (arenaInfo) =>
            {
                if (arenaInfo != null)
                {
                    Debug.Log("[PeakRank] 巅峰竞技场开放中，开始请求初始数据");
                    // 竞技场开放，请求初始数据
                    RequestPersonInfo();
                }
                else
                {
                    Debug.Log("[PeakRank] 巅峰竞技场未开放");
                }
            });
        }

        /// <summary>
        /// 获取个人排名
        /// </summary>
        public uint GetPersonalRank()
        {
            if (_rankInfo != null)
            {
                return _rankInfo.RoleRank;
            }
            return 0;
        }

        /// <summary>
        /// 请求个人信息
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void RequestPersonInfo(Action<PeakPersonInfoResp> callback = null)
        {
            var req = new PeakPersonInfoReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.PeakPersonInfo, req, (message) =>
            {
                _personInfo = message as PeakPersonInfoResp;
                if (_personInfo != null)
                {
                    Debug.Log("[PeakRank] 获取个人信息成功");
                    // 获取排名数据
                    RequestRankInfo();
                }
                else
                {
                    Debug.LogError("[PeakRank] 获取个人信息返回为空");
                }
                callback?.Invoke(_personInfo);
            });
        }

        /// <summary>
        /// 请求排名数据
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void RequestRankInfo(Action<PeakRankResp> callback = null)
        {
            var req = new PeakRankReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.PeakRank, req, (message) =>
            {
                _rankInfo = message as PeakRankResp;
                if (_rankInfo != null)
                {
                    Debug.Log("[PeakRank] 获取排名数据成功");
                    // 处理排名数据
                    ProcessRankData();
                }
                else
                {
                    Debug.LogError("[PeakRank] 获取排名数据返回为空");
                }
                callback?.Invoke(_rankInfo);
            });
        }

        /// <summary>
        /// 请求排名区间数据
        /// </summary>
        /// <param name="rankMin">最小排名</param>
        /// <param name="rankMax">最大排名</param>
        /// <param name="callback">回调函数</param>
        // public void RequestRankIntervalInfo(Action<PeakRankIntervalResp> callback = null)
        // {
        //     var req = new PeakRankIntervalReq
        //     {
        //         RankMin = 1,
        //         RankMax = 800
        //     };

        //     GameEntry.LDLNet.Send(Protocol.MessageID.PeakRankInterval, req, (message) =>
        //     {
        //         _rankIntervalInfo = message as PeakRankIntervalResp;
        //         if (_rankIntervalInfo != null)
        //         {
        //             Debug.Log("[PeakRank] 获取排名区间数据成功");
        //             // 处理排名区间数据
        //             ProcessRankIntervalData();
        //             // 触发UI刷新事件
        //         }
        //         else
        //         {
        //             Debug.LogError("[PeakRank] 获取排名区间数据返回为空");
        //         }
        //         callback?.Invoke(_rankIntervalInfo);
        //     });
        // }

        /// <summary>
        /// 发起挑战
        /// </summary>
        /// <param name="rank">目标排名</param>
        /// <param name="roleId">目标角色ID</param>
        /// <param name="callback">回调函数</param>
        public void Challenge(uint rank, ulong roleId, Action<PeakChallengeResp> callback = null)
        {
            // 检查是否还有挑战次数
            int remainingChallenges = GetRemainingChallenges();
            if (remainingChallenges <= 0)
            {
                //Debug.LogWarning("[PeakRank] 挑战次数已用完");
                // 显示提示信息
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(42017) // "挑战次数已用完"
                });

                // 调用回调，传入null表示挑战失败
                //callback?.Invoke(null);
                return;
            }

            var req = new PeakChallengeReq
            {
                Rank = rank,
                RoleId = roleId
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.PeakChallenge, req, (message) =>
            {
                var resp = message as PeakChallengeResp;
                if (resp != null)
                {
                    Debug.Log("[PeakRank] 挑战请求成功");
                    // 更新个人信息
                    RequestPersonInfo();
                }
                else
                {
                    Debug.LogError("[PeakRank] 挑战请求返回为空");
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 处理排名数据
        /// </summary>
        private void ProcessRankData()
        {
            if (_rankInfo == null || _rankInfo.RankList == null)
                return;

            _competitorList.Clear();

            // 处理排名列表数据
            foreach (var rankData in _rankInfo.RankList)
            {
                ArenaScrollData competitor = new ArenaScrollData();
                competitor.Initialize((int)rankData.Rank, rankData.RoleId, false, (int)rankData.ServerId);
                _competitorList.Add(competitor);
            }

            // 获取排名分组配置表
            List<arena_compete_rank_group> rankGroups = GameEntry.LDLTable.GetTable<arena_compete_rank_group>();
            if (rankGroups != null && rankGroups.Count > 0 && _rankInfo.RankList.Count > 0)
            {
                // 按照id排序
                //rankGroups.Sort((a, b) => a.id.CompareTo(b.id));

                // 循环处理每个分组
                foreach (var group in rankGroups)
                {
                    // 只处理id > 3的分组（前三名单独处理）
                    if (group.rank_min > 3 && group.rank_min <= _rankInfo.RankList.Count)
                    {
                        // 创建分组标题数据
                        ArenaScrollData groupHeader = new ArenaScrollData();
                        // 使用负数id表示这是一个分组标题
                        groupHeader.Initialize(-group.rank_min, 0, false);
                        //groupHeader.Name = ToolScriptExtend.GetLang(group.name_lang);

                        // 查找插入位置
                        int insertIndex = _competitorList.FindIndex(c => c.Rank >= group.rank_min);
                        if (insertIndex >= 0)
                        {
                            _competitorList.Insert(insertIndex, groupHeader);
                        }
                        else
                        {
                            // 如果没找到合适的位置，添加到末尾
                            //_competitorList.Add(groupHeader);
                        }
                    }
                }
            }
            GameEntry.Event.Fire(PeakRankRefreshEventArgs.EventId, PeakRankRefreshEventArgs.Create());
            // 处理前三名数据
            if (_rankInfo.TopThree != null && _rankInfo.TopThree.Count > 0)
            {
                // 可以单独处理前三名数据
                // ...
            }
        }
        
        /// <summary>
        /// 处理排名区间数据
        /// </summary>
        // private void ProcessRankIntervalData()
        // {
        //     if (_rankIntervalInfo == null || _rankIntervalInfo.RankList == null)
        //         return;

        //     // 处理排名区间数据
        //     foreach (var rankData in _rankIntervalInfo.RankList)
        //     {
        //         // 查找是否已存在
        //         int existingIndex = _competitorList.FindIndex(c => c.RoleId == rankData.RoleId);
        //         if (existingIndex >= 0)
        //         {
        //             // 更新已存在的数据
        //             _competitorList[existingIndex].Rank = (int)rankData.Rank;
        //         }
        //         else
        //         {
        //             // 添加新数据
        //             ArenaScrollData competitor = new ArenaScrollData();
        //             competitor.Initialize((int)rankData.Rank, (uint)rankData.RoleId, false);
        //             _competitorList.Add(competitor);
        //         }
        //     }

        //     // 按排名排序
        //     _competitorList.Sort((a, b) => a.Rank.CompareTo(b.Rank));
        // }

        /// <summary>
        /// 获取排名奖励配置列表
        /// </summary>
        /// <returns>排名奖励配置列表</returns>
        // public List<peak_rank_reward> GetRankRewards()
        // {
        //     // 获取排名奖励配置表
        //     List<peak_rank_reward> rankRewards = GameEntry.LDLTable.GetTable<peak_rank_reward>();
        //     if (rankRewards == null || rankRewards.Count == 0)
        //     {
        //         Debug.LogError("[PeakRank] 获取排名奖励配置表失败");
        //         return new List<peak_rank_reward>();
        //     }

        //     // 按照排名范围从小到大排序
        //     rankRewards.Sort((a, b) => a.rank_min.CompareTo(b.rank_min));

        //     return rankRewards;
        // }

        /// <summary>
        /// 获取挑战次数
        /// </summary>
        public int GetRemainingChallenges()
        {
            if (_personInfo != null)
            {
                // 计算剩余挑战次数
                int maxChallenges = GetMaxChallenges();
                return maxChallenges - (int)_personInfo.UsedFightTimes;
            }
            return 0;
        }

        /// <summary>
        /// 获取最大挑战次数
        /// </summary>
        public int GetMaxChallenges()
        {
            // 从配置表获取基础挑战次数
            int baseChallenges = int.Parse(ArenaManager.Instance.GetArenaSetting(2002)[0]); ; // 默认值，实际应从配置表获取

            if (_personInfo != null)
            {
                // 加上已购买的挑战次数
                return baseChallenges + (int)_personInfo.BuyFightTimes;
            }
            return baseChallenges;
        }

        /// <summary>
        /// 获取前三名竞争者数据
        /// </summary>
        /// <returns>前三名竞争者数据列表</returns>
        public List<ArenaScrollData> GetTopThreeCompetitors()
        {
            List<ArenaScrollData> topThree = new List<ArenaScrollData>();

            // 确保列表已排序且有足够的数据
            if (_competitorList != null && _competitorList.Count > 0)
            {
                // 获取前三名
                int count = 3;
                for (int i = 0; i < count; i++)
                {
                    topThree.Add(_competitorList[i]);
                }
            }

            return topThree;
        }

        public List<ArenaScrollData> GetPeakRankScrollData()
        {
            List<ArenaScrollData> data = new List<ArenaScrollData>();

            // 确保列表已排序且有足够的数据
            if (_competitorList != null && _competitorList.Count > 0)
            {
                for (int i = 0; i < _competitorList.Count; i++)
                {
                    data.Add(_competitorList[i]);
                }
            }

            return data;
        }

        /// <summary>
        /// 判断指定索引的项是否为标签页（分组标题）
        /// </summary>
        /// <param name="index">要检查的索引</param>
        /// <returns>如果是标签页返回true，否则返回false</returns>
        /// // 从第四个开始，前三不加入滑动列表
        public bool IsTabLabel(int index)
        {
            // 检查索引是否有效
            if (_competitorList != null && index >= 0 && index + 3 <= _competitorList.Count)
            {
                // 负数Rank表示这是一个分组标题/标签页
                return _competitorList[index + 3].Rank < 0;
            }

            return false;
        }

        /// <summary>
        /// 获取排名分组配置
        /// </summary>
        /// <param name="groupId">分组ID</param>
        /// <returns>排名分组配置</returns>
        public arena_compete_rank_group GetRankGroupConfig(int rankMin)
        {
            // 获取排名分组配置表
            List<arena_compete_rank_group> rankGroups = GameEntry.LDLTable.GetTable<arena_compete_rank_group>();
            if (rankGroups != null && rankGroups.Count > 0)
            {
                // 查找对应的分组配置
                return rankGroups.Find(g => g.rank_min == rankMin);
            }

            return null;
        }
        /// <summary>
        /// 获取战报
        public void RequestReportInfo(Action<PeakReportsResp> callback = null)
        {
            var req = new PeakReportsReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.PeakReports, req, (message) =>
            {
                var resp = message as PeakReportsResp;
                if (resp != null && resp.ReportList.Count > 0)
                {
                    // 更新本地缓存的挑战数据
                    if (resp.ReportList != null && resp.ReportList.Count > 0)
                    {
                        ReportInfo = resp;
                    }

                    //GameEntry.Event.Fire(NoviceCompRefreshEventArgs.EventId, NoviceCompRefreshEventArgs.Create());
                }
                else
                {
                    Debug.LogWarning("[NoviceTraining] 刷新对手返回数据为空");
                }
                callback?.Invoke(ReportInfo);
            });
        }
        /// <summary>
        /// 判断是否可以挑战指定排名的对手
        /// </summary>
        /// <param name="targetRank">目标排名</param>
        /// <returns>如果可以挑战返回true，否则返回false</returns>
        public bool CanChallenge(int targetRank)
        {
            // 检查个人信息是否存在
            if (_personInfo == null || _rankInfo == null)
            {
                Debug.LogError("[PeakRank] 个人信息或排名信息为空，无法判断是否可挑战");
                return false;
            }

            // 获取自己的排名
            int myRank = (int)_rankInfo.RoleRank;

            // 不能挑战自己
            if (myRank == targetRank)
            {
                return false;
            }

            // 获取排名分组配置表
            List<arena_compete_rank_group> rankGroups = GameEntry.LDLTable.GetTable<arena_compete_rank_group>();
            if (rankGroups == null || rankGroups.Count == 0)
            {
                Debug.LogError("[PeakRank] 获取排名分组配置表失败");
                return false;
            }

            // 查找自己所在的分组
            arena_compete_rank_group myGroup = null;
            foreach (var group in rankGroups)
            {
                if (myRank >= group.rank_min && myRank <= group.rank_max)
                {
                    myGroup = group;
                    break;
                }
            }

            if (myGroup == null)
            {
                Debug.LogError($"[PeakRank] 找不到排名 {myRank} 所在的分组");
                return false;
            }

            // 检查目标排名是否在允许挑战的范围内
            // 1. 检查是否在同一分组内
            if (targetRank >= myGroup.challenge_rank_min && targetRank <= myGroup.challenge_rank_max)
            {
                return true;
            }
            return false;
        }
    }
    
}
