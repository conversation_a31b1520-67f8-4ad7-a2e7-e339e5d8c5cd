using System;
using System.Collections.Generic;
using UnityEngine;
using GameFramework;
using Novice;
using Game.Hotfix.Config;
using Roledata;


namespace Game.Hotfix
{
    /// <summary>
    /// 新兵训练营数据管理类
    /// </summary>
    public class NoviceTrainingData
    {
        /// <summary>
        /// 个人信息
        /// </summary>
        public NovicePersonInfoResp PersonInfo { get; set; }

        /// <summary>
        /// 挑战数据
        /// </summary>
        public NoviceCompInfoResp CompInfo { get; set; }
        //挑战记录
        public NoviceReportsResp ReportInfo { get; set; }
        /// <summary>
        /// 排行榜数据
        /// </summary>
        public NoviceRankResp RankInfo { get; private set; }
        public NoviceDrawAchieveResp AchieveInfo { get; private set; }
        public List<ArenaScrollData> newSoldierData = new List<ArenaScrollData>();
        public List<int> achieveList = new List<int>();

        /// <summary>
        /// 初始化
        /// </summary>
        public void Init()
        {
            // 初始化数据
            PersonInfo = null;
            CompInfo = null;
            RankInfo = null;

            // 先检查竞技场是否开放
            ArenaManager.Instance.RequestArenaData(1, (arenaInfo) =>
            {
                if (arenaInfo != null)
                {
                    Debug.Log("[PeakRank] 新兵训练营竞技场开放中，开始请求初始数据");
                    // 竞技场开放，请求初始数据
                    InitSoliderData();
                }
                else
                {
                    Debug.Log("[PeakRank] 新兵训练营竞技场未开放");
                }
            });
            //RequestSoliderData();

        }
        public int GetMyselfRank()
        {
            if (RankInfo != null)
            {
                return (int)RankInfo.RoleRank;
            }
            return 0;
        }
        private void InitSoliderData()
        {
            var arena_novice_robot = Game.GameEntry.LDLTable.GetTable<arena_novice_robot>();
            // 按照id从小到大排序
            //arena_novice_robot.Sort((a, b) => a.rank.CompareTo(b.id));

            // 初始化newSoldierData列表
            //newSoldierData = new List<ArenaScrollData>();

            // 遍历机器人数据并添加到newSoldierData
            foreach (var item in arena_novice_robot)
            {
                // 创建新的ArenaScrollData对象
                ArenaScrollData soldierData = new ArenaScrollData();

                // 设置机器人数据
                soldierData.Initialize(
                    item.rank,                          // 排名
                    (uint)item.id,        // 机器人角色ID (使用10000000+id作为唯一标识)
                    true,                             // 是机器人
                    0,
                    null                              // 不需要回调
                );

                // 添加到列表
                newSoldierData.Add(soldierData);
            }
            newSoldierData.Sort((a, b) => a.Rank.CompareTo(b.Rank));
            // 可以在这里添加日志，确认数据已正确加载
            RequestSoliderData();
            Debug.Log($"[NoviceTraining] 初始化新兵训练营数据，共加载 {newSoldierData.Count} 个机器人");
        }
        private void RequestSoliderData()
        {
            // 请求更大范围的排行榜数据
            RequestRankInfo(1, 100, (message) =>
            {

            });
        }

        #region 协议请求


        /// <summary>
        /// 请求个人数据
        /// </summary>
        public void RequestPersonInfo(Action<NovicePersonInfoResp> callback = null, bool needSend = false)
        {
            // 如果已有数据，直接返回
            if (PersonInfo != null && needSend == false)
            {
                Debug.Log("[NoviceTraining] 使用缓存的个人数据");
                callback?.Invoke(PersonInfo);
                return;
            }

            var req = new NovicePersonInfoReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.NovicePersonInfo, req, (message) =>
            {
                PersonInfo = message as NovicePersonInfoResp;
                if (PersonInfo != null)
                {
                    //Debug.LogError("[NoviceTraining] 获取个人数据成功");
                }
                else
                {
                    //Debug.LogError("[NoviceTraining] 获取个人数据返回为空");
                }
                callback?.Invoke(PersonInfo);
            });
        }

        /// <summary>
        /// 请求挑战数据
        /// </summary>
        public void RequestCompInfo(Action<NoviceCompInfoResp> callback = null)
        {
            // 如果已有数据，直接返回
            if (CompInfo != null)
            {
                Debug.Log("[NoviceTraining] 使用缓存的挑战数据");
                callback?.Invoke(CompInfo);
                return;
            }

            var req = new NoviceCompInfoReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.NoviceCompInfo, req, (message) =>
            {
                CompInfo = message as NoviceCompInfoResp;
                if (CompInfo != null && CompInfo.CompList.Count > 0)
                {
                    Debug.LogError("[NoviceTraining] 获取挑战数据成功");
                }
                else
                {
                    Debug.LogError("[NoviceTraining] 获取挑战数据返回为空");
                }
                callback?.Invoke(CompInfo);
            });
        }

        /// <summary>
        /// 请求排行榜数据
        /// </summary>
        /// <param name="rankMin">最小排名（包含）</param>
        /// <param name="rankMax">最大排名（包含）</param>
        /// <param name="callback">回调函数，参数为排行榜数据列表</param>
        public void RequestRankInfo(uint rankMin = 1, uint rankMax = 100, Action<List<NoviceRank>> callback = null)
        {
            var req = new NoviceRankReq
            {
                RankMin = rankMin,
                RankMax = rankMax
            };
            GameEntry.LDLNet.Send(Protocol.MessageID.NoviceRank, req, (message) =>
            {
                RankInfo = message as NoviceRankResp;
                List<NoviceRank> rankList = new List<NoviceRank>();

                if (RankInfo != null && RankInfo.RankList != null)
                {
                    //Debug.LogError("[NoviceTraining] 获取排行榜数据成功");
                    rankList.AddRange(RankInfo.RankList);
                    foreach (var item in RankInfo.RankList)
                    {
                        ChangeRobotData(item);
                    }
                    GameEntry.Event.Fire(NoviceCompRefreshEventArgs.EventId, NoviceCompRefreshEventArgs.Create());
                }
                else
                {
                    Debug.LogError("[NoviceTraining] 获取排行榜数据返回为空");
                }
                callback?.Invoke(rankList);
            });
        }
        public void ChangeRobotData(NoviceRank data)
        {
            //转成ArenaScrollData
            if (data.Rank > 0)
            {
                ArenaScrollData scrollData = new ArenaScrollData();
                scrollData.Initialize((int)data.Rank, (uint)data.RoleId, false);
                if (newSoldierData[scrollData.Rank] != null)
                {
                    newSoldierData[scrollData.Rank] = scrollData;
                }
            }
        }
        /// <summary>
        /// 发起挑战
        /// </summary>
        public void Challenge(int rank, int roleId, Action<NoviceChallengeResp> callback = null)
        {
            var req = new NoviceChallengeReq
            {
                Rank = (uint)rank,
                RoleId = (ulong)roleId, // 需要设置正确的RoleId值
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.NoviceChallenge, req, (message) =>
            {
                var resp = message as NoviceChallengeResp;
                if (resp == null)
                {
                    Debug.LogError("[NoviceTraining] 发起挑战返回数据为空");
                    return;
                }
                if (PersonInfo != null)
                {
                    PersonInfo.UsedFightTimes = resp.UsedFightTimes;
                }
                if (resp.IsWin == true && CompInfo != null && CompInfo.CompList.Count > 0)
                {
                    CompInfo.CompList.Clear();
                    CompInfo.CompList.AddRange(resp.CompList);
                    GameEntry.UI.CloseUIForm(EnumUIForm.UIArenaChallengeForm);
                    RequestPersonInfo(null, true);
                    RequestSoliderData();
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = "挑战成功"
                    });
                }
                else
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = "挑战失败"
                    });
                    GameEntry.UI.CloseUIForm(EnumUIForm.UIArenaChallengeForm);
                    GameEntry.Event.Fire(NoviceCompRefreshEventArgs.EventId, NoviceCompRefreshEventArgs.Create());
                }
                //Debug.LogError("[NoviceTraining] 发起挑战结果: " + (resp.Success ? "成功" : "失败"));
                callback?.Invoke(resp);
            });
        }


        /// <summary>
        /// 购买挑战次数
        /// </summary>
        public void BuyChallengeTimes(int buyTimes, Action<NoviceBuyChallengeTimesResp> callback = null)
        {
            var req = new NoviceBuyChallengeTimesReq();
            req.Times = (uint)buyTimes;
            GameEntry.LDLNet.Send(Protocol.MessageID.NoviceBuyChallengeTimes, req, (message) =>
            {
                var resp = message as NoviceBuyChallengeTimesResp;
                if (resp != null)
                {
                    //Debug.LogError("[NoviceTraining] 购买挑战次数结果: ");
                    if (PersonInfo != null)
                    {
                        PersonInfo.BuyFightTimes = resp.BuyFightTimes;
                    }

                    GameEntry.Event.Fire(NoviceCompRefreshEventArgs.EventId, NoviceCompRefreshEventArgs.Create());
                }
                else
                {
                    Debug.LogError("[NoviceTraining] 为31.k购买挑战次数返回数据为空");
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 领取排名成就
        /// </summary>
        public void DrawAchievement(uint achieveId, Action<NoviceDrawAchieveResp> callback = null)
        {
            var req = new NoviceDrawAchieveReq
            {
                Id = achieveId
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.NoviceDrawAchieve, req, (message) =>
            {
                var resp = message as NoviceDrawAchieveResp;
                if (resp != null)
                {
                    AchieveInfo = resp;
                    List<reward> rewards = new();
                    foreach (var item in resp.Rewards)
                    {
                        rewards.Add(new reward()
                        {
                            item_id = (itemid)item.Code,
                            num = item.Amount
                        });
                    }
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);
                    RequestPersonInfo((personInfo) =>
                    {
                        if (personInfo != null)
                        {
                            PersonInfo = personInfo;
                            callback?.Invoke(resp);
                        }
                    }, true);
                    Debug.LogError("[NoviceTraining] 领取排名成就结果: ");
                }
                else
                {
                    Debug.LogError("[NoviceTraining] 领取排名成就返回数据为空");
                }

            });
        }
        /// <summary>
        /// 判断是否显示成就领取按钮
        /// </summary>
        /// <param name="achieveId">成就配置ID</param>
        /// <returns>是否显示领取按钮</returns>
        public bool IsShowAchievementBtn(uint achieveId)
        {
            // 检查个人信息是否存在
            if (PersonInfo == null || PersonInfo.DrawAchieveList == null)
            {
                return false;
            }

            // 检查是否已领取过该成就
            if (PersonInfo.DrawAchieveList.Contains(achieveId))
            {
                return false; // 已领取过，不显示按钮
            }
            // // 检查是否已领取过该成就
            // if (AchieveInfo != null && AchieveInfo.DrawAchieveList.Contains(achieveId))
            // {
            //     return false; // 已领取过，不显示按钮
            // }

            // 获取成就配置
            var achieveRewards = GameEntry.LDLTable.GetTable<arena_novice_achieve_reward>();
            var achievement = achieveRewards.Find(a => a.id == achieveId);
            if (achievement == null)
            {
                return false; // 找不到对应成就配置
            }

            // 检查排名是否达到要求
            uint currentRank = (uint)GetCurrentRank();
            if (currentRank == 0 || currentRank > achievement.rank)
            {
                return false; // 排名未达到要求
            }

            // 检查AchieveInfo中是否包含该成就
            if (AchieveInfo != null && AchieveInfo.DrawAchieveList != null)
            {
                if (AchieveInfo.DrawAchieveList.Contains(achieveId))
                {
                    return false; // 已在本次会话中领取过
                }
            }

            return true; // 可以显示领取按钮
        }
        /// <summary>
        /// 刷新对手
        /// </summary>
        public void RefreshComp(Action<NoviceRefreshCompResp> callback = null)
        {
            var req = new NoviceRefreshCompReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.NoviceRefreshComp, req, (message) =>
            {
                var resp = message as NoviceRefreshCompResp;
                if (resp != null && CompInfo.CompList.Count > 0)
                {
                    // 更新本地缓存的挑战数据
                    if (CompInfo != null && resp.CompList != null && resp.CompList.Count > 0)
                    {
                        CompInfo.CompList.Clear();
                        CompInfo.CompList.AddRange(resp.CompList);
                        Debug.Log($"[NoviceTraining] 更新本地挑战对手数据，共{resp.CompList.Count}个对手");
                    }

                    if (PersonInfo != null)
                    {
                        PersonInfo.RefreshCompTimes = resp.RefreshCompTimes;
                    }
                    GameEntry.Event.Fire(NoviceCompRefreshEventArgs.EventId, NoviceCompRefreshEventArgs.Create());
                }
                else
                {
                    Debug.LogWarning("[NoviceTraining] 刷新对手返回数据为空");
                }
                callback?.Invoke(resp);
            });
        }

        //新兵训练营战报
        public void RefreshRepotr(Action<NoviceReportsResp> callback = null)
        {
            var req = new NoviceReportsReq();
            GameEntry.LDLNet.Send(Protocol.MessageID.NoviceReports, req, (message) =>
            {
                var resp = message as NoviceReportsResp;
                if (resp != null && resp.ReportList.Count > 0)
                {
                    // 更新本地缓存的挑战数据
                    //if (ReportInfo != null && resp.ReportList != null && resp.ReportList.Count > 0)
                    //{
                    ReportInfo = resp;
                    Debug.Log($"[NoviceTraining] 更新本地挑战对手数据，共{resp.ReportList.Count}个对手");
                    //}

                    //GameEntry.Event.Fire(NoviceCompRefreshEventArgs.EventId, NoviceCompRefreshEventArgs.Create());
                }
                else
                {
                    Debug.LogWarning("[NoviceTraining] 刷新对手返回数据为空");
                }
                if (ReportInfo == null)
                {
                    ReportInfo = resp;
                }
                callback?.Invoke(ReportInfo);
            });
        }

        #endregion

        /// <summary>
        /// 获取剩余挑战次数
        /// </summary>
        public int GetRemainingChallengeTimes()
        {
            // 根据Novice.cs中的字段，应该使用UsedFightTimes
            return PersonInfo != null ? (int)(PersonInfo.UsedFightTimes) : 0;
        }

        /// <summary>
        /// 获取当前排名
        /// </summary>
        public int GetCurrentRank()
        {
            // 根据Novice.cs中的字段，应该使用RankTop
            return PersonInfo != null ? (int)PersonInfo.RankTop : 0;
        }

        /// <summary>
        /// 获取已购买次数
        /// </summary>
        public int GetBoughtTimes()
        {
            // 根据Novice.cs中的字段，应该使用BuyFightTimes
            return PersonInfo != null ? (int)PersonInfo.BuyFightTimes : 0;
        }

        /// <summary>
        /// 获取最大购买次数
        /// </summary>
        public int GetMaxBuyTimes()
        {
            // 这个字段在Novice.cs中不存在，可能需要从其他地方获取或使用默认值
            return 5; // 默认值，根据实际情况调整
        }

        /// <summary>
        /// 获取下次购买价格
        /// </summary>
        public int GetNextBuyPrice()
        {
            // 这个字段在Novice.cs中不存在，可能需要从配置表获取
            return 100; // 默认值，根据实际情况调整
        }

        /// <summary>
        /// 获取新兵训练营机器人数据列表
        /// </summary>
        /// <returns>机器人数据列表</returns>
        public List<ArenaScrollData> GetNewSoldierData()
        {
            // 如果列表为空，先初始化数据
            if (newSoldierData == null || newSoldierData.Count == 0)
            {
                InitSoliderData();
            }

            // 只返回前100名
            int count = Math.Min(100, newSoldierData.Count);
            return newSoldierData.GetRange(0, count);
        }

        /// <summary>
        /// 获取排名奖励配置列表
        /// </summary>
        /// <returns>排名奖励配置列表</returns>
        public List<arena_novice_rank_reward> GetRankRewards()
        {
            // 获取排名奖励配置表
            List<arena_novice_rank_reward> rankRewards = GameEntry.LDLTable.GetTable<arena_novice_rank_reward>();
            if (rankRewards == null || rankRewards.Count == 0)
            {
                Debug.LogError("[NoviceTraining] 获取排名奖励配置表失败");
                return new List<arena_novice_rank_reward>();
            }

            // 按照排名范围从小到大排序
            rankRewards.Sort((a, b) => a.rank_min.CompareTo(b.rank_min));

            // // 标记已领取的奖励
            // if (RankInfo != null && RankInfo.RankRewards != null)
            // {
            //     foreach (var reward in rankRewards)
            //     {
            //         // 检查是否已领取
            //         reward.is_claimed = RankInfo.RankRewards.Contains((uint)reward.rank_min);
            //     }
            // }

            return rankRewards;
        }


        public Common.RewardStatus GetStatus(int achieveId)
        {
            // 检查个人信息是否存在
            if (PersonInfo == null || PersonInfo.DrawAchieveList == null)
            {
                return Common.RewardStatus.Unfinished;
            }

            // 检查是否已领取过该成就
            if (PersonInfo.DrawAchieveList.Contains((uint)achieveId))
            {
                return Common.RewardStatus.Received; ; // 已领取过，不显示按钮
            }

            // 获取成就配置
            var achieveRewards = GameEntry.LDLTable.GetTable<arena_novice_achieve_reward>();
            var achievement = achieveRewards.Find(a => a.id == achieveId);
            if (achievement == null)
            {
                return Common.RewardStatus.Unfinished; // 找不到对应成就配置
            }

            // 检查排名是否达到要求
            uint currentRank = GetPersonalRank();
            if (currentRank == 0 || currentRank > achievement.rank)
            {
                return Common.RewardStatus.Receivable; // 排名未达到要求
            }

            return Common.RewardStatus.Receivable;
        }
        /// <summary>
        /// 获取成就奖励配置列表
        /// </summary>
        /// <returns>成就奖励配置列表</returns>
        public List<arena_novice_achieve_reward> GetAchievementRewards()
        {
            // 获取成就奖励配置表
            List<arena_novice_achieve_reward> achieveRewards = GameEntry.LDLTable.GetTable<arena_novice_achieve_reward>();
            if (achieveRewards == null || achieveRewards.Count == 0)
            {
                Debug.LogError("[NoviceTraining] 获取成就奖励配置表失败");
                return new List<arena_novice_achieve_reward>();
            }

            // 按照排名从小到大排序
            achieveRewards.Sort((a, b) => b.rank.CompareTo(a.rank));

            if (achieveRewards.Count > 1)
            {
                achieveRewards.Sort((a, b) =>
                {
                    int aWeight = a.rank;
                    int bWeight = b.rank;
                    // int aStatus = GetStatus(a.id) == Common.RewardStatus.Receivable ? 1000000 : 0;
                    // int bStatus = GetStatus(b.id) == Common.RewardStatus.Receivable ? 1000000 : 0;

                    int aRewarded = GetStatus(a.id) == Common.RewardStatus.Received ? -1000000 : 0;
                    int bRewarded = GetStatus(b.id) == Common.RewardStatus.Received ? -1000000 : 0;

                    return (bWeight + bRewarded).CompareTo(aWeight + aRewarded);
                }
                );
            }


            // // 标记已领取的奖励
            // if (RankInfo != null && RankInfo.AchieveRewards != null)
            // {
            //     foreach (var reward in achieveRewards)
            //     {
            //         // 检查是否已领取
            //         reward.is_claimed = RankInfo.AchieveRewards.Contains((uint)reward.id);
            //     }
            // }

            return achieveRewards;
        }

        /// <summary>
        /// 获取个人排名
        /// </summary>
        /// <returns>个人排名，未上榜返回0</returns>
        public uint GetPersonalRank()
        {
            if (RankInfo != null)
            {
                return RankInfo.RoleRank;
            }
            return 0;
        }

        /// <summary>
        /// 领取排名奖励
        /// </summary>
        /// <param name="rankMin">奖励对应的最小排名</param>
        /// <param name="callback">回调函数，参数为是否成功和奖励列表</param>
        // public void ClaimRankReward(uint rankMin, Action<bool, List<reward>> callback = null)
        // {
        //     var req = new NoviceDrawRankRewardReq
        //     {
        //         RankMin = rankMin
        //     };

        //     GameEntry.LDLNet.Send(Protocol.MessageID.NoviceDrawRankReward, req, (message) =>
        //     {
        //         var resp = message as NoviceDrawRankRewardResp;
        //         bool success = false;
        //         List<reward> rewards = new List<reward>();

        //         if (resp != null)
        //         {
        //             success = true;

        //             // 更新已领取奖励列表
        //             if (RankInfo != null && !RankInfo.RankRewards.Contains(rankMin))
        //             {
        //                 RankInfo.RankRewards.Add(rankMin);
        //             }

        //             // 解析奖励
        //             if (resp.Rewards != null && resp.Rewards.Count > 0)
        //             {
        //                 foreach (var item in resp.Rewards)
        //                 {
        //                     rewards.Add(new reward
        //                     {
        //                         item_id = (itemid)item.Code,
        //                         num = item.Amount
        //                     });
        //                 }
        //             }

        //             Debug.Log($"[NoviceTraining] 领取排名奖励成功，排名：{rankMin}");
        //         }
        //         else
        //         {
        //             Debug.LogError($"[NoviceTraining] 领取排名奖励失败，排名：{rankMin}");
        //         }

        //         callback?.Invoke(success, rewards);
        //     });
        // }

        /// <summary>
        /// 领取成就奖励
        /// </summary>
        /// <param name="achieveId">成就ID</param>
        /// <param name="callback">回调函数，参数为是否成功和奖励列表</param>
        // public void ClaimAchievementReward(uint achieveId, Action<bool, List<reward>> callback = null)
        // {
        //     DrawAchievement(achieveId, (resp) =>
        //     {
        //         bool success = false;
        //         List<reward> rewards = new List<reward>();

        //         if (resp != null)
        //         {
        //             success = true;

        //             // 更新已领取奖励列表
        //             if (RankInfo != null && !RankInfo.AchieveRewards.Contains(achieveId))
        //             {
        //                 RankInfo.AchieveRewards.Add(achieveId);
        //             }

        //             // 解析奖励
        //             if (resp.Rewards != null && resp.Rewards.Count > 0)
        //             {
        //                 foreach (var item in resp.Rewards)
        //                 {
        //                     rewards.Add(new reward
        //                     {
        //                         item_id = (itemid)item.Code,
        //                         num = item.Amount
        //                     });
        //                 }
        //             }

        //             Debug.Log($"[NoviceTraining] 领取成就奖励成功，成就ID：{achieveId}");
        //         }
        //         else
        //         {
        //             Debug.LogError($"[NoviceTraining] 领取成就奖励失败，成就ID：{achieveId}");
        //         }

        //         callback?.Invoke(success, rewards);
        //     });
        // }
        
        // <summary>
        /// 获取最大挑战次数
        /// </summary>
        public int GetMaxChallenges()
        {
            // 从配置表获取基础挑战次数
            int baseChallenges = int.Parse(ArenaManager.Instance.GetArenaSetting(1002)[0]); // 默认值，实际应从配置表获取
            if (PersonInfo != null)
            {
                // 加上已购买的挑战次数
                return baseChallenges + (int)PersonInfo.BuyFightTimes;
            }
            return baseChallenges;
        }
    }
    /// <summary>
    /// 竞技场通用滑动数据
    public class ArenaScrollData
    {
        public int Rank { get; set; }          // 排名
        public ulong RoleId { get; set; }       // 角色ID
        public string Name { get; set; }        // 名称
        public int Level { get; set; }          // 等级
        public ulong Power { get; set; }        // 战力
        public string HeadPath { get; set; }    // 头像路径
        public bool IsRobot { get; set; }       // 是否是机器人
        public bool IsCurrentPlayer { get; set; } // 是否是当前玩家
        public string UnionName { get; set; }   // 公会名称
        public bool isTitle { get; set; } //是否是标题
        public int server_id { get; set; } //服务器id
        public void Initialize(int rank, ulong roleId, bool isRobot, int serverId = 0, Action<ArenaScrollData> callback = null)
        {
            if (rank < 0)
            {
                isTitle = true;
            }
            else
            {
                isTitle = false;
            }
            if (isTitle)
            {
                Rank = rank;
                return;
            }
            Rank = rank;
            RoleId = roleId;
            IsRobot = isRobot;
            IsCurrentPlayer = roleId == GameEntry.RoleData.RoleID;
            if (serverId > 0)
            {
                server_id = serverId;
            }
            if (isRobot)
            {
                // 如果是机器人，通过RoleId获取arena_novice_robot配置
                var robotId = (int)roleId;
                RobotData robotData = RobotManager.GetRobotById((int)robotId);

                if (robotData != null)
                {
                    // 使用机器人数据
                    Name = robotData.Name;
                    Level = robotData.Level;
                    Power = (ulong)robotData.Power;
                    HeadPath = robotData.HeadPath;

                    // 回调
                    callback?.Invoke(this);
                }
                else
                {
                    // 如果获取失败，使用默认数据
                    Name = "机器人 " + rank;
                    Level = 1;
                    Power = 1000;
                    HeadPath = "";

                    // 回调
                    callback?.Invoke(this);
                }
            }
            else
            {
                // 如果是当前玩家
                if (IsCurrentPlayer)
                {
                    // 使用当前玩家数据
                    Name = GameEntry.RoleData.Name;
                    Level = 1; // 假设等级为10，根据实际情况调整
                    Power = GameEntry.RoleData.Power;
                    HeadPath = GameEntry.RoleData.HeadSystemAvatar.ToString();

                    // 回调
                    callback?.Invoke(this);
                }
                else
                {
                    // 检查是否是跨服玩家
                    bool isCrossServer = server_id != GameEntry.RoleData.ServerId; // 假设跨服ID大于10000000000，根据实际情况调整

                    if (isCrossServer)
                    {
                        // 如果是跨服玩家，使用RequestRoleQueryMulti获取数据
                        RoleMeta roleMeta = new RoleMeta
                        {
                            RoleId = roleId,
                            ServerId = (uint)server_id // 从roleId中提取serverId，根据实际情况调整
                        };

                        List<RoleMeta> roleMetaList = new List<RoleMeta> { roleMeta };

                        GameEntry.LogicData.RoleData.RequestRoleQueryMulti(roleMetaList, (resp) =>
                        {
                            if (resp != null && resp.Roles != null && resp.Roles.Count > 0)
                            {
                                var roleBrief = resp.Roles[0];

                                // 使用角色简要信息
                                Name = roleBrief.Name;
                                Level = (int)roleBrief.Level;
                                Power = roleBrief.Power;
                                HeadPath = roleBrief.HeadSystemAvatar.ToString();
                            }
                            else
                            {
                                // 如果获取失败，使用默认数据
                                Name = "跨服玩家 " + rank;
                                Level = 1;
                                Power = 1000;
                                HeadPath = "";
                            }

                            // 回调
                            callback?.Invoke(this);
                        });
                    }
                    else
                    {
                        // 如果是本服玩家，通过RequestRoleQueryLocalSingle获取数据
                        GameEntry.LogicData.RoleData.RequestRoleQueryLocalSingle(RoleId, (roleBrief) =>
                        {
                            if (roleBrief != null)
                            {
                                // 使用角色简要信息
                                Name = roleBrief.Name;
                                Level = (int)roleBrief.Level;
                                Power = roleBrief.Power;
                                HeadPath = roleBrief.HeadSystemAvatar.ToString();
                            }
                            else
                            {
                                // 如果获取失败，使用默认数据
                                Name = "玩家 " + rank;
                                Level = 1;
                                Power = 1000;
                                HeadPath = "";
                            }

                            // 回调
                            callback?.Invoke(this);
                        });
                    }
                }
            }
        }
    }
}












