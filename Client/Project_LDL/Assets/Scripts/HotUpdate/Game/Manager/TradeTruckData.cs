using System;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using Roledata;

namespace Game.Hotfix
{
    public class TradeTruckData
    {
        List<Trade.TradeCargoTransport> myTruckList = new();
        List<Trade.TradeCargoTransport> otherTruckList = new();

        public List<Trade.TradeCargoTransport> MyTruckList => myTruckList;
        public List<Trade.TradeCargoTransport> OtherTruckList => otherTruckList;

        public int TruckTradeTodayCount { get; set; }
        public int TruckPlunderTodayCount { get; set; }
        public bool IsFilterTruck { get; set; }

        public void Init(RoleTrade roleTrade)
        {

        }

        #region 接口

        #endregion

        #region 配置表

        public trade GetTruckConfigByID(int id)
        {
            return GameEntry.LDLTable.GetTableById<trade>(id);
        }

        public trade_setting GetTradeSettingByID(int id)
        {
            return GameEntry.LDLTable.GetTableById<trade_setting>(id);
        }

        #endregion

        #region 协议

        /// <summary>
        /// 请求货车信息
        /// </summary>
        /// <param name="type">类型 1 我的货车 2 他人货车</param>
        /// <param name="filter_my_server">是否过滤本服</param>
        /// <param name="callback">回调</param>
        public void RequestTruckList(int type, bool filter_my_server, Action<Trade.TradeCargoTransportListResp> callback = null)
        {
            Trade.TradeCargoTransportListReq req = new()
            {
                Type = type,
                FilterMyServer = filter_my_server
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeCargoTransportListReq, req, (message) =>
            {
                Trade.TradeCargoTransportListResp resp = message as Trade.TradeCargoTransportListResp;
                if (resp != null)
                {
                    if (type == 1)
                    {
                        myTruckList = new List<Trade.TradeCargoTransport>(resp.CargoTransportList);
                        TruckTradeTodayCount = resp.Times;
                    }
                    else if (type == 2)
                    {
                        otherTruckList = new List<Trade.TradeCargoTransport>(resp.CargoTransportList);
                        TruckPlunderTodayCount = resp.Times;
                    }
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车历史记录
        /// </summary>
        /// <param name="tradeRecordType">类型</param>
        /// <param name="callback">回调</param>
        public void RequestTruckRecord(Trade.TradeRecordType tradeRecordType, Action<Trade.TradeVanRecordListResp> callback = null, int page = 0)
        {
            Trade.TradeVanRecordListReq req = new()
            {
                Type = tradeRecordType
            };

            if (tradeRecordType == Trade.TradeRecordType.Rob)
            {
                req.Page = page;
            }

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanRecordListReq, req, (message) =>
            {
                Trade.TradeVanRecordListResp resp = message as Trade.TradeVanRecordListResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车详情
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckDetail(long id, Action<Trade.TradeVanDetailResp> callback = null)
        {
            Trade.TradeVanDetailReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanDetailReq, req, (message) =>
            {
                Trade.TradeVanDetailResp resp = message as Trade.TradeVanDetailResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求刷新货车品质
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckRefresh(long id, Action<Trade.TradeVanRefreshResp> callback = null)
        {
            Trade.TradeVanRefreshReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanRefreshReq, req, (message) =>
            {
                Trade.TradeVanRefreshResp resp = message as Trade.TradeVanRefreshResp;
                if (resp != null)
                {
                    bool isFound = false;
                    for (int i = 0; i < myTruckList.Count; i++)
                    {
                        if (myTruckList[i].Id == id)
                        {
                            myTruckList[i] = resp.CargoTransport;
                            isFound = true;
                        }
                    }
                    if (!isFound)
                    {
                        myTruckList.Add(resp.CargoTransport);
                        myTruckList.Sort((a, b) => a.Position.CompareTo(b.Position));
                    }
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车出发
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="teamType">队伍</param>
        /// <param name="callback">回调</param>
        public void RequestTruckDepart(long id, Team.TeamType teamType, Action<Trade.TradeVanDepartResp> callback = null)
        {
            Trade.TradeVanDepartReq req = new()
            {
                Id = id,
                TeamType = teamType
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanDepartReq, req, (message) =>
            {
                Trade.TradeVanDepartResp resp = message as Trade.TradeVanDepartResp;
                if (resp != null)
                {
                    TruckTradeTodayCount++;
                    bool isFound = false;
                    for (int i = 0; i < myTruckList.Count; i++)
                    {
                        if (myTruckList[i].Id == id)
                        {
                            myTruckList[i] = resp.CargoTransport;
                            isFound = true;
                        }
                    }
                    if (!isFound)
                    {
                        myTruckList.Add(resp.CargoTransport);
                        myTruckList.Sort((a, b) => a.Position.CompareTo(b.Position));
                    }
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车车位信息
        /// </summary>
        /// <param name="pos">位置</param>
        /// <param name="callback">回调</param>
        public void RequestTruckParking(int pos, Action<Trade.TradeVanSetOutResp> callback = null)
        {
            Trade.TradeVanSetOutReq req = new()
            {
                Position = pos
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanSetOutReq, req, (message) =>
            {
                Trade.TradeVanSetOutResp resp = message as Trade.TradeVanSetOutResp;
                if (resp != null)
                {
                    bool isFound = false;
                    for (int i = 0; i < myTruckList.Count; i++)
                    {
                        if (myTruckList[i].Position == pos)
                        {
                            myTruckList[i] = resp.Van;
                            isFound = true;
                        }
                    }
                    if (!isFound)
                    {
                        myTruckList.Add(resp.Van);
                        myTruckList.Sort((a, b) => a.Position.CompareTo(b.Position));
                    }
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车领取奖励
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckReward(long id, Action<Trade.TradeVanReceiveRewardResp> callback = null)
        {
            Trade.TradeVanReceiveRewardReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanReceiveRewardReq, req, (message) =>
            {
                Trade.TradeVanReceiveRewardResp resp = message as Trade.TradeVanReceiveRewardResp;
                if (resp != null)
                {
                    for (int i = myTruckList.Count - 1; i >= 0 ; i--)
                    {
                        if (myTruckList[i].Id == id)
                        {
                            myTruckList.RemoveAt(i);
                        }
                    }
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求货车掠夺
        /// </summary>
        /// <param name="id">货车 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckRob(long id, Action<Trade.TradeVanRobResp> callback = null)
        {
            Trade.TradeVanRobReq req = new()
            {
                Id = id
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanRobReq, req, (message) =>
            {
                Trade.TradeVanRobResp resp = message as Trade.TradeVanRobResp;
                if (resp != null)
                {
                    TruckPlunderTodayCount++;

                    for (int i = 0; i < otherTruckList.Count; i++)
                    {
                        if (otherTruckList[i].Id == id)
                        {
                            otherTruckList[i].RobTimes++;
                        }
                    }
                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求通缉玩家
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="id">记录 id</param>
        /// <param name="robID">掠夺记录 id</param>
        /// <param name="callback">回调</param>
        public void RequestTruckWanted(Trade.TradeRecordType type, long id, long robID, Action<Trade.TradeVanWantedResp> callback = null)
        {
            Trade.TradeVanWantedReq req = new()
            {
                Type = type,
                Id = id,
                RobId = robID
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanWantedReq, req, (message) =>
            {
                Trade.TradeVanWantedResp resp = message as Trade.TradeVanWantedResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 请求收藏货车记录
        /// </summary>
        /// <param name="id">记录 id</param>
        /// <param name="flag">收藏/取消</param>
        /// <param name="callback">回调</param>
        public void RequestTruckCollect(long id, bool flag, Action<Trade.TradeVanCollectResp> callback = null)
        {
            Trade.TradeVanCollectReq req = new()
            {
                Id = id,
                Flag = flag
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.TradeVanCollectReq, req, (message) =>
            {
                Trade.TradeVanCollectResp resp = message as Trade.TradeVanCollectResp;
                if (resp != null)
                {

                }
                callback?.Invoke(resp);
            });
        }

        #endregion
    }
}
