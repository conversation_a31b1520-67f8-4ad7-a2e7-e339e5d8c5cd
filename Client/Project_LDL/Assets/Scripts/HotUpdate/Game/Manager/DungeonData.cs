using Roledata;
using UnityEngine;

namespace Game.Hotfix
{
    public class DungeonData
    {
        public int CurDungeonId => m_CurDungeonId;
        
        private int m_CurDungeonId;

        public DungeonData()
        {
            
        }

        public void Init(RoleDungeon dungeons)
        {
            if (dungeons != null)
                m_CurDungeonId = dungeons.DungeonId;
        }

        public void SetDungeonId(int id)
        {
            m_CurDungeonId = id;
        }
    }
}
