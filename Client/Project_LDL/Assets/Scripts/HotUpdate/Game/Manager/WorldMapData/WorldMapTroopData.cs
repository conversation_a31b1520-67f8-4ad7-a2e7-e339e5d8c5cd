using System.Collections.Generic;
using UnityEngine;
namespace Game.Hotfix
{
    public class WorldMapTroopData
    {
        private Dictionary<ulong, TroopData> m_TroopData;//部队使用唯一ID

        public WorldMapTroopData()
        {
            m_TroopData = new Dictionary<ulong, TroopData>();


            //增加测试数据
            TroopData troopData = new TroopData();
            troopData
            
            
            AddTroopData(1, troopData);

        }

        /// <summary>
        /// 添加部队数据 (Create)
        /// </summary>
        /// <param name="id">部队唯一ID</param>
        /// <param name="troopData">部队数据</param>
        /// <returns>是否添加成功</returns>
        public bool AddTroopData(ulong id, TroopData troopData)
        {
            if (troopData == null)
            {
                Debug.LogError($"TroopData is null for id: {id}");
                return false;
            }

            if (m_TroopData.ContainsKey(id))
            {
                Debug.LogWarning($"TroopData with id {id} already exists. Use UpdateTroopData instead.");
                return false;
            }

            m_TroopData[id] = troopData;
            return true;
        }

        /// <summary>
        /// 获取指定ID的部队数据 (Read)
        /// </summary>
        /// <param name="id">部队唯一ID</param>
        /// <returns>部队数据，如果不存在返回null</returns>
        public TroopData GetTroopData(ulong id)
        {
            m_TroopData.TryGetValue(id, out TroopData troopData);
            return troopData;
        }

        /// <summary>
        /// 获取所有部队数据 (Read)
        /// </summary>
        /// <returns>所有部队数据的字典副本</returns>
        public Dictionary<ulong, TroopData> GetAllTroopData()
        {
            return new Dictionary<ulong, TroopData>(m_TroopData);
        }

        /// <summary>
        /// 检查是否包含指定ID的部队数据 (Read)
        /// </summary>
        /// <param name="id">部队唯一ID</param>
        /// <returns>是否存在</returns>
        public bool ContainsTroopData(ulong id)
        {
            return m_TroopData.ContainsKey(id);
        }

        /// <summary>
        /// 获取部队数据总数 (Read)
        /// </summary>
        /// <returns>部队数据总数</returns>
        public int GetTroopCount()
        {
            return m_TroopData.Count;
        }

        /// <summary>
        /// 获取所有部队ID列表 (Read)
        /// </summary>
        /// <returns>所有部队ID的集合</returns>
        public ICollection<ulong> GetAllTroopIds()
        {
            return m_TroopData.Keys;
        }

        /// <summary>
        /// 获取所有部队数据列表 (Read)
        /// </summary>
        /// <returns>所有部队数据的集合</returns>
        public ICollection<TroopData> GetAllTroopDataValues()
        {
            return m_TroopData.Values;
        }

        /// <summary>
        /// 更新部队数据 (Update)
        /// </summary>
        /// <param name="id">部队唯一ID</param>
        /// <param name="troopData">新的部队数据</param>
        /// <returns>是否更新成功</returns>
        public bool UpdateTroopData(ulong id, TroopData troopData)
        {
            if (troopData == null)
            {
                Debug.LogError($"TroopData is null for id: {id}");
                return false;
            }

            if (!m_TroopData.ContainsKey(id))
            {
                Debug.LogWarning($"TroopData with id {id} does not exist. Use AddTroopData instead.");
                return false;
            }

            m_TroopData[id] = troopData;
            return true;
        }

        /// <summary>
        /// 添加或更新部队数据 (Create/Update)
        /// </summary>
        /// <param name="id">部队唯一ID</param>
        /// <param name="troopData">部队数据</param>
        /// <returns>是否操作成功</returns>
        public bool SetTroopData(ulong id, TroopData troopData)
        {
            if (troopData == null)
            {
                Debug.LogError($"TroopData is null for id: {id}");
                return false;
            }

            m_TroopData[id] = troopData;
            return true;
        }

        /// <summary>
        /// 删除指定ID的部队数据 (Delete)
        /// </summary>
        /// <param name="id">部队唯一ID</param>
        /// <returns>是否删除成功</returns>
        public bool RemoveTroopData(ulong id)
        {
            return m_TroopData.Remove(id);
        }

        /// <summary>
        /// 清空所有部队数据 (Delete)
        /// </summary>
        public void ClearAllTroopData()
        {
            m_TroopData.Clear();
        }

        /// <summary>
        /// 批量删除部队数据 (Delete)
        /// </summary>
        /// <param name="ids">要删除的部队ID列表</param>
        /// <returns>成功删除的数量</returns>
        public int RemoveTroopDataBatch(IEnumerable<ulong> ids)
        {
            if (ids == null)
                return 0;

            int removedCount = 0;
            foreach (ulong id in ids)
            {
                if (m_TroopData.Remove(id))
                    removedCount++;
            }
            return removedCount;
        }

    }
}