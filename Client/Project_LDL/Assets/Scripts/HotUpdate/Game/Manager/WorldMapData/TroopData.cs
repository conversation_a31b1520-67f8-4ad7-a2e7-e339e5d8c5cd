using Google.Protobuf.Collections;
using UnityEngine;

namespace Game.Hotfix
{
    public class TroopData
    {
        private Troop.Troop m_Troop;

        public TroopData()
        {
        }

        public Vector3 GetPosition()
        {
            if (m_Troop.Paths.Count > 0)
            {
                var temp = m_Troop.Paths[0];

                return new Vector3(temp.X / GameDefine.TroopPathScale, 0.02f, temp.Y / GameDefine.TroopPathScale);
            }

            return Vector3.zero;
        }
        
        public Vector3 GetPosition()
        {
            
        }

        public RepeatedField<Troop.Path> GetPath()
        {
            return m_Troop.Paths;
        }
    }
}