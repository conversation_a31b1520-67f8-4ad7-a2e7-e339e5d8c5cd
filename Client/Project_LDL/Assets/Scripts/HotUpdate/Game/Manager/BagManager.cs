using UnityEngine;
using Game.Hotfix.Config;
using GameFramework.Resource;
using System.Collections.Generic;
using Game.Hotfix;
using System;
public class BagManager : MonoBehaviour
{
    
    // private static BagManager instance;
    //  public static BagManager Instance
    //  {
    //      get
    //     {
    //         if (instance == null)
    //          {
    //              instance = new BagManager();
    //         }
    //         return instance;
    //     }
    // }
    public static void CreatItem(Transform parent,itemid itemId,long count,Action<UIItemModule> callBack)
    {
        if (parent == null || itemId == itemid.itemid_nil) return;

        GameEntry.Resource.LoadAsset("Assets/ResPackage/Prefab/Common/itemObj.prefab", typeof(UIItemModule),
        new LoadAssetCallbacks((assetName, asset, duration, userData) =>
        {
            // lastItem itemid
            UIItemModule uIItemModule = asset as UIItemModule;
            if (uIItemModule == null)
            {
                GameObject obj = asset as GameObject;
                uIItemModule = obj.GetComponent<UIItemModule>();
            }
            ItemModule _itemModule = new ItemModule();
            uIItemModule = Instantiate(uIItemModule);
            uIItemModule.itemModule = _itemModule;
            _itemModule.SetData(itemId, count);
            //if (uIItemModule != null)
            //{
            uIItemModule.Init(parent.transform, itemId, count);
            uIItemModule.DisplayInfo();
            //}
            callBack?.Invoke(uIItemModule);
        }));
    }

    public void GetServerData ()
    {

    }

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        
    }
    

    // Update is called once per frame
    void Update()
    {
        
    }
}
