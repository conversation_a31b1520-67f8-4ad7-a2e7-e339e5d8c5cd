using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class HUDBuildingDispatch : HUDItemTickAble
    {
        [SerializeField] private UIButton m_BtnDisplayRoot;
        [SerializeField] private UIImage m_imgIsDispatch;
        [SerializeField] private UIImage m_imgIsUpStar;
        [SerializeField] private Animation m_Animation;
        
        private EL_Building m_Building;
        private BuildingModule m_BuildingModule;
        void Start()
        {
            m_BtnDisplayRoot.onClick.AddListener(OnBtnTrainClick);
        }
        
        protected override void OnInit(object param)
        {
            base.OnInit(param);
            m_Building = Owner as EL_Building;
            m_BuildingModule = m_Building?.GetBuildingModule();
            ResetUI();
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnSurvivorChange,OnSurvivorChange);
        }

        protected override void OnUnInit()
        {
            base.OnUnInit();
            m_BuildingModule.RemoveEventListener(BuildingModuleEvent.OnSurvivorChange, OnSurvivorChange);
        }

        protected void OnSurvivorChange(object obj)
        {
            if (obj is SurvivorChangeState state)
            {
                if (state == SurvivorChangeState.Remove)
                {
                    GameEntry.HUD.HideHUD(this);
                }
                else
                {
                    ResetUI();
                }
            }
        }
        
        private void ResetUI()
        {
            buildtype buildingType = m_BuildingModule.GetBuildingType();
            // 人才大厅的显示可升星
            m_imgIsDispatch.gameObject.SetActive(buildingType != buildtype.buildtype_talenthall);
            m_imgIsUpStar.gameObject.SetActive(buildingType == buildtype.buildtype_talenthall);
        }
        
        protected override Vector3 GetOffset()
        {
            if (m_BuildingModule != null)
            {
                var offset = m_BuildingModule.GetOffsetCenter();
                return new Vector3(offset.x, 2, offset.y);
            }

            return base.GetOffset();
        }

        private void OnBtnTrainClick()
        {
            buildtype buildingType = m_BuildingModule.GetBuildingType();
            if (buildingType != buildtype.buildtype_talenthall)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingDispatchForm,m_BuildingModule);
            }
            else 
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingSurvivorForm);
            }
        }
    }
}