using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class HUDPvePathLock : HUDItemTickAble
    {
        [SerializeField] private UIText m_txtLockDes;

        private PvePathModule m_PvePathModule;

        protected override void OnInit(object param)
        {
            base.OnInit(param);

            if (TryInit(param))
            {
                GameEntry.Event.Subscribe(OnBuildingLevelChangeEventArgs.EventId, OnBuildingLevelChangeEvent);
                
            }
            else
            {
                GameEntry.HUD.HideHUD(this);
            }
        }

        private bool TryInit(object param)
        {
            if (param is PvePathModule pvePathModule)
            {
                m_PvePathModule = pvePathModule;
                if (!pvePathModule.CanTriggerEvent())
                {
                    var config = pvePathModule.GetConfig();
                    if (config != null)
                    {
                        var buildingId = config.build_condition.build_id;
                        var buildingLevel = config.build_condition.build_lv;
                        var buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById((uint)buildingId);
                        if (buildingModule != null)
                        {
                            m_txtLockDes.text = "需要建筑" + buildingModule.BuildingName + "等级Lv." + buildingLevel; //TODO中文
                            return true;
                        }
                    }
                }
            }

            return false;
        }


        private void OnBuildingLevelChangeEvent(object sender, GameEventArgs e)
        {
            if (TryHide(e))
            {
                GameEntry.HUD.HideHUD(this);
            }
        }

        private bool TryHide(GameEventArgs e)
        {
            if (e is OnBuildingLevelChangeEventArgs args)
            {
                if (m_PvePathModule != null)
                {
                    var config = m_PvePathModule.GetConfig();
                    if (config != null)
                    {
                        var buildingId = config.build_condition.build_id;
                        if (buildingId == args.BuildingModule.BuildingId)
                        {
                            if (m_PvePathModule.CanTriggerEvent())
                                return true;
                        }
                    }
                }
            }

            return false;
        }

        protected override Vector3 GetOffset()
        {
            return new Vector3(3.3f, 3.2f, -1.25f);
        }

        protected override void OnUnInit()
        {
            if (GameEntry.Event.Check(OnBuildingLevelChangeEventArgs.EventId, OnBuildingLevelChangeEvent))
                GameEntry.Event.Unsubscribe(OnBuildingLevelChangeEventArgs.EventId, OnBuildingLevelChangeEvent);
            
            base.OnUnInit();
        }
    }
}