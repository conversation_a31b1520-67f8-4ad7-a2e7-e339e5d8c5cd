// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: activity.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Activity {

  /// <summary>Holder for reflection information generated from activity.proto</summary>
  public static partial class ActivityReflection {

    #region Descriptor
    /// <summary>File descriptor for activity.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ActivityReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cg5hY3Rpdml0eS5wcm90bxIIYWN0aXZpdHkaEGdhbWVjb25maWcucHJvdG8a",
            "DWFydGljbGUucHJvdG8i0gEKDEFjdGl2aXR5VGltZRIqCgR0eXBlGAEgASgO",
            "MhwucGJfZ2FtZWNvbmZpZy5hY3Rpdml0eV90eXBlEjIKCHRlbXBsYXRlGAIg",
            "ASgOMiAucGJfZ2FtZWNvbmZpZy5hY3Rpdml0eV90ZW1wbGF0ZRISCgpsb29w",
            "X3RpbWVzGAMgASgNEhQKDHByZXZpZXdfdGltZRgEIAEoAxISCgpzdGFydF90",
            "aW1lGAUgASgDEhAKCGVuZF90aW1lGAYgASgDEhIKCmNsb3NlX3RpbWUYByAB",
            "KAMiFgoUQWN0aXZpdHlPcGVuSW5mb3NSZXEiPQoVQWN0aXZpdHlPcGVuSW5m",
            "b3NSZXNwEiQKBGxpc3QYASADKAsyFi5hY3Rpdml0eS5BY3Rpdml0eVRpbWUi",
            "OgoSUHVzaEFjdGl2aXR5Q2hhbmdlEiQKBGxpc3QYASADKAsyFi5hY3Rpdml0",
            "eS5BY3Rpdml0eVRpbWUihQEKD1B1c2hBY3Rpdml0eURlbBIqCgR0eXBlGAEg",
            "ASgOMhwucGJfZ2FtZWNvbmZpZy5hY3Rpdml0eV90eXBlEjIKCHRlbXBsYXRl",
            "GAIgASgOMiAucGJfZ2FtZWNvbmZpZy5hY3Rpdml0eV90ZW1wbGF0ZRISCgps",
            "b29wX3RpbWVzGAMgASgNInMKEUFjdGl2aXR5Q29uZmlnUmVxEioKBHR5cGUY",
            "ASABKA4yHC5wYl9nYW1lY29uZmlnLmFjdGl2aXR5X3R5cGUSMgoIdGVtcGxh",
            "dGUYAiABKA4yIC5wYl9nYW1lY29uZmlnLmFjdGl2aXR5X3RlbXBsYXRlIhQK",
            "EkFjdGl2aXR5Q29uZmlnUmVzcCKFAQoPQWN0aXZpdHlEYXRhUmVxEioKBHR5",
            "cGUYASABKA4yHC5wYl9nYW1lY29uZmlnLmFjdGl2aXR5X3R5cGUSMgoIdGVt",
            "cGxhdGUYAiABKA4yIC5wYl9nYW1lY29uZmlnLmFjdGl2aXR5X3RlbXBsYXRl",
            "EhIKCmxvb3BfdGltZXMYAyABKA0iEgoQQWN0aXZpdHlEYXRhUmVzcCKqAQoP",
            "QWN0aXZpdHlEcmF3UmVxEioKBHR5cGUYASABKA4yHC5wYl9nYW1lY29uZmln",
            "LmFjdGl2aXR5X3R5cGUSMgoIdGVtcGxhdGUYAiABKA4yIC5wYl9nYW1lY29u",
            "ZmlnLmFjdGl2aXR5X3RlbXBsYXRlEhIKCmxvb3BfdGltZXMYAyABKA0SDwoH",
            "ZHJhd19pZBgEIAEoBBISCgpleHRyYV9hcmdzGAUgAygEIjUKEEFjdGl2aXR5",
            "RHJhd1Jlc3ASIQoHcmV3YXJkcxgBIAMoCzIQLmFydGljbGUuQXJ0aWNsZSKp",
            "AQoOQWN0aXZpdHlCdXlSZXESKgoEdHlwZRgBIAEoDjIcLnBiX2dhbWVjb25m",
            "aWcuYWN0aXZpdHlfdHlwZRIyCgh0ZW1wbGF0ZRgCIAEoDjIgLnBiX2dhbWVj",
            "b25maWcuYWN0aXZpdHlfdGVtcGxhdGUSEgoKbG9vcF90aW1lcxgDIAEoDRIP",
            "CgdkcmF3X2lkGAQgASgEEhIKCmV4dHJhX2FyZ3MYBSADKAQiNAoPQWN0aXZp",
            "dHlCdXlSZXNwEiEKB3Jld2FyZHMYASADKAsyEC5hcnRpY2xlLkFydGljbGUi",
            "aAoZQWN0aXZpdHlIZXJvU3RhckNvbmRpdGlvbhIKCgJpZBgBIAEoBBIXCg9z",
            "dGFyX2NvbmRpdGlvbnMYAiADKA0SJgoHcmV3YXJkcxgDIAMoCzIVLnBiX2dh",
            "bWVjb25maWcucmV3YXJkIrIBChpQdXNoQWN0aXZpdHlIZXJvU3RhckNvbmZp",
            "ZxIyCgh0ZW1wbGF0ZRgBIAEoDjIgLnBiX2dhbWVjb25maWcuYWN0aXZpdHlf",
            "dGVtcGxhdGUSJwoIaGVyb19pZHMYAiADKA4yFS5wYl9nYW1lY29uZmlnLml0",
            "ZW1pZBI3Cgpjb25kaXRpb25zGAMgAygLMiMuYWN0aXZpdHkuQWN0aXZpdHlI",
            "ZXJvU3RhckNvbmRpdGlvbiJ0ChhQdXNoQWN0aXZpdHlIZXJvU3RhckRhdGES",
            "MgoIdGVtcGxhdGUYASABKA4yIC5wYl9nYW1lY29uZmlnLmFjdGl2aXR5X3Rl",
            "bXBsYXRlEhIKCmxvb3BfdGltZXMYAiABKA0SEAoIZHJhd19pZHMYAyADKAQi",
            "WgoVQWN0aXZpdHlSZWNoYXJnZVNjb3JlEgoKAmlkGAEgASgEEg0KBXNjb3Jl",
            "GAIgASgNEiYKB3Jld2FyZHMYAyADKAsyFS5wYl9nYW1lY29uZmlnLnJld2Fy",
            "ZCKHAQoaUHVzaEFjdGl2aXR5UmVjaGFyZ2VDb25maWcSMgoIdGVtcGxhdGUY",
            "ASABKA4yIC5wYl9nYW1lY29uZmlnLmFjdGl2aXR5X3RlbXBsYXRlEjUKDHNj",
            "b3JlUmV3YXJkcxgCIAMoCzIfLmFjdGl2aXR5LkFjdGl2aXR5UmVjaGFyZ2VT",
            "Y29yZSKDAQoYUHVzaEFjdGl2aXR5UmVjaGFyZ2VEYXRhEjIKCHRlbXBsYXRl",
            "GAEgASgOMiAucGJfZ2FtZWNvbmZpZy5hY3Rpdml0eV90ZW1wbGF0ZRISCgps",
            "b29wX3RpbWVzGAIgASgNEg0KBXNjb3JlGAMgASgNEhAKCGRyYXdfaWRzGAQg",
            "AygEIqwBChdBY3Rpdml0eUJhdHRsZVBhc3NTY29yZRIKCgJpZBgBIAEoBBIN",
            "CgVzY29yZRgCIAEoDRImCgdyZXdhcmQwGAMgAygLMhUucGJfZ2FtZWNvbmZp",
            "Zy5yZXdhcmQSJgoHcmV3YXJkMRgEIAMoCzIVLnBiX2dhbWVjb25maWcucmV3",
            "YXJkEiYKB3Jld2FyZDIYBSADKAsyFS5wYl9nYW1lY29uZmlnLnJld2FyZCLK",
            "AQocUHVzaEFjdGl2aXR5QmF0dGxlUGFzc0NvbmZpZxIyCgh0ZW1wbGF0ZRgB",
            "IAEoDjIgLnBiX2dhbWVjb25maWcuYWN0aXZpdHlfdGVtcGxhdGUSNwoMc2Nv",
            "cmVSZXdhcmRzGAIgAygLMiEuYWN0aXZpdHkuQWN0aXZpdHlCYXR0bGVQYXNz",
            "U2NvcmUSEQoJYm94X3Njb3JlGAMgASgNEioKC2JveF9yZXdhcmRzGAQgAygL",
            "MhUucGJfZ2FtZWNvbmZpZy5yZXdhcmQiOQoWQWN0aXZpdHlCYXR0bGVQYXNz",
            "RHJhdxIKCgJpZBgBIAEoBBITCgtkcmF3X2dyYWRlcxgCIAMoDSKkAQoaUHVz",
            "aEFjdGl2aXR5QmF0dGxlUGFzc0RhdGESMgoIdGVtcGxhdGUYASABKA4yIC5w",
            "Yl9nYW1lY29uZmlnLmFjdGl2aXR5X3RlbXBsYXRlEhIKCmxvb3BfdGltZXMY",
            "AiABKA0SDQoFc2NvcmUYAyABKA0SLwoFZHJhd3MYBCADKAsyIC5hY3Rpdml0",
            "eS5BY3Rpdml0eUJhdHRsZVBhc3NEcmF3QhtaGXNlcnZlci9hcGkvcGIvcGJf",
            "YWN0aXZpdHliBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::PbGameconfig.GameconfigReflection.Descriptor, global::Article.ArticleReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityTime), global::Activity.ActivityTime.Parser, new[]{ "Type", "Template", "LoopTimes", "PreviewTime", "StartTime", "EndTime", "CloseTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityOpenInfosReq), global::Activity.ActivityOpenInfosReq.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityOpenInfosResp), global::Activity.ActivityOpenInfosResp.Parser, new[]{ "List" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.PushActivityChange), global::Activity.PushActivityChange.Parser, new[]{ "List" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.PushActivityDel), global::Activity.PushActivityDel.Parser, new[]{ "Type", "Template", "LoopTimes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityConfigReq), global::Activity.ActivityConfigReq.Parser, new[]{ "Type", "Template" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityConfigResp), global::Activity.ActivityConfigResp.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityDataReq), global::Activity.ActivityDataReq.Parser, new[]{ "Type", "Template", "LoopTimes" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityDataResp), global::Activity.ActivityDataResp.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityDrawReq), global::Activity.ActivityDrawReq.Parser, new[]{ "Type", "Template", "LoopTimes", "DrawId", "ExtraArgs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityDrawResp), global::Activity.ActivityDrawResp.Parser, new[]{ "Rewards" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityBuyReq), global::Activity.ActivityBuyReq.Parser, new[]{ "Type", "Template", "LoopTimes", "DrawId", "ExtraArgs" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityBuyResp), global::Activity.ActivityBuyResp.Parser, new[]{ "Rewards" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityHeroStarCondition), global::Activity.ActivityHeroStarCondition.Parser, new[]{ "Id", "StarConditions", "Rewards" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.PushActivityHeroStarConfig), global::Activity.PushActivityHeroStarConfig.Parser, new[]{ "Template", "HeroIds", "Conditions" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.PushActivityHeroStarData), global::Activity.PushActivityHeroStarData.Parser, new[]{ "Template", "LoopTimes", "DrawIds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityRechargeScore), global::Activity.ActivityRechargeScore.Parser, new[]{ "Id", "Score", "Rewards" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.PushActivityRechargeConfig), global::Activity.PushActivityRechargeConfig.Parser, new[]{ "Template", "ScoreRewards" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.PushActivityRechargeData), global::Activity.PushActivityRechargeData.Parser, new[]{ "Template", "LoopTimes", "Score", "DrawIds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityBattlePassScore), global::Activity.ActivityBattlePassScore.Parser, new[]{ "Id", "Score", "Reward0", "Reward1", "Reward2" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.PushActivityBattlePassConfig), global::Activity.PushActivityBattlePassConfig.Parser, new[]{ "Template", "ScoreRewards", "BoxScore", "BoxRewards" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.ActivityBattlePassDraw), global::Activity.ActivityBattlePassDraw.Parser, new[]{ "Id", "DrawGrades" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Activity.PushActivityBattlePassData), global::Activity.PushActivityBattlePassData.Parser, new[]{ "Template", "LoopTimes", "Score", "Draws" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Messages
  /// <summary>
  /// 活动
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityTime : pb::IMessage<ActivityTime>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityTime> _parser = new pb::MessageParser<ActivityTime>(() => new ActivityTime());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityTime> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityTime() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityTime(ActivityTime other) : this() {
      type_ = other.type_;
      template_ = other.template_;
      loopTimes_ = other.loopTimes_;
      previewTime_ = other.previewTime_;
      startTime_ = other.startTime_;
      endTime_ = other.endTime_;
      closeTime_ = other.closeTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityTime Clone() {
      return new ActivityTime(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::PbGameconfig.activity_type type_ = global::PbGameconfig.activity_type.Nil;
    /// <summary>
    /// 活动类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_type Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 2;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "loop_times" field.</summary>
    public const int LoopTimesFieldNumber = 3;
    private uint loopTimes_;
    /// <summary>
    /// 循环次数(第几次开)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint LoopTimes {
      get { return loopTimes_; }
      set {
        loopTimes_ = value;
      }
    }

    /// <summary>Field number for the "preview_time" field.</summary>
    public const int PreviewTimeFieldNumber = 4;
    private long previewTime_;
    /// <summary>
    /// 预告开始时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long PreviewTime {
      get { return previewTime_; }
      set {
        previewTime_ = value;
      }
    }

    /// <summary>Field number for the "start_time" field.</summary>
    public const int StartTimeFieldNumber = 5;
    private long startTime_;
    /// <summary>
    /// 活动开始时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long StartTime {
      get { return startTime_; }
      set {
        startTime_ = value;
      }
    }

    /// <summary>Field number for the "end_time" field.</summary>
    public const int EndTimeFieldNumber = 6;
    private long endTime_;
    /// <summary>
    /// 活动结束时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long EndTime {
      get { return endTime_; }
      set {
        endTime_ = value;
      }
    }

    /// <summary>Field number for the "close_time" field.</summary>
    public const int CloseTimeFieldNumber = 7;
    private long closeTime_;
    /// <summary>
    /// 活动关闭时间
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long CloseTime {
      get { return closeTime_; }
      set {
        closeTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityTime);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityTime other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (Template != other.Template) return false;
      if (LoopTimes != other.LoopTimes) return false;
      if (PreviewTime != other.PreviewTime) return false;
      if (StartTime != other.StartTime) return false;
      if (EndTime != other.EndTime) return false;
      if (CloseTime != other.CloseTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::PbGameconfig.activity_type.Nil) hash ^= Type.GetHashCode();
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      if (LoopTimes != 0) hash ^= LoopTimes.GetHashCode();
      if (PreviewTime != 0L) hash ^= PreviewTime.GetHashCode();
      if (StartTime != 0L) hash ^= StartTime.GetHashCode();
      if (EndTime != 0L) hash ^= EndTime.GetHashCode();
      if (CloseTime != 0L) hash ^= CloseTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (PreviewTime != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(PreviewTime);
      }
      if (StartTime != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(StartTime);
      }
      if (EndTime != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(EndTime);
      }
      if (CloseTime != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(CloseTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (PreviewTime != 0L) {
        output.WriteRawTag(32);
        output.WriteInt64(PreviewTime);
      }
      if (StartTime != 0L) {
        output.WriteRawTag(40);
        output.WriteInt64(StartTime);
      }
      if (EndTime != 0L) {
        output.WriteRawTag(48);
        output.WriteInt64(EndTime);
      }
      if (CloseTime != 0L) {
        output.WriteRawTag(56);
        output.WriteInt64(CloseTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::PbGameconfig.activity_type.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      if (LoopTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(LoopTimes);
      }
      if (PreviewTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(PreviewTime);
      }
      if (StartTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(StartTime);
      }
      if (EndTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(EndTime);
      }
      if (CloseTime != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(CloseTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityTime other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::PbGameconfig.activity_type.Nil) {
        Type = other.Type;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      if (other.LoopTimes != 0) {
        LoopTimes = other.LoopTimes;
      }
      if (other.PreviewTime != 0L) {
        PreviewTime = other.PreviewTime;
      }
      if (other.StartTime != 0L) {
        StartTime = other.StartTime;
      }
      if (other.EndTime != 0L) {
        EndTime = other.EndTime;
      }
      if (other.CloseTime != 0L) {
        CloseTime = other.CloseTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 32: {
            PreviewTime = input.ReadInt64();
            break;
          }
          case 40: {
            StartTime = input.ReadInt64();
            break;
          }
          case 48: {
            EndTime = input.ReadInt64();
            break;
          }
          case 56: {
            CloseTime = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 32: {
            PreviewTime = input.ReadInt64();
            break;
          }
          case 40: {
            StartTime = input.ReadInt64();
            break;
          }
          case 48: {
            EndTime = input.ReadInt64();
            break;
          }
          case 56: {
            CloseTime = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4001 活动开启列表请求
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityOpenInfosReq : pb::IMessage<ActivityOpenInfosReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityOpenInfosReq> _parser = new pb::MessageParser<ActivityOpenInfosReq>(() => new ActivityOpenInfosReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityOpenInfosReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityOpenInfosReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityOpenInfosReq(ActivityOpenInfosReq other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityOpenInfosReq Clone() {
      return new ActivityOpenInfosReq(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityOpenInfosReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityOpenInfosReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityOpenInfosReq other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityOpenInfosResp : pb::IMessage<ActivityOpenInfosResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityOpenInfosResp> _parser = new pb::MessageParser<ActivityOpenInfosResp>(() => new ActivityOpenInfosResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityOpenInfosResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityOpenInfosResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityOpenInfosResp(ActivityOpenInfosResp other) : this() {
      list_ = other.list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityOpenInfosResp Clone() {
      return new ActivityOpenInfosResp(this);
    }

    /// <summary>Field number for the "list" field.</summary>
    public const int ListFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Activity.ActivityTime> _repeated_list_codec
        = pb::FieldCodec.ForMessage(10, global::Activity.ActivityTime.Parser);
    private readonly pbc::RepeatedField<global::Activity.ActivityTime> list_ = new pbc::RepeatedField<global::Activity.ActivityTime>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Activity.ActivityTime> List {
      get { return list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityOpenInfosResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityOpenInfosResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!list_.Equals(other.list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      list_.WriteTo(output, _repeated_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      list_.WriteTo(ref output, _repeated_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += list_.CalculateSize(_repeated_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityOpenInfosResp other) {
      if (other == null) {
        return;
      }
      list_.Add(other.list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            list_.AddEntriesFrom(input, _repeated_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            list_.AddEntriesFrom(ref input, _repeated_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4002 活动信息变更(新增或修改)推送
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushActivityChange : pb::IMessage<PushActivityChange>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushActivityChange> _parser = new pb::MessageParser<PushActivityChange>(() => new PushActivityChange());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushActivityChange> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityChange() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityChange(PushActivityChange other) : this() {
      list_ = other.list_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityChange Clone() {
      return new PushActivityChange(this);
    }

    /// <summary>Field number for the "list" field.</summary>
    public const int ListFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Activity.ActivityTime> _repeated_list_codec
        = pb::FieldCodec.ForMessage(10, global::Activity.ActivityTime.Parser);
    private readonly pbc::RepeatedField<global::Activity.ActivityTime> list_ = new pbc::RepeatedField<global::Activity.ActivityTime>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Activity.ActivityTime> List {
      get { return list_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushActivityChange);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushActivityChange other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!list_.Equals(other.list_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= list_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      list_.WriteTo(output, _repeated_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      list_.WriteTo(ref output, _repeated_list_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += list_.CalculateSize(_repeated_list_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushActivityChange other) {
      if (other == null) {
        return;
      }
      list_.Add(other.list_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            list_.AddEntriesFrom(input, _repeated_list_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            list_.AddEntriesFrom(ref input, _repeated_list_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4003 活动删除
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushActivityDel : pb::IMessage<PushActivityDel>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushActivityDel> _parser = new pb::MessageParser<PushActivityDel>(() => new PushActivityDel());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushActivityDel> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityDel() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityDel(PushActivityDel other) : this() {
      type_ = other.type_;
      template_ = other.template_;
      loopTimes_ = other.loopTimes_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityDel Clone() {
      return new PushActivityDel(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::PbGameconfig.activity_type type_ = global::PbGameconfig.activity_type.Nil;
    /// <summary>
    /// 活动类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_type Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 2;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "loop_times" field.</summary>
    public const int LoopTimesFieldNumber = 3;
    private uint loopTimes_;
    /// <summary>
    /// 循环次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint LoopTimes {
      get { return loopTimes_; }
      set {
        loopTimes_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushActivityDel);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushActivityDel other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (Template != other.Template) return false;
      if (LoopTimes != other.LoopTimes) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::PbGameconfig.activity_type.Nil) hash ^= Type.GetHashCode();
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      if (LoopTimes != 0) hash ^= LoopTimes.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::PbGameconfig.activity_type.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      if (LoopTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(LoopTimes);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushActivityDel other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::PbGameconfig.activity_type.Nil) {
        Type = other.Type;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      if (other.LoopTimes != 0) {
        LoopTimes = other.LoopTimes;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4004 活动配置数据请求(以推送形式返回具体活动数据)
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityConfigReq : pb::IMessage<ActivityConfigReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityConfigReq> _parser = new pb::MessageParser<ActivityConfigReq>(() => new ActivityConfigReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityConfigReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityConfigReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityConfigReq(ActivityConfigReq other) : this() {
      type_ = other.type_;
      template_ = other.template_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityConfigReq Clone() {
      return new ActivityConfigReq(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::PbGameconfig.activity_type type_ = global::PbGameconfig.activity_type.Nil;
    /// <summary>
    /// 活动类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_type Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 2;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityConfigReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityConfigReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (Template != other.Template) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::PbGameconfig.activity_type.Nil) hash ^= Type.GetHashCode();
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::PbGameconfig.activity_type.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityConfigReq other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::PbGameconfig.activity_type.Nil) {
        Type = other.Type;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityConfigResp : pb::IMessage<ActivityConfigResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityConfigResp> _parser = new pb::MessageParser<ActivityConfigResp>(() => new ActivityConfigResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityConfigResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityConfigResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityConfigResp(ActivityConfigResp other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityConfigResp Clone() {
      return new ActivityConfigResp(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityConfigResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityConfigResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityConfigResp other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4005 活动玩家数据请求(以推送形式返回具体活动数据)
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityDataReq : pb::IMessage<ActivityDataReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityDataReq> _parser = new pb::MessageParser<ActivityDataReq>(() => new ActivityDataReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityDataReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDataReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDataReq(ActivityDataReq other) : this() {
      type_ = other.type_;
      template_ = other.template_;
      loopTimes_ = other.loopTimes_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDataReq Clone() {
      return new ActivityDataReq(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::PbGameconfig.activity_type type_ = global::PbGameconfig.activity_type.Nil;
    /// <summary>
    /// 活动类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_type Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 2;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "loop_times" field.</summary>
    public const int LoopTimesFieldNumber = 3;
    private uint loopTimes_;
    /// <summary>
    /// 循环次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint LoopTimes {
      get { return loopTimes_; }
      set {
        loopTimes_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityDataReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityDataReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (Template != other.Template) return false;
      if (LoopTimes != other.LoopTimes) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::PbGameconfig.activity_type.Nil) hash ^= Type.GetHashCode();
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      if (LoopTimes != 0) hash ^= LoopTimes.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::PbGameconfig.activity_type.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      if (LoopTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(LoopTimes);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityDataReq other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::PbGameconfig.activity_type.Nil) {
        Type = other.Type;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      if (other.LoopTimes != 0) {
        LoopTimes = other.LoopTimes;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityDataResp : pb::IMessage<ActivityDataResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityDataResp> _parser = new pb::MessageParser<ActivityDataResp>(() => new ActivityDataResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityDataResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDataResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDataResp(ActivityDataResp other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDataResp Clone() {
      return new ActivityDataResp(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityDataResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityDataResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityDataResp other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4006 活动通用领奖协议
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityDrawReq : pb::IMessage<ActivityDrawReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityDrawReq> _parser = new pb::MessageParser<ActivityDrawReq>(() => new ActivityDrawReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityDrawReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDrawReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDrawReq(ActivityDrawReq other) : this() {
      type_ = other.type_;
      template_ = other.template_;
      loopTimes_ = other.loopTimes_;
      drawId_ = other.drawId_;
      extraArgs_ = other.extraArgs_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDrawReq Clone() {
      return new ActivityDrawReq(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::PbGameconfig.activity_type type_ = global::PbGameconfig.activity_type.Nil;
    /// <summary>
    /// 活动类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_type Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 2;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "loop_times" field.</summary>
    public const int LoopTimesFieldNumber = 3;
    private uint loopTimes_;
    /// <summary>
    /// 循环次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint LoopTimes {
      get { return loopTimes_; }
      set {
        loopTimes_ = value;
      }
    }

    /// <summary>Field number for the "draw_id" field.</summary>
    public const int DrawIdFieldNumber = 4;
    private ulong drawId_;
    /// <summary>
    /// 领取奖励的id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong DrawId {
      get { return drawId_; }
      set {
        drawId_ = value;
      }
    }

    /// <summary>Field number for the "extra_args" field.</summary>
    public const int ExtraArgsFieldNumber = 5;
    private static readonly pb::FieldCodec<ulong> _repeated_extraArgs_codec
        = pb::FieldCodec.ForUInt64(42);
    private readonly pbc::RepeatedField<ulong> extraArgs_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// 额外参数(根据具体活动而定，无需额外参数时传空)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> ExtraArgs {
      get { return extraArgs_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityDrawReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityDrawReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (Template != other.Template) return false;
      if (LoopTimes != other.LoopTimes) return false;
      if (DrawId != other.DrawId) return false;
      if(!extraArgs_.Equals(other.extraArgs_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::PbGameconfig.activity_type.Nil) hash ^= Type.GetHashCode();
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      if (LoopTimes != 0) hash ^= LoopTimes.GetHashCode();
      if (DrawId != 0UL) hash ^= DrawId.GetHashCode();
      hash ^= extraArgs_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (DrawId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(DrawId);
      }
      extraArgs_.WriteTo(output, _repeated_extraArgs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (DrawId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(DrawId);
      }
      extraArgs_.WriteTo(ref output, _repeated_extraArgs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::PbGameconfig.activity_type.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      if (LoopTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(LoopTimes);
      }
      if (DrawId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(DrawId);
      }
      size += extraArgs_.CalculateSize(_repeated_extraArgs_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityDrawReq other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::PbGameconfig.activity_type.Nil) {
        Type = other.Type;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      if (other.LoopTimes != 0) {
        LoopTimes = other.LoopTimes;
      }
      if (other.DrawId != 0UL) {
        DrawId = other.DrawId;
      }
      extraArgs_.Add(other.extraArgs_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 32: {
            DrawId = input.ReadUInt64();
            break;
          }
          case 42:
          case 40: {
            extraArgs_.AddEntriesFrom(input, _repeated_extraArgs_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 32: {
            DrawId = input.ReadUInt64();
            break;
          }
          case 42:
          case 40: {
            extraArgs_.AddEntriesFrom(ref input, _repeated_extraArgs_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityDrawResp : pb::IMessage<ActivityDrawResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityDrawResp> _parser = new pb::MessageParser<ActivityDrawResp>(() => new ActivityDrawResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityDrawResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDrawResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDrawResp(ActivityDrawResp other) : this() {
      rewards_ = other.rewards_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityDrawResp Clone() {
      return new ActivityDrawResp(this);
    }

    /// <summary>Field number for the "rewards" field.</summary>
    public const int RewardsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Article.Article> _repeated_rewards_codec
        = pb::FieldCodec.ForMessage(10, global::Article.Article.Parser);
    private readonly pbc::RepeatedField<global::Article.Article> rewards_ = new pbc::RepeatedField<global::Article.Article>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Article.Article> Rewards {
      get { return rewards_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityDrawResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityDrawResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!rewards_.Equals(other.rewards_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= rewards_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      rewards_.WriteTo(output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      rewards_.WriteTo(ref output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += rewards_.CalculateSize(_repeated_rewards_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityDrawResp other) {
      if (other == null) {
        return;
      }
      rewards_.Add(other.rewards_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            rewards_.AddEntriesFrom(input, _repeated_rewards_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            rewards_.AddEntriesFrom(ref input, _repeated_rewards_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4007 活动通用购买协议
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityBuyReq : pb::IMessage<ActivityBuyReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityBuyReq> _parser = new pb::MessageParser<ActivityBuyReq>(() => new ActivityBuyReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityBuyReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBuyReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBuyReq(ActivityBuyReq other) : this() {
      type_ = other.type_;
      template_ = other.template_;
      loopTimes_ = other.loopTimes_;
      drawId_ = other.drawId_;
      extraArgs_ = other.extraArgs_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBuyReq Clone() {
      return new ActivityBuyReq(this);
    }

    /// <summary>Field number for the "type" field.</summary>
    public const int TypeFieldNumber = 1;
    private global::PbGameconfig.activity_type type_ = global::PbGameconfig.activity_type.Nil;
    /// <summary>
    /// 活动类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_type Type {
      get { return type_; }
      set {
        type_ = value;
      }
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 2;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "loop_times" field.</summary>
    public const int LoopTimesFieldNumber = 3;
    private uint loopTimes_;
    /// <summary>
    /// 循环次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint LoopTimes {
      get { return loopTimes_; }
      set {
        loopTimes_ = value;
      }
    }

    /// <summary>Field number for the "draw_id" field.</summary>
    public const int DrawIdFieldNumber = 4;
    private ulong drawId_;
    /// <summary>
    /// 购买的id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong DrawId {
      get { return drawId_; }
      set {
        drawId_ = value;
      }
    }

    /// <summary>Field number for the "extra_args" field.</summary>
    public const int ExtraArgsFieldNumber = 5;
    private static readonly pb::FieldCodec<ulong> _repeated_extraArgs_codec
        = pb::FieldCodec.ForUInt64(42);
    private readonly pbc::RepeatedField<ulong> extraArgs_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// 额外参数(根据具体活动而定，无需额外参数时传空)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> ExtraArgs {
      get { return extraArgs_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityBuyReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityBuyReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Type != other.Type) return false;
      if (Template != other.Template) return false;
      if (LoopTimes != other.LoopTimes) return false;
      if (DrawId != other.DrawId) return false;
      if(!extraArgs_.Equals(other.extraArgs_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Type != global::PbGameconfig.activity_type.Nil) hash ^= Type.GetHashCode();
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      if (LoopTimes != 0) hash ^= LoopTimes.GetHashCode();
      if (DrawId != 0UL) hash ^= DrawId.GetHashCode();
      hash ^= extraArgs_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (DrawId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(DrawId);
      }
      extraArgs_.WriteTo(output, _repeated_extraArgs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Type != global::PbGameconfig.activity_type.Nil) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(LoopTimes);
      }
      if (DrawId != 0UL) {
        output.WriteRawTag(32);
        output.WriteUInt64(DrawId);
      }
      extraArgs_.WriteTo(ref output, _repeated_extraArgs_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Type != global::PbGameconfig.activity_type.Nil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Type);
      }
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      if (LoopTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(LoopTimes);
      }
      if (DrawId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(DrawId);
      }
      size += extraArgs_.CalculateSize(_repeated_extraArgs_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityBuyReq other) {
      if (other == null) {
        return;
      }
      if (other.Type != global::PbGameconfig.activity_type.Nil) {
        Type = other.Type;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      if (other.LoopTimes != 0) {
        LoopTimes = other.LoopTimes;
      }
      if (other.DrawId != 0UL) {
        DrawId = other.DrawId;
      }
      extraArgs_.Add(other.extraArgs_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 32: {
            DrawId = input.ReadUInt64();
            break;
          }
          case 42:
          case 40: {
            extraArgs_.AddEntriesFrom(input, _repeated_extraArgs_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Type = (global::PbGameconfig.activity_type) input.ReadEnum();
            break;
          }
          case 16: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 24: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 32: {
            DrawId = input.ReadUInt64();
            break;
          }
          case 42:
          case 40: {
            extraArgs_.AddEntriesFrom(ref input, _repeated_extraArgs_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityBuyResp : pb::IMessage<ActivityBuyResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityBuyResp> _parser = new pb::MessageParser<ActivityBuyResp>(() => new ActivityBuyResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityBuyResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBuyResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBuyResp(ActivityBuyResp other) : this() {
      rewards_ = other.rewards_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBuyResp Clone() {
      return new ActivityBuyResp(this);
    }

    /// <summary>Field number for the "rewards" field.</summary>
    public const int RewardsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Article.Article> _repeated_rewards_codec
        = pb::FieldCodec.ForMessage(10, global::Article.Article.Parser);
    private readonly pbc::RepeatedField<global::Article.Article> rewards_ = new pbc::RepeatedField<global::Article.Article>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Article.Article> Rewards {
      get { return rewards_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityBuyResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityBuyResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!rewards_.Equals(other.rewards_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= rewards_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      rewards_.WriteTo(output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      rewards_.WriteTo(ref output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += rewards_.CalculateSize(_repeated_rewards_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityBuyResp other) {
      if (other == null) {
        return;
      }
      rewards_.Add(other.rewards_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            rewards_.AddEntriesFrom(input, _repeated_rewards_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            rewards_.AddEntriesFrom(ref input, _repeated_rewards_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// ------------------------------------------英雄升星
  /// 英雄升星数据结构
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityHeroStarCondition : pb::IMessage<ActivityHeroStarCondition>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityHeroStarCondition> _parser = new pb::MessageParser<ActivityHeroStarCondition>(() => new ActivityHeroStarCondition());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityHeroStarCondition> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityHeroStarCondition() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityHeroStarCondition(ActivityHeroStarCondition other) : this() {
      id_ = other.id_;
      starConditions_ = other.starConditions_.Clone();
      rewards_ = other.rewards_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityHeroStarCondition Clone() {
      return new ActivityHeroStarCondition(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private ulong id_;
    /// <summary>
    /// 条件id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "star_conditions" field.</summary>
    public const int StarConditionsFieldNumber = 2;
    private static readonly pb::FieldCodec<uint> _repeated_starConditions_codec
        = pb::FieldCodec.ForUInt32(18);
    private readonly pbc::RepeatedField<uint> starConditions_ = new pbc::RepeatedField<uint>();
    /// <summary>
    /// 星级条件
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> StarConditions {
      get { return starConditions_; }
    }

    /// <summary>Field number for the "rewards" field.</summary>
    public const int RewardsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::PbGameconfig.reward> _repeated_rewards_codec
        = pb::FieldCodec.ForMessage(26, global::PbGameconfig.reward.Parser);
    private readonly pbc::RepeatedField<global::PbGameconfig.reward> rewards_ = new pbc::RepeatedField<global::PbGameconfig.reward>();
    /// <summary>
    /// 奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.reward> Rewards {
      get { return rewards_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityHeroStarCondition);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityHeroStarCondition other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if(!starConditions_.Equals(other.starConditions_)) return false;
      if(!rewards_.Equals(other.rewards_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0UL) hash ^= Id.GetHashCode();
      hash ^= starConditions_.GetHashCode();
      hash ^= rewards_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      starConditions_.WriteTo(output, _repeated_starConditions_codec);
      rewards_.WriteTo(output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      starConditions_.WriteTo(ref output, _repeated_starConditions_codec);
      rewards_.WriteTo(ref output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      size += starConditions_.CalculateSize(_repeated_starConditions_codec);
      size += rewards_.CalculateSize(_repeated_rewards_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityHeroStarCondition other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      starConditions_.Add(other.starConditions_);
      rewards_.Add(other.rewards_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            starConditions_.AddEntriesFrom(input, _repeated_starConditions_codec);
            break;
          }
          case 26: {
            rewards_.AddEntriesFrom(input, _repeated_rewards_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            starConditions_.AddEntriesFrom(ref input, _repeated_starConditions_codec);
            break;
          }
          case 26: {
            rewards_.AddEntriesFrom(ref input, _repeated_rewards_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4051 英雄升星活动数据推送
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushActivityHeroStarConfig : pb::IMessage<PushActivityHeroStarConfig>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushActivityHeroStarConfig> _parser = new pb::MessageParser<PushActivityHeroStarConfig>(() => new PushActivityHeroStarConfig());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushActivityHeroStarConfig> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityHeroStarConfig() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityHeroStarConfig(PushActivityHeroStarConfig other) : this() {
      template_ = other.template_;
      heroIds_ = other.heroIds_.Clone();
      conditions_ = other.conditions_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityHeroStarConfig Clone() {
      return new PushActivityHeroStarConfig(this);
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 1;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "hero_ids" field.</summary>
    public const int HeroIdsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::PbGameconfig.itemid> _repeated_heroIds_codec
        = pb::FieldCodec.ForEnum(18, x => (int) x, x => (global::PbGameconfig.itemid) x);
    private readonly pbc::RepeatedField<global::PbGameconfig.itemid> heroIds_ = new pbc::RepeatedField<global::PbGameconfig.itemid>();
    /// <summary>
    /// 英雄id列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.itemid> HeroIds {
      get { return heroIds_; }
    }

    /// <summary>Field number for the "conditions" field.</summary>
    public const int ConditionsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Activity.ActivityHeroStarCondition> _repeated_conditions_codec
        = pb::FieldCodec.ForMessage(26, global::Activity.ActivityHeroStarCondition.Parser);
    private readonly pbc::RepeatedField<global::Activity.ActivityHeroStarCondition> conditions_ = new pbc::RepeatedField<global::Activity.ActivityHeroStarCondition>();
    /// <summary>
    /// 条件列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Activity.ActivityHeroStarCondition> Conditions {
      get { return conditions_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushActivityHeroStarConfig);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushActivityHeroStarConfig other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Template != other.Template) return false;
      if(!heroIds_.Equals(other.heroIds_)) return false;
      if(!conditions_.Equals(other.conditions_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      hash ^= heroIds_.GetHashCode();
      hash ^= conditions_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      heroIds_.WriteTo(output, _repeated_heroIds_codec);
      conditions_.WriteTo(output, _repeated_conditions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      heroIds_.WriteTo(ref output, _repeated_heroIds_codec);
      conditions_.WriteTo(ref output, _repeated_conditions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      size += heroIds_.CalculateSize(_repeated_heroIds_codec);
      size += conditions_.CalculateSize(_repeated_conditions_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushActivityHeroStarConfig other) {
      if (other == null) {
        return;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      heroIds_.Add(other.heroIds_);
      conditions_.Add(other.conditions_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 18:
          case 16: {
            heroIds_.AddEntriesFrom(input, _repeated_heroIds_codec);
            break;
          }
          case 26: {
            conditions_.AddEntriesFrom(input, _repeated_conditions_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 18:
          case 16: {
            heroIds_.AddEntriesFrom(ref input, _repeated_heroIds_codec);
            break;
          }
          case 26: {
            conditions_.AddEntriesFrom(ref input, _repeated_conditions_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4052 英雄升星活动数据推送
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushActivityHeroStarData : pb::IMessage<PushActivityHeroStarData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushActivityHeroStarData> _parser = new pb::MessageParser<PushActivityHeroStarData>(() => new PushActivityHeroStarData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushActivityHeroStarData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityHeroStarData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityHeroStarData(PushActivityHeroStarData other) : this() {
      template_ = other.template_;
      loopTimes_ = other.loopTimes_;
      drawIds_ = other.drawIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityHeroStarData Clone() {
      return new PushActivityHeroStarData(this);
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 1;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "loop_times" field.</summary>
    public const int LoopTimesFieldNumber = 2;
    private uint loopTimes_;
    /// <summary>
    /// 循环次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint LoopTimes {
      get { return loopTimes_; }
      set {
        loopTimes_ = value;
      }
    }

    /// <summary>Field number for the "draw_ids" field.</summary>
    public const int DrawIdsFieldNumber = 3;
    private static readonly pb::FieldCodec<ulong> _repeated_drawIds_codec
        = pb::FieldCodec.ForUInt64(26);
    private readonly pbc::RepeatedField<ulong> drawIds_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// 已领取的条件id列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> DrawIds {
      get { return drawIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushActivityHeroStarData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushActivityHeroStarData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Template != other.Template) return false;
      if (LoopTimes != other.LoopTimes) return false;
      if(!drawIds_.Equals(other.drawIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      if (LoopTimes != 0) hash ^= LoopTimes.GetHashCode();
      hash ^= drawIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(LoopTimes);
      }
      drawIds_.WriteTo(output, _repeated_drawIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(LoopTimes);
      }
      drawIds_.WriteTo(ref output, _repeated_drawIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      if (LoopTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(LoopTimes);
      }
      size += drawIds_.CalculateSize(_repeated_drawIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushActivityHeroStarData other) {
      if (other == null) {
        return;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      if (other.LoopTimes != 0) {
        LoopTimes = other.LoopTimes;
      }
      drawIds_.Add(other.drawIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 16: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 26:
          case 24: {
            drawIds_.AddEntriesFrom(input, _repeated_drawIds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 16: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 26:
          case 24: {
            drawIds_.AddEntriesFrom(ref input, _repeated_drawIds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// -------------------------------------累充
  /// 累充数据结构
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityRechargeScore : pb::IMessage<ActivityRechargeScore>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityRechargeScore> _parser = new pb::MessageParser<ActivityRechargeScore>(() => new ActivityRechargeScore());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityRechargeScore> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityRechargeScore() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityRechargeScore(ActivityRechargeScore other) : this() {
      id_ = other.id_;
      score_ = other.score_;
      rewards_ = other.rewards_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityRechargeScore Clone() {
      return new ActivityRechargeScore(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private ulong id_;
    /// <summary>
    /// 条件id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "score" field.</summary>
    public const int ScoreFieldNumber = 2;
    private uint score_;
    /// <summary>
    /// 积分
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Score {
      get { return score_; }
      set {
        score_ = value;
      }
    }

    /// <summary>Field number for the "rewards" field.</summary>
    public const int RewardsFieldNumber = 3;
    private static readonly pb::FieldCodec<global::PbGameconfig.reward> _repeated_rewards_codec
        = pb::FieldCodec.ForMessage(26, global::PbGameconfig.reward.Parser);
    private readonly pbc::RepeatedField<global::PbGameconfig.reward> rewards_ = new pbc::RepeatedField<global::PbGameconfig.reward>();
    /// <summary>
    /// 奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.reward> Rewards {
      get { return rewards_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityRechargeScore);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityRechargeScore other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Score != other.Score) return false;
      if(!rewards_.Equals(other.rewards_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0UL) hash ^= Id.GetHashCode();
      if (Score != 0) hash ^= Score.GetHashCode();
      hash ^= rewards_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      if (Score != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Score);
      }
      rewards_.WriteTo(output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      if (Score != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Score);
      }
      rewards_.WriteTo(ref output, _repeated_rewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      if (Score != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Score);
      }
      size += rewards_.CalculateSize(_repeated_rewards_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityRechargeScore other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      if (other.Score != 0) {
        Score = other.Score;
      }
      rewards_.Add(other.rewards_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 16: {
            Score = input.ReadUInt32();
            break;
          }
          case 26: {
            rewards_.AddEntriesFrom(input, _repeated_rewards_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 16: {
            Score = input.ReadUInt32();
            break;
          }
          case 26: {
            rewards_.AddEntriesFrom(ref input, _repeated_rewards_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4061 累充活动配置数据推送
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushActivityRechargeConfig : pb::IMessage<PushActivityRechargeConfig>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushActivityRechargeConfig> _parser = new pb::MessageParser<PushActivityRechargeConfig>(() => new PushActivityRechargeConfig());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushActivityRechargeConfig> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityRechargeConfig() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityRechargeConfig(PushActivityRechargeConfig other) : this() {
      template_ = other.template_;
      scoreRewards_ = other.scoreRewards_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityRechargeConfig Clone() {
      return new PushActivityRechargeConfig(this);
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 1;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "scoreRewards" field.</summary>
    public const int ScoreRewardsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Activity.ActivityRechargeScore> _repeated_scoreRewards_codec
        = pb::FieldCodec.ForMessage(18, global::Activity.ActivityRechargeScore.Parser);
    private readonly pbc::RepeatedField<global::Activity.ActivityRechargeScore> scoreRewards_ = new pbc::RepeatedField<global::Activity.ActivityRechargeScore>();
    /// <summary>
    /// 积分奖励列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Activity.ActivityRechargeScore> ScoreRewards {
      get { return scoreRewards_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushActivityRechargeConfig);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushActivityRechargeConfig other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Template != other.Template) return false;
      if(!scoreRewards_.Equals(other.scoreRewards_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      hash ^= scoreRewards_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      scoreRewards_.WriteTo(output, _repeated_scoreRewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      scoreRewards_.WriteTo(ref output, _repeated_scoreRewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      size += scoreRewards_.CalculateSize(_repeated_scoreRewards_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushActivityRechargeConfig other) {
      if (other == null) {
        return;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      scoreRewards_.Add(other.scoreRewards_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 18: {
            scoreRewards_.AddEntriesFrom(input, _repeated_scoreRewards_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 18: {
            scoreRewards_.AddEntriesFrom(ref input, _repeated_scoreRewards_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4062 累充活动玩家数据推送
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushActivityRechargeData : pb::IMessage<PushActivityRechargeData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushActivityRechargeData> _parser = new pb::MessageParser<PushActivityRechargeData>(() => new PushActivityRechargeData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushActivityRechargeData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityRechargeData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityRechargeData(PushActivityRechargeData other) : this() {
      template_ = other.template_;
      loopTimes_ = other.loopTimes_;
      score_ = other.score_;
      drawIds_ = other.drawIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityRechargeData Clone() {
      return new PushActivityRechargeData(this);
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 1;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "loop_times" field.</summary>
    public const int LoopTimesFieldNumber = 2;
    private uint loopTimes_;
    /// <summary>
    /// 循环次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint LoopTimes {
      get { return loopTimes_; }
      set {
        loopTimes_ = value;
      }
    }

    /// <summary>Field number for the "score" field.</summary>
    public const int ScoreFieldNumber = 3;
    private uint score_;
    /// <summary>
    /// 当前积分
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Score {
      get { return score_; }
      set {
        score_ = value;
      }
    }

    /// <summary>Field number for the "draw_ids" field.</summary>
    public const int DrawIdsFieldNumber = 4;
    private static readonly pb::FieldCodec<ulong> _repeated_drawIds_codec
        = pb::FieldCodec.ForUInt64(34);
    private readonly pbc::RepeatedField<ulong> drawIds_ = new pbc::RepeatedField<ulong>();
    /// <summary>
    /// 已领取的id列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> DrawIds {
      get { return drawIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushActivityRechargeData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushActivityRechargeData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Template != other.Template) return false;
      if (LoopTimes != other.LoopTimes) return false;
      if (Score != other.Score) return false;
      if(!drawIds_.Equals(other.drawIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      if (LoopTimes != 0) hash ^= LoopTimes.GetHashCode();
      if (Score != 0) hash ^= Score.GetHashCode();
      hash ^= drawIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(LoopTimes);
      }
      if (Score != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Score);
      }
      drawIds_.WriteTo(output, _repeated_drawIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(LoopTimes);
      }
      if (Score != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Score);
      }
      drawIds_.WriteTo(ref output, _repeated_drawIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      if (LoopTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(LoopTimes);
      }
      if (Score != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Score);
      }
      size += drawIds_.CalculateSize(_repeated_drawIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushActivityRechargeData other) {
      if (other == null) {
        return;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      if (other.LoopTimes != 0) {
        LoopTimes = other.LoopTimes;
      }
      if (other.Score != 0) {
        Score = other.Score;
      }
      drawIds_.Add(other.drawIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 16: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 24: {
            Score = input.ReadUInt32();
            break;
          }
          case 34:
          case 32: {
            drawIds_.AddEntriesFrom(input, _repeated_drawIds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 16: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 24: {
            Score = input.ReadUInt32();
            break;
          }
          case 34:
          case 32: {
            drawIds_.AddEntriesFrom(ref input, _repeated_drawIds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// ----------------------------------------战令
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityBattlePassScore : pb::IMessage<ActivityBattlePassScore>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityBattlePassScore> _parser = new pb::MessageParser<ActivityBattlePassScore>(() => new ActivityBattlePassScore());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityBattlePassScore> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBattlePassScore() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBattlePassScore(ActivityBattlePassScore other) : this() {
      id_ = other.id_;
      score_ = other.score_;
      reward0_ = other.reward0_.Clone();
      reward1_ = other.reward1_.Clone();
      reward2_ = other.reward2_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBattlePassScore Clone() {
      return new ActivityBattlePassScore(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private ulong id_;
    /// <summary>
    /// 条件id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "score" field.</summary>
    public const int ScoreFieldNumber = 2;
    private uint score_;
    /// <summary>
    /// 积分
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Score {
      get { return score_; }
      set {
        score_ = value;
      }
    }

    /// <summary>Field number for the "reward0" field.</summary>
    public const int Reward0FieldNumber = 3;
    private static readonly pb::FieldCodec<global::PbGameconfig.reward> _repeated_reward0_codec
        = pb::FieldCodec.ForMessage(26, global::PbGameconfig.reward.Parser);
    private readonly pbc::RepeatedField<global::PbGameconfig.reward> reward0_ = new pbc::RepeatedField<global::PbGameconfig.reward>();
    /// <summary>
    /// 免费档奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.reward> Reward0 {
      get { return reward0_; }
    }

    /// <summary>Field number for the "reward1" field.</summary>
    public const int Reward1FieldNumber = 4;
    private static readonly pb::FieldCodec<global::PbGameconfig.reward> _repeated_reward1_codec
        = pb::FieldCodec.ForMessage(34, global::PbGameconfig.reward.Parser);
    private readonly pbc::RepeatedField<global::PbGameconfig.reward> reward1_ = new pbc::RepeatedField<global::PbGameconfig.reward>();
    /// <summary>
    /// 付费1档奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.reward> Reward1 {
      get { return reward1_; }
    }

    /// <summary>Field number for the "reward2" field.</summary>
    public const int Reward2FieldNumber = 5;
    private static readonly pb::FieldCodec<global::PbGameconfig.reward> _repeated_reward2_codec
        = pb::FieldCodec.ForMessage(42, global::PbGameconfig.reward.Parser);
    private readonly pbc::RepeatedField<global::PbGameconfig.reward> reward2_ = new pbc::RepeatedField<global::PbGameconfig.reward>();
    /// <summary>
    /// 付费2档奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.reward> Reward2 {
      get { return reward2_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityBattlePassScore);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityBattlePassScore other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Score != other.Score) return false;
      if(!reward0_.Equals(other.reward0_)) return false;
      if(!reward1_.Equals(other.reward1_)) return false;
      if(!reward2_.Equals(other.reward2_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0UL) hash ^= Id.GetHashCode();
      if (Score != 0) hash ^= Score.GetHashCode();
      hash ^= reward0_.GetHashCode();
      hash ^= reward1_.GetHashCode();
      hash ^= reward2_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      if (Score != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Score);
      }
      reward0_.WriteTo(output, _repeated_reward0_codec);
      reward1_.WriteTo(output, _repeated_reward1_codec);
      reward2_.WriteTo(output, _repeated_reward2_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      if (Score != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Score);
      }
      reward0_.WriteTo(ref output, _repeated_reward0_codec);
      reward1_.WriteTo(ref output, _repeated_reward1_codec);
      reward2_.WriteTo(ref output, _repeated_reward2_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      if (Score != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Score);
      }
      size += reward0_.CalculateSize(_repeated_reward0_codec);
      size += reward1_.CalculateSize(_repeated_reward1_codec);
      size += reward2_.CalculateSize(_repeated_reward2_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityBattlePassScore other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      if (other.Score != 0) {
        Score = other.Score;
      }
      reward0_.Add(other.reward0_);
      reward1_.Add(other.reward1_);
      reward2_.Add(other.reward2_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 16: {
            Score = input.ReadUInt32();
            break;
          }
          case 26: {
            reward0_.AddEntriesFrom(input, _repeated_reward0_codec);
            break;
          }
          case 34: {
            reward1_.AddEntriesFrom(input, _repeated_reward1_codec);
            break;
          }
          case 42: {
            reward2_.AddEntriesFrom(input, _repeated_reward2_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 16: {
            Score = input.ReadUInt32();
            break;
          }
          case 26: {
            reward0_.AddEntriesFrom(ref input, _repeated_reward0_codec);
            break;
          }
          case 34: {
            reward1_.AddEntriesFrom(ref input, _repeated_reward1_codec);
            break;
          }
          case 42: {
            reward2_.AddEntriesFrom(ref input, _repeated_reward2_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4071 战令配置推送
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushActivityBattlePassConfig : pb::IMessage<PushActivityBattlePassConfig>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushActivityBattlePassConfig> _parser = new pb::MessageParser<PushActivityBattlePassConfig>(() => new PushActivityBattlePassConfig());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushActivityBattlePassConfig> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityBattlePassConfig() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityBattlePassConfig(PushActivityBattlePassConfig other) : this() {
      template_ = other.template_;
      scoreRewards_ = other.scoreRewards_.Clone();
      boxScore_ = other.boxScore_;
      boxRewards_ = other.boxRewards_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityBattlePassConfig Clone() {
      return new PushActivityBattlePassConfig(this);
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 1;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "scoreRewards" field.</summary>
    public const int ScoreRewardsFieldNumber = 2;
    private static readonly pb::FieldCodec<global::Activity.ActivityBattlePassScore> _repeated_scoreRewards_codec
        = pb::FieldCodec.ForMessage(18, global::Activity.ActivityBattlePassScore.Parser);
    private readonly pbc::RepeatedField<global::Activity.ActivityBattlePassScore> scoreRewards_ = new pbc::RepeatedField<global::Activity.ActivityBattlePassScore>();
    /// <summary>
    /// 积分奖励列表
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Activity.ActivityBattlePassScore> ScoreRewards {
      get { return scoreRewards_; }
    }

    /// <summary>Field number for the "box_score" field.</summary>
    public const int BoxScoreFieldNumber = 3;
    private uint boxScore_;
    /// <summary>
    /// 兑换宝箱所需积分
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BoxScore {
      get { return boxScore_; }
      set {
        boxScore_ = value;
      }
    }

    /// <summary>Field number for the "box_rewards" field.</summary>
    public const int BoxRewardsFieldNumber = 4;
    private static readonly pb::FieldCodec<global::PbGameconfig.reward> _repeated_boxRewards_codec
        = pb::FieldCodec.ForMessage(34, global::PbGameconfig.reward.Parser);
    private readonly pbc::RepeatedField<global::PbGameconfig.reward> boxRewards_ = new pbc::RepeatedField<global::PbGameconfig.reward>();
    /// <summary>
    /// 宝箱奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::PbGameconfig.reward> BoxRewards {
      get { return boxRewards_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushActivityBattlePassConfig);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushActivityBattlePassConfig other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Template != other.Template) return false;
      if(!scoreRewards_.Equals(other.scoreRewards_)) return false;
      if (BoxScore != other.BoxScore) return false;
      if(!boxRewards_.Equals(other.boxRewards_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      hash ^= scoreRewards_.GetHashCode();
      if (BoxScore != 0) hash ^= BoxScore.GetHashCode();
      hash ^= boxRewards_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      scoreRewards_.WriteTo(output, _repeated_scoreRewards_codec);
      if (BoxScore != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(BoxScore);
      }
      boxRewards_.WriteTo(output, _repeated_boxRewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      scoreRewards_.WriteTo(ref output, _repeated_scoreRewards_codec);
      if (BoxScore != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(BoxScore);
      }
      boxRewards_.WriteTo(ref output, _repeated_boxRewards_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      size += scoreRewards_.CalculateSize(_repeated_scoreRewards_codec);
      if (BoxScore != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BoxScore);
      }
      size += boxRewards_.CalculateSize(_repeated_boxRewards_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushActivityBattlePassConfig other) {
      if (other == null) {
        return;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      scoreRewards_.Add(other.scoreRewards_);
      if (other.BoxScore != 0) {
        BoxScore = other.BoxScore;
      }
      boxRewards_.Add(other.boxRewards_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 18: {
            scoreRewards_.AddEntriesFrom(input, _repeated_scoreRewards_codec);
            break;
          }
          case 24: {
            BoxScore = input.ReadUInt32();
            break;
          }
          case 34: {
            boxRewards_.AddEntriesFrom(input, _repeated_boxRewards_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 18: {
            scoreRewards_.AddEntriesFrom(ref input, _repeated_scoreRewards_codec);
            break;
          }
          case 24: {
            BoxScore = input.ReadUInt32();
            break;
          }
          case 34: {
            boxRewards_.AddEntriesFrom(ref input, _repeated_boxRewards_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class ActivityBattlePassDraw : pb::IMessage<ActivityBattlePassDraw>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ActivityBattlePassDraw> _parser = new pb::MessageParser<ActivityBattlePassDraw>(() => new ActivityBattlePassDraw());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ActivityBattlePassDraw> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBattlePassDraw() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBattlePassDraw(ActivityBattlePassDraw other) : this() {
      id_ = other.id_;
      drawGrades_ = other.drawGrades_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ActivityBattlePassDraw Clone() {
      return new ActivityBattlePassDraw(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private ulong id_;
    /// <summary>
    /// 条件id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "draw_grades" field.</summary>
    public const int DrawGradesFieldNumber = 2;
    private static readonly pb::FieldCodec<uint> _repeated_drawGrades_codec
        = pb::FieldCodec.ForUInt32(18);
    private readonly pbc::RepeatedField<uint> drawGrades_ = new pbc::RepeatedField<uint>();
    /// <summary>
    /// 领取了第几档奖励
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<uint> DrawGrades {
      get { return drawGrades_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ActivityBattlePassDraw);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ActivityBattlePassDraw other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if(!drawGrades_.Equals(other.drawGrades_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0UL) hash ^= Id.GetHashCode();
      hash ^= drawGrades_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      drawGrades_.WriteTo(output, _repeated_drawGrades_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(Id);
      }
      drawGrades_.WriteTo(ref output, _repeated_drawGrades_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(Id);
      }
      size += drawGrades_.CalculateSize(_repeated_drawGrades_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ActivityBattlePassDraw other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0UL) {
        Id = other.Id;
      }
      drawGrades_.Add(other.drawGrades_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            drawGrades_.AddEntriesFrom(input, _repeated_drawGrades_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            drawGrades_.AddEntriesFrom(ref input, _repeated_drawGrades_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 4072 战令数据推送
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushActivityBattlePassData : pb::IMessage<PushActivityBattlePassData>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushActivityBattlePassData> _parser = new pb::MessageParser<PushActivityBattlePassData>(() => new PushActivityBattlePassData());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushActivityBattlePassData> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Activity.ActivityReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityBattlePassData() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityBattlePassData(PushActivityBattlePassData other) : this() {
      template_ = other.template_;
      loopTimes_ = other.loopTimes_;
      score_ = other.score_;
      draws_ = other.draws_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushActivityBattlePassData Clone() {
      return new PushActivityBattlePassData(this);
    }

    /// <summary>Field number for the "template" field.</summary>
    public const int TemplateFieldNumber = 1;
    private global::PbGameconfig.activity_template template_ = global::PbGameconfig.activity_template._0;
    /// <summary>
    /// 活动模版
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::PbGameconfig.activity_template Template {
      get { return template_; }
      set {
        template_ = value;
      }
    }

    /// <summary>Field number for the "loop_times" field.</summary>
    public const int LoopTimesFieldNumber = 2;
    private uint loopTimes_;
    /// <summary>
    /// 循环次数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint LoopTimes {
      get { return loopTimes_; }
      set {
        loopTimes_ = value;
      }
    }

    /// <summary>Field number for the "score" field.</summary>
    public const int ScoreFieldNumber = 3;
    private uint score_;
    /// <summary>
    /// 当前积分
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Score {
      get { return score_; }
      set {
        score_ = value;
      }
    }

    /// <summary>Field number for the "draws" field.</summary>
    public const int DrawsFieldNumber = 4;
    private static readonly pb::FieldCodec<global::Activity.ActivityBattlePassDraw> _repeated_draws_codec
        = pb::FieldCodec.ForMessage(34, global::Activity.ActivityBattlePassDraw.Parser);
    private readonly pbc::RepeatedField<global::Activity.ActivityBattlePassDraw> draws_ = new pbc::RepeatedField<global::Activity.ActivityBattlePassDraw>();
    /// <summary>
    /// 奖励领取情况
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Activity.ActivityBattlePassDraw> Draws {
      get { return draws_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushActivityBattlePassData);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushActivityBattlePassData other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Template != other.Template) return false;
      if (LoopTimes != other.LoopTimes) return false;
      if (Score != other.Score) return false;
      if(!draws_.Equals(other.draws_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Template != global::PbGameconfig.activity_template._0) hash ^= Template.GetHashCode();
      if (LoopTimes != 0) hash ^= LoopTimes.GetHashCode();
      if (Score != 0) hash ^= Score.GetHashCode();
      hash ^= draws_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(LoopTimes);
      }
      if (Score != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Score);
      }
      draws_.WriteTo(output, _repeated_draws_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Template != global::PbGameconfig.activity_template._0) {
        output.WriteRawTag(8);
        output.WriteEnum((int) Template);
      }
      if (LoopTimes != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(LoopTimes);
      }
      if (Score != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(Score);
      }
      draws_.WriteTo(ref output, _repeated_draws_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Template != global::PbGameconfig.activity_template._0) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Template);
      }
      if (LoopTimes != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(LoopTimes);
      }
      if (Score != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Score);
      }
      size += draws_.CalculateSize(_repeated_draws_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushActivityBattlePassData other) {
      if (other == null) {
        return;
      }
      if (other.Template != global::PbGameconfig.activity_template._0) {
        Template = other.Template;
      }
      if (other.LoopTimes != 0) {
        LoopTimes = other.LoopTimes;
      }
      if (other.Score != 0) {
        Score = other.Score;
      }
      draws_.Add(other.draws_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 16: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 24: {
            Score = input.ReadUInt32();
            break;
          }
          case 34: {
            draws_.AddEntriesFrom(input, _repeated_draws_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Template = (global::PbGameconfig.activity_template) input.ReadEnum();
            break;
          }
          case 16: {
            LoopTimes = input.ReadUInt32();
            break;
          }
          case 24: {
            Score = input.ReadUInt32();
            break;
          }
          case 34: {
            draws_.AddEntriesFrom(ref input, _repeated_draws_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
