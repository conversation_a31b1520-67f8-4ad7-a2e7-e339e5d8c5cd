namespace Game.Hotfix
{
    public enum UISurvivorRefreshType
    {
        DispatchRefresh,        //派遣刷新
        UpStarRefresh,          //升星刷新
    }
    public class OpenSurvivorListParams
    {
        public uint m_SurvivorId = 0;
        public BuildingModule m_BuildingModule;
        public int m_Pos;
        public OpenSurvivorListParams(uint sId,BuildingModule buildingModule,int pos)
        {
            m_SurvivorId = sId;
            m_BuildingModule = buildingModule;
            m_Pos = pos;
        }
    }
    
    public class RefreshUIDispatchParams
    {
        public UISurvivorRefreshType m_RefreshType;
        public object m_userData;

        public RefreshUIDispatchParams(UISurvivorRefreshType refreshType, object mUserData = null)
        {
            m_RefreshType = refreshType;
            m_userData = mUserData;
        }
    }
}