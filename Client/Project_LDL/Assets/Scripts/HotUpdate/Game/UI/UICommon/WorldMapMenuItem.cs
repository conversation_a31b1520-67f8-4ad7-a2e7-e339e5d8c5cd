using System;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class WorldMapMenuItem : MonoBehaviour
    {
        [SerializeField] private GameObject goBody;
        [SerializeField] private GameObject goBtnTemplate;
        [SerializeField] private GameObject goBtnParent;
        
        private Camera m_MainCamera;
        
        public void ShowButtons(List<int> menuList,Action<build_menubutton> onBtnClick)
        {
            GameObjectUtility.DestroyAllChild(goBtnParent);
            
            if (menuList != null && menuList.Count > 0)
            {
                goBody.SetActive(true);
                foreach (var menuId in menuList)
                {
                    var menuCfg = Game.GameEntry.LDLTable.GetTableById<build_menubutton>(menuId);
                    if (menuCfg!=null)
                    {
                        var btnGo = Instantiate(this.goBtnTemplate, this.goBtnParent.transform);
                        var img = btnGo.GetComponent<UIImage>();
                        img.SetImage(menuCfg.icon_path);
                        
                        var btn = btnGo.GetComponent<UIButton>();
                        btn.onClick.AddListener(() =>
                        {
                            onBtnClick?.Invoke(menuCfg);
                        });
                    }
                }
                
                GameObjectUtility.SetBtnCircularLayout(goBtnParent.transform);
            }
            else
            {
                goBody.SetActive(false);
            }
        }
        
    }
}