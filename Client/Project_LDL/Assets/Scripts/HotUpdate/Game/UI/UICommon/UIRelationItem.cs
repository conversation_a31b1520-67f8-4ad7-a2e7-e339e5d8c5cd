using System;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.Serialization;

namespace Game.Hotfix
{
    public class UIRelationItem : MonoBehaviour
    {

        public List<UIImage> icons;
        public List<Transform> items;

        public UIText restrainTankMissile;
        public UIText restrainMissilePlane;
        public UIText restrainPlaneTank;
        
        public UIText buff3_0;
        public UIText buff3_2;
        public UIText buff4_0;
        public UIText buff5_0;

        private void OnEnable()
        {
            var table = GameEntry.LDLTable.GetKeyValueTable<battle_const_set>();
            Debug.LogError(table.team_services_30_buff);


            if (table == null) return;


            if (restrainTankMissile != null)
                restrainTankMissile.text =
                    ToolScriptExtend.GetLangFormat(522000002, (table.restrain_tanke_missile / 10000).ToString());
            if (restrainMissilePlane != null)
                restrainMissilePlane.text =
                    ToolScriptExtend.GetLangFormat(522000003, (table.restrain_missile_plane / 10000).ToString());
            if (restrainPlaneTank != null)
                restrainPlaneTank.text =
                    ToolScriptExtend.GetLangFormat(522000004, (table.restrain_plane_tanke / 10000).ToString());

            if (buff3_0 != null)
                buff3_0.text = ToolScriptExtend.GetLangFormat(522000010, (table.team_services_30_buff / 10000).ToString());
            if (buff3_2 != null)
                buff3_2.text = ToolScriptExtend.GetLangFormat(522000010, (table.team_services_32_buff / 10000).ToString());
            if (buff4_0 != null)
                buff4_0.text = ToolScriptExtend.GetLangFormat(522000010, (table.team_services_41_buff / 10000).ToString());
            if (buff5_0 != null)
                buff5_0.text = ToolScriptExtend.GetLangFormat(522000010, (table.team_services_50_buff / 10000).ToString());
            
        }


        public void SetRelation(List<hero_services> list,EnumBattleRelation relation)
        {
            for (int i = 0; i < icons.Count; i++)
            {
                var path = GetIconByType(list[i]);
                if (i < icons.Count)
                {
                    icons[i].transform.parent.gameObject.SetActive(!string.IsNullOrEmpty(path));
                    icons[i].SetImage(path);
                }
            }

            SetItemEnable(items[0], relation == EnumBattleRelation.Relation3_0);
            SetItemEnable(items[1], relation == EnumBattleRelation.Relation3_2);
            SetItemEnable(items[2], relation == EnumBattleRelation.Relation4_0);
            SetItemEnable(items[3], relation == EnumBattleRelation.Relation5_0);
        }

        private void SetItemEnable(Transform item,bool enable)
        {
            if (item == null)
                return;
            item.Find("iconEnable")?.gameObject.SetActive(enable);
            item.Find("iconDisable")?.gameObject.SetActive(!enable);
            
            item.Find("bgEnable")?.gameObject.SetActive(enable);
            item.Find("bgDisable")?.gameObject.SetActive(!enable);
        }
        
        private string GetIconByType(hero_services type)
        {
            if (type == hero_services.hero_services_aircraft)
                return "Assets/ResPackage/Sprite/ui_zhandou/chuzhan_buff_icon4.png";
            if (type == hero_services.hero_services_missile)
                return "Assets/ResPackage/Sprite/ui_zhandou/chuzhan_buff_icon5.png";
            if (type == hero_services.hero_services_tank)
                return "Assets/ResPackage/Sprite/ui_zhandou/chuzhan_buff_icon6.png";
            return "";
        }
        
    }
}