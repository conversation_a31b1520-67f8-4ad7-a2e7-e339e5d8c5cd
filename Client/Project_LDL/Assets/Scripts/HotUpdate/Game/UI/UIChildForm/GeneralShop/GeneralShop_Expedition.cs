using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    //远征商店
    public class GeneralShop_Expedition : GeneralShopLogic
    {
        public override void OnInit()
        {
            curStoreType = storetype.storetype_expedition;
            base.OnInit();
        }
        
        protected override void SetGridItemInfo(int index,GameObject obj)
        {
            var data = GoodsList[index];
            var goodsConfig = GameEntry.LogicData.GeneralShopData.GetGoodsConfigById(data.Id);
            if (goodsConfig == null) return;
            
            var goodsId = data.Id;
            var itemId = goodsConfig.goods.item_id;
            var itemCount = goodsConfig.goods.num;
            var priceId = goodsConfig.store_cost.item_id;
            var unitPrice = (int)goodsConfig.store_cost.num;
            var maxCount = goodsConfig.purchase_number;
            
            var root = obj.transform;
            var btn = root.Find("btn").GetComponent<UIButton>();
            var name = root.Find("btn/name").GetComponent<UIText>();
            var coin = root.Find("btn/price/coin").GetComponent<UIImage>();
            var price = root.Find("btn/price").GetComponent<UIText>();
            var rewardRoot = root.Find("btn/reward");
            var soldOutTip = root.Find("btn/soldOutTip");
            var buyCount = root.Find("btn/buyCount").GetComponent<UIText>();
            
            rewardRoot.localScale = Vector3.one *rewardSize;
            if (rewardRoot.childCount == 0)
            {
                Instantiate(rewardObj,rewardRoot);
            }

            name.text = ToolScriptExtend.GetItemName(itemId);
            var child = rewardRoot.GetChild(0);
            var module = child.GetComponent<UIItemModule>();
            if (module != null)
            {
                GameEntry.LogicData.MallData.SetRewardInfo(rewardRoot, child.gameObject, itemId, (int)itemCount,1.3f);
            }
            
            BindCheckBtnLogic(btn,data.Id);
            
            //判断是否售罄
            var isSoldOut = IsSoldOut(goodsId);
            ToolScriptExtend.SetGameObjectGrey(rewardRoot,isSoldOut);
            soldOutTip.gameObject.SetActive(isSoldOut);
            coin.gameObject.SetActive(!isSoldOut);
            price.gameObject.SetActive(!isSoldOut);
            buyCount.gameObject.SetActive(!isSoldOut);
            module.SetGrey(isSoldOut);
            
            if (!isSoldOut)
            {
                var boughtCount = GameEntry.LogicData.GeneralShopData.GetBoughtCount(curStoreType, goodsId);
                //同盟、远征商店这类道具，左侧数值为可购买数值，而不是已购买数值
                var showCount = maxCount - boughtCount;
                buyCount.text = $"{Mathf.Max(showCount,0)}/{maxCount}";
                GameEntry.LogicData.GeneralShopData.CheckCoinEnough(priceId,unitPrice , out var hex);
                price.text =  $"<color={hex}>{unitPrice}</color>";
                coin.SetImage(ToolScriptExtend.GetItemIcon(priceId));
            }
            else
            {
                //已售罄
                soldOutTip.GetComponent<UIText>().text = ToolScriptExtend.GetLang(1100275);
            }
        }
    }
}
