using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    //礼包商城
    public class Mall_GiftShop:SwitchPanelLogic
    {
        public GameObject childItem;
        public GameObject prefabRoot;
        public GameObject m_goRewardObj;
        public ScrollRect scrollRect;

        private Transform listRoot;
        private List<Gift.Gift> GiftList;
        private int sumCount = 0;

        private List<TimerNode> timeList;
        private bool IsTimer = false;//是否开始倒计时逻辑
        private MallData Manager;
        private class TimerNode
        {
            public int time;
            public UIText txt;
        }

        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            IsTimer = false;
            Manager = GameEntry.LogicData.MallData;
            prefabRoot.SetActive(false);
            listRoot = scrollRect.content.transform;
            timeList = new List<TimerNode>();
            ToolScriptExtend.ClearAllChild(listRoot);
            Manager.SetParticleDepth(childItem);
            Manager.RequestNewMsg(paymenttype.paymenttype_giftpackmall,InitPageView);
        }
        
        private void InitPageView()
        {
            GiftList = Manager.GetGiftList(Config.paymenttype.paymenttype_giftpackmall);
            sumCount = GiftList.Count;
            IsTimer = false;
            timeList.Clear();
            StartCoroutine(CreateGifts());
        }

        //再次打开
        public override void OnReOpen()
        {
            Manager.RequestNewMsg(paymenttype.paymenttype_giftpackmall,InitPageView);
        }
        
        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
            Manager.RequestNewMsg(paymenttype.paymenttype_giftpackmall,InitPageView);
        }
        
        //事件刷新
        public override void OnRefresh(object userData)
        {
            
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
            
        }
        
        //隐藏面板时逻辑
        public override void OnClose()
        {
            
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
            
        }

        //计时器逻辑
        public override void OnTimer()
        {
            if (IsTimer)
            {
                if(timeList.Count == 0) return;
                foreach (var node in timeList)
                {
                    if (node.time > 0)
                    {
                        node.time--;
                        node.txt.text = ToolScriptExtend.FormatTime(node.time);
                    }
                    else
                    {
                        //有礼包截止了，需要重新请求信息
                        IsTimer = false;
                        Manager.C2SSupermarketInfo((resp) =>
                        {
                            InitPageView();
                        });
                        break;
                    }
                }
            }
        }

        //资源释放
        public override void Release()
        {
            GiftList?.Clear();
            timeList?.Clear();
        }

        IEnumerator CreateGifts()
        {
            var childCount = listRoot.childCount;
            if (childCount > 0)
            {
                if (sumCount != childCount)
                {
                    GameEntry.LogicData.MallData.RecycleOrCreate(childItem,listRoot,sumCount);
                }
            }
            for (var i = 0; i < sumCount; i++)
            {
                yield return null;
                var obj =(i + 1 < childCount)? listRoot.GetChild(i).gameObject:Instantiate(childItem, listRoot);
                obj.SetActive(true);
                UpdateItemLogic(i, obj);
            }

            yield return null;
            IsTimer = true;
        }
        
        protected void UpdateItemLogic(int index, GameObject obj)
        {
            if (index >= GiftList.Count && index < 0) return;
            
            var msg = GiftList[index];
            var data = Manager.GetGiftConfig(msg.Id);
            if (data == null) return;

            // var paymentData = Manager.GetPaymentConfig(data.payment_id);
            
            var root = obj.transform;
            var title = root.Find("title").GetComponent<UIText>();//礼包名称
            var icon = root.Find("icon").GetComponent<UIImage>();//礼包图标
            var discount = root.Find("Image/discount").GetComponent<UIText>();//折扣
            var desc = root.Find("desc").GetComponent<UIText>();//礼包描述
            
            var rewardRoot = root.Find("Scroll View/Viewport/Content");
            var btn = root.Find("btn").GetComponent<UIButton>();//购买按钮
            var price = root.Find("btn/price").GetComponent<UIText>();//价格
            var soldOut = root.Find("soldOut");//售罄按钮
            var timerObj = root.Find("TimerObj");
            var timerTxt = timerObj.Find("txt").GetComponent<UIText>();
            
            var isSoldOut = IsSoldOut(msg.Id, msg.Amount);
            if(!isSoldOut){
                var time = (ulong)msg.EndAt - TimeComponent.Now;
                var recordTime = time <= 0 ? 0 : (int)time;
                timeList.Add(new TimerNode(){time = recordTime,txt = timerTxt});
                timerTxt.text = ToolScriptExtend.FormatTime(recordTime);
            }
            timerObj.gameObject.SetActive(!isSoldOut);
            btn.gameObject.SetActive(!isSoldOut);
            soldOut.gameObject.SetActive(isSoldOut);
            
            title.text = ToolScriptExtend.GetLang(data.gift_pack_name);
            icon.SetImage(data.gift_pack_icon);
            discount.text = $"{data.cost_effectiveness}%";
            desc.text = ToolScriptExtend.GetLang(data.gift_pack_desc);
            price.text = Manager.GetPrice(data.payment_id);
            
            BindBtnLogic(btn, () =>
            {
                GameEntry.PaymentData.Pay(data.payment_id);
            });
            
            Manager.CreateRechargeScore(data.payment_id,btn.transform);
            
            //奖励列表  需要额外将payment中同盟宝箱奖励加入到列表中
            var rewardList = Manager.GetRewardList(msg.Id);
            
            Manager.RecycleOrCreate(m_goRewardObj,rewardRoot,rewardList.Count);
            for (var i = 0; i < rewardList.Count; i++)
            {
                var child = rewardRoot.GetChild(i);
                var node = child.Find("node");
                node.localScale = Vector3.one*0.5f;
                var cell = rewardList[i];
                var com = child.GetComponentsInChildren<UIItemModule>();
                if (com.Length == 0)
                {
                    Instantiate(Manager.RewardObj,node);
                }
                Manager.SetRewardInfo(node, node.GetChild(0).gameObject, cell.item_id, (int)cell.num,1.3f);
            }
            
            var effect = root.Find("effect"); 
            Manager.SetParticleDepth(effect.gameObject);
        }
        
        /// <summary>
        /// 是否已售罄
        /// </summary>
        /// <param name="giftId">gift_pack中的id</param>
        /// <param name="boughtCount">已购买次数</param>
        /// <returns></returns>
        public bool IsSoldOut(int giftId,int boughtCount)
        {
            if (ToolScriptExtend.GetConfigById<gift_pack>(giftId, out var giftData))
            {
                return boughtCount >= giftData.buy_limit_times;
            }
            return false;
        }
    }
}
