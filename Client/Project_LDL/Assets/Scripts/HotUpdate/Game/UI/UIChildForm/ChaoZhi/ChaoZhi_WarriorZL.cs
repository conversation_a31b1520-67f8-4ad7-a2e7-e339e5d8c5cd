using System;
using System.Collections.Generic;
using System.Linq;
using Activity;
using Game.Hotfix.Config;
using Mosframe;
using Spine.Unity;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class ChaoZhi_WarriorZL:SwitchPanelLogic
    {
        [SerializeField] private UIText m_txtTitle;//标题
        [SerializeField] private UIText m_txtTimer; //倒计时文本
        [SerializeField] private SkeletonGraphic m_spuiRole;//活动spine
        [SerializeField] private UIText m_txtDiscount;//购买折扣
        [SerializeField] private UIText m_txtDesc;//高级奖励描述
        [SerializeField] private UIButton m_btnHelp;//说明按钮
        [SerializeField] private UIButton m_btnBuy;//购买按钮
        [SerializeField] private UIButton m_btnCheck;//英雄查看跳转
        [SerializeField] private UIImage m_imgScore;//积分图标
        [SerializeField] private UIText m_txtScore;//积分数量
        
        [SerializeField] private UISwitchTagGroup m_TagGroup;//标签节点
        
        [SerializeField] private GameObject m_goRewardNode;//奖励部分
        [SerializeField] private GameObject m_goDailyTaskNode;//每日任务部分
        [SerializeField] private GameObject m_goTargetTaskNode;//目标任务部分
        
        [SerializeField] private UIButton m_btnGetAll; //一键领取按钮
        
        //预制体
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goReward;
        [SerializeField] private GameObject m_goTagItem;
        [SerializeField] private GameObject m_goTaskItem;
        
        [SerializeField] private GameObject m_goItemObj;
        [SerializeField] private Transform m_goItemRoot;
        [SerializeField] private Slider m_sliderProgress;
        [SerializeField] private ScrollRect mainScroll;
        [SerializeField] private RectTransform mainContent;
        [SerializeField] private RectTransform sliderRect;        
        
        //每日任务列表
        public TableViewV m_DailyTableViewV;
        
        //目标任务列表
        public TableViewV m_TargetTableViewV;
        
        private bool isTimer = false;
        private int curIndex = -1;
        private string iconPath;
        private int timerCount; //倒计时数值
        
        private ActivityTime activityMsg;
        protected int TemplateId;
        private PushActivityBattlePassConfig ConfigData;
        private PushActivityBattlePassData MsgData;
        protected ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        protected float startRatio = 0;
        
        [SerializeField] private GameObject m_goInfiniteBox;
        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            m_goPrefab.SetActive(false);
            iconPath = "Sprite/ui_chaozhihuodong/icon_leichongjifen4.png";
            startRatio = 0.016f;
            var bindData = GetBindData();
            if (bindData != null)
            {
                if (bindData is ActivityTime data)
                {
                    activityMsg = data;
                    ChaoZhiManager.C2SLoadActivityInfo(activityMsg);
                }
            }
            
            m_DailyTableViewV.GetItemCount = () => 8;
            m_DailyTableViewV.GetItemGo = () => m_goTaskItem;
            m_DailyTableViewV.UpdateItemCell = UpdateTaskLogic;
            m_DailyTableViewV.InitTableViewByIndex(0);
            
            m_TargetTableViewV.GetItemCount = () => 10;
            m_TargetTableViewV.GetItemGo = () => m_goTaskItem;
            m_TargetTableViewV.UpdateItemCell = UpdateTaskLogic;
            m_TargetTableViewV.InitTableViewByIndex(0);
            
            var nameList = new List<string>()
            {
                ToolScriptExtend.GetLang(80100032),//"奖励", 
                ToolScriptExtend.GetLang(1100267), //每日任务
                ToolScriptExtend.GetLang(1100413),//"目标任务"
            };
            m_TagGroup.Init(m_goTagItem,m_TagGroup.transform,nameList, (index) =>
            {
                OnSwitchTagLogic(index);
            });
            
            // isTimer = false;
            // // Manager.ActivityZL.reward
            // var list = new List<(ChaoZhiData.ActivityZL, int)>()
            // {
            //     (ChaoZhiData.ActivityZL.reward,111),//奖励标签
            //     (ChaoZhiData.ActivityZL.dailyTask,222),//每日任务标签
            //     (ChaoZhiData.ActivityZL.targetTask,333),//目标任务标签
            // };
            //
            // InitPageView();
            
            // //购买所有礼包按钮
            // BindBtnLogic(m_btnGet, () =>
            // {
            //     // var req = new ActivityDrawReq
            //     // {
            //     //     Type = activityMsg.Type,
            //     //     Template = activityMsg.Template,
            //     //     LoopTimes = activityMsg.LoopTimes,
            //     //     DrawId = (ulong)Manager.GetRoleUpStarTaskId()
            //     // };
            //     // Manager.C2SActivityDrawReq(req, (resp) =>
            //     // {
            //     //     
            //     //     
            //     // });
            // });
            CheckMultiLanguage(gameObject);
        }
        
        //再次打开
        public override void OnReOpen()
        {
            
            
        }

        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
            
        }
        
        //事件刷新
        public override void OnRefresh(object userData)
        {
            var data = (ValueTuple<string, int>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "ChaoZhi_WarriorZL")
            {
                //1活动信息更新
                if (param == 1)
                {
                    TemplateId = (int)activityMsg.Template;
                    ConfigData = ChaoZhiManager.GetZLConfig(TemplateId);
                    MsgData = ChaoZhiManager.GetZLMsg(TemplateId);
                    InitConfigPageView();
                    InitPageView();
                }
            }
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
        }

        //隐藏面板时逻辑
        public override void OnClose()
        {
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
        }

        //计时器逻辑
        public override void OnTimer()
        {
            if (!isTimer) return;
            var temp = timerCount - 1;
            if (temp >= 0)
            {
                timerCount--;
                m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
                if (timerCount == 0)
                {
                    isTimer = false;
                    //初始化请求
                  
                }
            }
        }

        //资源释放
        public override void Release()
        {
            
        }

        private void OnSwitchTagLogic(int index)
        {
            m_goRewardNode.SetActive(index == 0);
            m_goDailyTaskNode.SetActive(index == 1);
            m_goTargetTaskNode.SetActive(index == 2);
            
            
            
        }
        
        private void InitConfigPageView()
        {
            var TemplateId = (int)MsgData.Template;
            if (!ToolScriptExtend.GetTable<activity_main>(out var table)) return;
            var activityMain = table.FirstOrDefault(x => x.id == TemplateId);
            if(activityMain == null)return;
            //活动标题
            m_txtTitle.text = ToolScriptExtend.GetLang(activityMain.name);
            
            //说明按钮
            BindBtnLogic(m_btnHelp, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, activityMain.activity_explain);
            });
            if (!ToolScriptExtend.GetTable<activity_battlepass_main>(out var table1)) return;
            var config = table1.FirstOrDefault(x => (int)x.activity_templateid == TemplateId);
            if(config == null)return;

            var heroId = (int)config.hero_image;
            //活动spine
            ToolScriptExtend.ShowHeroSpine(heroId,m_spuiRole);
            //积分信息：图标
            m_imgScore.SetImage("Sprite/ui_chaozhihuodong/icon_leichongjifen4.png");
            //查看英雄跳转
            BindBtnLogic(m_btnCheck, () =>
            {
                GameEntry.LogicData.HeroData.OpenHeroSingleForm((itemid)heroId);
            });


            if (ToolScriptExtend.GetTable<activity_battlepass_grade>(out var table2))
            {
                var gradeConfig = table2.FirstOrDefault(x => (int)x.activity_templateid == TemplateId);
                if (gradeConfig != null)
                {
                    //全购状态  性价比  原价  折扣价  累充积分
                    m_txtDiscount.text = $"{gradeConfig.effectiveness}%";
                    GameEntry.LogicData.MallData.CreateRechargeScore(gradeConfig.payment_id, m_btnBuy.transform,40,-90);
                    if (ToolScriptExtend.GetConfigById<payment>((int)gradeConfig.payment_id, out var data1))
                    {
                        var priceTxt = m_btnBuy.transform.Find("Text").GetComponent<UIText>();
                        priceTxt.text = ToolScriptExtend.GetLang(data1.price_lang_id);
                        //购买按钮
                        BindBtnLogic(m_btnBuy, () =>
                        {
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIZhanLingBuyForm,TemplateId);
                        });
                    }
                }
            }
            
            //高级奖励描述
            m_txtDesc.text = ToolScriptExtend.GetLang(1100412);
            
            //一键领取按钮
            BindBtnLogic(m_btnGetAll, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100426)//目前没有领取的奖励
                });
            });
        }

        private void InitPageView()
        {
            //活动倒计时
            timerCount = ChaoZhiManager.GetRemainTime((ulong)activityMsg.EndTime);
            isTimer = timerCount > 0;
            m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
            
            //积分信息数量
            m_txtScore.text = MsgData.Score.ToString();
            
            //购买状态显示
            var isSoldOut = false;
            m_txtDesc.gameObject.SetActive(!isSoldOut);
            m_btnBuy.gameObject.SetActive(!isSoldOut);
            
            //切页标签组
            //切页标签——奖励列表
            ShowMainReward();
            //切页标签——每日任务
            ShowDailyTask();
            //切页标签——目标任务
            ShowTargetTask();
            //无限宝箱
            ShowInfiniteBox();
            
            CalculateProgress();
        }
        
        //每日任务
        private void ShowDailyTask()
        {
             // if (!ToolScriptExtend.GetTable<activity_task>(out var table)) return;
             // var activityMain = table.Where(x => (int)x.activity_templateid == TemplateId).ToList();
             //
             // if (m_TableViewV.itemPrototype == null)
             // {
             //     m_TableViewV.InitTableViewByIndex(0);
             // }
             // else{
             //     m_TableViewV.ReloadData();
             // }
            
            
        }
        
        //目标任务
        private void ShowTargetTask()
        {
            
            
            
        }
        
        //无限宝箱
        private void ShowInfiniteBox()
        {
            var root = m_goInfiniteBox.transform;
            var m_txtInfiniteBoxDesc = root.Find("m_txtInfiniteBoxDesc").GetComponent<UIText>();
            var lockObj = root.Find("Lock");
            var m_sliderInfinity = root.Find("m_sliderInfinity").GetComponent<Slider>();
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var btnGray = root.Find("btnGray").GetComponent<UIButton>();
            var btnCheck = root.Find("Image/btnCheck").GetComponent<UIButton>();
            
            var scoreList = ConfigData.ScoreRewards.Select(x => x.Score).ToList();
            scoreList.Sort((a, b) => (int)a - (int)b);
            var maxValue = scoreList.Last();

            var isUnlock = MsgData.Score >= maxValue;
            lockObj.gameObject.SetActive(!isUnlock);
            m_sliderInfinity.gameObject.SetActive(isUnlock);
            btnGet.gameObject.SetActive(isUnlock);
            btnGray.gameObject.SetActive(isUnlock);
            if (isUnlock)
            {
                m_txtInfiniteBoxDesc.text = "开启需要积累战令点数";
                
            }
            else
            {
                m_txtInfiniteBoxDesc.text = ToolScriptExtend.GetLangFormat(1100416, maxValue.ToString()); //战令分数达到{0}后解锁
                
            }
            var rewards = new List<reward>();
            foreach (var reward in ConfigData.BoxRewards)
            {
                rewards.Add(new reward(){item_id = (itemid)reward.ItemId,num = reward.Num});
            }
           ToolScriptExtend.BindBtnLogic(btnCheck, () =>
           {
               GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardPreviewForm,rewards);
           });
        }

        //一键领取按钮状态判断
        private void CheckOneGetStatus()
        {
            
            
        }


        #region 奖励页签
        //奖励列表
        private void ShowMainReward()
        {
            var list = ConfigData.ScoreRewards.ToList();
            var itemCount = list.Count;
            AutoFitContent(itemCount);
            
            ToolScriptExtend.RecycleOrCreate(m_goItemObj,m_goItemRoot,itemCount);
            for (var i = 0; i < itemCount; i++)
            {
                var child = m_goItemRoot.GetChild(i);
                SetItemInfo(i,child,list[i]);
            }

            CheckMultiLanguage(gameObject);
        }
        
        protected void SetItemInfo(int index,Transform root,ActivityBattlePassScore scoreData)
        {
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var btnReGet = root.Find("btnReGet").GetComponent<UIButton>();
            var btnLock = root.Find("btnLock").GetComponent<UIButton>();
            var goFinish = root.Find("goFinish").gameObject;
            var scoreIcon = root.Find("scoreIcon").GetComponent<UIImage>();
            var scoreTxt = root.Find("scoreIcon/scoreTxt").GetComponent<UIText>();
            
            var freeReward = root.Find("freeReward");
            var payReward1 = root.Find("payReward1");
            var payReward2 = root.Find("payReward2");
            
            var status = 0;
            //奖励领取状态0：未解锁  1：可领取  2：已领取 3:继续领取
            btnLock.gameObject.SetActive(status == 0);
            btnGet.gameObject.SetActive(status == 1);
            goFinish.gameObject.SetActive(status == 2);
            btnReGet.gameObject.SetActive(status == 3);
            
            scoreTxt.text = scoreData.Score.ToString();
            if (!string.IsNullOrEmpty(iconPath))
            {
                scoreIcon.SetImage(iconPath);
            }
            
            //奖励
            //免费奖励
            var reward1 = scoreData.Reward0.ToList().First();
            ShowRewardMsg(true,freeReward, reward1);
            
            //付费第一档奖励
            var temp = scoreData.Reward1.ToList();
            ShowRewardMsg(true,payReward1, temp[0]);
            ShowRewardMsg(true,payReward2, temp[1]);
        }
        
        // 积分里程碑奖励领取状态  0：未解锁 1:可领取 2：继续领取 3：已领取
        private int CheckItemStatus(ulong id,int score)
        {
            //未解锁：积分未达成，未购买
            
            
            //可领取：免费未领取或者充值奖励未领取
            
            
            //继续领取：免费奖励已领取，未付费
            
            
            //已领取：免费奖励和充值奖励都已领取

            return 1;

        }
        
        private void ShowRewardMsg(bool isFree,Transform root,PbGameconfig.reward data)
        {
            var rewardData = new reward() { item_id = (itemid)data.ItemId, num = data.Num };
            
            if (root.childCount == 0)
            {
                var obj = Instantiate(m_goReward, root);
                var freeNode = obj.transform.Find("node");
                var effect = obj.transform.Find("effect");
                var lockObj = obj.transform.Find("btnLock");
                
                effect.gameObject.SetActive(!isFree);
                lockObj.gameObject.SetActive(!isFree);
                
                freeNode.localScale = Vector3.one * 0.6f;
                BagManager.CreatItem(freeNode,rewardData.item_id,rewardData.num, (module) =>
                {
                    module.SetClick(module.OpenTips);
                });
            }
            else
            {
                var child = root.GetComponentInChildren<UIItemModule>();
                if (child != null)
                {
                    ToolScriptExtend.SetItemObjInfo(child.transform.parent, child.gameObject, rewardData.item_id, (int)rewardData.num);
                }
            }
        }
        
        protected void AutoFitContent(int count)
        {
            var itemRect = m_goItemObj.GetComponent<RectTransform>();
            var itemHeight = itemRect.rect.height;
            var spacing = m_goItemRoot.GetComponent<VerticalLayoutGroup>().spacing;
            var heightSum = itemHeight * count + spacing * (count - 1);
            
            mainContent.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,heightSum);
            sliderRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,heightSum-50);
        }
        
        public void UpdateTaskLogic(int index, GameObject obj)
        {
            var root = obj.transform.Find("bg");
            var name = root.Find("name").GetComponent<UIText>();
            var btnGo = root.Find("btnGo").GetComponent<UIButton>();
            var btnGet = root.Find("btnGet").GetComponent<UIButton>();
            var goFinish = root.Find("goFinish");
            var btnReGet = root.Find("btnReGet").GetComponent<UIButton>();
            
            //奖励领取状态0：未解锁  1：可领取  2：已领取 3:继续领取
            var status = 2;
            btnGo.gameObject.SetActive(status == 0);
            btnGet.gameObject.SetActive(status == 1);
            goFinish.gameObject.SetActive(status == 2);
            btnReGet.gameObject.SetActive(status == 3);
            name.text = "在商城购买一次任意礼包(0/1)";
        }

         //计算进度值
         private void CalculateProgress()
         {
             var scoreList = ConfigData.ScoreRewards.Select(x => x.Score).ToList();
             scoreList.Sort((a, b) => (int)a - (int)b);
             var maxValue = scoreList.Last();
             if (MsgData.Score >= maxValue)
             {
                 m_sliderProgress.value = 1;
             }
             else
             {
                 var list1 = new List<int>();
                 foreach (var score in scoreList)
                 {
                     list1.Add((int)score);
                 }

                 var ratioList = new List<float>();
                 var nodeList = new List<int>();
                 var lastValue = 0;

                 var checkCount = scoreList.Count;
                 var unit = (1 - startRatio) / (checkCount - 1);
                 for (var i = 0; i < checkCount; i++)
                 {
                     ratioList.Add(i == 0 ? startRatio : unit);
                 }
                 
                 for (var i = 0; i < checkCount; i++)
                 {
                     if (i == 0)
                     {
                         ratioList.Add(unit * 1.0f / 2);
                     }
                     else
                     {
                         ratioList.Add(unit);
                     }
                 }

                 for (var i = 0; i < list1.Count; i++)
                 {
                     var curScore = list1[i];
                     var offset = curScore - lastValue;
                     nodeList.Add(offset);
                     lastValue = curScore;
                 }

                 var value = MsgData.Score;
                 float ratioSum = 0;
                 int offsetSum = 0;
                 for (var i = 0; i < list1.Count; i++)
                 {
                     var curScore = list1[i];
                     if (value > curScore)
                     {
                         ratioSum += ratioList[i];
                         offsetSum = curScore;
                     }
                     else
                     {
                         var finalOffset = value - offsetSum;
                         ratioSum += (finalOffset * 1.0f / nodeList[i]) * ratioList[i];
                         break;
                     }
                 }

                 m_sliderProgress.value = ratioSum;
             }
         }

         #endregion
    }
}

