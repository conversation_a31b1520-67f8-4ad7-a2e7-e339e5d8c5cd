using UnityEngine;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine.UI;

namespace Game.Hotfix
{
    //系统公告可能包含：奖励、超链接跳转、奖励+超链接跳转
    public class MailDetail_SystemNotice:MailDetailLogic
    {
        [SerializeField] private UIText m_txtTitle;
        [SerializeField] private RectTransform contentRect;
        [SerializeField] private UIText m_txtContent;
        [SerializeField] private Transform rewardRoot;
        [SerializeField] private UIText m_txtTime;
        
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goReward;
        //点赞和拉踩
        [SerializeField] private RectTransform judgeRoot;
        [SerializeField] private UIText m_txtGoodCount;
        [SerializeField] private UIButton m_btnGood;
        [SerializeField] private UIText m_txtBadCount;
        [SerializeField] private UIButton m_btnBad;
        
        private string title;//标题
        private string timeRecord;//时间戳
        private string content;//内容文本

        
        protected override void DecodeMsg(string msg)
        {
            var list = msg.Split("|");
            title = list[0];
            timeRecord = list[1];
            content = list[2];
        }

        public override void ShowDetail(MailDetailInfo msg)
        {
            mailContentType = msg.contentType;
            // DecodeMsg(msg.content);

            m_txtTitle.text = "更新公告";
            
            m_txtGoodCount.text = GetJudgeCount(360);
            m_txtBadCount.text = GetJudgeCount(66);

            LayoutRebuilder.ForceRebuildLayoutImmediate(judgeRoot);
            ToolScriptExtend.BindBtnLogic(m_btnGood, () =>
            {
                
                
            });
            
            ToolScriptExtend.BindBtnLogic(m_btnBad, () =>
            {
                
                
            });
            
            m_txtContent.text = "尊敬的指挥官，恭喜您所在的同盟获得本周同盟对决的最终胜利，周赛的胜利奖励如下：";
            
            // //展示奖励列表
            // List<reward> rewards = new List<reward>()
            // {
            //     new reward() { item_id = itemid.itemid_1, num = 111 },
            //     new reward() { item_id = itemid.itemid_3, num = 22 },
            //     new reward() { item_id = itemid.itemid_4, num = 333 },
            //     new reward() { item_id = itemid.itemid_8, num = 444 },
            //     new reward() { item_id = itemid.itemid_1, num = 555 },
            //     new reward() { item_id = itemid.itemid_5, num = 666 },
            //     // new reward() { item_id = itemid.itemid_5, num = 666 },
            //     // new reward() { item_id = itemid.itemid_5, num = 666 },
            //     // new reward() { item_id = itemid.itemid_5, num = 666 },
            //     // new reward() { item_id = itemid.itemid_5, num = 666 },
            // };
            //
            // ToolScriptExtend.RecycleOrCreate(m_goReward, rewardRoot, rewards.Count);
            // for (var j = 0; j < rewards.Count; j++)
            // {
            //     var info = rewards[j];
            //     var rewardChild = rewardRoot.GetChild(j);
            //     var node = rewardChild.Find("node");
            //     node.localScale = Vector3.one * 0.7f;
            //     if (node.childCount == 0)
            //     {
            //         BagManager.CreatItem(node, info.item_id, (int)info.num, (module) =>
            //         {
            //             module.SetClick(module.OpenTips);
            //             ToolScriptExtend.SetItemObjTxtScale(module.gameObject, 1.3f);
            //         });
            //     }
            //     else
            //     {
            //         ToolScriptExtend.SetItemObjInfo(node, node.GetChild(0).gameObject, info.item_id, (int)info.num);
            //     }
            // }
            //
            // LayoutRebuilder.ForceRebuildLayoutImmediate(mainRect);
            // LayoutRebuilder.ForceRebuildLayoutImmediate(rewardRect);
            
            // m_txtTitle.text = title;
            // m_txtContent2.text = content;
            // m_txtTime.text = timeRecord;
            //
            // var rect = m_txtContent2.transform.parent.GetComponent<RectTransform>();
            // LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
            // LayoutRebuilder.ForceRebuildLayoutImmediate(contentRect);
        }

        private string GetJudgeCount(long count)
        {
            return $"({ToolScriptExtend.FormatNumberWithUnit(count)})";
        }
    }
    
}
