using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;
using hero_config = Game.Hotfix.Config.hero_config;
using itemid = Game.Hotfix.Config.itemid;
using paymenttype = Game.Hotfix.Config.paymenttype;
using shopping_dailydeal = Game.Hotfix.Config.shopping_dailydeal;

namespace Game.Hotfix
{ 
    //每日特惠
    public class Mall_DailyDeal : SwitchPanelLogic
    {
        public UIButton m_btnDesc;
        public GameObject m_goPrefab;
        public GameObject m_goItem;
        public Transform m_transTimerRoot;
        public UIButton m_btnSwitch;
        public UIText m_txtName;
        public UIText m_txtDiscount;
        public UIButton m_btnBuyAll;
        public UIText m_txtSumPrice;
        public Transform m_transGiftRoot;
        public UIButton m_btnFree;
        public UIText m_txtOriPrice;
        public UIText m_txtOriPriceGrey;
        public UIButton m_btnBuyAllGrey;
        public UIText m_txtSumPriceGrey;
        public GameObject m_goFreeOff;
        public GameObject m_goFreeOn;

        public GameObject m_goReward;
        public UIImage heroImage;

        private UIText m_txtTimer; //倒计时文本
        private int timerCount; //倒计时数值

        private List<Gift.Gift> GiftList;
        public shopping_dailydeal mainConfig;
        private MallData Manager;
        private bool isTimer = false;
        private GameObject timerObj;
        
        private dailydealshowtype showType = dailydealshowtype.dailydealshowtype_1;
        public GameObject m_goEffect;
        public GameObject discountBg;
        
        public Spine.Unity.SkeletonGraphic m_spuiRole;
        
        //实例化对象时初始化
        public override void OnInit()
        {
            base.OnInit();
            m_goPrefab.SetActive(false);
            Manager = GameEntry.LogicData.MallData;
            isTimer = false;
            ToolScriptExtend.LoadPrefab("Common/TimerObj", (obj) =>
            {
                timerObj = Instantiate(obj, m_transTimerRoot);
                m_txtTimer = timerObj.transform.Find("txt").GetComponent<UIText>();
            });

            //说明按钮
            BindBtnLogic(m_btnDesc, () => { GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 5); });

            //切换按钮
            BindBtnLogic(m_btnSwitch, () =>
            {
                var isLocked = Manager.IsDailyDealLocked();
                if (isLocked)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        //今日已选定礼包购买，无法变更
                        Content = ToolScriptExtend.GetLang(1100350)
                    });
                    return;
                }
                
                if (showType == dailydealshowtype.dailydealshowtype_2)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UISelectDailyDealForm);
                }
                else if (showType == dailydealshowtype.dailydealshowtype_3)
                {
                    //TODO
                    GameEntry.UI.OpenUIForm(EnumUIForm.UISelectDailyDealForm);
                }
            });

            //购买所有礼包按钮
            BindBtnLogic(m_btnBuyAll, () =>
            {
                if (mainConfig != null)
                {
                    GameEntry.PaymentData.Pay(mainConfig.merge_payment_id);
                }
            });

            //领取免费礼包按钮
            BindBtnLogic(m_btnFree, () =>
            {
                var isFree = GameEntry.LogicData.MallData.IsCanFree(paymenttype.paymenttype_dailydeal);
                if (!isFree) return;
                Manager.C2SDailyDiscountFreeReceive((resp) => { SetFreeRewardStatus(); });
            });
            
            //合购失效礼包按钮
            BindBtnLogic(m_btnBuyAllGrey, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100292)
                });
            });
            
            Manager.RequestNewMsg(paymenttype.paymenttype_dailydeal);
            Manager.SetParticleDepth(m_goEffect);
            Manager.SetParticleCanvasDepth(discountBg,10);
        }

        //再次打开
        public override void OnReOpen()
        {
            Manager.RequestNewMsg(paymenttype.paymenttype_dailydeal);
        }

        //本界面刷新，一般由UISwitchPage.RefreshCurPage触发
        public override void OnRefreshSelf()
        {
            base.OnRefreshSelf();
            Manager.RequestNewMsg(paymenttype.paymenttype_dailydeal);
        }


        //事件刷新
        public override void OnRefresh(object userData)
        {
            var data = (System.ValueTuple<string, int>)userData;
            var flag = data.Item1;
            var param = data.Item2;
            if (flag == "DailyDeal")
            {
                InitPageView();
            }
        }

        /// <summary>
        /// 被选中触发的逻辑
        /// </summary>
        /// <param name="isOn">是否被选中</param>
        public override void OnSelect(bool isOn)
        {
        }

        //隐藏面板时逻辑
        public override void OnClose()
        {
        }

        //每帧更新逻辑，类似于Unity的Update函数
        public override void OnUpdate()
        {
        }

        //计时器逻辑
        public override void OnTimer()
        {
            if (!isTimer) return;
            var temp = timerCount - 1;
            if (temp >= 0)
            {
                timerCount--;
                m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);
                if (timerCount == 0)
                {
                    isTimer = false;
                    //初始化请求
                    Manager.RequestNewMsg(paymenttype.paymenttype_dailydeal);
                }
            }
        }

        //资源释放
        public override void Release()
        {
            GiftList?.Clear();
        }

        public void InitPageView()
        {
            CheckDealCount();
            
            var manager = GameEntry.LogicData.MallData;
            GiftList = manager.GetGiftList(Config.paymenttype.paymenttype_dailydeal);

            //计算倒计时
            timerCount = manager.GetShoppingTime(paymenttype.paymenttype_dailydeal);
            isTimer = timerCount > 0;
            timerObj.SetActive(isTimer);
            m_txtTimer.text = ToolScriptExtend.FormatTime(timerCount);

            var configId = manager.GetCurDailyDealId();
            if (!ToolScriptExtend.GetConfigById<shopping_dailydeal>(configId, out mainConfig)) return;

            //英雄spine、名称
            if (ToolScriptExtend.GetConfigById<hero_config>(mainConfig.hero_id, out var heroData))
            {
                m_txtName.text = ToolScriptExtend.GetItemName((itemid)mainConfig.hero_id);
                // heroImage.SetImage(heroData.hero_pic);

                ToolScriptExtend.ShowHeroSpine(mainConfig.hero_id, m_spuiRole);
            }

            //判断切换按钮状态
            //判断免费宝箱是否可领取
            SetFreeRewardStatus();

            //全购状态  性价比  原价  折扣价  累充积分
            // data.merge_payment_id
            m_txtDiscount.text = $"{mainConfig.cost_effectiveness}%";

            manager.CreateRechargeScore(mainConfig.merge_payment_id, m_btnBuyAll.transform);

            if (ToolScriptExtend.GetConfigById<Config.payment>((int)mainConfig.merge_payment_id, out var data1))
            {
                m_txtSumPrice.text = ToolScriptExtend.GetLang(data1.price_lang_id);
                m_txtOriPrice.text = ToolScriptExtend.GetLang(data1.original_price_lang_id);
                m_txtOriPriceGrey.text = ToolScriptExtend.GetLang(data1.original_price_lang_id);
                m_txtSumPriceGrey.text = ToolScriptExtend.GetLang(data1.price_lang_id);
            }

            //礼包列表  礼包状态
            ShowGiftList();

            var isLocked = Manager.IsDailyDealLocked();
            ToolScriptExtend.SetGameObjectGrey(m_btnSwitch.transform,isLocked);
        }

        //展示礼包列表
        private void ShowGiftList()
        {
            var boughtCount = 0;
            var childCount = m_transGiftRoot.childCount;
            if (childCount == 0)
            {
                for (var i = 0; i < 3; i++)
                {
                    Instantiate(m_goItem, m_transGiftRoot);
                }
            }
            
            childCount = m_transGiftRoot.childCount;
            for (var i = 0; i < childCount; i++)
            {
                var child = m_transGiftRoot.GetChild(i);
                SetGiftInfo(i, child.gameObject,out bool hasBuy);
                if (hasBuy)
                {
                    boughtCount++;
                }
            }
            
            var isLocked = Manager.IsDailyDealLocked();
            m_btnBuyAll.gameObject.SetActive(!isLocked && boughtCount == 0);
            m_btnBuyAllGrey.gameObject.SetActive(isLocked || boughtCount > 0);
            m_goEffect.SetActive(boughtCount == 0);
        }

        private void SetGiftInfo(int index, GameObject obj,out bool hasBuy)
        {
            hasBuy = false;
            var manager = GameEntry.LogicData.MallData;
            if (index >= GiftList.Count && index < 0) return;

            var msg = GiftList[index];
            var data = manager.GetGiftConfig(msg.Id);
            if (data == null) return;

            var paymentData = manager.GetPaymentConfig(data.payment_id);

            var root = obj.transform;
            var icon = root.Find("icon").GetComponent<UIImage>(); //碎片图标
            var clipTxt = root.Find("clipTxt").GetComponent<UIText>(); //碎片数量
            var iconBtn = icon.GetComponent<UIButton>();
            var discount = root.Find("Image_1/discount").GetComponent<UIText>(); //折扣
            var diamondIcon = root.Find("diamondIcon").GetComponent<UIImage>(); //钻石图标
            var diamondTxt = root.Find("diamondTxt").GetComponent<UIText>(); //钻石数量
            var rewardRoot = root.Find("Scroll View/Viewport/rewardRoot");
            var btn = root.Find("btn").GetComponent<UIButton>(); //购买按钮
            var price = root.Find("btn/price").GetComponent<UIText>(); //价格
            var soldOut = root.Find("soldOut"); //售罄按钮
            var head = root.Find("head");
            var effect = root.Find("effect");

            price.text = manager.GetPrice(data.payment_id);
            
            manager.CreateRechargeScore(data.payment_id, btn.transform);
            discount.text = $"{data.cost_effectiveness}%";
            BindBtnLogic(btn, () => { GameEntry.PaymentData.Pay(data.payment_id); });

            var rewardConfig = data.gift_pack_reward;
            //头奖  奖励列表的第一个奖
            var firstReward = rewardConfig[0];
            icon.SetImage(ToolScriptExtend.GetItemIcon(firstReward.item_id));
            clipTxt.text = $"x{firstReward.num}";
             
            head.localScale = Vector3.one*0.7f;
            var headReward = head.childCount == 0 ? Instantiate(Manager.RewardObj,head) : head.GetChild(0).gameObject;
            manager.SetRewardInfo(head,headReward,firstReward.item_id,(int)firstReward.num,1.5f);
            
            //充值钻石奖励
            diamondIcon.SetImage(ToolScriptExtend.GetItemIcon(itemid.itemid_6));
            diamondTxt.text = $"x{paymentData.diamond}";
            
            //奖励列表  需要额外将payment中同盟宝箱奖励加入到列表中
            var rewardList = new List<reward>();
            rewardList.AddRange(rewardConfig.Skip(1));

            var temp = manager.GetPaymentConfig(data.payment_id);
            if (temp != null)
            {
                if (temp.alliance_chest.Count > 0)
                {
                    foreach (var chest in temp.alliance_chest)
                    {
                        rewardList.Add(new Config.reward()
                        {
                            num = 1,
                            item_id = (itemid)chest
                        });
                    }
                }
            }

            manager.RecycleOrCreate(m_goReward, rewardRoot, rewardList.Count);
            for (var i = 0; i < rewardList.Count; i++)
            {
                var child = rewardRoot.GetChild(i);
                var node = child.Find("node");
                node.localScale = Vector3.one * 0.5f;
                var cell = rewardList[i];
                var com = child.GetComponentsInChildren<UIItemModule>();
                if (com.Length == 0)
                {
                    Instantiate(manager.RewardObj, node);
                }
                manager.SetRewardInfo(node, node.GetChild(0).gameObject, cell.item_id, (int)cell.num,1.5f);
            }
            
            var isSoldOut = msg.Amount >= data.buy_limit_times;
            btn.gameObject.SetActive(!isSoldOut);
            soldOut.gameObject.SetActive(isSoldOut);
            hasBuy = msg.Amount > 0;
            effect.gameObject.SetActive(!isSoldOut);
        }

        //设置每日免费奖励领取状态
        private void SetFreeRewardStatus()
        {
            var isFree = GameEntry.LogicData.MallData.IsCanFree(paymenttype.paymenttype_dailydeal);
            m_goFreeOff.SetActive(!isFree);
            m_goFreeOn.SetActive(isFree);
        }

        //判断特惠类型
        private void CheckDealCount()
        {
            var type = 0;
            if (GameEntry.LDLTable.HaseTable<shopping_dailydeal>())
            {
                var data = GameEntry.LDLTable.GetTable<shopping_dailydeal>();
                foreach (var info in data)
                {
                    if (info.dailydeal_demand == 0 || ToolScriptExtend.GetDemandUnlock(info.dailydeal_demand))
                    {
                        type = Mathf.Max((int)info.dailydeal_show_type, type);
                    }
                }
            }

            showType = (dailydealshowtype)type;
            m_btnSwitch.gameObject.SetActive(type > 1);
        }
    }
}