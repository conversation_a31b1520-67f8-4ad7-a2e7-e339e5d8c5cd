using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;

//超值活动公共逻辑
namespace Game.Hotfix
{
    //创角累充、每日累充、七日累充抽象公共基类
    public abstract class ChaoZhiRecharge:SwitchPanelLogic
    {
        [SerializeField] protected UIButton m_btnHelp;//帮助提示按钮
        [SerializeField] protected UIText m_txtTitle;//标题
        [SerializeField] protected UIText m_txtTimer; //倒计时文本
        [SerializeField] protected UIText m_txtDesc;//活动描述
        [SerializeField] protected UIText m_txtScore;//积分数量
        [SerializeField] protected UIImage m_imgIcon;//活动图标
        [SerializeField] protected GameObject m_goPrefab;
        [SerializeField] protected GameObject m_goReward;
        [SerializeField] protected GameObject m_goItemObj;
        [SerializeField] protected Transform m_goItemRoot;
        [SerializeField] protected Slider m_sliderProgress;
        [SerializeField] protected ScrollRect mainScroll;
        [SerializeField] protected RectTransform mainContent;
        [SerializeField] protected RectTransform sliderRect;
        [SerializeField] protected UIImage m_imgScore;//积分图标

        protected Config.activity_template TemplateId;
        protected int timerCount; //倒计时数值
        protected ChaoZhiData ChaoZhiManager => GameEntry.LogicData.ChaoZhiData;
        protected bool isTimer = false;
        private string iconPath;
        
        protected virtual void InitPageView()
        {
            var config = ChaoZhiManager.GetActivityConfig((int)TemplateId);
            if (config == null) return;
            //说明按钮
            BindBtnLogic(m_btnHelp, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, config.activity_explain);
            });
            m_txtTitle.text = ToolScriptExtend.GetLang(config.name);
            m_txtDesc.text = ToolScriptExtend.GetLang(config.activity_word);
            
            var list = ChaoZhiManager.GetListByTemplateId(TemplateId);
            list.Sort((a, b) => a.id - b.id);
            iconPath = list[0].icon;
            if (!string.IsNullOrEmpty(iconPath))
            {
                m_imgScore.SetImage(iconPath);
            }
            var itemCount = list.Count;
            AutoFitContent(itemCount);
            ToolScriptExtend.RecycleOrCreate(m_goItemObj,m_goItemRoot,itemCount);
            for (var i = 0; i < itemCount; i++)
            {
                var child = m_goItemRoot.GetChild(i);
                SetItemInfo(i,child,list[i]);
            }

            m_sliderProgress.value = 0;
            m_txtScore.text = 0.ToString();
            CheckMultiLanguage(gameObject);
        }
        
        protected void SetItemInfo(int index,Transform root,activity_recharge data)
        {
            var rewardScroll = root.Find("Scroll View").GetComponent<ScrollRect>();
            var rewardRoot = root.Find("Scroll View/Viewport/Content");
            var btnGet = root.Find("m_btnGet").GetComponent<UIButton>();
            var scoreIcon = root.Find("scoreIcon").GetComponent<UIImage>();
            var scoreTxt = root.Find("scoreIcon/scoreTxt").GetComponent<UIText>();
            var rewardList = data.achievement_bonus;
            ChaoZhiManager.ShowRewardList(m_goReward,rewardRoot, rewardList,0.56f);
            rewardScroll.enabled = rewardList.Count > 4;
            scoreTxt.text = data.need_points.ToString();
            SetItemStatus(root,0);
            if (!string.IsNullOrEmpty(iconPath))
            {
                scoreIcon.SetImage(iconPath);
            }
        }
        
        protected void AutoFitContent(int count)
        {
            var itemRect = m_goItemObj.GetComponent<RectTransform>();
            var itemHeight = itemRect.rect.height;
            var spacing = m_goItemRoot.GetComponent<VerticalLayoutGroup>().spacing;
            var heightSum = itemHeight * count + spacing * (count - 1);
            
            mainContent.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical,heightSum);
            sliderRect.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,heightSum-50);
        }
        
        /// <summary>
        /// 设置积分里程碑奖励领取状态
        /// </summary>
        /// <param name="root"></param>
        /// <param name="status">积分里程碑奖励领取状态  0：未解锁 1:可领取 2：已领取</param>
        protected void SetItemStatus(Transform root,int status)
        {
            var btnGet = root.Find("m_btnGet").GetComponent<UIButton>();
            var btnLock = root.Find("m_btnLock").GetComponent<UIButton>();
            var finishObj = root.Find("m_goFinish");
            
            btnGet.gameObject.SetActive(status == 1);
            btnLock.gameObject.SetActive(status == 0);
            finishObj.gameObject.SetActive(status == 2);

            if (status == 1)
            {
                ToolScriptExtend.BindBtnLogic(btnGet, () =>
                {
                    


                });
            }
        }
    }
}


