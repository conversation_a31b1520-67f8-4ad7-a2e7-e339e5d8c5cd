using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIWorldMapGatherMenu : UIWorldMapMenuBase
    {
        [SerializeField] private UIButton m_btnShare;
        [SerializeField] private UIButton m_btnFavorite;

        [SerializeField] private UIText m_txtTitle;
        [SerializeField] private UIText m_txtName;
        [SerializeField] private UIText m_txtProgress;
        [SerializeField] private UIText m_txtWorkerName;
        [SerializeField] private UIText m_txtProgressTime;
        [SerializeField] private UIText m_txtProgressCount;

        [SerializeField] private Image m_imgProgressBg;
        [SerializeField] private UIImage m_imgProgress;
        [SerializeField] private UIImage m_imgIcon;
        [SerializeField] private UIImage m_imgWorkerHead;

        [SerializeField] private RectTransform m_rectMove;
        [SerializeField] private RectTransform m_rectMenu;
        [SerializeField] private GameObject m_goTopInfo;
        [SerializeField] private GameObject m_goNotWorking;
        [SerializeField] private GameObject m_goWorking;

        void InitBind()
        {
            m_btnShare.onClick.AddListener(OnBtnShareClick);
            m_btnFavorite.onClick.AddListener(OnBtnFavoriteClick);
        }
    }
}
