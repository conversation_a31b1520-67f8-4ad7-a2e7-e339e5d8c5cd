using System.Collections;
using System.Collections.Generic;
using Battle;
using Game.Hotfix.Config;
using Google.Protobuf.Collections;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBattle5V5DungeonStatsForm : UGuiFormEx
    {
        private Report m_Report;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            HideDefault();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is Report report)
            {
                m_Report = report;
            }

            //
            if (m_Report == null)
                return;

            m_txtTime.text = TimeHelper.FormatGameTime(
                (int)(m_Report.Stats.Duration * BattleDefine.BATTLE_FRAME_DURATION),
                true);
            var role = m_Report.Defender.Role;
            m_txtEnemyName.text = "#" + role.ServerId + "[" + role.ServerId + "]" + role.Name;
            m_txtEnemyPower.text = "战力：" + role.Power;

            role = m_Report.Attacker.Role;
            m_txtMyselfName.text = "#" + role.ServerId + "[" + role.ServerId + "]" + role.Name;
            m_txtMyselfPower.text = "战力：" + role.Power;

            m_goEnemyVictory.SetActive(m_Report.Result == BattleResult.DefenderWin);
            m_goEnemyDefeat.SetActive(!m_goEnemyVictory.activeSelf);
            m_goMyselfVictory.SetActive(m_Report.Result == BattleResult.AttackerWin);
            m_goMyselfDefeat.SetActive(!m_goMyselfVictory.activeSelf);

            123
            
            RefreshHeroItem(m_transEnemyContent, m_Report.Stats.Defender, m_Report.Defender);
            RefreshHeroItem(m_transMyselfContent, m_Report.Stats.Attacker, m_Report.Attacker);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            m_Report = null;
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnEnemyClick()
        {
        }

        private void OnBtnMyselfClick()
        {
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        void HideDefault()
        {
            m_transHeroItem.gameObject.SetActive(false);
        }

        void RefreshHeroItem(Transform parent, RepeatedField<BattleTeamStats> battleTeamStats, Battle.Team team)
        {
            for (int i = 1; i <= 5; i++)
            {
                Transform item;
                if (i < parent.childCount)
                {
                    item = parent.GetChild(i);
                }
                else
                {
                    item = Instantiate(m_transHeroItem, parent);
                }

                var j = i - 1;
                if (j < battleTeamStats.Count && j < team.Heroes.Count)
                {
                    BattleTeamStats teamStats = battleTeamStats[j];
                    item.gameObject.SetActive(true);
                    RefreshItem(teamStats, item, team.Heroes[j]);
                }
                else
                {
                    item.gameObject.SetActive(false);
                }
            }
        }

        void RefreshItem(BattleTeamStats teamStats, Transform item, Battle.TeamHero teamHero)
        {
            
            var imgHeroBg = item.Find("heroBg")?.GetComponent<UIImage>();
            var imgHeroSp = item.Find("heroMask/heroSp")?.GetComponent<UIImage>();
            var txtHeroLevel = item.Find("levelTxt")?.GetComponent<UIText>();

            item_config itemConfig = ToolScriptExtend.GetItemConfig((itemid)teamHero.Code);
            
            imgHeroBg?.SetImage(ToolScriptExtend.GetQualityBg(itemConfig.quality));
            imgHeroSp?.SetImage(ToolScriptExtend.GetItemIcon(itemConfig.id));
            if (txtHeroLevel != null) txtHeroLevel.text = "LV." + teamHero.Level;
            
            var txtValueOutput = item.Find("valueOutput/Text")?.GetComponent<UIText>();
            var sliderValueOutput = item.Find("valueOutput/slider")?.GetComponent<Slider>();
            
            var txtValueInjury = item.Find("valueInjury/Text")?.GetComponent<UIText>();
            var sliderValueInjury = item.Find("valueInjury/slider")?.GetComponent<Slider>();
            
            var txtValueBuffEffect = item.Find("valueBuffEffect/Text")?.GetComponent<UIText>();
            var sliderValueBuffEffect = item.Find("valueBuffEffect/slider")?.GetComponent<Slider>();
            
            var txtValueWeaken = item.Find("valueWeaken/Text")?.GetComponent<UIText>();
            var sliderValueWeaken = item.Find("valueWeaken/slider")?.GetComponent<Slider>();
            
            
            
            
        }
    }
}