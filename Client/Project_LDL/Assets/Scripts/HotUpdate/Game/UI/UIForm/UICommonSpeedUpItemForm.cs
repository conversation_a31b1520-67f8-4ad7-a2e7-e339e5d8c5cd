using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using JetBrains.Annotations;
using Soldier;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using itemid = PbGameconfig.itemid;

namespace Game.Hotfix
{
    public class CommonSpeedUpItemParam
    {
        public BuildingModule m_buildingModule;
        public itemsubtype m_itemSubType;
        public List<Build.Soldier> m_Soldiers;
        public int m_TotalTime;
        public SoldierOpType m_soldierOpType;
        public SoldierCostType m_soldierCostType;

        public CommonSpeedUpItemParam(BuildingModule buildingModule,int totalTime,itemsubtype itemSubType,List<Build.Soldier> soldiers,[CanBeNull]SoldierOpType opType = SoldierOpType.Nil)
        {
            m_buildingModule = buildingModule;
            m_itemSubType = itemSubType;
            m_Soldiers = soldiers;
            m_TotalTime = totalTime;
            m_soldierOpType = opType;
        }
    }
    
    public partial class UICommonSpeedUpItemForm : UGuiFormEx
    {
        private int needTotalTime = 0;
        private Transform m_TransItem;
        private BuildingModule curBuildingModule;
        private itemsubtype curUseItemsubtype;
        private List<ItemModule> itemList = new List<ItemModule>();
        private List<Build.Soldier> curSoldiers;
        private Vector2 bgOldRectSizeDelta;
        private RectTransform bgRect;
        public SoldierOpType curSoldierOpType;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitBind();
            m_TransItem = m_scrollviewItemList.transform.Find("Viewport/Content").transform;
            bgRect = m_goBg.GetComponent<RectTransform>();
            bgOldRectSizeDelta = bgRect.sizeDelta;
        }


        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            CommonSpeedUpItemParam commonSpeedUpItemParam = (CommonSpeedUpItemParam)userData;
            if (commonSpeedUpItemParam == null)
            {
                Debug.LogError("传入参数有误！");
                return;
            }
            curBuildingModule = commonSpeedUpItemParam.m_buildingModule;
            needTotalTime = commonSpeedUpItemParam.m_TotalTime;
            curUseItemsubtype = commonSpeedUpItemParam.m_itemSubType;
            curSoldiers = commonSpeedUpItemParam.m_Soldiers;
            curSoldierOpType = commonSpeedUpItemParam.m_soldierOpType;
  

            ResetReward();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            bgRect.sizeDelta = bgOldRectSizeDelta;
            m_TransItem.gameObject.DestroyAllChild();
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }
        
        protected void ResetReward()
        {
            itemList = GetSpeedUpItemList();
            float useTime = 0;
            foreach (ItemModule itemModule in itemList)
            {
                BagManager.CreatItem(m_TransItem, itemModule.ItemId, itemModule.Count, (item)=>
                {
                    item.transform.localScale = new Vector3(0.6f, 0.6f, 1f);
                    item.GetComponent<UIButton>().useTween = false;
                    item.SetClick(()=>
                    {
                        item.OpenTips();   
                    });
                });
                item_config itemConfig = itemModule.GetItemConfig();
                float time = float.Parse(itemConfig.use_value[0]);
                time *= itemModule.Count;
                useTime += time;
            }

            string useTimeStr = TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(useTime));
            string totalTimeStr = TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(needTotalTime));
            bool isEnough = useTime >= needTotalTime;
            string colorStr = isEnough ? "#009944" : "#ff3535";
            m_txtSpeedUpTime.text = $"<color={colorStr}>{useTimeStr}</color>/{totalTimeStr}";

            m_goListBg3.gameObject.SetActive(!isEnough);
            if (!isEnough)
            {
                int leftTime = Mathf.FloorToInt(needTotalTime - useTime);
                int costDiamond = GameEntry.LogicData.BuildingData.GetCostDiamond(leftTime);
                m_txtCostDiamond.text = costDiamond.ToString();
                m_txtTotalCostDiamond.text = costDiamond.ToString();
                RectTransform list3Rect = m_goListBg3.GetComponent<RectTransform>();
                Vector2 rectSizeDelta = bgRect.sizeDelta;
                Vector2 list3SizeDelta = list3Rect.sizeDelta;
                bgRect.sizeDelta = new Vector2(rectSizeDelta.x, rectSizeDelta.y + list3SizeDelta.y);
                LayoutRebuilder.ForceRebuildLayoutImmediate(bgRect);
            }
        }
        
        protected List<ItemModule> GetSpeedUpItemList()
        {
            List<ItemModule> list = new List<ItemModule>();
            List<itemsubtype> itemTypeList = new List<itemsubtype>();
            itemTypeList.Add(curUseItemsubtype);
            itemTypeList.Add(itemsubtype.itemsubtype_generalspeedup);
            float useTime = 0;

            foreach (itemsubtype itemSubType in itemTypeList)
            {
                var itemModules = GetBagItemListBySubType(itemSubType);
                foreach (ItemModule itemModule in itemModules)
                {
                    item_config itemConfig = itemModule.GetItemConfig();
                    float time = float.Parse(itemConfig.use_value[0]);
                    int needCount = 0;
                    for (int i = 0; i < itemModule.Count; i++)
                    {
                        if (useTime < needTotalTime)
                        {
                            useTime += time;
                            needCount += 1;
                        }
                        else
                        {
                            break;
                        }
                             
                    }

                    if (needCount != 0)
                    {
                        ItemModule _itemModule = new ItemModule();
                        _itemModule.SetData(itemModule.ItemId,needCount);
                        list.Add(_itemModule);
                    }
                }
            }
            
            return list;
        }
        
        protected List<ItemModule> GetBagItemListBySubType(Config.itemsubtype itemSubType)
        {
            var itemModules = GameEntry.LogicData.BagData.GetDataBySubType(itemSubType);
            itemModules.Sort((a, b) =>
            {
                Config.item_config itemConfigA = a.GetItemConfig();
                Config.item_config itemConfigB = b.GetItemConfig();
                if (itemConfigA.item_subtype == itemConfigB.item_subtype)
                {
                    return float.Parse(itemConfigA.use_value[0]).CompareTo(float.Parse(itemConfigB.use_value[0]));
                }

                return itemConfigA.item_subtype.CompareTo(itemConfigB.item_subtype);
            });
            return itemModules;
        }

        private void OnBtnExitClick()
        {

        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnOKClick()
        {

        }

        private void OnBtnDoneClick()
        {
            List<Article.Article> articles = new List<Article.Article>();
            foreach (ItemModule itemModule in itemList)
            {
                Article.Article article = new Article.Article();
                article.Code = (itemid)itemModule.ItemId;
                article.Amount = itemModule.Count;
            }

            if (curUseItemsubtype == itemsubtype.itemsubtype_healspeedup)
            {
                GameEntry.LogicData.BuildingData.SoldierTreatmentReq(curSoldiers, SoldierCostType.AccelerateItem,articles,(() =>
                {
                    Close();
                    if (GameEntry.UI.HasUIForm(EnumUIForm.UITreatSoldiersForm))
                    {
                        GameEntry.UI.CloseUIForm(EnumUIForm.UITreatSoldiersForm);
                    }
                }));
            }
            else if (curUseItemsubtype == itemsubtype.itemsubtype_trainspeedup && curSoldiers.Count > 0)
            {
                GameEntry.LogicData.BuildingData.SoldierOpReq((uint)curBuildingModule.BuildingId, curSoldiers[0],curSoldierOpType,SoldierCostType.AccelerateItem,articles,(() =>
                {
                    Close();
                }));
            }
        }

        private void OnBtnCanelClick()
        {

        }
    }
}
