using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public enum TruckDetailType
    {
        History,
        Plunder,
        Reward,
    }

    public partial class UITradeTruckDetailForm : UGuiFormEx
    {
        Trade.TradeVanDetailResp truckDetail;
        Trade.TradeVanRecord truckRecord;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is UITradeTruckDetailFormParams param)
            {
                truckDetail = param.truckDetail;
                truckRecord = param.truckRecord;
                switch (param.type)
                {
                    case TruckDetailType.History:
                        m_goTitle.SetActive(false);
                        m_goBubble.SetActive(true);
                        break;
                    case TruckDetailType.Plunder:
                        m_goTitle.SetActive(true);
                        m_goBubble.SetActive(false);
                        m_txtTitle.text = ToolScriptExtend.GetLangFormat(1224, "1h");
                        m_txtContent.text = ToolScriptExtend.GetLang(1225);
                        break;
                    case TruckDetailType.Reward:
                        m_goTitle.SetActive(true);
                        m_goBubble.SetActive(false);
                        m_txtTitle.text = ToolScriptExtend.GetLang(1227);
                        m_txtContent.text = ToolScriptExtend.GetLang(1228);
                        break;
                }
                RefreshPanel();
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        void RefreshPanel()
        {
            if (truckDetail != null)
            {
                Trade.TradeCargoTransport truckData = truckDetail.CargoTransport;
                if (truckData == null) return;

                trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(truckData.TradeId);
                car_quality quality = truckConfig.car_quality;
                m_imgTruckIcon.SetImage(GetTruckIcon(quality));
                m_imgQuality.SetImage(ToolScriptExtend.GetQualityIcon((int)quality), true);
                if (truckData.Boxcar.Count > 0)
                {
                    List<Trade.TradeGoods> rewards = new(truckData.Boxcar[0].Goods);
                    RefreshReward(rewards);
                }
            }
            else if (truckRecord != null)
            {
                trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(truckRecord.TradeId);
                car_quality quality = truckConfig.car_quality;
                m_imgTruckIcon.SetImage(GetTruckIcon(quality));
                m_imgQuality.SetImage(ToolScriptExtend.GetQualityIcon((int)quality), true);
                if (truckRecord.Goods.Count > 0)
                {
                    List<Trade.TradeGoods> rewards = new(truckRecord.Goods);
                    RefreshReward(rewards);
                }
            }
        }

        void RefreshReward(List<Trade.TradeGoods> rewards)
        {
            foreach (Transform item in m_transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < rewards.Count; i++)
            {
                if (i < m_transContentReward.childCount)
                {
                    m_transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = m_transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                }
                else
                {
                    Transform item = Instantiate(m_transReward, m_transContentReward);
                    BagManager.CreatItem(item, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                    });
                }
            }
        }

        string GetTruckIcon(car_quality quality)
        {
            return quality switch
            {
                car_quality.car_quality_1 => "Sprite/ui_maoyi/maoyi_dis_car1.png",
                car_quality.car_quality_2 => "Sprite/ui_maoyi/maoyi_dis_car2.png",
                car_quality.car_quality_3 => "Sprite/ui_maoyi/maoyi_dis_car3.png",
                car_quality.car_quality_4 => "Sprite/ui_maoyi/maoyi_dis_car4.png",
                car_quality.car_quality_5 => "Sprite/ui_maoyi/maoyi_dis_car5.png",
                _ => "Sprite/ui_maoyi/maoyi_dis_car1.png",
            };
        }
    }
}
