using System.Collections;
using System.Collections.Generic;
using GameFramework.Event;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Sirenix.Utilities;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionTechUpForm : UGuiFormEx
    {
        private int normalNumMax;
        private int normalCostId;
        private int normalCostNum;

        private int diamondCostId;
        private int diamondCostNum;
        private int diamondAdd;
        private int diamondAddMax;

        private int rewardId;
        private int rewardCount;
        private int expId;
        private int expCount;

        private int groupId;
        private float deltaTime = 0;
        private int cutTime;
        private int recoverTime;

        private bool isLongPress;
        private bool isShowTip;
        private int clickCount;
        private int playTime;
        private float longTime;
        private float longClickCount;
        private ObjectPool<GameObject> critObjList;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            var config = GameEntry.LogicData.UnionData.GetUnionConst(25);
            normalNumMax = config != null ? int.Parse(config[0]) : 0;

            config = GameEntry.LogicData.UnionData.GetUnionConst(27);
            normalCostId = config != null ? int.Parse(config[0]) : 0;
            normalCostNum = config != null ? int.Parse(config[1]) : 0;

            config = GameEntry.LogicData.UnionData.GetUnionConst(28);
            diamondCostId = config != null ? int.Parse(config[0]) : 0;
            diamondCostNum = config != null ? int.Parse(config[1]) : 0;

            config = GameEntry.LogicData.UnionData.GetUnionConst(29);
            diamondAdd = config != null ? int.Parse(config[0]) : 0;

            config = GameEntry.LogicData.UnionData.GetUnionConst(29);
            diamondAddMax = config != null ? int.Parse(config[0]) : 0;

            config = GameEntry.LogicData.UnionData.GetUnionConst(33);
            var num = config != null ? int.Parse(config[0]) : 0;
            m_txtRecommend.text = ToolScriptExtend.GetLangFormat(80100007, num / 10000 + "");

            config = GameEntry.LogicData.UnionData.GetUnionConst(31);
            rewardId = config != null ? int.Parse(config[0]) : 0;
            rewardCount = config != null ? int.Parse(config[1]) : 0;
            m_imgReward_1.SetImage(ToolScriptExtend.GetItemIcon(rewardId));
            m_txtReward_1.text = rewardCount + "";

            config = GameEntry.LogicData.UnionData.GetUnionConst(32);
            expId = 17;
            expCount = config != null ? int.Parse(config[0]) : 0;
            var itemIcon = ToolScriptExtend.GetItemIcon(expId);
            m_imgProgressIcon.SetImage(itemIcon);
            m_imgReward_2.SetImage(itemIcon);
            m_txtReward_2.text = expCount + "";

            m_txtMax.text = ToolScriptExtend.GetLang(80100017);

            m_btnCost_1.longPressInterval = 0.5f;
            m_btnCost_1.SetLongPress(() =>
            {
                isLongPress = true;
                OnBtnCost_1Click();
                isLongPress = false;
            });

            m_txtDesc.SetHyperlinkCallback((refValue, innerValue) =>
            {
                var showStr = m_txtDesc.text;
                var idxList = ToolScriptExtend.FindStrAllIndex(showStr, innerValue + "<");
                if (idxList.Count <= 0) return;

                var curIdx = idxList[0];
                idxList = ToolScriptExtend.FindStrAllIndex(showStr, innerValue);
                for (int i = 0; i < idxList.Count; i++)
                {
                    if (curIdx == idxList[i])
                    {
                        curIdx = i;
                        break;
                    }
                }

                showStr = ToolScriptExtend.GetRealStr(showStr, "<color=", "</color>");
                showStr = ToolScriptExtend.GetRealStr(showStr, "<a href=", "</a>");

                var startIndex = 0;
                idxList = ToolScriptExtend.FindStrAllIndex(showStr, innerValue);
                for (int i = 0; i < idxList.Count; i++)
                {
                    if (curIdx == i)
                    {
                        startIndex = idxList[i];
                        break;
                    }
                }
                var endIndex = startIndex + innerValue.Length;
                m_txtDesc.AnchorUIToTextSegment(startIndex, endIndex, m_goTipDesc.transform, new Vector2(0, -40f));

                var halfWidth = Screen.width / 2;
                var halfBgWidth = 275;
                var posX = m_goTipDesc.transform.localPosition.x;
                var posY = m_goTipDesc.transform.localPosition.y;
                float offsetX = 0;
                if (posX - halfBgWidth < -halfWidth)
                {
                    offsetX = Mathf.Abs(posX - halfBgWidth + halfWidth);
                }
                else if (posX + halfBgWidth > halfWidth)
                {
                    offsetX = -(posX + halfBgWidth - halfWidth);
                }
                if (offsetX != 0)
                    m_goTipDesc.transform.SetLocalPosition(posX + offsetX, posY, 0);

                m_txtTipDesc.text = ToolScriptExtend.GetLang(int.Parse(refValue));

                m_btnTipMask.gameObject.SetActive(true);
                m_goTipDesc.SetActive(true);

                var rect = m_goTipDesc.GetComponent<RectTransform>();
                LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
            });

            critObjList = new(() =>
            {
                return Instantiate(m_goCrit, transform);
            },
            (go) =>
            {
                go.SetActive(true);
            },
            (go) =>
            {
                go.SetActive(false);
            },
            (go) =>
            {
                Destroy(go);
            });
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (!GameEntry.Event.Check(UnionTechChangeEventArgs.EventId, OnUpdateEvent))
                GameEntry.Event.Subscribe(UnionTechChangeEventArgs.EventId, OnUpdateEvent);

            Timers.Instance.Remove("UnionTechUpForm");
            m_goTip.SetActive(false);
            OnBtnTipMaskClick();

            isLongPress = false;
            isShowTip = false;
            clickCount = 0;
            playTime = 0;
            longTime = 0;

            groupId = (int)userData;
            OnUpdateInfo();

        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);

            if (GameEntry.Event.Check(UnionTechChangeEventArgs.EventId, OnUpdateEvent))
                GameEntry.Event.Unsubscribe(UnionTechChangeEventArgs.EventId, OnUpdateEvent);

            if (GameEntry.UI.HasUIForm(EnumUIForm.UIUnionTechForm))
            {
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIUnionTechForm, 1);
            }
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            deltaTime += elapseSeconds;
            if (deltaTime >= 1)
            {
                deltaTime = 0;

                if (cutTime > 0)
                {
                    cutTime--;
                    m_txtWait.text = ToolScriptExtend.GetLangFormat(80100019, TimeHelper.FormatGameTimeWithDays(cutTime));
                }

                if (recoverTime > 0)
                {
                    recoverTime--;
                    m_txtNum_1.text = ToolScriptExtend.GetLangFormat(1161, TimeHelper.FormatGameTimeWithDays(recoverTime));

                    if (recoverTime == 0)
                    {
                        GameEntry.LogicData.UnionData.OnReqUnionTechList(OnUpdateInfo);
                    }
                }

                if (m_btnCost_1.longPressInterval < 0.5f && TimeComponent.Now - longTime > 0.5f)
                {
                    longClickCount = 0;
                    m_btnCost_1.longPressInterval = 0.5f;
                }
            }
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);

            var headEff = m_goCrit.transform.Find("battle_baodian").gameObject;
            ToolScriptExtend.SetParticleSystemSortingOrder(headEff, Depth);
        }

        private void OnUpdateEvent(object sender, GameEventArgs e)
        {
            OnUpdateInfo();
        }

        private void OnUpdateInfo()
        {
            cutTime = 0;
            recoverTime = 0;

            var unionData = GameEntry.LogicData.UnionData;
            var techId = unionData.GetTechId(groupId);
            var levelConfig = unionData.GetTechLvConfig(techId);
            if (levelConfig != null)
            {
                var iconStr = levelConfig.tech_icon;
                if (!iconStr.IsNullOrWhitespace())
                    m_imgIcon.SetImage(levelConfig.tech_icon);

                m_txtName.text = ToolScriptExtend.GetLang(levelConfig.tech_name);
                m_txtNum.text = $"{levelConfig.tech_lv}/{levelConfig.tech_max_lv}";
                m_txtDesc.text = ToolScriptExtend.GetLang(levelConfig.tech_desc);

                var config = GameEntry.LogicData.UnionData.GetUnionPermission();
                var isCanRecommend = config != null && config.is_settech;

                var techData = unionData.GetTechData(levelConfig.tech_group);
                var isUnlock = unionData.GetTechUnlock(levelConfig.tech_group);
                var isMaxLv = levelConfig.tech_lv >= levelConfig.tech_max_lv;
                var isBtnGray = false;
                if (isUnlock)
                {
                    if (!isMaxLv)
                    {
                        if (isCanRecommend)
                        {
                            var langId = techData.Recommend ? 80100006 : 80100005;
                            m_txtBtn.text = ToolScriptExtend.GetLang(langId);
                        }

                        var curExp = techData.Exp;
                        var maxExp = levelConfig.next_lv_exp;
                        var ratio = (float)curExp / maxExp;
                        ratio = ratio <= 1 ? ratio : 1;
                        m_imgProgress.rectTransform.sizeDelta = new Vector2(300 * ratio, 47);
                        m_txtProgress.text = $"{curExp}/{maxExp}";

                        if (techData.Status == Union.UnionStatus.Studying)
                        {
                            cutTime = (int)(techData.StartTime + levelConfig.lv_time - (long)TimeComponent.Now);
                            deltaTime = 0;

                            m_txtWait.text = ToolScriptExtend.GetLangFormat(80100019, TimeHelper.FormatGameTimeWithDays(cutTime));
                            m_txtTip.text = "";

                            var btnBg = m_btnSure.GetComponent<UIImage>();
                            btnBg.SetImage("Sprite/ui_public/button3.png");
                            m_txtSure.text = ToolScriptExtend.GetLang(80100024);
                        }
                        else if (techData.Status == Union.UnionStatus.Waiting)
                        {
                            m_txtWait.text = ToolScriptExtend.GetLangFormat(80100018, TimeHelper.FormatGameTimeWithDays(levelConfig.lv_time));
                            m_txtTip.text = ToolScriptExtend.GetLang(80100020);

                            var btnBg = m_btnSure.GetComponent<UIImage>();
                            btnBg.SetImage("Sprite/ui_public/button2.png");
                            m_txtSure.text = ToolScriptExtend.GetLang(80100023);
                            m_btnSure.SetButtonGray(!isCanRecommend);
                        }
                        else
                        {
                            var donateGold = GameEntry.LogicData.UnionData.GetTechDonateData(Union.UnionDonateType.Gold);
                            isBtnGray = donateGold.DonateTimes <= 0;
                            if (isBtnGray)
                            {
                                m_btnCost_1.nextLongPressTime = 0;
                                recoverTime = (int)(donateGold.DonateRecoveryTime - (long)TimeComponent.Now);
                                recoverTime = recoverTime <= 0 ? 0 : recoverTime;
                                m_txtNum_1.text = ToolScriptExtend.GetLangFormat(1161, TimeHelper.FormatGameTimeWithDays(recoverTime));
                            }
                            else
                            {
                                m_txtNum_1.text = ToolScriptExtend.GetLangFormat(80100014, donateGold.DonateTimes + "", normalNumMax + "");
                            }
                            m_txtCost_1.text = normalCostNum + "";

                            var donateDiamond = GameEntry.LogicData.UnionData.GetTechDonateData(Union.UnionDonateType.Diamond);
                            var addNum = donateDiamond.DonateTimes;
                            addNum = addNum < diamondAddMax ? addNum : diamondAddMax;
                            addNum = addNum > 1 ? addNum - 1 : 0;
                            m_txtCost_2.text = diamondCostNum + diamondAdd * addNum + "";
                        }

                        if (techData.Status != Union.UnionStatus.Normal)
                        {
                            m_btnCost_1.nextLongPressTime = 0;
                            if (m_goTip.activeSelf)
                            {
                                Timers.Instance.Remove("UnionTechUpForm");
                                m_goTip.SetActive(false);
                            }
                        }
                    }
                }
                m_goRecommend.SetActive(!isMaxLv && techData.Recommend && techData.Status == Union.UnionStatus.Normal);
                m_btnRecommend.gameObject.SetActive(isUnlock && !isMaxLv && isCanRecommend && techData.Status == Union.UnionStatus.Normal);
                m_goProgress.SetActive(isUnlock && !isMaxLv && techData.Status != Union.UnionStatus.Studying);
                m_goReward.SetActive(isUnlock && !isMaxLv && techData.Status == Union.UnionStatus.Normal);
                m_goWait.SetActive(isUnlock && !isMaxLv && techData.Status != Union.UnionStatus.Normal);
                m_goMax.SetActive(isUnlock && isMaxLv);
                m_btnCost_1.SetButtonGray(isBtnGray);

                if (!isMaxLv)
                {
                    var nextConfig = unionData.GetTechLvConfig(techId + 1);
                    if (nextConfig != null)
                    {
                        var showStr = nextConfig.attribute_desc;
                        m_txtNext.text = nextConfig.tech_group == 10010100 ? ToolScriptExtend.GetLang(int.Parse(showStr)) : showStr;
                    }
                }

                var descStr = levelConfig.attribute_desc;
                m_txtCur.text = levelConfig.tech_group == 10010100 ? ToolScriptExtend.GetLang(int.Parse(descStr)) : descStr;
                m_goShowCur.transform.SetLocalPositionY(isMaxLv ? 0 : 28);
                m_goShowNext.SetActive(!isMaxLv);
            }
        }

        private void OnShowTip()
        {
            m_goTip.SetActive(true);
            Timers.Instance.Add("UnionTechUpForm", 5f, (param) =>
            {
                m_goTip.SetActive(false);
            });
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnTipClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 9);
        }

        private void OnBtnRecommendClick()
        {
            var techData = GameEntry.LogicData.UnionData.GetTechData(groupId);
            GameEntry.LogicData.UnionData.OnReqUnionSetRecommendTech(groupId, !techData.Recommend, () =>
            {
                OnUpdateInfo();
            });
        }

        private void OnBtnCost_1Click()
        {
            var curNum = GameEntry.LogicData.BagData.GetAmountById((Config.itemid)normalCostId);
            if (curNum >= normalCostNum)
            {
                var donateGold = GameEntry.LogicData.UnionData.GetTechDonateData(Union.UnionDonateType.Gold);
                if (donateGold.DonateTimes <= 0)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLang(1162),
                    });
                    return;
                }

                GameEntry.LogicData.UnionData.OnReqUnionTechDonate(groupId, (int)Union.UnionDonateType.Gold, (multiple) =>
                {
                    OnItemMultiple(Union.UnionDonateType.Gold, multiple);
                    OnUpdateInfo();
                });

                if (isLongPress)
                {
                    if (isShowTip && m_goTip.activeSelf)
                    {
                        Timers.Instance.Remove("UnionTechUpForm");
                        m_goTip.SetActive(false);
                    }
                    longTime = TimeComponent.Now;
                    longClickCount++;
                    if (longClickCount >= 5) m_btnCost_1.longPressInterval = 0.2f;
                }
                else if (!isShowTip)
                {
                    clickCount++;
                    if (clickCount == 4 && donateGold.DonateTimes >= 5)
                    {
                        isShowTip = true;
                        OnShowTip();
                    }
                    longClickCount = 0;
                    m_btnCost_1.longPressInterval = 0.5f;
                }
            }
            else
            {
                // 打开道具获取途径弹窗
                ItemModule itemModule = new((Config.itemid)normalCostId);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(normalCostNum));
            }
        }

        private void OnBtnCost_2Click()
        {
            var donateDiamond = GameEntry.LogicData.UnionData.GetTechDonateData(Union.UnionDonateType.Diamond);
            var addNum = donateDiamond.DonateTimes;
            addNum = addNum < diamondAddMax ? addNum : diamondAddMax;
            addNum = addNum > 1 ? addNum - 1 : 0;
            var costNum = diamondCostNum + diamondAdd * addNum;
            var curNum = GameEntry.LogicData.BagData.GetAmountById((Config.itemid)diamondCostId);
            if (curNum >= costNum)
            {
                GameEntry.LogicData.UnionData.OnReqUnionTechDonate(groupId, (int)Union.UnionDonateType.Diamond, (multiple) =>
                {
                    OnItemMultiple(Union.UnionDonateType.Diamond, multiple);
                    OnUpdateInfo();
                });
            }
            else
            {
                // 打开道具获取途径弹窗
                ItemModule itemModule = new((Config.itemid)diamondCostId);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(costNum));
            }
        }

        private void OnItemMultiple(Union.UnionDonateType _type, int multiple)
        {
            Transform btnTrans;
            if (_type == Union.UnionDonateType.Gold)
            {
                btnTrans = m_btnCost_1.transform;
            }
            else
            {
                btnTrans = m_btnCost_2.transform;
            }

            if (multiple > 1)
            {
                var critObj = critObjList.Get();
                critObj.transform.SetPositionX(btnTrans.position.x + (_type == Union.UnionDonateType.Gold ? -0.5f : 0.5f));
                critObj.transform.SetSiblingIndex(-1);

                var critTxt = critObj.transform.Find("m_txtCrit").GetComponent<UIText>();
                critTxt.text = ToolScriptExtend.GetLangFormat(1163, multiple + "");

                var timerKey = "UnionTechUpForm_OnItemMultiple_" + playTime;
                Timers.Instance.Add(timerKey, 0.7f, (param) =>
                {
                    Timers.Instance.Remove(timerKey);
                    critObjList.Release(critObj);
                });
                playTime += 1;
            }

            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIResFlyForm) as UIResFlyForm;
            Vector2 starPos = form.GetPosByTrans(btnTrans);
            Vector2 endPos = form.GetPosByTrans(m_imgProgressIcon.gameObject.transform);
            FlyResManager.UIFly((Config.itemid)expId, 3, starPos, endPos);
            FlyResManager.FlyResByItemId((Config.itemid)rewardId, 3, btnTrans);
        }

        private void OnBtnSureClick()
        {
            var techData = GameEntry.LogicData.UnionData.GetTechData(groupId);
            if (techData.Status == Union.UnionStatus.Waiting)
            {
                var config = GameEntry.LogicData.UnionData.GetUnionPermission();
                var isCanRecommend = config != null && config.is_settech;
                if (isCanRecommend)
                {
                    GameEntry.LogicData.UnionData.OnReqUnionTechStudy(groupId, () =>
                    {
                        OnUpdateInfo();
                    });
                }
                else
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = "权限不足，无法升级",
                    });
                }
            }
            else { Close(); }
        }

        private void OnBtnMaxClick()
        {
            Close();
        }

        private void OnBtnTipMaskClick()
        {
            m_btnTipMask.gameObject.SetActive(false);
            m_goTipDesc.SetActive(false);
        }
    }
}
