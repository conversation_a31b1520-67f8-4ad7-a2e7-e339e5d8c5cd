using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIMainFaceForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnBorder;
        [SerializeField] private UIButton m_btnEnergy;
        [SerializeField] private UIButton m_btnBuff1;
        [SerializeField] private UIButton m_btnBuff2;
        [SerializeField] private UIButton m_btnBuff3;
        [SerializeField] private UIButton m_btnBuff4;
        [SerializeField] private UIButton m_btnBuff5;
        [SerializeField] private UIButton m_btnShop;
        [SerializeField] private UIButton m_btnChat;
        [SerializeField] private UIButton m_btnHero;
        [SerializeField] private UIButton m_btnWorld;
        [SerializeField] private UIButton m_btnVIP;
        [SerializeField] private UIButton m_btnBuildQueue;
        [SerializeField] private UIButton m_btnStudyQueue;
        [SerializeField] private UIButton m_btnTrain;
        [SerializeField] private UIButton m_btnTruck;
        [SerializeField] private UIButton m_btnSecret;
        [SerializeField] private UIButton m_btnWorker;
        [SerializeField] private UIButton m_btnRadar;
        [SerializeField] private UIButton m_btnBuilding;
        [SerializeField] private UIButton m_btnTurnToTask;
        [SerializeField] private UIButton m_btnTask;
        [SerializeField] private UIButton m_btnWorthyActivity;
        [SerializeField] private UIButton m_btnSpecialEvent;
        [SerializeField] private UIButton m_btnPioneer;
        [SerializeField] private UIButton m_btnLeagueBattle;
        [SerializeField] private UIButton m_btnSeason;
        [SerializeField] private UIButton m_btnLimitGift;
        [SerializeField] private UIButton m_btnLeagueHelp;
        [SerializeField] private UIButton m_btnEquipment;
        [SerializeField] private UIButton m_btnLeague;
        [SerializeField] private UIButton m_btnEmail;
        [SerializeField] private UIButton m_btnServiceMsg;
        [SerializeField] private UIButton m_btnBag;

        [SerializeField] private UIText m_txtLevel;
        [SerializeField] private UIText m_txtEnergy;
        [SerializeField] private UIText m_txtBattlePower;
        [SerializeField] private UIText m_txtRedDiamond;
        [SerializeField] private UIText m_txtBuildQueue;
        [SerializeField] private UIText m_txtBuildRed;
        [SerializeField] private UIText m_txtTask;
        [SerializeField] private UIText m_txtTaskRedCount;

        [SerializeField] private Image m_imgHead;
        [SerializeField] private Image m_imgEnergy;
        [SerializeField] private Image m_imgRedDiamond;
        [SerializeField] private Image m_imgHeroEntry;
        [SerializeField] private UIImage m_imgRedBuilding;
        [SerializeField] private Image m_imgTask;
        [SerializeField] private UIImage m_imgTaskRed;
        [SerializeField] private Image m_imgTaskComp;

        [SerializeField] private ScrollRect m_scrollviewTask;

        [SerializeField] private Slider m_slider;

        [SerializeField] private RectTransform m_rectMoveNotice;
        [SerializeField] private GameObject m_goResource;
        [SerializeField] private GameObject m_goHeroRed;
        [SerializeField] private GameObject m_goLeftTopCity;
        [SerializeField] private GameObject m_goLeftTopWorld;
        [SerializeField] private GameObject m_goTaskBg;
        [SerializeField] private GameObject m_goGiftContent;
        [SerializeField] private GameObject m_goTaskEff;
        [SerializeField] private GameObject m_goUnionRed;
        [SerializeField] private GameObject m_goLocation;
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goResItem;
        [SerializeField] private GameObject m_goExtend;

        void InitBind()
        {
            m_btnBorder.onClick.AddListener(OnBtnBorderClick);
            m_btnEnergy.onClick.AddListener(OnBtnEnergyClick);
            m_btnBuff1.onClick.AddListener(OnBtnBuff1Click);
            m_btnBuff2.onClick.AddListener(OnBtnBuff2Click);
            m_btnBuff3.onClick.AddListener(OnBtnBuff3Click);
            m_btnBuff4.onClick.AddListener(OnBtnBuff4Click);
            m_btnBuff5.onClick.AddListener(OnBtnBuff5Click);
            m_btnShop.onClick.AddListener(OnBtnShopClick);
            m_btnChat.onClick.AddListener(OnBtnChatClick);
            m_btnHero.onClick.AddListener(OnBtnHeroClick);
            m_btnWorld.onClick.AddListener(OnBtnWorldClick);
            m_btnVIP.onClick.AddListener(OnBtnVIPClick);
            m_btnBuildQueue.onClick.AddListener(OnBtnBuildQueueClick);
            m_btnStudyQueue.onClick.AddListener(OnBtnStudyQueueClick);
            m_btnTrain.onClick.AddListener(OnBtnTrainClick);
            m_btnTruck.onClick.AddListener(OnBtnTruckClick);
            m_btnSecret.onClick.AddListener(OnBtnSecretClick);
            m_btnWorker.onClick.AddListener(OnBtnWorkerClick);
            m_btnRadar.onClick.AddListener(OnBtnRadarClick);
            m_btnBuilding.onClick.AddListener(OnBtnBuildingClick);
            m_btnTurnToTask.onClick.AddListener(OnBtnTurnToTaskClick);
            m_btnTask.onClick.AddListener(OnBtnTaskClick);
            m_btnWorthyActivity.onClick.AddListener(OnBtnWorthyActivityClick);
            m_btnSpecialEvent.onClick.AddListener(OnBtnSpecialEventClick);
            m_btnPioneer.onClick.AddListener(OnBtnPioneerClick);
            m_btnLeagueBattle.onClick.AddListener(OnBtnLeagueBattleClick);
            m_btnSeason.onClick.AddListener(OnBtnSeasonClick);
            m_btnLimitGift.onClick.AddListener(OnBtnLimitGiftClick);
            m_btnLeagueHelp.onClick.AddListener(OnBtnLeagueHelpClick);
            m_btnEquipment.onClick.AddListener(OnBtnEquipmentClick);
            m_btnLeague.onClick.AddListener(OnBtnLeagueClick);
            m_btnEmail.onClick.AddListener(OnBtnEmailClick);
            m_btnServiceMsg.onClick.AddListener(OnBtnServiceMsgClick);
            m_btnBag.onClick.AddListener(OnBtnBagClick);
        }
    }
}
