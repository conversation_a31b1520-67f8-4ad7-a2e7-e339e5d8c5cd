using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class UITeamFormHUD : MonoBehaviour
    {
        private UITeamFormHeroContainer m_HeroContainer;
        private EnumBattlePos m_BattlePos;

        private UIImage m_ImageIcon;
        private UIImage m_ImageIconBg;
        private UIImage m_ServicesSpIcon;
        private UIImage m_PositionSpIcon;
        private UIText m_TxtLevel;
        private GameObject m_starObj;

        private RectTransform m_SelfRT;
        private RectTransform m_ParentRT;
        private Camera m_Camera;

        private Vector2 m_TempV2;

        public void Init(UITeamFormHeroContainer battleFiled, EnumBattlePos battlePos)
        {
            m_HeroContainer = battleFiled;
            m_BattlePos = battlePos;

            m_ImageIconBg = transform.Find("offset/imgIconBg")?.GetComponent<UIImage>();
            m_ImageIcon = transform.Find("offset/imgIconBg/mask/imgIcon")?.GetComponent<UIImage>();
            m_ServicesSpIcon = transform.Find("offset/servicesSp")?.GetComponent<UIImage>();
            m_PositionSpIcon = transform.Find("offset/positionSp")?.GetComponent<UIImage>();
            m_TxtLevel = transform.Find("offset/txtLevel")?.GetComponent<UIText>();
            m_starObj = transform.Find("offset/bg/starObj").gameObject;

            m_Camera = GameEntry.Camera.UICamera;//GameEntry.Camera.BattleCamera;
            m_SelfRT = transform.GetComponent<RectTransform>();
            m_ParentRT = transform.parent.GetComponent<RectTransform>();
        }

        public void Show()
        {
            var battleHero = m_HeroContainer.GetBattleHero(m_BattlePos);
            var heroData = GameEntry.LogicData.HeroData;
            var heroVo = heroData.GetHeroModule((itemid)battleHero.HeroId);
            m_TxtLevel.text = ToolScriptExtend.GetLangFormat(80000135, heroVo.level.ToString());
            m_ImageIconBg.SetImage(GetItemBgPath(heroVo.Quality));
            m_ImageIcon.SetImage(heroVo.HeroHead);
            m_PositionSpIcon.SetImage(heroData.GetPositionImgPath(heroVo.Position), true);
            m_ServicesSpIcon.SetImage(heroData.GetServicesImgPath(heroVo.Services), true);
            var starNum = heroVo.StarNum;
            var starOrder = heroVo.StarOrder;
            var count = m_starObj.transform.childCount;
            for (int i = 0; i < count; i++)
            {
                var starSp = m_starObj.transform.GetChild(i).GetComponent<UIImage>();
                string pathStr;
                if (i < starNum) { pathStr = "Sprite/ui_hero/hero_icon_star5.png"; }
                else if (i < starNum + 1 && starOrder > 0) { pathStr = string.Format("Sprite/ui_hero/hero_icon_star{0}.png", starOrder); }
                else { pathStr = "Sprite/ui_hero/hero_icon_star0.png"; }
                starSp.SetImage(pathStr);
            }

            gameObject.SetActive(true);
        }

        public void Hide()
        {
            ResetPos();
            gameObject.SetActive(false);
        }

        private void Update()
        {
            var hero = m_HeroContainer.GetBattleHero(m_BattlePos);
            if (hero == null)
                return;

            m_SelfRT.anchoredPosition = WorldPosToScreenPos(hero.GetPosition());
        }

        private void ResetPos()
        {
            var trans = m_HeroContainer.GetTeamPosByUid(m_BattlePos);
            m_SelfRT.anchoredPosition = WorldPosToScreenPos(trans.position);
        }

        private Vector2 WorldPosToScreenPos(Vector3 worldPos)
        {
            
            var screenPoint = RectTransformUtility.WorldToScreenPoint(m_Camera, worldPos);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(m_ParentRT, screenPoint,
                GameEntry.Camera.UICamera, out m_TempV2);
            return m_TempV2;
        }

        public string GetItemBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_blue.png",
                quality.quality_purple => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_purlpep.png",
                quality.quality_orange => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_yellow.png",
                _ => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_blue.png",
            };
        }
    }
}