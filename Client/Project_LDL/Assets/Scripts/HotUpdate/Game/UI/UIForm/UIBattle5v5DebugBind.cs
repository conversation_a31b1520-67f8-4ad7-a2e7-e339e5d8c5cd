using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIBattle5v5Debug : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnCreateTeam;
        [SerializeField] private UIButton m_btnRelease;

        [SerializeField] private UIText m_txtCreateTeam;
        [SerializeField] private UIText m_txtRelaease;

        [SerializeField] private InputField m_inputPos;
        [SerializeField] private InputField m_inputHero;

        void InitBind()
        {
            m_btnCreateTeam.onClick.AddListener(OnBtnCreateTeamClick);
            m_btnRelease.onClick.AddListener(OnBtnReleaseClick);
        }
    }
}
