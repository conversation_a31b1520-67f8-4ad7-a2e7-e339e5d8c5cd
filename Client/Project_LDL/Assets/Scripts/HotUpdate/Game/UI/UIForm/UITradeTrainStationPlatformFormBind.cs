using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTrainStationPlatformForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnTip;

        [SerializeField] private UIText m_txtTitle;

        [SerializeField] private ScrollRect m_scrollview;

        [SerializeField] private Transform m_transTrainGuard;
        [SerializeField] private Transform m_transStationPlatform;
        [SerializeField] private Transform m_transCarriage;

        void InitBind()
        {
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnTip.onClick.AddListener(OnBtnTipClick);
        }
    }
}
