using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using GameFramework.Localization;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public partial class UIPeakRewardForm : UGuiFormEx
    {
        private List<arena_compete_rank_reward> rewardList = new List<arena_compete_rank_reward>();
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            
            m_TableViewV.GetItemCount = GetItemCount;
            m_TableViewV.GetItemGo = GetItemGo;
            m_TableViewV.UpdateItemCell = UpdateItemCell;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            
            // 加载奖励配置
            LoadRewardConfig();
            
            // 初始化TableView
            m_TableViewV.InitTableViewByIndex(0);
        }

        private void LoadRewardConfig()
        {
            // 获取竞技场排名奖励配置
            rewardList = GameEntry.LDLTable.GetTable<arena_compete_rank_reward>();
            if (rewardList == null || rewardList.Count == 0)
            {
                Debug.LogError("[PeakReward] 获取奖励配置失败");
                rewardList = new List<arena_compete_rank_reward>();
            }
            else
            {
                Debug.Log($"[PeakReward] 加载奖励配置成功，共{rewardList.Count}条");
                
                // 按照排名范围排序
                rewardList.Sort((a, b) => a.rank_min.CompareTo(b.rank_min));
            }
        }

        public int GetItemCount()
        {
            return rewardList.Count;
        }

        public GameObject GetItemGo()
        {
            return m_goItem;
        }

        private void UpdateItemCell(int index, GameObject cellObj)
        {
            var data = rewardList[index];
            if (data == null)
            {
                Debug.LogError("[PeakReward] 奖励配置为空");
                return;
            }
            
            // 设置排名图标和文本
            var rankIcon = cellObj.transform.Find("imgRank")?.GetComponent<UIImage>();
            var rankText = cellObj.transform.Find("rank")?.GetComponent<UIText>();
             var RankText = cellObj.transform.Find("Rank")?.GetComponent<UIText>();
            // 根据排名设置不同的图标
            if (rankIcon != null)
            {
                rankIcon.gameObject.SetActive(true);
                if (data.rank_min == 1 && data.rank_max == 1)
                {
                    // 第一名
                    rankIcon.SetImage("Sprite/ui_youjian/paiming_icon1.png");
                    rankText.text = "";
                    RankText.text = "1";
                }
                else if (data.rank_min == 2 && data.rank_max == 2)
                {
                    // 第二名
                    rankIcon.SetImage("Sprite/ui_youjian/paiming_icon2.png");
                    rankText.text = "";
                    RankText.text = "2";
                }
                else if (data.rank_min == 3 && data.rank_max == 3)
                {
                    // 第三名
                    rankIcon.SetImage("Sprite/ui_youjian/paiming_icon3.png");
                    rankText.text = "";
                    RankText.text = "3";
                }
                else
                {
                    // 其他名次
                    rankIcon.gameObject.SetActive(false);
                    RankText.text = "";

                    // 设置排名范围文本
                    if (data.rank_min == data.rank_max)
                    {
                        rankText.text = data.rank_min.ToString();
                    }
                    else
                    {
                        rankText.text = $"{data.rank_min}-{data.rank_max}";
                    }
                }
            }
            
            // 获取奖励内容容器
            Transform rewardContent = cellObj.transform.Find("goGiftRewardList/viewPort/goGiftContent");
            if (rewardContent == null)
            {
                Debug.LogError("[PeakReward] 找不到奖励内容容器");
                return;
            }
            
            // 清空现有的奖励项
            for (int i = 0; i < rewardContent.childCount; i++)
            {
                rewardContent.GetChild(i).gameObject.SetActive(false);
            }
            
            // 显示奖励项
            List<reward> rewards = data.reward;
            for (int i = 0; i < rewards.Count; i++)
            {
                var rewardData = rewards[i];
                
                // 如果已有足够的子物体，则重用,,,,,,,,,, 
                if (i < rewardContent.childCount)
                {
                    GameObject rewardItem = rewardContent.GetChild(i).gameObject;
                    rewardItem.SetActive(true);
                    
                    // 获取UIItemModule组件
                    UIItemModule itemModule = rewardItem.GetComponent<UIItemModule>();
                    if (itemModule != null)
                    {
                        // 设置物品数据
                        itemModule.SetData(rewardData.item_id, rewardData.num);
                        itemModule.DisplayInfo();
                        
                        // 设置点击事件
                        itemModule.SetClick(() => {
                            itemModule.OpenTips();
                        });
                        
                        // 禁用按钮动画效果
                        UIButton itemButton = rewardItem.GetComponent<UIButton>();
                        if (itemButton != null)
                        {
                            itemButton.useTween = false;
                        }
                    }
                }
                else
                {
                    // 如果子物体不足，则创建新的
                    BagManager.CreatItem(rewardContent, rewardData.item_id, rewardData.num, (itemModule) => {
                        // 设置点击事件
                        itemModule.SetClick(() => {
                            itemModule.OpenTips();
                        });
                        itemModule.SetScale(0.5f);
                        // 禁用按钮动画效果
                        UIButton itemButton = itemModule.GetComponent<UIButton>();
                        if (itemButton != null)
                        {
                            itemButton.useTween = false;
                        }
                    });
                }
            }
            
            // 高亮当前排名对应的奖励
            int currentRank = (int)GameEntry.LogicData.PeakRankData.GetPersonalRank();
            bool isCurrentReward = currentRank >= data.rank_min && currentRank <= data.rank_max;
            
            // 设置背景颜色
            // var bgImage = cellObj.transform.Find("bg")?.GetComponent<UIImage>();
            // if (bgImage != null)
            // {
            //     // 根据是否是当前排名设置不同的背景
            //     if (isCurrentReward)
            //     {
            //         // 高亮背景
            //         bgImage.color = new Color(0.9f, 0.9f, 0.9f, 0.5f);
            //     }
            //     else
            //     {
            //         // 普通背景
            //         bgImage.color = new Color(1f, 1f, 1f, 0.2f);
            //     }
            // }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }
    }
}
