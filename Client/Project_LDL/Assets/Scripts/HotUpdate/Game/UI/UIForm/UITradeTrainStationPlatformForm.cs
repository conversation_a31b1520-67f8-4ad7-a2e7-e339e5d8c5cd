using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;
using GameFramework.Event;

namespace Game.Hotfix
{
    public partial class UITradeTrainStationPlatformForm : UGuiFormEx
    {
        PageViewVertical pageView;
        GameObject curPlayer;
        GameObject curBtnRide;
        bool isRiding;
        bool isThanks;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitPanel();

            pageView = m_scrollview.GetComponent<PageViewVertical>();
            pageView.CreatePageView(2);
            pageView.OnPageChanged = OnPageChanged;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            ResizeBg();
            pageView.pageTo(1, false);
            RefreshPanel();

            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            if (userData is bool flag)
            {
                isThanks = flag;
            }
        }

        void OnItemChange(object sender, GameEventArgs e)
        {
            RefreshContractCount();
        }

        private void OnBtnTrainHeadClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainInviteConductorForm);
        }

        private void OnBtnVIPClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainVIPForm);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnTipClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainDescForm);
        }

        private void OnBtnTopPageClick()
        {
            pageView.pageTo(0);
        }

        private void OnBtnThumbsUpAngelClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = "点赞成功！",
            });
            m_btnThumbsUpAngel.gameObject.SetActive(false);
        }

        private void OnBtnRefreshGoodsClick()
        {
            trade_set setting = GameEntry.TradeTruckData.GetTradeSet();
            if (setting == null) return;
            itemid costID = setting.train_refeshcost.item_id;
            long costNum = setting.train_refeshcost.num;
            long curNum = GameEntry.LogicData.BagData.GetAmountById(costID);
            if (curNum < costNum)
            {
                ItemModule itemModule = new(costID);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(costNum));
                return;
            }

            if (GameEntry.TradeTruckData.myTrain == null) return;

            GameEntry.TradeTruckData.RequestTrainRefreshGoods(GameEntry.TradeTruckData.myTrain.Id, (result) =>
            {
                ColorLog.Pink("刷新火车货物", result);
                RefreshPanel();
            });
        }

        private void OnBtnThanksListClick()
        {

        }

        void OnPageChanged(int index, GameObject obj)
        {
            if (index == 0)
            {
                m_txtTitle.text = ToolScriptExtend.GetLang(1300);
                m_goGuardianAngel.SetActive(true);
                m_btnTopPage.gameObject.SetActive(false);
                m_transRewardParent.GetChild(0).gameObject.SetActive(true);
                m_transBuff.gameObject.SetActive(true);
                m_goWelcome.SetActive(false);

                Transform team = m_rectTrainGuard.GetChild(0);
                foreach (Transform item in team)
                {
                    item.Find("txtPower").gameObject.SetActive(true);
                    item.Find("model1/Text").gameObject.SetActive(true);
                    item.Find("model1/starObj").gameObject.SetActive(true);
                    item.Find("model2/Text").gameObject.SetActive(true);
                    item.Find("model2/starObj").gameObject.SetActive(true);
                    item.Find("model3/Text").gameObject.SetActive(true);
                    item.Find("model3/starObj").gameObject.SetActive(true);
                    item.Find("model4/Text").gameObject.SetActive(true);
                    item.Find("model4/starObj").gameObject.SetActive(true);
                    item.Find("model5/Text").gameObject.SetActive(true);
                    item.Find("model5/starObj").gameObject.SetActive(true);
                }
            }
            else
            {
                m_txtTitle.text = ToolScriptExtend.GetLang(1299);
                m_goGuardianAngel.SetActive(false);
                m_btnTopPage.gameObject.SetActive(true);
                m_transRewardParent.GetChild(0).gameObject.SetActive(false);
                m_transBuff.gameObject.SetActive(false);
                m_goWelcome.SetActive(true);

                Transform team = m_rectTrainGuard.GetChild(0);
                foreach (Transform item in team)
                {
                    item.Find("txtPower").gameObject.SetActive(false);
                    item.Find("model1/Text").gameObject.SetActive(false);
                    item.Find("model1/starObj").gameObject.SetActive(false);
                    item.Find("model2/Text").gameObject.SetActive(false);
                    item.Find("model2/starObj").gameObject.SetActive(false);
                    item.Find("model3/Text").gameObject.SetActive(false);
                    item.Find("model3/starObj").gameObject.SetActive(false);
                    item.Find("model4/Text").gameObject.SetActive(false);
                    item.Find("model4/starObj").gameObject.SetActive(false);
                    item.Find("model5/Text").gameObject.SetActive(false);
                    item.Find("model5/starObj").gameObject.SetActive(false);
                }
            }
        }

        void InitPanel()
        {
            foreach (Transform item in m_transCarriage)
            {
                GameObject player = item.Find("player").gameObject;

                UIButton btnRide = item.Find("btnRide").GetComponent<UIButton>();
                btnRide.onClick.RemoveAllListeners();
                btnRide.onClick.AddListener(() =>
                {
                    if (isRiding)
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UICommonConfirmForm, new DialogParams
                        {
                            Title = ToolScriptExtend.GetLang(1100147),
                            Content = ToolScriptExtend.GetLang(1274),
                            ConfirmText = ToolScriptExtend.GetLang(1100144),
                            CancelText = ToolScriptExtend.GetLang(1100143),
                            OnClickConfirm = (data) =>
                            {
                                if (curPlayer != null)
                                {
                                    curPlayer.SetActive(false);
                                }
                                if (curBtnRide != null)
                                {
                                    curBtnRide.SetActive(true);
                                }
                                player.SetActive(true);
                                btnRide.gameObject.SetActive(false);
                                curPlayer = player;
                                curBtnRide = btnRide.gameObject;

                                if (!isThanks)
                                {
                                    GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainThanksForm, true);
                                }
                            },
                        });
                    }
                    else
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainThanksForm, false);
                        isRiding = true;
                        if (curPlayer != null)
                        {
                            curPlayer.SetActive(false);
                        }
                        if (curBtnRide != null)
                        {
                            curBtnRide.SetActive(true);
                        }
                        player.SetActive(true);
                        btnRide.gameObject.SetActive(false);
                        curPlayer = player;
                        curBtnRide = btnRide.gameObject;
                    }
                });
            }
        }

        void RefreshPanel()
        {
            if (GameEntry.TradeTruckData.myTrain == null) return;

            int index = 0;
            foreach (Transform item in m_transRewardParent)
            {
                if (index < GameEntry.TradeTruckData.myTrain.Boxcar.Count)
                {
                    Trade.TradeBoxcar tradeBoxcar = GameEntry.TradeTruckData.myTrain.Boxcar[index];
                    if (tradeBoxcar != null && tradeBoxcar.Goods.Count > 0)
                    {
                        Transform grid = item.Find("grid");
                        List<Trade.TradeGoods> rewards = new(tradeBoxcar.Goods);
                        bool needEffect = index > 0;
                        RefreshReward(rewards, grid, m_transReward, needEffect);
                    }
                }
                index++;
            }

            Trade.TradeBoxcar tradeBoxcarHead = GameEntry.TradeTruckData.myTrain.Boxcar[0];
            if (tradeBoxcarHead != null && tradeBoxcarHead.Passengers.Count > 0)
            {
                Trade.TradePassenger tradePassenger = tradeBoxcarHead.Passengers[0];
                m_btnTrainHead.gameObject.SetActive(false);
                m_goPlayerTrainHead.SetActive(true);
                m_goBottomTip.SetActive(false);
            }
            else
            {
                m_btnTrainHead.gameObject.SetActive(true);
                m_goPlayerTrainHead.SetActive(false);
                m_goBottomTip.SetActive(true);
            }

            RefreshContractCount();
        }

        void RefreshReward(List<Trade.TradeGoods> rewards, Transform transContentReward, Transform transReward, bool needEffect = true)
        {
            foreach (Transform item in transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < rewards.Count; i++)
            {
                if (i < transContentReward.childCount)
                {
                    transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                }
                else
                {
                    int j = i;
                    Transform transRewardItem = Instantiate(transReward, transContentReward);
                    BagManager.CreatItem(transRewardItem, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.transform.Find("count").localScale = new Vector2(1.6f, 1.6f);
                        if (j == 0)
                        {
                            GameObject effect = Instantiate(m_goItemEffect, transRewardItem);
                            effect.transform.localScale = new Vector3(2f, 2f, 1f);
                            effect.SetActive(true);
                        }
                    });
                }
            }
        }

        void RefreshContractCount()
        {
            trade_set setting = GameEntry.TradeTruckData.GetTradeSet();
            if (setting == null) return;
            itemid costID = setting.train_refeshcost.item_id;
            long costNum = setting.train_refeshcost.num;
            long curNum = GameEntry.LogicData.BagData.GetAmountById(costID);
            m_txtContractCount.text = $"{curNum}/{costNum}";
        }

        void ResizeBg()
        {
            RectTransform rect = transform.GetComponent<RectTransform>();
            float rate = rect.rect.height / 1920f;
            m_rectBg.localScale = new Vector3(rate, rate, 1);
            float height = m_rectBg.rect.height * rate / 2;
            m_rectTrainGuard.sizeDelta = new Vector2(m_rectTrainGuard.rect.width, height);
            m_rectStationPlatform.sizeDelta = new Vector2(m_rectStationPlatform.rect.width, height);
            LayoutRebuilder.ForceRebuildLayoutImmediate(m_rectContent);
        }
    }
}
