using System;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class WmpElementUiHud : WmpUiHudBase
    {
        [SerializeField] public GameObject goZombies;
        [SerializeField] public GameObject goGather;
        [SerializeField] public GameObject goDoomElite;

        [SerializeField] public GameObject goFood;
        [SerializeField] public GameObject goIron;
        [SerializeField] public GameObject goGold;

        [SerializeField] public GameObject goLevelItem;

        [SerializeField] public UIText txtLevel;


        private Camera m_MainCamera;
        private RectTransform m_SelfRect;
        private RectTransform m_ParentRect;
        private Vector2 m_TempV2;

        private Vector2Int m_Pos;
        private int m_Id;

        private WorldMapElementType m_BigType;
        private WorldMapGatherType m_SmallType;

        public void SetData(int id, int x, int y)
        {
            m_Id = id;
            m_Pos = new Vector2Int(x, y);

            var mapElement = GameEntry.LDLTable.GetTableById<map_element>(id);
            m_BigType = (WorldMapElementType)mapElement.big_type;
            m_SmallType = (WorldMapGatherType)mapElement.small_type;

            txtLevel.text = mapElement.level.ToString();

            ResetDisplay();
        }

        public override void Depool()
        {
            if (m_MainCamera == null)
                m_MainCamera = GameEntry.Camera.WorldMapCamera;
            if (m_ParentRect == null)
                m_ParentRect = transform.parent.GetComponent<RectTransform>();
            if (m_SelfRect == null)
                m_SelfRect = transform.GetComponent<RectTransform>();

            GameEntry.Event?.Subscribe(OnWorldMapChangeLODArgs.EventId, OnWorldMapChangeLOD);
        }

        private void OnWorldMapChangeLOD(object sender, GameEventArgs e)
        {
            ResetDisplay();
        }

        private void ResetDisplay()
        {
            if (CameraComponent.ZoomLevel > WorldMapLOD.Level1 && CameraComponent.ZoomLevel < WorldMapLOD.Level4)
            {
                goZombies.SetActive(m_BigType == WorldMapElementType.Zombies);
                goGather.SetActive(m_BigType == WorldMapElementType.Gather);
                goDoomElite.SetActive(m_BigType == WorldMapElementType.DoomElite);

                if (goGather.activeSelf)
                {
                    goFood.SetActive(m_SmallType == WorldMapGatherType.Food);
                    goGold.SetActive(m_SmallType == WorldMapGatherType.Gold);
                    goIron.SetActive(m_SmallType == WorldMapGatherType.Iron);
                }
            }
            else
            {
                goZombies.SetActive(false);
                goGather.SetActive(false);
                goDoomElite.SetActive(false);
            }

            goLevelItem.SetActive(CameraComponent.ZoomLevel <= WorldMapLOD.Level2);
        }

        public override void Repool()
        {
            GameEntry.Event?.Unsubscribe(OnWorldMapChangeLODArgs.EventId, OnWorldMapChangeLOD);
        }

        private void LateUpdate()
        {
            ResetPos();
        }

        private void ResetPos()
        {
            var screenPoint = RectTransformUtility.WorldToScreenPoint(m_MainCamera, new Vector3(m_Pos.x, 0, m_Pos.y)+GameDefine.HalfOffset);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(m_ParentRect, screenPoint,
                GameEntry.Camera.UICamera, out m_TempV2);
            m_SelfRect.anchoredPosition = m_TempV2;
        }
    }
}