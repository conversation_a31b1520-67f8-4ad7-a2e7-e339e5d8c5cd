using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UISelectFinalHeroForm : UGuiFormEx
    {
        private RectTransform contentRect;
        private class SelectNode
        {
            public int index;
            public GameObject mark;
            public GameObject heart;
            public UIButton btn;
            public itemid heroId;
        }
        
        private List<itemid> heroList;
        private List<SelectNode> nodeList = new List<SelectNode>();
        private int curSelectIndex = -1;
        private itemid curHeroId = itemid.itemid_nil;

        private UIImage img_Type;
        private UIImage img_Attr;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            contentRect = m_goContent.GetComponent<RectTransform>();
            img_Type = m_btnType.GetComponent<UIImage>();
            img_Attr = m_btnAttr.GetComponent<UIImage>();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            heroList = GameEntry.LogicData.RecruitData.GetWishHeroList();
            
            curSelectIndex = -1;
            ShowHeroList();
            
            var recordHeroId = GameEntry.LogicData.RecruitData.GetWishHero();
            var initIndex = -1;
            for (var i = 0; i < nodeList.Count; i++)
            {
                if (recordHeroId == heroList[i])
                {
                    initIndex = i;
                    break;
                }
            }

            if (initIndex == -1)
            {
                foreach (var node in nodeList)
                {
                    node.mark.SetActive(false);
                    node.heart.SetActive(false);
                }
                m_btnSelect.gameObject.SetActive(true);
                m_btnSelected.gameObject.SetActive(false);
            }
            else
            {
                curHeroId = heroList[initIndex];
                OnSelectIndex(initIndex);
                JumpToTarget(initIndex, heroList.Count);
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            
        }

        private void OnBtnBackClick()
        {
           Close();
        }

        private void OnBtnSelectClick()
        {
            if (curSelectIndex < 0 || curSelectIndex >= heroList.Count)
            {
                return;
            }
            curHeroId = heroList[curSelectIndex];
            SetSelectView(curSelectIndex);
            GameEntry.LogicData.RecruitData.C2SRecruitSetWish(1001, curHeroId, (msg) =>
            {
            });
            Close();
        }

        private void OnBtnSelectedClick()
        {
             
        }

        private void OnBtnTypeClick()
        {

        }

        private void OnBtnAttrClick()
        {

        }

        private void OnBtnDetailClick()
        {
            GameEntry.LogicData.HeroData.OpenHeroSingleForm((itemid)heroList[curSelectIndex]);
        }

        private void ShowHeroList()
        {
            var parent = m_goContent.transform;
            ToolScriptExtend.ClearAllChild(parent);
            nodeList.Clear();

            for (var i = 0; i < heroList.Count; i++)
            {
                var index = i;
                var obj = Instantiate(m_goSelectItem, parent);
                SetHeroInfo(heroList[i], obj);
                
                var btn = obj.transform.Find("btn").GetComponent<UIButton>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    OnSelectIndex(index);
                });
                
                nodeList.Add(new SelectNode()
                {
                    index = index,
                    mark = obj.transform.Find("mark").gameObject,
                    heart = obj.transform.Find("flag").gameObject,
                    btn = btn,
                    heroId = heroList[i]
                });
            }
        }

        private void OnSelectIndex(int index)
        {
            if (curSelectIndex == index)
            {
                return;
            }

            curSelectIndex = index;
            SetSelectView(index);
            var tempHeroId = heroList[index];
            DisplayHeroView(tempHeroId);
            SetTipStr(tempHeroId);
        }

        private void SetSelectView(int index)
        {
            foreach (var node in nodeList)
            {
                bool isCur = node.index == index;
                node.mark.SetActive(isCur);
                
                bool hasConfirm = curHeroId == node.heroId;
                node.heart.SetActive(hasConfirm);
            }
            
            var temp = heroList[curSelectIndex];
            m_btnSelect.gameObject.SetActive(temp != curHeroId);
            m_btnSelected.gameObject.SetActive(temp == curHeroId);
        }

        private void DisplayHeroView(itemid heroId)
        {
            var heroData = GameEntry.LogicData.RecruitData.GetHeroDataById(heroId);
            if (heroData == null) return;
            m_imgVertical.SetImage(heroData.hero_pic);
            m_txtName.text = ToolScriptExtend.GetItemName(heroId);
            m_txtTip.text = ToolScriptExtend.GetLang(heroData.hero_introduction);
            
            img_Type.SetImage(GameEntry.LogicData.HeroData.GetServicesImgPath(heroData.services));
            img_Attr.SetImage(GameEntry.LogicData.HeroData.GetPositionImgPath(heroData.position));
            m_imgQuality.SetImage(GameEntry.LogicData.HeroData.GetQualityImgPath(heroData.quality));
        }
        
        private void JumpToTarget(int index,int sumCount)
        {
            if (sumCount <= 4) return;
            if (index < 2)
            {
                m_scrollviewList.normalizedPosition = new Vector2(0, 1);
            }
            else if (index > sumCount - 2)
            {
                m_scrollviewList.normalizedPosition = new Vector2(1, 1);
            }
            else
            {
                var layout = m_goContent.GetComponent<HorizontalLayoutGroup>();

                var value = index - 1;
                value = value < 0 ? 0 : value;
                var width = layout.padding.left + layout.spacing * (index - 1)+206*index;

                width -= 355;
                m_scrollviewList.content.anchoredPosition = new Vector2(-width, 0);
            }
        }
        
        private void SetTipStr(itemid heroId)
        {
            //如果是已获得的非5星的英雄则显示“选择该英雄为心愿”，
            //如果是未获得的英雄则显示“未获得该英雄”，
            //如果是已获得的5星的英雄则显示“XXX已达5星”
            var isOwn = GameEntry.LogicData.HeroData.IsHeroActive(heroId);
            if (isOwn)
            {
                var heroModule = GameEntry.LogicData.HeroData.GetHeroModule(heroId);
                var is5Star = heroModule.StarNum >=5;
                if (is5Star)
                {
                    //该英雄已达5星
                    m_txtDesc.text = ToolScriptExtend.GetLang(1100118);
                }
                else
                {
                    //选择该英雄为心愿
                    m_txtDesc.text = ToolScriptExtend.GetLang(1100116);
                }
            }
            else
            {
                //未获得该英雄
                m_txtDesc.text = ToolScriptExtend.GetLang(1100117);
            }
        }
        
        private void SetHeroInfo(itemid heroId,GameObject obj)
        {
            var root = obj.transform;
            var heroData = GameEntry.LogicData.RecruitData.GetHeroDataById(heroId);
            if (heroData != null)
            {
                var icon = root.Find("icon").GetComponent<UIImage>();
                var position = root.Find("position").GetComponent<UIImage>();
                var service = root.Find("service").GetComponent<UIImage>();
                var txt = root.Find("txt").GetComponent<UIText>();
                
                icon.SetImage(heroData.hero_head);
                service.SetImage(GameEntry.LogicData.HeroData.GetServicesImgPath(heroData.services));
                position.SetImage(GameEntry.LogicData.HeroData.GetPositionImgPath(heroData.position));
                txt.text = "Lv.1";
            }
        }
        
    }
}
