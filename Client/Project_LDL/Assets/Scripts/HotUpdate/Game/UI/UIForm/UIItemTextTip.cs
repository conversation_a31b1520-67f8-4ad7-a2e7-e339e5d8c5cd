
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public partial class UIItemTextTip : UGuiFormEx
    {
        public enum ItemTextTipType{
            MidLeft = 1,
            MidRight = 2,
            TopLeft = 3,
            TopRight =4,
            UnderLeft = 5,
            UnderRight = 6,
        }
        public ItemTextTipType type = ItemTextTipType.MidRight;
        public UIItemModule item;
        public float txtHeight;
        public float txtWidth;
        //偏移缩小比例
        public float offsetX_pre;
        //偏移缩小比例
        public float offsetY_pre;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);                    
            item = userData as UIItemModule;
            offsetX_pre = 0.9f;
            offsetY_pre = 0.7f;
            RectTransform txt_rectTransform = m_goNode.transform.GetComponent<RectTransform>();
            txtHeight = txt_rectTransform.rect.height;
            txtWidth = txt_rectTransform.rect.width;
            Vector3 worldPosition = item.itemObj.transform.position;
            RectTransform rectTransform = item.itemObj.transform.GetComponent<RectTransform>();
             //init type
            Vector3 screenPosition = GameEntry.Camera.UICamera.WorldToScreenPoint(worldPosition);
            float screenWidth = Screen.width;
            float screenHeight = Screen.height;
            // Debug.LogError(screenWidth);
            // Debug.LogError(screenHeight);
            // Debug.LogError(screenPosition);

            if (screenPosition.x < txtWidth)
            {
                //贴左
                if(screenPosition.y <= screenHeight/2 && txtHeight/2 > screenPosition.y)
                {
                    //right top
                    type = ItemTextTipType.TopRight;
                }
                else if(screenPosition.y > screenHeight/2 && txtHeight/2 > screenHeight-screenPosition.y)
                {
                    //right under
                    type = ItemTextTipType.UnderRight;
                }
                else
                {
                    //right mid
                    type = ItemTextTipType.MidRight;
                }                                              
            }
            else if (txtWidth >= screenWidth-screenPosition.x)
            {
                 //贴右
                if(screenPosition.y <= screenWidth/2 && txtHeight/2 > screenPosition.y)
                {
                    // left top
                    type = ItemTextTipType.TopLeft;
                }
                else if(screenPosition.y > screenWidth/2 && txtHeight/2 > screenHeight-screenPosition.y)
                {
                  //left under
                    type = ItemTextTipType.UnderLeft;
                }
                else{
                    type = ItemTextTipType.MidLeft;
                }            
            }
            else
            {
                //中间的
                if(screenPosition.y > screenWidth/2 && txtHeight/2 > screenHeight-screenPosition.y)
                {
                  
                    type = ItemTextTipType.UnderRight;
                }
                else if(screenPosition.y <= screenWidth/2 && txtHeight/2 > screenPosition.y)
                {
                   
                    type = ItemTextTipType.TopRight;
                }
                else if(screenPosition.y <= screenHeight/2 && txtHeight/2 > screenHeight-screenPosition.y)
                {
                   
                    type = ItemTextTipType.TopRight;
                }
                else if(screenPosition.y > screenHeight/2 && txtHeight/2 > screenHeight-screenPosition.y)
                {
                    
                    type = ItemTextTipType.TopLeft;
                }
                else {
                    type = ItemTextTipType.MidRight;
                }          
            }
            //Debug.LogError(type);
            //GameEntry.Camera.UICamera
            //偏移
            Vector3 offset = new Vector3(0f,0f,0f);
            float cellHeight = rectTransform.rect.height*offsetY_pre;
            switch (type)
            {               
                case ItemTextTipType.MidLeft:          
                    offset.x = -rectTransform.rect.width*offsetX_pre;
                    m_goArrowLeft.SetActive(false);
                    m_goArrowRight.SetActive(true);
                    break;
                case ItemTextTipType.MidRight:
                    offset.x = rectTransform.rect.width*offsetX_pre;
                    m_goArrowLeft.SetActive(true);
                    m_goArrowRight.SetActive(false);
                    break;
                case ItemTextTipType.TopLeft:                
                    offset.x = -rectTransform.rect.width*offsetX_pre;
                    offset.y = cellHeight/2+txtHeight/2;
                    m_goArrowLeft.SetActive(false);
                    m_goArrowRight.SetActive(false);
                    break;
                case ItemTextTipType.TopRight:
                    offset.x = rectTransform.rect.width*offsetX_pre;
                    offset.y = cellHeight/2+txtHeight/2;
                    m_goArrowLeft.SetActive(false);
                    m_goArrowRight.SetActive(false);
                    break;
                case ItemTextTipType.UnderLeft:
                    offset.x = -rectTransform.rect.width*offsetX_pre;
                    offset.y = -cellHeight/2-txtHeight/2;
                    m_goArrowLeft.SetActive(false);
                    m_goArrowRight.SetActive(false);
                    break;
                case ItemTextTipType.UnderRight:
                    offset.x = rectTransform.rect.width*offsetX_pre;
                    offset.y = -cellHeight/2-txtHeight/2;
                    m_goArrowLeft.SetActive(false);
                    m_goArrowRight.SetActive(false);
                    break;                 
                default:
                break;
            }
            
            m_goNode.transform.position = worldPosition;                
            m_goNode.transform.localPosition = 
            new Vector3(m_goNode.transform.localPosition.x + offset.x,m_goNode.transform.localPosition.y+offset.y,m_goNode.transform.localPosition.z);
            item_config config = item.GetItemConfig();
            m_txtName.text = ToolScriptExtend.GetLang(config.name);
            long count = GameEntry.LogicData.BagData.GetAmountById(item.ItemId);
            m_txtCount.text = ToolScriptExtend.GetLang(1100293) + ToolScriptExtend.FormatNumberWithUnit(count);
            m_txtCount.gameObject.SetActive(true);
            string desc;
            // if(config.item_subtype ==itemsubtype.itemsubtype_levelchest || config.item_subtype == itemsubtype.itemsubtype_useitemgetitem){
            //     desc =   item.itemModule.ITM_GetUseTotalNum(1);
            // }
            // else
            // {
                desc = item.itemModule.GetItemDes();
                if (string.IsNullOrEmpty(desc))
                {
                    desc = ToolScriptExtend.GetLang(config.detail_desc);
                }
            //}
           
            m_txtDes.text = desc;
           // Debug.LogError(worldPosition);
            m_btnDetail.gameObject.SetActive(item.itemModule.GetItemConfig().i_button != ibutton.ibutton_none);
            m_btnDetail.onClick.RemoveAllListeners();
            if(item.itemModule.GetItemConfig().i_button == ibutton.ibutton_probability){
                m_btnDetail.onClick.AddListener(() =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIItemBoxProForm,item.itemModule.ItemId);                  
                    //Debug.Log("弹出概率公示弹窗  ");
                });
            }
        }   

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        public void OnBtnCloseClick()
        {
           Close();
        }
        public void OnBtnDetailClick(){
            
        }
        public void ShowCountText(bool isShow)
        {
            m_txtCount.gameObject.SetActive(isShow);
        }
    }
}
