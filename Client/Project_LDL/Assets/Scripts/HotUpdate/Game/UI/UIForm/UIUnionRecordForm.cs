using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using Sirenix.Utilities;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionRecordForm : UGuiFormEx
    {
        private int selelctIdx;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_txtNone.text = ToolScriptExtend.GetLang(1097);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            int count = m_transBtnList.childCount;
            for (int i = 0; i < count; i++)
            {
                var rootTrans = m_transBtnList.GetChild(i);

                var selectBg = rootTrans.Find("selectBg");
                selectBg.gameObject.SetActive(false);

                var rectTrans = rootTrans.GetComponent<RectTransform>();
                rectTrans.sizeDelta = new Vector2(251, 102);

                int index = i;
                var btn = rootTrans.GetComponent<Button>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    OnSelectBtn(index);
                });
            }

            selelctIdx = -1;
            OnSelectBtn(0);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnSelectBtn(int index)
        {
            if(selelctIdx == index) return;
            
            Transform trans = m_transBtnList.GetChild(index);
            if (trans != null)
            {
                var rectTrans = trans.GetComponent<RectTransform>();
                var selectBg = trans.Find("selectBg").gameObject;
                rectTrans.sizeDelta = new Vector2(266, 102);
                selectBg.SetActive(true);
            }

            if (selelctIdx > -1)
            {
                trans = m_transBtnList.GetChild(selelctIdx);
                if (trans != null)
                {
                    var rectTrans = trans.GetComponent<RectTransform>();
                    var selectBg = trans.Find("selectBg").gameObject;
                    rectTrans.sizeDelta = new Vector2(251, 102);
                    selectBg.SetActive(false);
                }
            }
            selelctIdx = index;
            OnUpdateInfo();

            var rect = m_transBtnList.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
        }

        private void OnUpdateInfo()
        {
            GameEntry.LogicData.UnionData.OnReqUnionRecord(selelctIdx + 1, (resp) =>
            {
                var list = resp.Records;
                list.Sort((a, b) =>
                {
                    return a.Time > b.Time ? -1 : 1;
                });

                var count = list.Count;
                m_TableViewV.GetItemCount = () => count;
                m_TableViewV.GetItemGo = () => m_goItem;
                m_TableViewV.UpdateItemCell = (index, go) =>
                {
                    var rootTrans = go.transform;
                    var bg = rootTrans.GetComponent<UIImage>();
                    var icon = rootTrans.Find("icon").GetComponent<UIImage>();
                    var descTxt = rootTrans.Find("descTxt").GetComponent<UIText>();
                    var dateTxt = rootTrans.Find("dateTxt").GetComponent<UIText>();

                    var data = list[index];
                    var config = GameEntry.LDLTable.GetTableById<union_log>(data.TypeId);

                    bg.SetImage(config.bg_location);
                    icon.SetImage(config.icon_location);

                    var strArr = data.Args.ToArray();
                    if (config.id == 206 || config.id == 207)
                    {
                        var namrStr1 = "";
                        if (int.TryParse(strArr[2], out int positionId))
                        {
                            var postCfg = GameEntry.LogicData.UnionData.GetUnionPosition(positionId);
                            if (postCfg != null)
                                namrStr1 = ToolScriptExtend.GetLang(postCfg.position_name);
                        }

                        var namrStr2 = "";
                        if (int.TryParse(strArr[3], out positionId))
                        {
                            var postCfg = GameEntry.LogicData.UnionData.GetUnionPosition(positionId);
                            if (postCfg != null)
                                namrStr2 = ToolScriptExtend.GetLang(postCfg.position_name);
                        }

                        descTxt.text = ToolScriptExtend.GetLangFormat(config.lg_desc, strArr[0], strArr[1], namrStr1, namrStr2);
                    }
                    else
                    {
                        descTxt.text = ToolScriptExtend.GetLangFormat(config.lg_desc, strArr);
                    }

                    dateTxt.text = TimeHelper.ToDateTimeText(data.Time / 1000);
                };
                m_TableViewV.InitTableViewByIndex(0);

                m_txtNone.gameObject.SetActive(count <= 0);
            });
        }

        private void OnBtnExitClick()
        {
            Close();
        }
    }
}
