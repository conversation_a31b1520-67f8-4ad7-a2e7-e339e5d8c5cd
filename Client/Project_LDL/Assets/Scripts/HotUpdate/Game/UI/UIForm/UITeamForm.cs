using System;
using System.Collections;
using System.Collections.Generic;
using Battle;
using DG.Tweening;
using Fight;
using Game.Hotfix.Config;
using GameFramework.Event;
using JetBrains.Annotations;
using Team;
using UnityEngine;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;
using Action = System.Action;

namespace Game.Hotfix
{
    public enum UITeamFormType
    {
        /// <summary>
        /// 普通队列
        /// </summary>
        Normal = 1,
    }

    public class UITeamFormParam
    {
        public UITeamFormType TeamFormType;
        
    }

    public partial class UITeamForm : UGuiFormEx
    {
        public int SelectType => selectType;
        private int selectType = -1;

        private UITeamFormType m_CurTeamFormType;

        List<Dictionary<EnumBattlePos, int>> m_TeamData;
        
        private Dictionary<EnumBattlePos, UITeamFormHUD> m_HudsList;

        private List<TeamType> m_TeamTypes;
        private ushort m_CurTeamTypeIndex;

        private bool m_TeamDataDirty = false;
        private bool m_RelationDirty = false;
        
        private Tweener m_RelationTweener;

        private EnumBattleRelation m_CurRelation = EnumBattleRelation.None;

        private UITeamFormHeroContainer m_HeroContainer; 
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_HeroContainer = m_goHeroContainer.GetComponent<UITeamFormHeroContainer>();
            
            int childCount = m_transBtnList.childCount;
            var heroData = GameEntry.LogicData.HeroData;
            for (int i = 0; i < childCount; i++)
            {
                var trans = m_transBtnList.GetChild(i);

                if (i > 0)
                {
                    var icon = trans.Find("bg/icon").GetComponent<Image>();
                    var selecSp = trans.Find("selectBg/selecSp").GetComponent<Image>();
                    icon.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                    selecSp.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                }

                int index = i;
                var rectTrans = trans.GetComponent<RectTransform>();
                // rectTrans.sizeDelta = new Vector2(245, 91);

                var btn = trans.GetComponent<Button>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() => { OnSelectBtn(index); });
            }
        }

        private void LateUpdate()
        {
            if (m_RelationDirty)
            {
                m_RelationDirty = false;
                TryResetRelationUI();
            }
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            // if (userData is xxx)
            // {
            //     m_CurTeamFormType == 
            // }
            
            if (m_CurTeamFormType == UITeamFormType.Normal)
            {
                m_TeamTypes = new List<TeamType>();
                m_TeamTypes.Add(TeamType.Common1);
                m_TeamTypes.Add(TeamType.Common2);
                m_TeamTypes.Add(TeamType.Common3);
                m_TeamTypes.Add(TeamType.Common4);
            }
            
            m_TeamData = new List<Dictionary<EnumBattlePos, int>>();
            for (int i = 0; i < m_TeamTypes.Count; i++)
            {
                var teamDic = GameEntry.LogicData.TeamData.GetTeamDic(m_TeamTypes[i]);
                m_TeamData.Add(teamDic);
            }
            
            m_TeamDataDirty = false;

            InitTouch();

            InitHud();

            if (selectType != 0)
            {
                OnSelectBtn(0);
            }
            else
            {
                OnUpdateInfo();
            }

            RefreshAirportAttr();

            InitUI();

            TryResetRelationUI(true);

            m_HeroContainer.OnOpen();
            
            m_HeroContainer.OnHeroCreateCall += OnHeroCreate;
            m_HeroContainer.OnHeroDeleteCall += OnHeroDelete;
            
            GameEntry.Event.Subscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            
            m_HeroContainer.OnHeroCreateCall -= OnHeroCreate;
            m_HeroContainer.OnHeroDeleteCall -= OnHeroDelete;
            
            m_HeroContainer.OnClose();
            
            GameEntry.Event.Unsubscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            UnInitHud();
            UnInitTouch();
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void InitUI()
        {
            UpdatePower();
        }

        private void UpdatePower()
        {

            double power = m_HeroContainer.GetPowerFromHeroModule();
            m_txtBattleValue.text = ToolScriptExtend.FormatNumberWithUnit(power);
        }

        private void OnBtnPowerLeftClick()
        {
            m_goPower.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnPowerRightClick()
        {
        }

        private void OnBtnLeftRelationClick()
        {
            var list = m_HeroContainer.GetTeamArmyTypeList();
            EnumBattleRelation relation = ToolScriptExtend.GetRelation(list);
            
            var relationItem = m_goRelation.GetComponent<UIRelationItem>();
            relationItem?.SetRelation(list, relation);
            
            m_goRelation.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnRightRelationClick()
        {
        }

        private void OnBtnExitClick()
        {
            // TrySyncTeam(() =>
            // {
            //     var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
            //     if (tCurrentProcedure is Procedure5v5Battle procedure5V5Battle)
            //     {
            //         procedure5V5Battle.GoBackToMain();
            //     }   
            // });
            Close();
        }

        private void OnBtnAirSupportClick()
        {
            m_goAirSupport.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnUnknowClick()
        {
        }

        private void OnBtnMaskClick()
        {
            m_goPower.SetActive(false);
            m_goRelation.SetActive(false);
            m_goAirSupport.SetActive(false);
            m_goPopupAirSupportAttr.SetActive(false);
            m_btnMaskAirSupport.gameObject.SetActive(false);
            m_btnMask.gameObject.SetActive(false);
        }

        private void OnBtnMaskAirSupportClick()
        {
            m_goPopupAirSupportAttr.SetActive(false);
            m_btnMaskAirSupport.gameObject.SetActive(false);
        }

        private void OnHeroUpdate(object sender, GameEventArgs e)
        {
            OnUpdateInfo();
        }

        private void OnSelectBtn(int index)
        {
            if (selectType == index)
                return;

            Transform trans = m_transBtnList.GetChild(index);
            if (trans != null)
            {
                var rectTrans = trans.GetComponent<RectTransform>();
                var bg = trans.Find("bg").gameObject;
                var selectBg = trans.Find("selectBg").gameObject;
                // rectTrans.sizeDelta = new Vector2(260, 91);
                bg.SetActive(false);
                selectBg.SetActive(true);
            }

            if (selectType > -1)
            {
                trans = m_transBtnList.GetChild(selectType);
                if (trans != null)
                {
                    var rectTrans = trans.GetComponent<RectTransform>();
                    var bg = trans.Find("bg").gameObject;
                    var selectBg = trans.Find("selectBg").gameObject;
                    // rectTrans.sizeDelta = new Vector2(245, 91);
                    bg.SetActive(true);
                    selectBg.SetActive(false);
                }
            }

            selectType = index;
            OnUpdateInfo();

            var rect = m_transBtnList.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
        }

        private void OnUpdateInfo()
        {
            List<HeroModule> heroList = GetHeroList();

            Transform rootTrans = m_scrollview.content;
            ToolScriptExtend.RecycleOrCreate(m_transHeroItem.gameObject, rootTrans, heroList.Count);
            for (int i = 0; i < heroList.Count; i++)
            {
                var item = rootTrans.GetChild(i);
                OnUpdateItem(item, heroList, i);
            }
        }

        private List<HeroModule> GetHeroList()
        {
            List<HeroModule> heroList = GameEntry.LogicData.HeroData.GetHeroModuleList((hero_services)selectType, true,
                (heroModule) => heroModule.IsActive);
            return heroList;
        }

        private void OnUpdateItem(Transform item, List<HeroModule> heroList, int index)
        {
            var heroVo = heroList[index];
            OnUpdateItem(item, heroVo);
        }

        private void OnUpdateItem(Transform item, HeroModule heroVo)
        {
            var heroItem = item.GetComponent<UIHeroItem>();
            heroItem.Refresh(heroVo);

            heroItem.SetSelected(IsInBattle((int)heroVo.id));

            heroItem.RemoveAllClickListeners();
            heroItem.AddClickListener(() =>
            {
                int heroId = (int)heroVo.id;
                if (m_HeroContainer.IsInBattle(heroId))
                {
                    m_HeroContainer.RemoveHero(heroId);
                    OnUpdateItem(item, heroVo);
                }
                else
                {
                    var emptyPos = m_HeroContainer.GetEmptyPosition();
                    if (emptyPos != null)
                    {
                        m_HeroContainer.CreateHero(emptyPos.Value, heroId);
                        OnUpdateItem(item, heroVo);
                    }
                    else
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = "队伍人数已满",
                        });
                    }
                }
            });
        }

        private bool IsInBattle(int heroId)
        {
            return m_HeroContainer.IsInBattle(heroId);
        }

        private void RefreshAirportAttr()
        {
            foreach (Transform item in m_goAirSupportAttr.transform)
            {
                UIButton btn = item.GetComponent<UIButton>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    btn.AnchorUIToButton(m_goPopupAirSupportAttr.transform, new Vector2(0, 50f));
                    m_goPopupAirSupportAttr.SetActive(true);
                    m_btnMaskAirSupport.gameObject.SetActive(true);
                });
            }
        }

        private void TrySyncTeam(Action callBack)
        {
            if (!m_TeamDataDirty)
            {
                callBack?.Invoke();
                return;
            }

            m_TeamDataDirty = false;

            //TODO

            callBack?.Invoke();
        }


        #region touch 相关逻辑

        [CanBeNull] private UITeamFormHero m_OpDragBattleHero;
        private EnumBattlePos? m_OpCurPos; //当前手指所在区域

        private void InitTouch()
        {
            var controller = GameEntry.Input;
            controller.tapped += OnTapped;
            controller.dragged += OnDragged;
            controller.released += OnReleased;
            controller.pressed += OnPressed;
        }

        private void UnInitTouch()
        {
            var controller = GameEntry.Input;
            controller.tapped -= OnTapped;
            controller.dragged -= OnDragged;
            controller.released -= OnReleased;
            controller.pressed -= OnPressed;
        }

        private void OnTapped(PointerActionInfo pointer)
        {
            Vector3 worldPos = GameEntry.Camera.UICamera.ScreenToWorldPoint(pointer.currentPosition);
            EnumBattlePos? pos = m_HeroContainer.IsInPosBounds(worldPos);
            if (pos != null)
            {
                var hero = m_HeroContainer.GetBattleHero(pos.Value);
                if (hero != null)
                {
                    m_HeroContainer.RemoveHero(hero);
                    OnUpdateInfo();
                }
            }
        }

        private void OnDragged(PointerActionInfo pointer)
        {
            if (m_OpDragBattleHero == null)
                return;

            Vector3 worldPos = GameEntry.Camera.UICamera.ScreenToWorldPoint(pointer.currentPosition);
            m_OpDragBattleHero.SetPosition(worldPos);
            EnumBattlePos? pos = m_HeroContainer.IsInPosBounds(worldPos);
            if (pos == null)
            {
                //没有在任何区域内部
                MoveBackCache();
            }
            else
            {
                //移动到了新区域
                if (m_OpDragBattleHero.BattlePos == pos.Value)
                {
                    //新区域是我自己
                    MoveBackCache();
                }
                else
                {
                    if (m_OpCurPos == pos.Value)
                    {
                        //已经互换 什么也不做
                    }
                    else
                    {
                        MoveBackCache();
                        MoveAndCache(pos.Value);
                    }
                }
            }
        }

        private void OnReleased(PointerActionInfo pointer)
        {
            if (m_OpCurPos != null && m_OpDragBattleHero != null)
            {
                int heroIdA = m_OpDragBattleHero.HeroId;
                EnumBattlePos posA = m_OpDragBattleHero.BattlePos;
                int? heroIdB = null;
                EnumBattlePos posB = m_OpCurPos.Value;

                //删除原有英雄
                if (m_OpDragBattleHero != null)
                    m_HeroContainer.RemoveHero(m_OpDragBattleHero);
                //删除目标位置英雄
                var heroTarget = m_HeroContainer.GetBattleHero(m_OpCurPos.Value);
                if (heroTarget != null)
                {
                    heroIdB = heroTarget.HeroId;
                    m_HeroContainer.RemoveHero(heroTarget);
                }

                //创建新英雄
                m_HeroContainer.CreateHero(posB, heroIdA);
                if (heroIdB != null)
                    m_HeroContainer.CreateHero(posA, heroIdB.Value);

                m_OpCurPos = null;
                m_OpDragBattleHero = null;
            }
            else
            {
                if (m_OpDragBattleHero != null)
                    m_OpDragBattleHero.MoveBack();
                m_OpDragBattleHero = null;

                MoveBackCache();
            }
        }

        private void OnPressed(PointerActionInfo pointer)
        {
            Vector3 worldPos = GameEntry.Camera.UICamera.ScreenToWorldPoint(pointer.currentPosition);
            EnumBattlePos? pos = m_HeroContainer.IsInPosBounds(worldPos);

            if (pos != null)
            {
                var hero = m_HeroContainer.GetBattleHero(pos.Value);
                if (hero != null)
                {
                    m_OpDragBattleHero = hero;
                }
            }
        }

        private void MoveBackCache()
        {
            if (m_OpCurPos != null)
            {
                //将之前的英雄归位
                var tempHero = m_HeroContainer.GetBattleHero(m_OpCurPos.Value);
                if (tempHero != null)
                    tempHero.MoveBack();
                m_OpCurPos = null;
            }
        }

        private void MoveAndCache(EnumBattlePos pos)
        {
            if (m_OpDragBattleHero == null)
                return;

            var hero = m_HeroContainer.GetBattleHero(pos);
            if (hero != null)
            {
                hero.MoveTo(m_OpDragBattleHero.BattlePos);
            }

            m_OpCurPos = pos;
        }

        #endregion

        #region 战斗单位头部Hud

        public void InitHud()
        {
            m_HudsList = new Dictionary<EnumBattlePos, UITeamFormHUD>();

            foreach (EnumBattlePos pos in Enum.GetValues(typeof(EnumBattlePos)))
            {
                if((int)pos > BattleDefine.AttackTeamMaxPos)
                    continue;
                var hud = CreateHud(pos);
                var hero = m_HeroContainer.GetBattleHero(pos);
                if (hero != null)
                    hud.Show();
                else
                    hud.Hide();
            }
        }

        private void OnHeroDelete(EnumBattlePos pos)
        {
            if (m_HudsList.TryGetValue(pos, out UITeamFormHUD choose))
            {
                choose.Hide();
            }

            UpdatePower();

            m_TeamDataDirty = true;
            m_RelationDirty = true;
        }

        private void OnHeroCreate(EnumBattlePos pos)
        {
            if (m_HudsList.TryGetValue(pos, out UITeamFormHUD choose))
            {
                choose.Show();
            }

            UpdatePower();

            m_TeamDataDirty = true;
            m_RelationDirty = true;
        }

        public void UnInitHud()
        {
            foreach (var item in m_HudsList)
            {
                Destroy(item.Value.gameObject);
            }

            m_HudsList.Clear();
            m_HudsList = null;
        }

        private UITeamFormHUD CreateHud(EnumBattlePos pos)
        {
            var newItem = Instantiate(m_transHudItem);
            newItem.transform.parent = m_transHudParent;
            newItem.transform.localPosition = Vector3.one;
            newItem.transform.localScale = Vector3.one;

            var hud = newItem.gameObject.GetOrAddComponent<UITeamFormHUD>();
            hud.Init(m_HeroContainer, pos);
            m_HudsList.Add(pos, hud);
            return hud;
        }

        #endregion


        #region 羁绊相关

        private void TryResetRelationUI(bool force = false)
        {
            var list = m_HeroContainer.GetTeamArmyTypeList();
            EnumBattleRelation relation = ToolScriptExtend.GetRelation(list);
            if (m_CurRelation!=relation || force)
            {
                m_CurRelation = relation;
                SetRelationIcon();
            }
        }

        private void SetRelationIcon()
        {
            if(m_RelationTweener!=null && m_RelationTweener.IsPlaying())
                m_RelationTweener.Kill();
            m_btnLeftRelation.transform.localScale = Vector3.one;
            m_RelationTweener = m_btnLeftRelation.transform.DOPunchScale(Vector3.one * 0.5f, 0.3f);
            
            if (m_CurRelation == EnumBattleRelation.None)
            {
                m_imgLeftRelationA.gameObject.SetActive(false);
                m_imgLeftRelationB.gameObject.SetActive(false);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            } 
            else if (m_CurRelation == EnumBattleRelation.Relation3_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(false);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }
            else if (m_CurRelation == EnumBattleRelation.Relation3_2)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(true);
                
                m_imgLeftRelationB.SetImageGray(true);
                m_imgLeftRelationC.SetImageGray(true);
            }else if (m_CurRelation == EnumBattleRelation.Relation4_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }else if (m_CurRelation == EnumBattleRelation.Relation5_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(true);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }
        }
        
        #endregion
    }
}