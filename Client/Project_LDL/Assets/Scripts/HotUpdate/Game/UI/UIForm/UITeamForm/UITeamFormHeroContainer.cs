using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class UITeamFormHero
    {
        public int HeroId => (int)m_HeroModule.id;
        public hero_services Services => m_HeroModule.Services;
        public EnumBattlePos BattlePos => m_BattlePos;


        private EnumBattlePos m_BattlePos;
        
        private HeroModule m_HeroModule;

        public void SetPosition(Vector3 worldPos)
        {
            
        }
        
    }
    
    public class UITeamFormHeroContainer : MonoBehaviour
    {
        private Dictionary<EnumBattlePos, UITeamFormHero> m_HeroDict = new Dictionary<EnumBattlePos, UITeamFormHero>();
        
        public delegate void HeroChangeDelegate(EnumBattlePos pos);
        
        
        public HeroChangeDelegate OnHeroCreateCall = null;
        public HeroChangeDelegate OnHeroDeleteCall = null;

        public double GetPowerFromHeroModule()
        {
            return 0;
        }
        
        public List<hero_services> GetTeamArmyTypeList()
        {
            var list = new List<hero_services>();
            for (int i = 1; i <= 5; i++)
            {
                var hero = GetBattleHero((EnumBattlePos)i);
                if(hero!=null)
                {
                    list.Add(hero.Services);
                }
            }
            return list;
        }

        private UITeamFormHero GetBattleHero(int heroId)
        {
            foreach (var item in m_HeroDict)
            {

                if ((int)item.Value.HeroId == heroId)
                {
                    return item.Value;
                }
            }

            return null;
        }
        
        public UITeamFormHero GetBattleHero(EnumBattlePos pos)
        {
            if (m_HeroDict.TryGetValue(pos, out UITeamFormHero hero))
            {
                return hero;
            }
            return null;
        }

        public bool IsInBattle(int heroId)
        {
            if(GetBattleHero(heroId)!=null)
                return true;
            return false;
        }

        public void RemoveHero(UITeamFormHero hero)
        {
            
        }
        
        public void RemoveHero(int heroId){
            
        }

        public void CreateHero(EnumBattlePos pos,int heroid)
        {
            
        } 

        public EnumBattlePos? GetEmptyPosition()
        {
            return null;
        }

        public EnumBattlePos? IsInPosBounds(Vector3 worldPos)
        {
            return null;
        }
        
        
    }
}

