using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class UIBattle5v5ChooseHUD : MonoBehaviour
    {
        private BattleFiled m_BattleFiled;
        private EnumBattlePos m_BattlePos;

        private UIImage m_ImageIcon;
        private UIImage m_ImageIconBg;
        private UIText m_TxtLevel;

        private RectTransform m_SelfRT;
        private RectTransform m_ParentRT;
        private Camera m_Camera;

        private Vector2 m_TempV2;

        public void Init(BattleFiled battleFiled, EnumBattlePos battlePos)
        {
            m_BattleFiled = battleFiled;
            m_BattlePos = battlePos;

            m_ImageIconBg = transform.Find("offset/imgIconBg")?.GetComponent<UIImage>();
            m_ImageIcon = transform.Find("offset/imgIcon")?.GetComponent<UIImage>();
            m_TxtLevel = transform.Find("offset/txtLevel")?.GetComponent<UIText>();

            m_Camera = GameEntry.Camera.BattleCamera;
            m_SelfRT = transform.GetComponent<RectTransform>();
            m_ParentRT = transform.parent.GetComponent<RectTransform>();
        }

        public void Show()
        {
            var battleHero = m_BattleFiled.TeamCtrl.GetBattleHero(m_BattlePos);
            var heroData = GameEntry.LogicData.HeroData;
            var heroVo = heroData.GetHeroModule((itemid)battleHero.HeroId);
            m_TxtLevel.text = ToolScriptExtend.GetLangFormat(80000135, heroVo.level.ToString());
            m_ImageIconBg.SetImage(GetItemBgPath(heroVo.Quality));
            m_ImageIcon.SetImage(heroVo.HeroHead);

            gameObject.SetActive(true);
        }

        public void Hide()
        {
            ResetPos();
            gameObject.SetActive(false);
        }

        private void Update()
        {
            var hero = m_BattleFiled.TeamCtrl.GetBattleHero(m_BattlePos);
            if (hero == null)
                return;

            m_SelfRT.anchoredPosition = WorldPosToScreenPos(hero.GetPosition());
        }

        private void ResetPos()
        {
            m_SelfRT.anchoredPosition = WorldPosToScreenPos(m_BattleFiled.GetTeamPosByUid(m_BattlePos));
        }

        private Vector2 WorldPosToScreenPos(Vector3 worldPos)
        {
            var screenPoint = RectTransformUtility.WorldToScreenPoint(m_Camera, worldPos);
            RectTransformUtility.ScreenPointToLocalPointInRectangle(m_ParentRT, screenPoint,
                GameEntry.Camera.UICamera, out m_TempV2);
            return m_TempV2;
        }

        public string GetItemBgPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                quality.quality_blue => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_blue.png",
                quality.quality_purple => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_purlpep.png",
                quality.quality_orange => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_yellow.png",
                _ => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_blue.png",
            };
        }
    }
}