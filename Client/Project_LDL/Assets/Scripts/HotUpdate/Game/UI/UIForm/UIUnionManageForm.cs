using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionManageForm : UGuiFormEx
    {
        private Union.UnionMember memberData;
        private Roledata.RoleBrief roleData;
        private bool deportationState;
        private bool adjustmemberState;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            var param = userData as UIUnionMemberFormParams;
            memberData = param.MemberData;
            roleData = param.RoleData;

            OnUpdateInfo();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnUpdateInfo()
        {
            // m_imgHeadBg.SetImage(roleData.HeadBorder);
            // m_imgHead.SetImage(roleData.IsCustomAvatar ? roleData.HeadSystemAvatar : roleData.HeadCustomAvatar);
            m_txtName.text = roleData.Name;

            var postId = memberData.OfficePosition > 0 ? memberData.OfficePosition : memberData.Permission;
            var postCfg = GameEntry.LogicData.UnionData.GetUnionPosition(postId);
            if (postCfg.res_location != string.Empty)
                m_imgPosition.SetImage(postCfg.res_location);
            m_txtDesc.text = ToolScriptExtend.GetLang(postCfg.position_desc);

            var config = GameEntry.LogicData.UnionData.GetUnionPermission();
            deportationState = config != null && config.is_deportation;
            m_btnOut.SetButtonGray(!deportationState);

            var myPermission = GameEntry.LogicData.UnionData.UnionPermission;
            adjustmemberState = config != null && config.is_adjustmember && myPermission > memberData.Permission;
            m_btnChange.SetButtonGray(!adjustmemberState);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnHeadClick()
        {
            // 打开角色信息界面
        }

        private void OnBtnOutClick()
        {
            // 踢出
            if (deportationState)
            {
                GameEntry.LogicData.UnionData.OnReqUnionKickOut(memberData.RoleId, (RoleId) =>
                {
                    ColorLog.Pink($"踢出成功 RoleId:{RoleId}");
                    Close();

                    GameEntry.UI.RefreshUIForm(EnumUIForm.UIUnionMemberForm, new UnionParams
                    {
                        ShowType = 1,
                    });
                });
            }
            else
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1095)
                });
            }
        }

        private void OnBtnChangeClick()
        {
            // 改变职位
            if (adjustmemberState)
            {
                // 打开职位设置界面
                GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionStepUpForm, new UIUnionMemberFormParams
                {
                    MemberData = memberData,
                    RoleData = roleData
                });
                Close();
            }
            else
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1095)
                });
            }
        }

        private void OnBtnChatClick()
        {
            // 打开私聊
            Close();
        }
    }
}
