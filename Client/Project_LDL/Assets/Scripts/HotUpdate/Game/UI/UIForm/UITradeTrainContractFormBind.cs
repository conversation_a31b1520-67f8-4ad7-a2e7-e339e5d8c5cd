using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTrainContractForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnCloseFull;
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnTip;
        [SerializeField] private UIButton m_btnBuy;

        [SerializeField] private UIText m_txtPrice;
        [SerializeField] private UIText m_txtTodayCount;

        [SerializeField] private Transform m_transContentReward;
        [SerializeField] private Transform m_transReward;

        void InitBind()
        {
            m_btnCloseFull.onClick.AddListener(OnBtnCloseFullClick);
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnTip.onClick.AddListener(OnBtnTipClick);
            m_btnBuy.onClick.AddListener(OnBtnBuyClick);
        }
    }
}
