using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITechUpDetailForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnDeatial;
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnDone;
        [SerializeField] private UIButton m_btnUpLevel;

        [SerializeField] private UIText m_txtName;
        [SerializeField] private UIText m_txtDes;
        [SerializeField] private UIText m_txtCurrent;
        [SerializeField] private UIText m_txtCurrentValue;
        [SerializeField] private UIText m_txtNext;
        [SerializeField] private UIText m_txtNextValue;
        [SerializeField] private UIText m_txtDoneValue;
        [SerializeField] private UIText m_txtOldTime;
        [SerializeField] private UIText m_txtUpLevelTime;

        [SerializeField] private UIImage m_imgIcon;

        [SerializeField] private ScrollRect m_scrollviewCost;

        [SerializeField] private GameObject m_goCostItem;
        [SerializeField] private GameObject m_goCostItem_1;
        [SerializeField] private GameObject m_goCostItem_2;
        [SerializeField] private GameObject m_goCostItem_3;

        void InitBind()
        {
            m_btnDeatial.onClick.AddListener(OnBtnDeatialClick);
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnDone.onClick.AddListener(OnBtnDoneClick);
            m_btnUpLevel.onClick.AddListener(OnBtnUpLevelClick);
        }
    }
}
