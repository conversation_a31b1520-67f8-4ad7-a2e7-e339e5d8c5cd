using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIUnionGiftForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnExit;
        [SerializeField] private UIButton m_btnGift;
        [SerializeField] private UIButton m_btnGo;
        [SerializeField] private UIButton m_btnOneKey;
        [SerializeField] private UIButton m_btnTipMask;
        [SerializeField] private UIButton m_btnTipDesc;

        [SerializeField] private UIText m_txtLevel;
        [SerializeField] private UIText m_txtProgress;
        [SerializeField] private UIText m_txtTip;
        [SerializeField] private UIText m_txtGo;
        [SerializeField] private UIText m_txtAnons;
        [SerializeField] private UIText m_txtNone;
        [SerializeField] private UIText m_txtTipDesc;

        [SerializeField] private UIImage m_imgProgress;
        [SerializeField] private UIImage m_imgExp;

        [SerializeField] private UIToggle m_togAnons;

        [SerializeField] private Transform m_transBtnList;
        [SerializeField] private Mosframe.TableView m_TableViewV;
        [SerializeField] private GameObject m_goItem;
        [SerializeField] private GameObject m_goTipLevel;
        [SerializeField] private Mosframe.TableView m_TableViewVLevel;
        [SerializeField] private GameObject m_goTipItem;
        [SerializeField] private GameObject m_goTipLimit;
        [SerializeField] private GameObject m_goTipDesc;

        void InitBind()
        {
            m_btnExit.onClick.AddListener(OnBtnExitClick);
            m_btnGift.onClick.AddListener(OnBtnGiftClick);
            m_btnGo.onClick.AddListener(OnBtnGoClick);
            m_btnOneKey.onClick.AddListener(OnBtnOneKeyClick);
            m_btnTipMask.onClick.AddListener(OnBtnTipMaskClick);
            m_btnTipDesc.onClick.AddListener(OnBtnTipDescClick);
        }
    }
}
