using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIEquipmentPromoteForm : UGuiFormEx
    {
        int id;
        itemid heroID;
        equipposition part;
        UIItemModule equipmentItem;
        UIItemModule equipmentItemBefore;
        UIItemModule equipmentItemAfter;
        List<HeroModule> heroList = new();
        List<GameObject> effectList = new();
        bool playEffect = false;
        int heroIndex;
        int equipmentIndex;
        int heroIndexOffset;
        bool inBag;
        int equipmentIndexInBag;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_goBasicAttributeItem.SetActive(false);
            m_goExtraAttributeItem.SetActive(false);
            m_goCost.SetActive(false);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            playEffect = false;

            HideDefault();

            if (userData != null)
            {
                EquipmentParams equipmentParams = userData as EquipmentParams;
                id = equipmentParams.ID;
                heroID = equipmentParams.HeroID;
                part = equipmentParams.Part;
                inBag = equipmentParams.InBag;
            }

            GameEntry.EquipmentData.CurDetailEquipmentID = id;

            RefreshInfo();

            GameEntry.Event.Subscribe(EquipmentChangeEventArgs.EventId, OnEquipmentChange);
            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);

            if (inBag)
            {
                RefreshSwitchBtnInBag();
            }
            else
            {
                UIHeroForm heroForm = GameEntry.UI.GetUIForm(EnumUIForm.UIHeroForm) as UIHeroForm;
                if (heroForm == null) return;
                int selectType = heroForm.SelectType;
                heroList = GameEntry.LogicData.HeroData.GetHeroModuleList((hero_services)selectType, true);

                UIHeroDevelopForm heroDevelopForm = GameEntry.UI.GetUIForm(EnumUIForm.UIHeroDevelopForm) as UIHeroDevelopForm;
                if (heroDevelopForm == null) return;
                heroIndex = heroDevelopForm.HeroIndex;

                RefreshSwitchBtn();
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            if (GameEntry.Event.Check(EquipmentChangeEventArgs.EventId, OnEquipmentChange))
                GameEntry.Event.Unsubscribe(EquipmentChangeEventArgs.EventId, OnEquipmentChange);
            if (GameEntry.Event.Check(ItemChangeEventArgs.EventId, OnItemChange))
                GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);

            foreach (var effect in effectList)
            {
                if (effect != null)
                {
                    effect.SetActive(false);
                }
            }

            id = 0;
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        void OnEquipmentChange(object sender, GameEventArgs e)
        {
            playEffect = true;
            RefreshInfo();

            if (inBag)
            {
                RefreshSwitchBtnInBag();
            }
        }

        void OnItemChange(object sender, GameEventArgs e)
        {
            RefreshMaterial();
        }

        private void OnBtnPromoteClick()
        {
            EquipmentModule equipment = GameEntry.EquipmentData.GetWearingByPart(heroID, part);
            if (inBag && id != 0)
            {
                equipment = GameEntry.EquipmentData.GetEquipmentByID(id);
            }
            if (equipment == null) return;

            if (equipment.PromoteMax)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = "已满星"
                });
                return;
            }

            equipment_promotion promotionConfig = GameEntry.EquipmentData.GetPromoteLevelConfig(equipment.code, equipment.promotion_level);
            if (promotionConfig != null)
            {
                bool notEnough = false;
                itemid needItemId = itemid.itemid_nil;
                long needNum = 0;
                for (int i = 0; i < promotionConfig.pormotion_cost.Count; i++)
                {
                    cost cost = promotionConfig.pormotion_cost[i];
                    long curNum = GameEntry.LogicData.BagData.GetAmountById(cost.item_id);
                    if (curNum < cost.num)
                    {
                        notEnough = true;

                        needItemId = cost.item_id;
                        needNum = cost.num;
                        break;
                    }
                }

                if (notEnough)
                {
                    ItemModule itemModule = new(needItemId);
                    GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(needNum));
                    return;
                }
            }

            ColorLog.Pink("装备晋升", equipment.id);
            GameEntry.EquipmentData.RequestEquipmentPromotion((uint)equipment.id, (result) =>
            {
                ColorLog.Pink("装备晋升回调", result);
            });
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnLeftClick()
        {
            if (inBag)
            {
                SwitchEquipmentInBag(equipmentIndexInBag - 1);
            }
            else
            {
                SwitchEquipment(equipmentIndex - 1);
            }
        }

        private void OnBtnRightClick()
        {
            if (inBag)
            {
                SwitchEquipmentInBag(equipmentIndexInBag + 1);
            }
            else
            {
                SwitchEquipment(equipmentIndex + 1);
            }
        }

        private void OnBtnMaskClick()
        {
            m_btnMask.gameObject.SetActive(false);
            m_goPopup.SetActive(false);
        }

        /// <summary>
        /// 默认隐藏
        /// </summary>
        void HideDefault()
        {
            m_goEquipmentChange.SetActive(false);
        }

        void SwitchEquipment(int index)
        {
            if (heroIndex < 0 || heroIndex >= heroList.Count) return;

            HeroModule heroModule = heroList[heroIndex];
            List<EquipmentModule> equipmentModules = GameEntry.EquipmentData.GetWearing(heroModule.id, true);

            heroIndexOffset = 1;

            bool hasLastEquipment = false;
            bool hasNextEquipment = false;

            if (index > equipmentIndex) hasNextEquipment = HasEquipment(heroIndex + 1);
            if (index < equipmentIndex) hasLastEquipment = HasEquipment(heroIndex - 1);

            bool nextEquipment = false;
            bool lastEquipment = false;

            int direction = 0;

            if (hasNextEquipment && index > equipmentIndex && index >= equipmentModules.Count)
            {
                heroIndex += heroIndexOffset;
                nextEquipment = true;
                direction = 1;
            }
            else if (hasLastEquipment && index < equipmentIndex && index < 0)
            {
                heroIndex -= heroIndexOffset;
                lastEquipment = true;
                direction = -1;
            }

            if (heroIndex < 0 || heroIndex >= heroList.Count) return;

            heroModule = heroList[heroIndex];
            heroID = heroModule.id;
            equipmentModules = GameEntry.EquipmentData.GetWearing(heroModule.id, true);
            GameEntry.Event.Fire(EquipmentSwitchEventArgs.EventId, EquipmentSwitchEventArgs.Create(direction, heroIndexOffset));

            if (nextEquipment)
            {
                index = 0;
            }
            else if (lastEquipment)
            {
                index = equipmentModules.Count - 1;
            }

            int minIndex = 0;
            int maxIndex = equipmentModules.Count - 1;
            
            if (index < minIndex)
            {
                index = minIndex;
            }
            if (index > maxIndex)
            {
                index = maxIndex;
            }

            hasLastEquipment = HasEquipment(heroIndex - 1);
            hasNextEquipment = HasEquipment(heroIndex + 1);
            
            m_btnLeft.gameObject.SetActive(index > minIndex || hasLastEquipment);
            m_btnRight.gameObject.SetActive(index < maxIndex || hasNextEquipment);
            part = equipmentModules[index].Part;
            equipmentIndex = index;

            UIEquipmentDetailForm equipmentDetailForm = GameEntry.UI.GetUIForm(EnumUIForm.UIEquipmentDetailForm) as UIEquipmentDetailForm;
            if (equipmentDetailForm != null)
            {
                equipmentDetailForm.SetEquipmentSwitchInfo(heroID, part, heroIndex, equipmentIndex);
            }

            playEffect = false;
            RefreshInfo();
        }

        bool HasEquipment(int index)
        {
            if (index < 0 || index >= heroList.Count) return false;
            int minIndex = 0;
            int maxIndex = heroList.Count - 1;
            
            if (index < minIndex)
            {
                index = minIndex;
            }
            if (index > maxIndex)
            {
                index = maxIndex;
            }

            HeroModule heroModule = heroList[index];
            List<EquipmentModule> equipmentModules = GameEntry.EquipmentData.GetWearing(heroModule.id, true);
            if (equipmentModules.Count == 0)
            {
                if (index > heroIndex)
                {
                    for (int i = index + 1; i < heroList.Count; i++)
                    {
                        if (i < heroList.Count)
                        {
                            heroModule = heroList[i];
                            equipmentModules = GameEntry.EquipmentData.GetWearing(heroModule.id, true);
                            if (equipmentModules.Count > 0)
                            {
                                heroIndexOffset = i - heroIndex;
                                return true;
                            }
                        }
                    }
                }
                else if (index < heroIndex)
                {
                    for (int i = index - 1; i >= 0; i--)
                    {
                        if (i >= 0)
                        {
                            heroModule = heroList[i];
                            equipmentModules = GameEntry.EquipmentData.GetWearing(heroModule.id, true);
                            if (equipmentModules.Count > 0)
                            {
                                heroIndexOffset = heroIndex - i;
                                return true;
                            }
                        }
                    }
                }

                return false;
            }

            return true;
        }

        void RefreshSwitchBtn()
        {
            if (heroIndex >= heroList.Count) return;
            HeroModule heroModule = heroList[heroIndex];
            List<EquipmentModule> equipmentModules = GameEntry.EquipmentData.GetWearing(heroModule.id, true);
            int minIndex = 0;
            int maxIndex = equipmentModules.Count - 1;
            for (int i = 0; i < equipmentModules.Count; i++)
            {
                if (equipmentModules[i].Part == part)
                {
                    equipmentIndex = i;
                    break;
                }
            }
            bool hasLastEquipment = HasEquipment(heroIndex - 1);
            bool hasNextEquipment = HasEquipment(heroIndex + 1);
            m_btnLeft.gameObject.SetActive(equipmentIndex > minIndex || hasLastEquipment);
            m_btnRight.gameObject.SetActive(equipmentIndex < maxIndex || hasNextEquipment);
        }

        void RefreshInfo()
        {
            EquipmentModule equipment = GameEntry.EquipmentData.GetWearingByPart(heroID, part);
            if (inBag && id != 0)
            {
                equipment = GameEntry.EquipmentData.GetEquipmentByID(id);
            }
            if (equipment == null) return;

            m_sliderProgress.value = equipment.promotion_level % 5 / 4f + 0.005f;

            if (m_sliderProgress.value >= 1)
            {
                m_goSingleEquipment.SetActive(false);
                m_goEquipmentChange.SetActive(true);

                EquipmentModule equipmentAfter = new(equipment)
                {
                    promotion_level = equipment.promotion_level + 1,
                };

                RefreshEquipmentItem(equipmentItemBefore, m_transEquipmentBefore, equipment);
                RefreshEquipmentItem(equipmentItemAfter, m_transEquipmentAfter, equipmentAfter);
            }
            else
            {
                m_goSingleEquipment.SetActive(true);
                m_goEquipmentChange.SetActive(false);
            }

            RefreshEquipmentItem(equipmentItem, m_transEquipment, equipment);

            item_config itemConfig = ToolScriptExtend.GetItemConfig(equipment.code);
            m_txtName.text = ToolScriptExtend.GetLang(itemConfig.name);
            m_txtNameBefore.text = ToolScriptExtend.GetLang(itemConfig.name);
            m_txtNameAfter.text = ToolScriptExtend.GetLang(itemConfig.name);

            foreach (Transform child in m_transContentBasic)
            {
                child.gameObject.SetActive(false);
            }

            float widthMax = 0;
            List<equip_attributes> attributes = GameEntry.EquipmentData.GetPromoteBasicProperty(equipment.code, equipment.promotion_level);
            List<equip_attributes> attributesAfter = GameEntry.EquipmentData.GetPromoteBasicProperty(equipment.code, equipment.promotion_level + 1);
            for (int i = 0; i < attributes.Count; i++)
            {
                GameObject item = null;
                if (i < m_transContentBasic.childCount)
                {
                    item = m_transContentBasic.GetChild(i).gameObject;
                    item.SetActive(true);
                }
                else
                {
                    item = Instantiate(m_goBasicAttributeItem, m_transContentBasic);
                    item.SetActive(true);
                }
                equip_attributes attr = attributes[i];
                UIText txtName = item.transform.Find("txtName").GetComponent<UIText>();
                txtName.text = ToolScriptExtend.GetAttrLang((int)attr.attributes_type);
                UIText txtValueBefore = item.transform.Find("value/txtValueBefore").GetComponent<UIText>();
                txtValueBefore.text = attr.value_type == valuetype.valuetype_1 ? $"+{attr.value}" : $"{attr.value / 100f}%";
                UIText txtValueAfter = item.transform.Find("value/txtValueAfter").GetComponent<UIText>();
                if (attributesAfter.Count > 0)
                {
                    equip_attributes attrAfter = attributesAfter[i];
                    txtValueAfter.text = attrAfter.value_type == valuetype.valuetype_1 ? $"+{attrAfter.value}" : $"{attrAfter.value / 100f}%";
                }

                widthMax = Mathf.Max(widthMax, txtValueAfter.preferredWidth);

                GameObject bg = item.transform.Find("bg").gameObject;
                bg.SetActive(i % 2 == 0);

                GameObject effect = item.transform.Find("battle_peiyang_wenzishaoguang_1").gameObject;
                SetParticleSystemSortingOrder(effect, Depth);
                effect.SetActive(false);

                if (playEffect)
                {
                    effect.SetActive(true);
                }
                
                if (!effectList.Contains(effect))
                {
                    effectList.Add(effect);
                }
            }

            for (int i = 0; i < attributes.Count; i++)
            {
                if (i < m_transContentBasic.childCount)
                {
                    GameObject item = m_transContentBasic.GetChild(i).gameObject;
                    UIText txtValueAfter = item.transform.Find("value/txtValueAfter").GetComponent<UIText>();
                    txtValueAfter.GetComponent<LayoutElement>().preferredWidth = widthMax;
                }
            }

            foreach (Transform child in m_transContentExtra)
            {
                child.gameObject.SetActive(false);
            }

            int extraIndex = 0;
            int promoteIndex = 0;
            int curPromote = 0;
            Dictionary<int, List<equip_attributes>> extraAttributes = GameEntry.EquipmentData.GetPromoteExtraProperty(equipment.code);
            foreach (var attrItem in extraAttributes)
            {
                for (int i = 0; i < attrItem.Value.Count; i++)
                {
                    equip_attributes attr = attrItem.Value[i];
                    bool isExtra = attrItem.Key >= 10;
                    int index = isExtra ? extraIndex : promoteIndex;

                    GameObject item = null;
                    if (index < m_transContentExtra.childCount)
                    {
                        item = m_transContentExtra.GetChild(index).gameObject;
                        item.SetActive(true);
                    }
                    else
                    {
                        item = Instantiate(m_goExtraAttributeItem, m_transContentExtra);
                        item.SetActive(true);
                    }

                    GameObject gemSlot = item.transform.Find("gemSlot").gameObject;
                    GameObject gem = item.transform.Find("gemSlot/gem").gameObject;

                    UIText txtName = item.transform.Find("txtName").GetComponent<UIText>();
                    UIText txtValue = item.transform.Find("txtValue").GetComponent<UIText>();

                    GameObject goValue = item.transform.Find("value").gameObject;
                    UIText txtValueBefore = item.transform.Find("value/txtValueBefore").GetComponent<UIText>();
                    UIText txtValueAfter = item.transform.Find("value/txtValueAfter").GetComponent<UIText>();

                    GameObject goLock = item.transform.Find("lock").gameObject;

                    bool isActive = equipment.equipment_level >= attrItem.Key;
                    bool isPromote = equipment.PromotionPhase >= attrItem.Key;
                    bool isLock = attrItem.Key == 5 && equipment.PromotionPhase < 5;

                    if (!isExtra && !isPromote && m_sliderProgress.value >= 1 && curPromote == 0)
                    {
                        curPromote = attrItem.Key;
                    }

                    string colorNameStr = "#938a8a";
                    string colorValueStr = "#938a8a";

                    if (isActive && !isLock)
                    {
                        if (isPromote)
                        {
                            colorNameStr = "#00f0ff";
                            colorValueStr = "#00f0ff";
                        }
                        else
                        {
                            colorNameStr = "#50ff81";
                            colorValueStr = "#ffffff";
                        }
                    }

                    ColorUtility.TryParseHtmlString(colorNameStr, out Color colorName);
                    ColorUtility.TryParseHtmlString(colorValueStr, out Color colorValue);

                    txtName.color = colorName;
                    ToolScriptExtend.EnableOutlineAndUIShadow(txtName, isActive && !isLock);
                    txtValue.color = colorValue;
                    ToolScriptExtend.EnableOutlineAndUIShadow(txtValue, isActive && !isLock);

                    txtName.text = ToolScriptExtend.GetAttrLang((int)attr.attributes_type);

                    UnderlineText(txtName, attr);

                    if (isExtra)
                    {
                        txtValue.text = attr.value_type == valuetype.valuetype_1 ? $"+{attr.value}" : $"{attr.value / 100f}%";
                        txtValueBefore.text = attr.value_type == valuetype.valuetype_1 ? $"+{attr.value}" : $"{attr.value / 100f}%";
                    }
                    else
                    {
                        if (isPromote || attrItem.Key == 5)
                        {
                            txtValue.text = attr.value_type == valuetype.valuetype_1 ? $"+{attr.value}" : $"{attr.value / 100f}%";
                        }

                        txtValueAfter.text = attr.value_type == valuetype.valuetype_1 ? $"+{attr.value}" : $"{attr.value / 100f}%";
                    }

                    gem.SetActive(isPromote);
                    gemSlot.SetActive(!isLock);
                    goLock.SetActive(isLock);

                    if (curPromote == attrItem.Key && attrItem.Key != 5)
                    {
                        goValue.SetActive(true);
                        txtValue.gameObject.SetActive(false);
                    }
                    else
                    {
                        goValue.SetActive(false);
                        txtValue.gameObject.SetActive(true);
                    }
                    
                    if (isExtra)
                    {
                        extraIndex++;
                    }
                    else
                    {
                        promoteIndex++;
                    }
                }
            }

            RefreshMaterial();
        }

        void RefreshEquipmentItem(UIItemModule uiItemModule, Transform parent, EquipmentModule equipment)
        {
            if (uiItemModule == null)
            {
                BagManager.CreatItem(parent, equipment.code, 1, (item)=>
                {
                    item.GetComponent<UIButton>().useTween = false;
                    item.IsShowCount(false);
                    item.SwitchQualityIcon(true);
                    item.txtEquipmentLevel.text = $"Lv.{equipment.equipment_level}";
                    item.txtEquipmentLevel.transform.localScale = new Vector3(1.2f, 1.2f, 1f);
                    item.RefreshEquipmentStar(equipment.PromotionPhase, equipment.CanPromote || equipment.PromoteMax);
                    item.RefreshEquipmentQuality(equipment.EquipmentQuality);
                    if (parent == m_transEquipment) equipmentItem = item;
                    else if (parent == m_transEquipmentBefore) equipmentItemBefore = item;
                    else if (parent == m_transEquipmentAfter) equipmentItemAfter = item;

                    if (equipment.target_id > 0)
                    {
                        HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule(equipment.target_id);
                        if (heroModule != null)
                        {
                            item.RefreshHeroHead(heroModule.Quality, heroModule.HeroHead);
                        }
                        else
                        {
                            item.heroBorder.gameObject.SetActive(false);
                        }
                    }
                    else
                    {
                        item.heroBorder.gameObject.SetActive(false);
                    }
                });
            }
            else
            {
                uiItemModule.itemModule.ItemId = equipment.code;
                uiItemModule.txtEquipmentLevel.text = $"Lv.{equipment.equipment_level}";
                uiItemModule.InitConfigData();
                uiItemModule.DisplayInfo();
                uiItemModule.RefreshEquipmentStar(equipment.PromotionPhase, equipment.CanPromote || equipment.PromoteMax);
                uiItemModule.RefreshEquipmentQuality(equipment.EquipmentQuality);
                if (equipment.target_id > 0)
                {
                    HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule(equipment.target_id);
                    if (heroModule != null)
                    {
                        uiItemModule.RefreshHeroHead(heroModule.Quality, heroModule.HeroHead);
                    }
                    else
                    {
                        uiItemModule.heroBorder.gameObject.SetActive(false);
                    }
                }
                else
                {
                    uiItemModule.heroBorder.gameObject.SetActive(false);
                }
            }
        }

        void RefreshMaterial()
        {
            EquipmentModule equipment = GameEntry.EquipmentData.GetWearingByPart(heroID, part);
            if (inBag && id != 0)
            {
                equipment = GameEntry.EquipmentData.GetEquipmentByID(id);
            }
            if (equipment == null) return;

            equipment_promotion promotionConfig = GameEntry.EquipmentData.GetPromoteLevelConfig(equipment.code, equipment.promotion_level);
            if (promotionConfig != null)
            {
                foreach (Transform item in m_transCost)
                {
                    item.gameObject.SetActive(false);
                }

                for (int i = 0; i < promotionConfig.pormotion_cost.Count; i++)
                {
                    cost cost = promotionConfig.pormotion_cost[i];

                    GameObject item = null;
                    if (i < m_transCost.childCount)
                    {
                        item = m_transCost.GetChild(i).gameObject;
                        item.SetActive(true);
                    }
                    else
                    {
                        item = Instantiate(m_goCost, m_transCost);
                        item.SetActive(true);
                    }

                    UIText txtCost = item.GetComponent<UIText>();
                    UIImage imgCost = item.transform.Find("icon").GetComponent<UIImage>();

                    long curNum = GameEntry.LogicData.BagData.GetAmountById(cost.item_id);
                    string curNumStr = ToolScriptExtend.FormatNumberWithUnit(curNum);
                    string needNumStr = ToolScriptExtend.FormatNumberWithUnit(cost.num);
                    string colorHex = curNum >= cost.num ? "ffffff" : "f53d3d";

                    txtCost.text = $"<color=#{colorHex}>{curNumStr}</color>/{needNumStr}";
                    string iconPath = ToolScriptExtend.GetItemIcon(cost.item_id);
                    imgCost.SetImage(iconPath, false);
                }
            }

            if (equipment.PromoteMax)
            {
                m_goPromoteRedpoint.SetActive(false);
                m_btnPromote.SetButtonGray(true);
                m_sliderProgress.gameObject.SetActive(false);
            }
            else
            {
                m_goPromoteRedpoint.SetActive(equipment.IsPromoteEnough);
                m_btnPromote.SetButtonGray(false);
                m_sliderProgress.gameObject.SetActive(true);
            }
        }

        void UnderlineText(UIText txtName, equip_attributes attr)
        {
            string attrName = ToolScriptExtend.GetAttrLang((int)attr.attributes_type);

            if (attr.attributes_type == attributes_type.attributes_type_physicalDamageReduction)
            {
                string str = attrName[^2..];
                int startIndex = txtName.text.IndexOf(str);
                int endIndex = startIndex + str.Length;
                txtName.text = $"{attrName[0..^2]}<a href=\"\"><color=\"#ffb541\">{str}</color></a>";
                txtName.SetHyperlinkCallback((refValue, innerValue)=>
                {
                    m_btnMask.gameObject.SetActive(true);
                    m_goPopup.SetActive(true);
                    txtName.AnchorUIToTextSegment(startIndex, endIndex, m_goPopup.transform, new Vector2(0, -40f));
                });
            }
        }

        void SwitchEquipmentInBag(int index)
        {
            List<EquipmentModule> equipmentModules = GameEntry.EquipmentData.GetEquipmentListCanPromote();

            int minIndex = 0;
            int maxIndex = equipmentModules.Count - 1;
            
            if (index < minIndex)
            {
                index = minIndex;
            }
            if (index > maxIndex)
            {
                index = maxIndex;
            }

            m_btnLeft.gameObject.SetActive(index > minIndex);
            m_btnRight.gameObject.SetActive(index < maxIndex);

            equipmentIndexInBag = index;
            id = equipmentModules[index].id;

            GameEntry.Event.Fire(EquipmentSwitchEventArgs.EventId, EquipmentSwitchEventArgs.Create(0, 0, id));

            RefreshInfo();
        }

        void RefreshSwitchBtnInBag()
        {
            List<EquipmentModule> equipmentModules = GameEntry.EquipmentData.GetEquipmentListCanPromote();
            EquipmentModule equipmentModule = GameEntry.EquipmentData.GetEquipmentByID(id);
            if (equipmentModule == null) return;
            equipmentIndexInBag = equipmentModules.IndexOf(equipmentModule);
            int minIndex = 0;
            int maxIndex = equipmentModules.Count - 1;
            m_btnLeft.gameObject.SetActive(equipmentIndexInBag > minIndex);
            m_btnRight.gameObject.SetActive(equipmentIndexInBag < maxIndex);
        }
    }
}
