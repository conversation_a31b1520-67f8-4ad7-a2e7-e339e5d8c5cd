using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Events;

namespace Game.Hotfix
{
    public partial class UIVipTimeBuyForm : UGuiFormEx
    {
        private bool isStartTimer = false;
        private int remainTime = 0;
        private string m_checkBoxKey = "UIVipTimeBuyForm";
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            m_checkBoxKey += GameEntry.LogicData.RoleData.RoleID;
            m_goPrefab.SetActive(false);
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            isStartTimer = false;
            remainTime = 0;

            InitPageView();
            //开启一个定时器
            Timers.Instance.Add(GetInstanceID().ToString(), 1, (a) =>
            {
                if (isStartTimer)
                {
                    var temp = remainTime - 1;
                    if (temp >= 0)
                    {
                        remainTime--;
                        m_txtTimer.text = ToolScriptExtend.FormatTime(remainTime);
                        if (remainTime == 0)
                        {
                            isStartTimer = false;
                            GameEntry.UI.CloseUIForm(EnumUIForm.UIVipTimeBuyForm);
                        }
                    }
                }
            }, 86400);
            
            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
            GameEntry.Event.Subscribe(VipChangeEventArgs.EventId, OnVipChangeFunc);
            GameEntry.Event.Subscribe(MallChangeEventArgs.EventId, OnMallChangeFunc);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            Timers.Instance.Remove(GetInstanceID().ToString());
            
            GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);
            GameEntry.Event.Unsubscribe(VipChangeEventArgs.EventId, OnVipChangeFunc);
            GameEntry.Event.Unsubscribe(MallChangeEventArgs.EventId, OnMallChangeFunc);

        }

        //vip信息更新
        private void OnVipChangeFunc(object sender, GameEventArgs e)
        {
            ShowTitleInfo();
        }
        
        //商城信息更新更新
        private void OnMallChangeFunc(object sender, GameEventArgs e)
        {
            ShowProductList();
        }
        
        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        //道具数量更新
        void OnItemChange(object sender, GameEventArgs e)
        {
            if (e is ItemChangeEventArgs args)
            {
                
            }
            ShowProductList();
        }
        
        private void InitPageView()
        {
            ShowTitleInfo();

            ShowProductList();
        }
        
         //展示标题区域信息
        private void ShowTitleInfo()
        {
            isStartTimer = false;
            //剩余vip时间
            remainTime = GameEntry.LogicData.VipData.GetVipRemainTime();
            m_txtTimer.gameObject.SetActive(remainTime > 0);
            m_txtTimer.text = ToolScriptExtend.FormatTime(remainTime);

            if (remainTime > 0)
            {
                isStartTimer = true;
            }
        }
        
        //展示商品列表
        private void ShowProductList()
        {
            //最多点数提示
            if (!ToolScriptExtend.GetConfigById<vip_setting>(1009, out var buyData)) return;
            var str = buyData.value.FirstOrDefault();
            if (str == null) return;
            var list = str.Split("|");
            var result = new List<int>();
            foreach (var info in list)
            {
                if (int.TryParse(info, out int value))
                {
                    result.Add(value);
                }
            }

            var showCount = result.Count;
            var isMonthCardActive = GameEntry.LogicData.MallData.IsMonthlyCardActive();
            if (!isMonthCardActive)
            {
                showCount++;
            }

            ToolScriptExtend.RecycleOrCreate(m_goProduct, m_goContent.transform, showCount,true);
            for (var i = 0; i < result.Count; i++)
            {
                var child = m_goContent.transform.GetChild(i);
                SetProductInfo(child, result[i]);
            }

            if (!isMonthCardActive)
            {
                var child = m_goContent.transform.GetChild(showCount-1);
                SetJumpInfo(child,"Sprite/ui_vip/vip_jump_icon1.png",ToolScriptExtend.GetLang(1100159),ToolScriptExtend.GetLang(1100353), () =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIMallForm, paymenttype.paymenttype_monthycard);
                    GameEntry.UI.CloseUIForm(EnumUIForm.UIVipTimeBuyForm);
                });
            }
        }
        
        //显示商品信息
        private void SetProductInfo(Transform root, int id)
        {
            var data = ToolScriptExtend.GetItemConfig(id);
            if (data == null) return;

            var product = root.Find("product");
            var jump = root.Find("jump");
            product.gameObject.SetActive(true);
            jump.gameObject.SetActive(false);

            //道具item
            var node = root.Find("product/node");
            node.localScale = Vector3.one * 0.7f;
            // ToolScriptExtend.ClearAllChild(node);
            if (node.childCount == 0)
            {
                BagManager.CreatItem(node, (itemid)id, 0, (module) =>
                {
                    // module.SetScale();
                });
            }
            
            //名称
            var title = root.Find("product/title").GetComponent<UIText>();
            title.text = ToolScriptExtend.GetLang(data.name);

            //描述
            var desc = root.Find("product/desc").GetComponent<UIText>();
            desc.text = ToolScriptExtend.GetLang(data.desc);

            //购买
            var btn = root.Find("product/btn").GetComponent<UIButton>();
            var btnTxt = root.Find("product/btn/txt").GetComponent<UIText>();
            var price = root.Find("product/btn/price").GetComponent<UIText>();
            var icon = root.Find("product/btn/price/icon").GetComponent<UIImage>();

            var needCount = data.diamond;
            var isEnough = GameEntry.LogicData.GeneralShopData.CheckCoinEnough(itemid.itemid_6, needCount, out var hex);

            btnTxt.text = ToolScriptExtend.GetLang(1100057); //购买并使用
            price.text = $"<color={hex}>{needCount}</color>";
            icon.SetImage(ToolScriptExtend.GetItemIcon(itemid.itemid_6));
            ToolScriptExtend.BindBtnLogic(btn, () =>
            {
                if (!isEnough)
                {
                    var ownCount = GameEntry.LogicData.BagData.GetAmountById(itemid.itemid_6);
                    var offset = needCount - ownCount;
                    ItemModule itemModule = new(itemid.itemid_6);
                    GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(offset));
                    return;
                }
                
                bool isCheck = ToolScriptExtend.GetCommonTipsIsTodayCheckBox(m_checkBoxKey);
                if (isCheck)
                {
                    GameEntry.LogicData.BagData.OnReqBuyItemByDiamond((itemid)id,1,null);
                    return;
                }
                
                CommonTipsParms commonTipsParms = new CommonTipsParms();
                commonTipsParms.desc = ToolScriptExtend.GetLangFormat(1100188, needCount.ToString(),
                    ToolScriptExtend.GetLang(data.name));
                commonTipsParms.okFun = () =>
                {
                    GameEntry.LogicData.BagData.OnReqBuyItemByDiamond((itemid)id,1,null);
                };
                commonTipsParms.isShowCheck = true;
                commonTipsParms.checkBoxKey = m_checkBoxKey;
                GameEntry.UI.OpenUIForm(EnumUIForm.UICommonTipForm, commonTipsParms);
            });
        }

        private void SetJumpInfo(Transform root, string iconPath,string name, string descStr, UnityAction callback = null)
        {
            var product = root.Find("product");
            var jump = root.Find("jump");
            product.gameObject.SetActive(false);
            jump.gameObject.SetActive(true);
            
            //图标
            var icon = root.Find("jump/icon").GetComponent<UIImage>();
            icon.SetImage(iconPath);
            
            // 名称
            var title = root.Find("jump/title").GetComponent<UIText>();
            title.text = name;

            //描述
            var desc = root.Find("jump/desc").GetComponent<UIText>();
            desc.text = descStr;

            //跳转按钮
            var btn = root.Find("jump/btn").GetComponent<UIButton>();
            var btnTxt = root.Find("jump/btn/txt").GetComponent<UIText>();
            btnTxt.text = ToolScriptExtend.GetLang(711376);
            
            ToolScriptExtend.BindBtnLogic(btn, () =>
            {
                callback?.Invoke();
            });
            
        }
    }
}