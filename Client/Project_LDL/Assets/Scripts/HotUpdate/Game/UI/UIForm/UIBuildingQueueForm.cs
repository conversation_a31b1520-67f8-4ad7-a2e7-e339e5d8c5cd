using System;
using System.Collections;
using System.Collections.Generic;
using Build;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBuildingQueueForm : UGuiFormEx
    {
        private List<BuildQueueMoudle> buildQueueInfoList;
        private List<GameObject> itemList;
        private List<int> workIndexList = new List<int>();
        
        private float m_TimeElapsed = 0;
        private float m_TimeInterval = 1;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            
        }

        public void BindEvent()
        {
            GameEntry.Event.Subscribe(OnBuildingQueueRemoveEventArgs.EventId,OnRemoveBuildQueueChange);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            BindEvent();
            workIndexList = new List<int>();
            buildQueueInfoList = GameEntry.LogicData.QueueData.GetBuildQueueInfoList();
            itemList ??= new List<GameObject>();
            if (buildQueueInfoList.Count > 0)
            {
                for (int i = 0; i < 4; i++)
                {
                    var itemRect = Instantiate(m_goQueueItem, m_goQueueList.transform);
                    itemRect.SetActive(true);
                    itemList.Add(itemRect);
                    BuildQueueMoudle buildQueueMoudle = buildQueueInfoList[i];
                    if (buildQueueMoudle != null)
                    {
                        if (buildQueueMoudle.GetIsWork() && !workIndexList.Exists(w => i == w))
                        {
                            workIndexList.Add(i);
                        }
                    }
                    
                    UpdataQueueInfo(i, itemRect);
                }
            }
        }

        protected void UpdataQueueInfo(int index,GameObject obj)
        {
            UIImage queueIcon        = obj.transform.Find("queueIcon").GetComponent<UIImage>();
            UIText txtIndex          = obj.transform.Find("bg_1/txtIndex").GetComponent<UIText>();
            UIText txtQueueDesc      = obj.transform.Find("verRoot/txtQueueDesc").GetComponent<UIText>();
            UIText txtItemDesc       = obj.transform.Find("verRoot/txtItemDesc").GetComponent<UIText>();
            UIText txtRentTime       = obj.transform.Find("txtRentTime").GetComponent<UIText>();
            Slider sliderQueue       = obj.transform.Find("sliderQueueBg/sliderQueue").GetComponent<Slider>();
            UIImage sliderQueueBg    = obj.transform.Find("sliderQueueBg").GetComponent<UIImage>();
            UIImage unLock           = obj.transform.Find("unLock").GetComponent<UIImage>();
            UIButton btnBuy          = obj.transform.Find("btnBuy").GetComponent<UIButton>();
            UIButton btnDone         = obj.transform.Find("btnDone").GetComponent<UIButton>();
            UIButton btnBuild        = obj.transform.Find("btnBuild").GetComponent<UIButton>();
            UIButton btnSpeedUp      = obj.transform.Find("btnSpeedUp").GetComponent<UIButton>();
            UIButton btnUnLock       = obj.transform.Find("btnUnLock").GetComponent<UIButton>();
            
            btnBuy.gameObject.SetActive(false);
            btnDone.gameObject.SetActive(false);
            btnBuild.gameObject.SetActive(false);
            btnSpeedUp.gameObject.SetActive(false);
            unLock.gameObject.SetActive(false);
            btnUnLock.gameObject.SetActive(false);
            txtRentTime.gameObject.SetActive(false);

            txtIndex.text = $"{index + 1}";
            string imgPath = "Sprite/ui_jianzhu/jianzhu_image_xianzhi.png";
            BuildQueueMoudle buildQueueMoudle = buildQueueInfoList[index];
            bool isWork = false;
            bool isLock = false;
            bool isRent = false;
            if (buildQueueMoudle!=null)
            {
                isWork = buildQueueMoudle.GetIsWork();
                isLock = buildQueueMoudle.GetIsLock();
                isRent = buildQueueMoudle.GetIsRent();
            }
            if (!isWork)
            {
                queueIcon.SetImage(imgPath);
                sliderQueueBg.gameObject.SetActive(false);
                txtItemDesc.gameObject.SetActive(false);
                txtQueueDesc.text = ToolScriptExtend.GetLang(1100133);
                if (!isLock && !isRent)
                {
                    string lockStr = "";
                    if (index > 1)
                    {
                        BuildQueueMoudle lastBuildQueueMoudle = buildQueueInfoList[index - 1];
                        bool lastLock = lastBuildQueueMoudle.GetIsLock();
                        if (!lastLock)
                        {
                            unLock.gameObject.SetActive(true);
                            btnUnLock.gameObject.SetActive(true);
                            lockStr = ToolScriptExtend.GetLangFormat(1100151,$"{index}");
                        }
                        else
                        {
                            btnBuy.gameObject.SetActive(true);
                            unLock.gameObject.SetActive(false);
                            lockStr = ToolScriptExtend.GetLang(1100134);
                        }
                    }
                    else
                    {
                        btnBuy.gameObject.SetActive(true);
                        lockStr = ToolScriptExtend.GetLang(1100134);

                    }
                    txtQueueDesc.text = lockStr;
                    ToolScriptExtend.SetGameObjectGrey(btnUnLock.transform,true);
                    btnUnLock.onClick.RemoveAllListeners();
                    btnUnLock.onClick.AddListener(() =>
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = ToolScriptExtend.GetLangFormat(1100149,$"{index}"),
                        });
                    });
                    
                    btnBuy.onClick.RemoveAllListeners();
                    btnBuy.onClick.AddListener(() =>
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIUnLockBuildingQueueForm,index+1);
                    });
                }
                else if (isRent)
                {
                    Timers.Instance.Add("BuildingQueueRent", 1f, (param) =>
                    {
                        txtRentTime.gameObject.SetActive(true);
                        float rentRemainTime = buildQueueMoudle.GetRentRemainTime();
                        txtRentTime.text = TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(rentRemainTime));
                        if (rentRemainTime <= 0)
                        {
                            Timers.Instance.Remove("BuildingQueueRent");
                        }
                    }, -1);
                  
                }
                btnBuild.gameObject.SetActive(isLock || isRent);
                btnBuild.onClick.RemoveAllListeners();
                btnBuild.onClick.AddListener(() =>
                {
                    GameEntry.LogicData.BuildingData.FindBuildingCanBuilt();
                    Close();
                });
            }
            else
            {
                sliderQueueBg.gameObject.SetActive(true);
                txtItemDesc.gameObject.SetActive(true);
                QueueModule queueModule = GameEntry.LogicData.QueueData.GetQueueModuleByQueueID(buildQueueMoudle.QueueUid);
                if (queueModule != null)
                {
                    BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(queueModule.BindBuildNo);
                    if (buildingModule != null)
                    {
                        btnSpeedUp.gameObject.SetActive(true);
                        queueIcon.SetImage(buildingModule.BuildingIcon);
                        txtQueueDesc.text = ToolScriptExtend.GetLangFormat(1100135,buildingModule.LEVEL.ToString(),buildingModule.BuildingName);

                        float remainTime = queueModule.GetRemainTime();
                        var totalTime = queueModule.GetTotalTime();
                        if (remainTime > 0)
                        {
                            txtItemDesc.text = ToolScriptExtend.GetLang(1100136) + TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(remainTime));
                            sliderQueue.value = 1 - (remainTime / totalTime);
                        }
                        else
                        {
                            txtQueueDesc.text = ToolScriptExtend.GetLangFormat(1100137,buildingModule.LEVEL.ToString(),buildingModule.BuildingName);
                            txtItemDesc.gameObject.SetActive(false);
                            sliderQueue.value = 1;
                            btnSpeedUp.gameObject.SetActive(false);
                            btnDone.gameObject.SetActive(true);
                        }
                        
                        btnSpeedUp.onClick.RemoveAllListeners();
                        btnSpeedUp.onClick.AddListener(() =>
                        {
                            itemsubtype itemSubType = itemsubtype.itemsubtype_buildspeedup;
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingSpeedUpForm,new OpenSpeedUpParam(buildingModule,itemSubType));
                        });
                        
                        btnDone.onClick.RemoveAllListeners();
                        btnDone.onClick.AddListener(() =>
                        {
                            GameEntry.LogicData.BuildingData.BuildQueueFinishReq((uint)buildingModule.BuildingId, queueModule.QueueUid,(build) =>
                            {
                                GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingQueueForm);
                            });
                        });
                    }
                }
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            workIndexList = null;
            m_goQueueList.DestroyAllChild();
            buildQueueInfoList = null;
            itemList = null;
            Timers.Instance.Remove("BuildingQueueRent");
            GameEntry.Event.Unsubscribe(OnBuildingQueueRemoveEventArgs.EventId, OnRemoveBuildQueueChange);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            OnRefreshList();
        }

        protected void OnUpdateUI()
        {
            if (workIndexList.Count > 0)
            {
                foreach (int workIndex in workIndexList)
                {
                    GameObject obj = itemList[workIndex];
                    if (obj != null)
                    {
                        UpdataQueueInfo(workIndex, obj);
                    }

                    // BuildQueueMoudle buildQueueMoudle = buildQueueInfoList[workIndex];
                    // if (buildQueueMoudle != null)
                    // {
                    //     GameObject obj = itemList[workIndex];
                    //     
                        // UIText txtItemDesc       = obj.transform.Find("verRoot/txtItemDesc").GetComponent<UIText>();
                        // UIText txtQueueDesc      = obj.transform.Find("verRoot/txtQueueDesc").GetComponent<UIText>();
                        // Slider sliderQueue       = obj.transform.Find("sliderQueueBg/sliderQueue").GetComponent<Slider>();
                        // UIButton btnDone         = obj.transform.Find("btnDone").GetComponent<UIButton>();
                        //
                        // bool isWork = buildQueueMoudle.GetIsWork();
                        // bool isRent = buildQueueMoudle.GetIsRent();
                        // if (isWork)
                        // {
                        //     txtItemDesc.gameObject.SetActive(true);
                        //     QueueModule queueModule = GameEntry.LogicData.QueueData.GetQueueModuleByQueueID(buildQueueMoudle.QueueUid);
                        //     if (queueModule != null)
                        //     {
                        //         float remainTime = queueModule.GetRemainTime();
                        //         var totalTime = queueModule.GetTotalTime();
                        //         if (remainTime > 0)
                        //         {
                        //             txtItemDesc.text = ToolScriptExtend.GetLang(1100136) + TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(remainTime));
                        //             sliderQueue.value = 1 - (remainTime / totalTime);
                        //         }
                        //         else
                        //         {
                        //             BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(queueModule.BindBuildNo);
                        //             txtQueueDesc.text = ToolScriptExtend.GetLangFormat(1100137,buildingModule.LEVEL.ToString(),buildingModule.BuildingName);
                        //             txtItemDesc.gameObject.SetActive(false);
                        //             sliderQueue.value = 1;
                        //             btnDone.gameObject.SetActive(true);
                        //         }
                        //     }
                        // }

                    
                }
            }
        }
        
        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            m_TimeElapsed += elapseSeconds;

            if (m_TimeElapsed > m_TimeInterval)
            {
                m_TimeElapsed -= m_TimeInterval;
                OnUpdateUI();
            }
        }

        protected void OnRemoveBuildQueueChange(object sender, GameEventArgs e)
        {
            if (e is OnBuildingQueueRemoveEventArgs)
            {
                OnRefreshList();
            }
        }

        private void OnRefreshList()
        {
            if (itemList.Count > 0)
            {
                for (int i = 0; i < 4; i++)
                {
                    var itemRect = itemList[i];
                    BuildQueueMoudle buildQueueMoudle = buildQueueInfoList[i];
                    if (buildQueueMoudle != null)
                    {
                        if (!buildQueueMoudle.GetIsWork() && workIndexList.Exists(w => i == w))
                        {
                            workIndexList.Remove(i);
                        }
                    }
                    UpdataQueueInfo(i, itemRect);
                }
            }
        }
        
        private void OnBtnCloseClick()
        {
            Close();
        }
    }
}
