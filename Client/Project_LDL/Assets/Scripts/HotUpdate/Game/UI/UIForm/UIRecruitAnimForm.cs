using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using GameFramework.Event;
using Recruit;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;
using Random = UnityEngine.Random;

namespace Game.Hotfix
{
    public partial class UIRecruitAnimForm : UGuiFormEx
    {
        private int recruitId;
        private recruittype recruitType;
        private recruitnumtype recruitNumType;
        private Transform singleCardTrans;
        private Vector3 singleOriginalPos;
        private Vector3 singleOriginalAngles;

        private Transform tenCardTrans;

        private List<CardInfo> tenCardList = new List<CardInfo>();
        private List<Transform> tenGridList = new List<Transform>();
        private List<Transform> tenPosList = new List<Transform>();
        private List<GridInfo> recordList = new List<GridInfo>();
        private List<RecruitReward> rewardList = new List<RecruitReward>();

        private bool isCanOpenAll = true;

        private bool isRunning = false; //是否有正在请求的协议任务

        private class CardInfo
        {
            public GameObject obj;
            public Transform card;
            public GameObject front;
            public GameObject back;
            public GameObject node;
            public GameObject effect1;
            public GameObject effect2;
            public quality cardQuality;
            public bool isPlaying; //是否正在播放动画
        }

        private class GridInfo
        {
            public Vector3 position;
            public Quaternion rotation;
        }

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            singleCardTrans = m_goSingleCard.transform;
            singleOriginalPos = singleCardTrans.position;
            singleOriginalAngles = singleCardTrans.eulerAngles;
            tenCardTrans = m_goTen.transform;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            var param = userData as RecruitParams.RecruitResult;
            recruitId = param.recruitId;
            recruitType = param.recruitType;
            recruitNumType = param.numType;
            rewardList = param.rewardList;
            GameEntry.LogicData.RecruitData.SetBtnTxt(m_btnAnother, recruitNumType, recruitType);
            GameEntry.LogicData.RecruitData.SetTopDiamondView(m_goDiamond);
            GameEntry.LogicData.RecruitData.SetRecruitTicketView(recruitType, m_goTopItem);
            isRunning = false;
            CheckResEnough(m_btnAnother.gameObject, (int)recruitNumType);

            m_goSingle.SetActive(recruitNumType == recruitnumtype.recruitnumtype_one ||
                                 recruitNumType == recruitnumtype.recruitnumtype_free);
            m_goTen.SetActive(recruitNumType == recruitnumtype.recruitnumtype_ten);

            m_btnAnother.gameObject.SetActive(false);
            m_btnShowAll.gameObject.SetActive(false);
            m_btnBack.gameObject.SetActive(false);

            switch (recruitNumType)
            {
                case recruitnumtype.recruitnumtype_free:
                case recruitnumtype.recruitnumtype_one:
                    SingleEnterAnim();
                    LoadSingleCardInfo();
                    break;
                case recruitnumtype.recruitnumtype_ten:
                    if (tenCardList.Count == 0)
                    {
                        CollectTenCardData();
                    }

                    InitTenCardData();
                    LoadTenCardInfo();
                    TenEnterAnim();
                    break;
                default:
                    break;
            }

            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
            isCanOpenAll = true;
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);
            isCanOpenAll = true;
            isRunning = false;
        }

        //道具数量更新
        void OnItemChange(object sender, GameEventArgs e)
        {
            if (e is ItemChangeEventArgs args)
            {
            }

            //道具数量更新
            GameEntry.LogicData.RecruitData.SetTopDiamondView(m_goDiamond, true);
            GameEntry.LogicData.RecruitData.SetRecruitTicketView(recruitType, m_goTopItem, true);
            CheckResEnough(m_btnAnother.gameObject, (int)recruitNumType);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            var msgId = (int)userData;
            if (msgId == 1)
            {
            }
        }

        //再抽一次
        private void OnBtnAnotherClick()
        {
            var itemId = GameEntry.LogicData.RecruitData.GetIconIdByType(recruitType);
            var ownCount = GameEntry.LogicData.BagData.GetAmountById(itemId);
            var needCount = (int)recruitNumType;
            needCount = needCount <= 1 ? 1 : needCount;

            if (ownCount < needCount)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = "货币不足！"
                });
                return;
            }

            if (isRunning)
            {
                return;
            }

            isRunning = true;
            GameEntry.LogicData.RecruitData.C2SRecruitReq(recruitId, recruitNumType, msg =>
            {
                if (msg.List.Count <= 0) return;
                rewardList.Clear();
                rewardList.AddRange(msg.List);
                if (recruitNumType is recruitnumtype.recruitnumtype_one or recruitnumtype.recruitnumtype_free)
                {
                    m_btnAnother.gameObject.SetActive(false);
                    m_btnShowAll.gameObject.SetActive(false);
                    m_btnBack.gameObject.SetActive(false);
                    LoadSingleCardInfo();
                    var cardInfo = GetSingleCardInfo(singleCardTrans);
                    ResetCardView(cardInfo);
                    var btn = singleCardTrans.Find("btn").GetComponent<UIButton>();
                    btn.onClick.RemoveAllListeners();
                    
                    SetSingleEffectInfo(cardInfo);
                    SingleFlipAnim(cardInfo, SingleClickLogic);
                }
                else if (recruitNumType == recruitnumtype.recruitnumtype_ten)
                {
                    ResetTenCardStatus();
                    LoadTenCardInfo();
                }

                isRunning = false;
            });
        }

        //全部翻转
        private void OnBtnShowAllClick()
        {
            if (!isCanOpenAll)
            {
                return;
            }

            var waitCount = 0;
            foreach (var cardInfo in tenCardList)
            {
                if (cardInfo.node.activeInHierarchy)
                {
                    continue;
                }

                waitCount++;
            }

            var sequence = DOTween.Sequence();
            sequence.AppendCallback(() =>
            {
                isCanOpenAll = false;
                OpenTenCardInOrder();
            });
            sequence.AppendInterval(waitCount * 0.3f);
            sequence.OnComplete(() => { isCanOpenAll = true; });
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        #region 公共逻辑

        //重置卡牌显示效果
        private void ResetCardView(CardInfo cardInfo)
        {
            cardInfo.front.SetActive(false);
            cardInfo.node.SetActive(false);
            cardInfo.back.SetActive(true);
            cardInfo.card.eulerAngles = Vector3.zero;
            cardInfo.obj.transform.localScale = Vector3.one;
        }

        //根据传入数据控制卡牌显示效果
        private void SetCardInfoView(int index, CardInfo cardInfo)
        {
            if (index < 0 || index >= rewardList.Count)
            {
                Debug.LogError("索引越界！");
                return;
            }

            var info = rewardList[index];
            var manager = GameEntry.LogicData.RecruitData;

            var config = GameEntry.LDLTable.GetTableById<recruit_drops>((int)info.RecruitDropId);
            if (config == null) return;
            var rewardId = config.drop_reward.item_id;
            var rewardCount = (int)config.drop_reward.num;

            bool needSwitch = false;
            //判断是否是已获得英雄（需要转成英雄碎片）
            if (info.IsConvertPatch)
            {
                if (GameEntry.LogicData.RecruitData.ChangeHeroToPiece(rewardId, out var pieceId, out var pieceCount))
                {
                    rewardId = pieceId;
                    rewardCount *= pieceCount;
                    needSwitch = true;
                }
            }
            else { GameEntry.LogicData.HeroData.SetHeroNew(rewardId, true); }

            manager.GetItemData((int)rewardId, (data) =>
            {
                var bg = cardInfo.front.GetComponent<UIImage>();
                var qualityPath = manager.GetCardQualityBg(data.quality);
                bg.SetImage(qualityPath);
                var nodeRoot = cardInfo.node.transform;
                var nameTxt = nodeRoot.Find("name").GetComponent<UIText>();
                var icon = nodeRoot.Find("icon").GetComponent<UIImage>();
                var countTxt = nodeRoot.Find("count").GetComponent<UIText>();
                var border = nodeRoot.Find("border").GetComponent<UIImage>();
                
                var hero = nodeRoot.Find("hero");
                var icon_1 = nodeRoot.Find("hero/icon_1").GetComponent<UIImage>();
                var attr1 = nodeRoot.Find("attr1").GetComponent<UIImage>();
                var attr2 = nodeRoot.Find("attr2").GetComponent<UIImage>();
                var newFlag = nodeRoot.Find("new").GetComponent<UIImage>();
                
                border.SetImage(manager.GetCardQualityBorder(data.quality));
                
                var isWholeHero = data.item_type == itemtype.itemtype_hero && !needSwitch;
                icon.gameObject.SetActive(!isWholeHero);
                countTxt.gameObject.SetActive(!isWholeHero);
                icon_1.gameObject.SetActive(isWholeHero);
                attr1.gameObject.SetActive(isWholeHero);
                attr2.gameObject.SetActive(isWholeHero);
                newFlag.gameObject.SetActive(isWholeHero);
                hero.gameObject.SetActive(isWholeHero);
                
                if (isWholeHero) 
                {
                    //特殊处理完整英雄
                    if (ToolScriptExtend.GetConfigById<hero_config>((int)rewardId, out var heroData))
                    {
                        icon_1.SetImage(heroData.hero_head);
                        attr1.SetImage(GameEntry.LogicData.HeroData.GetServicesImgPath(heroData.services));
                        attr2.SetImage(GameEntry.LogicData.HeroData.GetPositionImgPath(heroData.position));
                    }
                }
                else
                {
                    countTxt.text = "x" + ToolScriptExtend.FormatNumberWithUnit(rewardCount);
                    icon.SetImage(ToolScriptExtend.GetItemIcon(rewardId));
                }
                
                nameTxt.text = ToolScriptExtend.GetLang(data.name);
                if (ColorUtility.TryParseHtmlString(manager.GetHexByQuality(data.quality), out var color))
                {
                    nameTxt.color = color;
                }

                cardInfo.cardQuality = data.quality;
                cardInfo.effect1.SetActive(false);
                cardInfo.effect2.SetActive(false);
                
            });
        }

        //绑定点击按钮逻辑
        private void BindBtnLogic(Button btn, CardInfo cardInfo, UnityAction callback)
        {
            btn.onClick.RemoveAllListeners();
            btn.onClick.AddListener(() =>
            {
                if (cardInfo.isPlaying)
                {
                    // Debug.Log("正在播放动画");
                    return;
                }

                if (cardInfo.node.activeInHierarchy)
                {
                    // Debug.Log("已经翻开了");
                    return;
                }

                SingleFlipAnim(cardInfo, callback);
            });
        }

        // //生成闪光特效
        // private void CreateEffect(Transform root,Config.quality quality)
        // {
        //     var path = "battle_kapai_huang1";
        //     if (quality == quality.quality_purple)
        //     {
        //         path = "battle_kapai_zise2";
        //     }
        //     else if (quality == quality.quality_orange)
        //     {
        //         path = "battle_kapai_huang1";
        //     }
        //     Game.GameEntry.Resource.LoadAsset($"Assets/ResPackage/Effect/Prefab/{path}.prefab", typeof(GameObject), new GameFramework.Resource.LoadAssetCallbacks(
        //         (string assetName, object asset, float duration, object userData) =>
        //         {
        //             Instantiate((GameObject)asset, root);
        //             SetParticleSystemSortingOrder(root.gameObject, Depth);
        //         }));
        // }

        #endregion

        #region 单抽动画逻辑

        private void SetSingleEffectInfo(CardInfo cardInfo)
        {
            var info = rewardList[0];
            var config = GameEntry.LDLTable.GetTableById<recruit_drops>((int)info.RecruitDropId);
            if (config == null) return;
            var rewardId = config.drop_reward.item_id;

            //判断是否是已获得英雄（需要转成英雄碎片）
            if (info.IsConvertPatch && GameEntry.LogicData.RecruitData.ChangeHeroToPiece(rewardId, out var pieceId, out var pieceCount))
            {
                rewardId = pieceId;
            }
            GameEntry.LogicData.RecruitData.GetItemData((int)rewardId, (data) =>
            {
                cardInfo.cardQuality = data.quality;
            });
        }
        
        //单抽入场动画
        private void SingleEnterAnim()
        {
            singleCardTrans.position = singleOriginalPos;
            var cardInfo = GetSingleCardInfo(singleCardTrans);
            var btn = singleCardTrans.Find("btn").GetComponent<UIButton>();
            BindBtnLogic(btn, cardInfo, SingleClickLogic);
            ResetCardView(cardInfo);

            SetSingleEffectInfo(cardInfo);
            singleCardTrans.DOMove(m_goSingleDes.transform.position, 0.5f).SetEase(Ease.InQuart);
        }

        private CardInfo GetSingleCardInfo(Transform root)
        {
            var cardInfo = new CardInfo();
            var card = root.Find("card");
            cardInfo.obj = root.gameObject;
            cardInfo.card = card;
            cardInfo.front = card.Find("front").gameObject;
            cardInfo.back = card.Find("back").gameObject;
            cardInfo.node = card.Find("node").gameObject;
            cardInfo.effect1 = root.Find("effect1").gameObject;
            cardInfo.effect2 = root.Find("effect2").gameObject;
            return cardInfo;
        }

        //单张卡翻转动画
        private void SingleFlipAnim(CardInfo cardInfo, UnityAction callback)
        {
            cardInfo.card.eulerAngles = Vector3.zero;
            cardInfo.card.localScale = Vector3.one;

            float duration = 0.6f;
            cardInfo.card.DOBlendableScaleBy(new Vector3(0.1f, 0.2f, 0), duration / 2).OnComplete(() =>
            {
                cardInfo.card.DOBlendableScaleBy(new Vector3(-0.1f, -0.2f, 0), duration / 2);
            });
            bool hasCheckEffect = false;
            cardInfo.card.DOBlendableLocalRotateBy(new Vector3(0, 180, 0), duration).OnUpdate(() =>
            {
                cardInfo.isPlaying = true;
                var value = cardInfo.card.eulerAngles.y;
                var frontActive = cardInfo.front.activeInHierarchy;
                var backActive = cardInfo.back.activeInHierarchy;
                var nodeActive = cardInfo.node.activeInHierarchy;

                var rotateValue = Mathf.Abs(value);
                if (rotateValue <= 90)
                {
                    if (frontActive)
                    {
                        cardInfo.front.SetActive(false);
                    }

                    if (!backActive)
                    {
                        cardInfo.back.SetActive(true);
                    }

                    if (nodeActive)
                    {
                        cardInfo.node.SetActive(false);
                    }
                }
                else
                {
                    if (!frontActive)
                    {
                        cardInfo.front.SetActive(true);
                    }

                    if (backActive)
                    {
                        cardInfo.back.SetActive(false);
                    }

                    if (!nodeActive)
                    {
                        cardInfo.node.SetActive(true);
                    }

                    if (!hasCheckEffect && rotateValue >= 150)
                    {
                        cardInfo.effect1.SetActive(cardInfo.cardQuality == quality.quality_orange);
                        cardInfo.effect2.SetActive(cardInfo.cardQuality == quality.quality_purple);
                        hasCheckEffect = true;
                    }
                }
                
            }).OnComplete(() =>
            {
                cardInfo.isPlaying = false;
                callback?.Invoke();
            });
        }

        //单抽卡点击查看逻辑
        private void SingleClickLogic()
        {
            m_btnAnother.gameObject.SetActive(true);
            m_btnBack.gameObject.SetActive(true);
        }

        //加载单张卡牌信息，图标、名称、数量、品质底
        private void LoadSingleCardInfo()
        {
            var cardInfo = GetSingleCardInfo(singleCardTrans);
            SetCardInfoView(0, cardInfo);
        }

        #endregion

        #region 十抽动画逻辑

        //收集界面动画数据
        private void CollectTenCardData()
        {
            //收集卡牌目标展位数据
            var gridRoot = tenCardTrans.Find("grid");
            tenGridList.Clear();
            for (var i = 0; i < 10; i++)
            {
                var child = gridRoot.GetChild(i);
                GameObject obj;
                obj = child.childCount > 0 ? child.GetChild(0).gameObject : Instantiate(m_goTenCard, child);
                obj.transform.localPosition = Vector3.zero;
                obj.SetActive(false);
                var cardInfo = GetSingleCardInfo(obj.transform);
                tenCardList.Add(cardInfo);
                tenGridList.Add(child);
            }

            //十抽位置预设位置收集
            var startPos = tenCardTrans.Find("StartPos");
            tenPosList.Clear();
            recordList.Clear();
            for (var i = 0; i < 10; i++)
            {
                var child = startPos.GetChild(i);
                child.GetPositionAndRotation(out Vector3 position, out Quaternion rotation);
                recordList.Add(new GridInfo { position = position, rotation = rotation });
                tenPosList.Add(child);
            }
        }

        //初始化十抽界面数据
        private void InitTenCardData()
        {
            m_btnShowAll.gameObject.SetActive(false);
            for (var i = 0; i < 10; i++)
            {
                var target = tenCardList[i];
                target.obj.SetActive(false);
                ResetCardView(target);
                var btn = target.obj.transform.Find("btn").GetComponent<UIButton>();
                BindBtnLogic(btn, target, TenClickLogic);
            }

            for (var i = 0; i < 10; i++)
            {
                var data = recordList[i];
                var target = tenPosList[i];
                target.localScale = Vector3.one;
                target.SetPositionAndRotation(data.position, data.rotation);
                target.gameObject.SetActive(true);
            }
        }

        //重置初始化十抽界面状态
        private void ResetTenCardStatus()
        {
            m_btnBack.gameObject.SetActive(false);
            m_btnShowAll.gameObject.SetActive(false);
            m_btnAnother.gameObject.SetActive(false);
            for (var i = 0; i < 10; i++)
            {
                var target = tenCardList[i];
                ResetCardView(target);
                var btn = target.obj.transform.Find("btn").GetComponent<UIButton>();
                btn.onClick.RemoveAllListeners();
                SingleFlipAnim(target, TenClickLogic);
            }
        }

        //十抽入场动画
        private void TenEnterAnim()
        {
            var list = new int[] { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 };
            var value = 9;
            while (value > 0)
            {
                var index = Random.Range(1, value);
                //优化后
                (list[value], list[index]) = (list[index], list[value]);
                value -= 1;
            }

            var sequence = DOTween.Sequence();

            foreach (var index in list)
            {
                sequence.AppendInterval(0.05f);
                sequence.AppendCallback(() =>
                {
                    var child = tenPosList[index];
                    var pos = tenGridList[index];
                    child.transform.localScale = new Vector3(1, 1.3f, 1);
                    child.DOMove(pos.position, 0.2f).SetEase(Ease.OutCubic).OnComplete(() =>
                    {
                        child.DOLocalRotate(Vector3.zero, 0.1f);
                        child.DOScale(Vector3.one, 0.1f);
                    });
                });
            }

            sequence.AppendInterval(0.5f);
            sequence.AppendCallback(() =>
            {
                foreach (var grid in tenPosList)
                {
                    grid.gameObject.SetActive(false);
                }

                foreach (var cardInfo in tenCardList)
                {
                    cardInfo.obj.SetActive(true);
                }
            }).OnComplete(() => { m_btnShowAll.gameObject.SetActive(true); });
        }

        //按顺序来翻转十抽卡牌
        public void OpenTenCardInOrder()
        {
            var sequence = DOTween.Sequence();
            foreach (var cardInfo in tenCardList)
            {
                if (cardInfo.node.activeInHierarchy)
                {
                    continue;
                }

                sequence.AppendInterval(0.1f);
                sequence.AppendCallback(() => { SingleFlipAnim(cardInfo, TenClickLogic); });
            }
        }

        //十抽卡点击查看逻辑
        private void TenClickLogic()
        {
            bool isAllOpen = true; //所有卡牌都已翻开查看
            for (var i = 0; i < 10; i++)
            {
                var cardInfo = tenCardList[i];
                if (!cardInfo.node.activeInHierarchy)
                {
                    isAllOpen = false;
                    break;
                }
            }

            if (!isAllOpen) return;
            m_btnShowAll.gameObject.SetActive(false);
            m_btnAnother.gameObject.SetActive(true);
            m_btnBack.gameObject.SetActive(true);
        }

        //加载十抽卡牌信息，图标、名称、数量、品质底
        private void LoadTenCardInfo()
        {
            for (var i = 0; i < 10; i++)
            {
                var cardInfo = tenCardList[i];
                SetCardInfoView(i, cardInfo);
            }
        }

        #endregion

        private void CheckResEnough(GameObject obj, int needCount)
        {
            needCount = needCount <= 1 ? 1 : needCount;
            var itemId = GameEntry.LogicData.RecruitData.GetIconIdByType(recruitType);
            var txt = obj.transform.Find("num").GetComponent<UIText>();
            var ownCount = GameEntry.LogicData.BagData.GetAmountById(itemId);
            txt.text = ownCount >= needCount
                ? $"<color=#FFFFFF>{needCount}</color>"
                : $"<color=#CA2525>{needCount}</color>";
        }
    }
}