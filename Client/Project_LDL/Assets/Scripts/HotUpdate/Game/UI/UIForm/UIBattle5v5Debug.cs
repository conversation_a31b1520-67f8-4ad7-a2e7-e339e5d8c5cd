using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBattle5v5Debug : UGuiFormEx
    {
        private Battle5v5Component m_Battle5V5Component;
        private BattleFiled m_BattleFiled;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnReleaseClick()
        {
            GameEntry.ObjectPool.ReleaseAllUnused();
            ShowTip("释放所有");
        }
        
        private void OnBtnCreateTeamClick()
        {
            var battleFiled = GetBattleFiled();
            if (battleFiled == null)
            {
                ShowTip("未找到战斗场景");
                return;
            }
            EnumBattlePos pos = 0;
            if (!EnumBattlePos.TryParse(this.m_inputPos.text,out pos))
            {
                ShowTip("位置无效");
                return;
            }
            
            int heroId = 0;
            if (!int.TryParse(m_inputHero.text,out heroId))
            {
                ShowTip("英雄ID无效");
                return;
            }

            var heroCfg = GameEntry.LDLTable.GetTableById<battle_role>(heroId);
            if (heroCfg == null)
            {
                ShowTip("未找到指定英雄");
                return;
            }

            battleFiled.TeamCtrl.CreateHero(pos, heroId, null, true);

        }

        private BattleFiled GetBattleFiled()
        {
            var battleComponent = GetBattleComponent();
            if (battleComponent != null)
            {
                return battleComponent.BattleFiled;
            }

            return null;
        }
        
        private Battle5v5Component GetBattleComponent()
        {
            if (m_Battle5V5Component == null)
            {
                m_Battle5V5Component = UnityGameFramework.Runtime.GameEntry.GetComponent<Battle5v5Component>();
            }
            return m_Battle5V5Component;
        }

        private void ShowTip(string txt)
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = txt,
            });
        }
    }
}
