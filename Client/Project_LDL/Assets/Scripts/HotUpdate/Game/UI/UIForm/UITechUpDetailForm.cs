using System;
using System.Collections;
using System.Collections.Generic;
using Build;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITechUpDetailForm : UGuiFormEx
    {
        public int techGroup = 0;
        public int curLevel = 0;
        public int nextLevel = 0;
        public bool isMax = false;
        public tech_config nextConfig;
        public tech_config config;
        private List<KeyValuePair<EnumBuildDemand, int>> m_DemandList;
        private List<GameObject> m_DemandObjList = new List<GameObject>();
        private GameObject _scrollViewCost;
        private GameObject _costObj;
        private BuildingModule _curBuildingModule;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitBind();
            _scrollViewCost = m_scrollviewCost.transform.Find("Viewport/Content").gameObject;
            _costObj = m_goCostItem.gameObject;
        }

        protected override void OnOpen(object userData)
        {
            var param = userData as TechDetail;
            techGroup = param.techGroup;
            _curBuildingModule = param._curBuildingModule;
            m_DemandList = new();  
            if (techGroup > 0)
            {
                curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup(techGroup);
                config = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel(techGroup, curLevel);
                nextConfig = GameEntry.LogicData.TechData.GetTechConfig(config.next_id);
                ResetCostParams();
                OnRefreshPanel();
                ResetDemandListView();
                ResetBuildInfo();
            }         
            base.OnOpen(userData);
        }
        public void OnRefreshPanel()
        {
            if (config == null) return;
            m_txtDes.text = ToolScriptExtend.GetLang(config.tech_desc);
            m_txtName.text = ToolScriptExtend.GetLang(config.tech_title);
            m_imgIcon.SetImage(config.tech_icon);
            m_txtCurrent.text = ToolScriptExtend.GetLang(80100009);
            m_txtNext.text = ToolScriptExtend.GetLang(80100010);



            if (config.tech_attributes.Count > 0)
            {
                m_txtCurrentValue.text = ToolScriptExtend.GetNameByAttrbuteType(config.tech_attributes[0].attributes_type)
                + ToolScriptExtend.GetAttrLang(config.tech_attributes[0].value);
            }
            else
            {
                m_txtCurrentValue.text = "0";
            }


            if (config.next_id != 0)
            {
                tech_config nextConfig = GameEntry.LogicData.TechData.GetTechConfig(config.next_id);
                float additionValue = (float)nextConfig.tech_attributes[0].value / 10000;
                m_txtNextValue.text = additionValue * 100 + "%";
            }
            else
            {
                //Max
            }
           
        }

        protected void ResetDemandListView()
        {
            if (m_DemandList.Count > 0)
            {
                for (var i = 0; i < m_DemandList.Count; i++)
                { 
                    GameObject itemRect;
                    if (m_DemandObjList.Count < i + 1)
                    {
                        itemRect = Instantiate(this._costObj,_scrollViewCost.transform);
                        itemRect.SetActive(true);
                        m_DemandObjList.Add(itemRect);
                    }
                    else
                    {
                        itemRect = m_DemandObjList[i];
                    }
                    UpdataCostListView(m_DemandList[i],itemRect,m_DemandList[i].Key);
                }
            }
        }

        protected void ResetCostParams()
        {
            if (nextConfig == null)
            {
                return ;
            }
            
            if (nextConfig.tech_demand != null)
            {
                int unLockCount = 0;
                for (var i = 0; i < nextConfig.tech_demand.Count; i++)
                {
                    var dictionary = new KeyValuePair<EnumBuildDemand, int>(EnumBuildDemand.Build,i);
                    int demandId = nextConfig.tech_demand[i];
                    demand_config config = GameEntry.LDLTable.GetTableById<demand_config>(demandId);
                    bool isUnlock = GameEntry.LogicData.BuildingData.GetBuildingDemandIsUnlock(
                        config.build_demand.build_type_demand, config.build_demand.build_level_demand,
                        config.build_demand.build_num_demand);
                    if (!isUnlock)
                    {
                        m_DemandList.Insert(0,dictionary);
                        unLockCount += 1;
                    }
                    else
                    {
                        m_DemandList.Add(dictionary);
                    }
                }

                int resNotEnoughCount = 0;
                for (var i = 0; i < nextConfig.tech_cost.Count; i++)
                {
                    var cost = nextConfig.tech_cost[i];
                    var dictionary = new KeyValuePair<EnumBuildDemand, int>(EnumBuildDemand.Resource,i);
                    bool isEnough = GameEntry.LogicData.BagData.GetResoureIsEnough(cost.item_id,cost.num);
                    if (!isEnough)
                    {
                        m_DemandList.Insert(resNotEnoughCount + unLockCount,dictionary);
                        resNotEnoughCount += 1;
                    }
                    else
                    {
                        m_DemandList.Add(dictionary);
                    }
                }
            }
        }
        
         protected void UpdataCostListView(object userData, GameObject obj, EnumBuildDemand demandType)
        {
            Image imgIcon = obj.transform.Find("imgIcon").GetComponent<Image>();
            Text txtCondition = obj.transform.Find("txtCondition").GetComponent<Text>();
            Button btnBuild = obj.transform.Find("btnBuild").GetComponent<Button>();
            Text txtBuild = obj.transform.Find("btnBuild/txtBuild").GetComponent<Text>();
            GameObject finish = obj.transform.Find("finish").gameObject;

            string iconPath = String.Empty;
            string strCondition = String.Empty;
            string name = string.Empty;
            string color = "#000000";
            string redColor = "#ff3535";
            itemid itemId = 0;
            btnBuild.gameObject.SetActive(false);
            txtBuild.text = ToolScriptExtend.GetLang(1100128);
            var config = (KeyValuePair<EnumBuildDemand, int>)userData;
            switch (demandType)
            {
                case EnumBuildDemand.Build:
                    int configIndex = config.Value;
                    int demandId = nextConfig.tech_demand[configIndex];
                    demand_config demandConfig = GameEntry.LDLTable.GetTableById<demand_config>(demandId);
                    bool isUnlock = false;
                    BuildingModule buildingModule = null;
                    if (demandConfig.build_demand != null)
                    {
                        buildtype buildTypeDemand = demandConfig.build_demand.build_type_demand;
                        int levelDemand = demandConfig.build_demand.build_level_demand;
                        int buildId = (int)buildTypeDemand * 100 + 1;
                        buildingModule = BuildingModule.Create(buildId, 1, 1);
                        isUnlock = GameEntry.LogicData.BuildingData.GetBuildingDemandIsUnlock(buildTypeDemand, levelDemand, 1);
                        string strColor = isUnlock ? color : redColor;
                        if (buildingModule != null)
                        {
                            name = buildingModule.BuildingName;
                            strCondition = string.Format("<color={0}>Lv.{1}{2}</color>", strColor, levelDemand, name);
                            iconPath = buildingModule.BuildingIcon;
                        }
                    }

                    finish.SetActive(isUnlock);
                    btnBuild.gameObject.SetActive(!isUnlock);
                    btnBuild.onClick.RemoveAllListeners();
                    btnBuild.onClick.AddListener(() =>
                    {
                        if (buildingModule != null)
                        {
                            bool isFind = GameEntry.LogicData.BuildingData.FindBuildingAndOpenMenu(buildingModule.GetBuildingType());
                            if (!isFind)
                            {
                                GameEntry.UI.OpenUIForm(EnumUIForm.UIMainFaceBuildForm, buildingModule.buildingCfg);
                                Close(true);
                                return;
                            }
                            Close();
                        }
                    });
                    break;
                case EnumBuildDemand.Resource:
                    cost cost = nextConfig.tech_cost[config.Value];
                    itemId = cost.item_id;
                    bool resoureIsEnough = GameEntry.LogicData.BagData.GetResoureIsEnough(itemId, cost.num);
                    long count = GameEntry.LogicData.BagData.GetAmountById(itemId);
                    string countStr = ToolScriptExtend.FormatNumberWithUnit(count);
                    string costStr = ToolScriptExtend.FormatNumberWithUnit(cost.num);
                    string colorStr = resoureIsEnough ? color : redColor;
                    strCondition = string.Format("<color={0}>{1}</color>/{2}", colorStr, countStr, costStr);
                    finish.SetActive(resoureIsEnough);
                    break;
            }

            if (itemId != 0)
            {
                var _itemConfig = GameEntry.LDLTable.GetTableById<item_config>(itemId);
                if (_itemConfig != null)
                {
                    iconPath = _itemConfig.icon;
                }
            }


            if (!string.IsNullOrEmpty(iconPath))
            {
                imgIcon.SetImage(iconPath);
            }

            txtCondition.text = strCondition;
        }

        protected void ResetBuildInfo()
        {

            string dateUtcTimeText = TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(nextConfig.time));
            m_txtOldTime.text = dateUtcTimeText;
            int realTime = GameEntry.LogicData.BuildingData.GetRealBuildingTime((int)nextConfig.time,false);
            string realTimeTxt = TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(realTime));
            m_txtUpLevelTime.text = realTimeTxt;
            
            int diamondCostTime = GameEntry.LogicData.BuildingData.GetRealBuildingTime((int)nextConfig.time);
            int costDiamond = GameEntry.LogicData.BuildingData.GetCostDiamond(diamondCostTime);
            List<ItemModule> resourceItems;
            bool resIsEnough = GameEntry.LogicData.BuildingData.GetResourceIsEnough(_curBuildingModule.GetBuildingType(), _curBuildingModule.LEVEL + 1,out resourceItems);
            int resTotalDiamond = 0;
            if (!resIsEnough)
            {
                foreach (ItemModule itemModule in resourceItems)
                {
                    int buyRescource = GameEntry.LogicData.BagData.GetDiamondBuyRescource(itemModule.ItemId, itemModule.Count);
                    resTotalDiamond += buyRescource;
                }
            }
            
            int totalCostDiamond = resTotalDiamond + costDiamond;
            m_txtDoneValue.text = totalCostDiamond.ToString();
            
            var canUpGrade = _curBuildingModule.CanUpGrade();
            if (canUpGrade)
            {
                m_btnDone.interactable = true;
                ToolScriptExtend.SetGameObjectGrey(m_btnDone.transform, false);
                m_btnUpLevel.interactable = true;
                ToolScriptExtend.SetGameObjectGrey(m_btnUpLevel.transform, false);
            }
            else
            {
                bool isBuildDemandUnLock = _curBuildingModule.GetIsBuildingDemandUnLock();
                if (isBuildDemandUnLock)
                {
                    long diamondCount = GameEntry.LogicData.BagData.GetAmountById(itemid.itemid_6);
                    bool isEnough = diamondCount >= totalCostDiamond;
                    m_btnDone.interactable = isEnough;
                    ToolScriptExtend.SetGameObjectGrey(m_btnDone.transform, !isEnough);  
                }
                else
                {
                    m_btnDone.interactable = false;
                    ToolScriptExtend.SetGameObjectGrey(m_btnDone.transform, true);  
                }

                m_btnUpLevel.interactable = false;
                ToolScriptExtend.SetGameObjectGrey(m_btnUpLevel.transform, true);
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            _scrollViewCost.DestroyAllChild();
            nextConfig = null;
            m_DemandObjList.Clear();
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnDeatialClick()
        {

        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnDoneClick()
        {

        }

        private void OnBtnUpLevelClick()
        {
            //Debug.LogError(techGroup);
            //int techID = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel(techGroup, curLevel).id;
            List<Article.Article> articles = new List<Article.Article>();
            GameEntry.LogicData.TechData.StudyTech(techGroup,(uint)_curBuildingModule.BuildingId,articles,(mesg) =>
            {
                Close();
            });
        }
    }
}
