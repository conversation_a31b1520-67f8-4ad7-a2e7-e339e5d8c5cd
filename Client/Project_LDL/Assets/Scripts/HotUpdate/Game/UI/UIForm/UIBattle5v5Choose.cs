using System;
using System.Collections;
using System.Collections.Generic;
using Battle;
using DG.Tweening;
using Fight;
using Game.Hotfix.Config;
using GameFramework.Event;
using JetBrains.Annotations;
using Team;
using UnityEngine;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;
using Action = System.Action;

namespace Game.Hotfix
{
    public partial class UIBattle5v5Choose : UGuiFormEx
    {
        public int SelectType => selectType;
        private int selectType = -1;
        private List<Transform> itemList = new();

        private bool m_IsDebug;
        private UIBattle5v5ChooseDebug m_UIBattle5V5ChooseDebug;
        private EnumBattleSide m_CurOpSide = EnumBattleSide.Left;

        private Dictionary<EnumBattlePos, UIBattle5v5ChooseHUD> m_HudsList;

        private EnumBattle5v5Type m_CurBattleType;

        private BattleFiled m_BattleFiled;
        private Battle5v5Component m_Battle5V5Component;

        private bool m_TeamDataDirty = false;

        private EnumBattleRelation m_CurRelation = EnumBattleRelation.None;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            int childCount = m_transBtnList.childCount;
            var heroData = GameEntry.LogicData.HeroData;
            for (int i = 0; i < childCount; i++)
            {
                var trans = m_transBtnList.GetChild(i);

                if (i > 0)
                {
                    var icon = trans.Find("bg/icon").GetComponent<Image>();
                    var selecSp = trans.Find("selectBg/selecSp").GetComponent<Image>();
                    icon.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                    selecSp.SetImage(heroData.GetServicesImgPath((hero_services)i), true);
                }

                int index = i;
                var rectTrans = trans.GetComponent<RectTransform>();
                // rectTrans.sizeDelta = new Vector2(245, 91);

                var btn = trans.GetComponent<Button>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() => { OnSelectBtn(index); });
            }
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            m_TeamDataDirty = false;

            m_CurBattleType = GameEntry.LogicData.Battle5v5Data.CurBattleType;
            m_IsDebug = m_CurBattleType == EnumBattle5v5Type.Debug;

            InitDebug();

            InitTouch();

            m_Battle5V5Component = UnityGameFramework.Runtime.GameEntry.GetComponent<Battle5v5Component>();
            m_BattleFiled = m_Battle5V5Component.BattleFiled;
            m_BattleFiled.TeamCtrl.ShowGround = true;

            InitHud();

            if (selectType != 0)
            {
                OnSelectBtn(0);
            }
            else
            {
                OnUpdateInfo();
            }

            RefreshAirportAttr();

            InitUI();

            SetRelationIcon();
            
            m_BattleFiled.AddEventListener(BattleFiledEvent.OnHeroCreate, OnHeroCreate);
            m_BattleFiled.AddEventListener(BattleFiledEvent.OnHeroDelete, OnHeroDelete);
            GameEntry.Event.Subscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            m_BattleFiled.TeamCtrl.ShowGround = false;
            m_BattleFiled.RemoveEventListener(BattleFiledEvent.OnHeroCreate, OnHeroCreate);
            m_BattleFiled.RemoveEventListener(BattleFiledEvent.OnHeroDelete, OnHeroDelete);
            GameEntry.Event.Unsubscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            UnInitDebug();
            UnInitHud();
            UnInitTouch();
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void InitUI()
        {
            if (m_CurBattleType == EnumBattle5v5Type.Debug)
            {
                m_txtStageNum.text = "测试战斗";
            }
            else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                if (param != null)
                {
                    m_txtStageNum.text = ToolScriptExtend.GetLangFormat(70000001, param.DungeonId.ToString());
                }
            }

            UpdatePower(EnumBattleSide.Left);
            UpdatePower(EnumBattleSide.Right);
        }

        private void UpdatePower(EnumBattleSide side)
        {
            if (side == EnumBattleSide.Left)
            {
                double power = 0;
                if (m_CurBattleType == EnumBattle5v5Type.Debug)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }
                else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }

                m_txtLeftBattleValue.text = ToolScriptExtend.FormatNumberWithUnit(power);
            }
            else if (side == EnumBattleSide.Right)
            {
                double power = 0;
                if (m_CurBattleType == EnumBattle5v5Type.Debug)
                {
                    power = m_BattleFiled.TeamCtrl.GetPowerFromHeroModule(side);
                }
                else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
                {
                    var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                    if (param != null)
                    {
                        power = param.MonsterGroupTotalPower;
                    }
                }

                m_txtRightBattleValue.text = ToolScriptExtend.FormatNumberWithUnit(power);
            }
        }

        private void OnBtnPowerLeftClick()
        {
            m_goPower.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnPowerRightClick()
        {
        }

        private void OnBtnLeftRelationClick()
        {
            m_goRelation.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnRightRelationClick()
        {
        }

        private void OnBtnExitClick()
        {
            TrySyncTeam(() =>
            {
                var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
                if (tCurrentProcedure is Procedure5v5Battle procedure5V5Battle)
                {
                    procedure5V5Battle.GoBackToMain();
                }   
            });
        }

        private void OnBtnBattleClick()
        {
            if (m_CurBattleType == EnumBattle5v5Type.Debug)
            {
                int level = 1;
                int star = 1;
                int.TryParse(m_UIBattle5V5ChooseDebug.inputLevel.text, out level);
                int.TryParse(m_UIBattle5V5ChooseDebug.inputStar.text, out star);

                Battle5v5Data battle5v5data = GameEntry.LogicData.Battle5v5Data;

                var attacker = m_BattleFiled.TeamCtrl.GetDebugTeam(EnumBattleSide.Left);
                var defender = m_BattleFiled.TeamCtrl.GetDebugTeam(EnumBattleSide.Right);
                battle5v5data.BattleDebugBeginReq(attacker, defender, level, star, (battleDebugResp) =>
                {
                    if (battleDebugResp != null && !string.IsNullOrEmpty(battleDebugResp.ReportId))
                    {
                        battle5v5data.DoBattleRecordReadReq(battleDebugResp.ReportId, (battleRecordReadResp) =>
                        {
                            if (battleRecordReadResp != null)
                            {
                                Report message =
                                    Report.Descriptor.Parser.ParseFrom(battleRecordReadResp.Report) as Report;
                                if (m_BattleFiled.PlayRecord(message))
                                {
                                    Close();
                                }
                            }
                        });
                    }
                });
            }
            else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var dungeonId = 0;
                var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                if (param != null)
                {
                    dungeonId = param.DungeonId;
                }

                TrySyncTeam(() =>
                {
                    Battle5v5Data battle5v5data = GameEntry.LogicData.Battle5v5Data;
                    battle5v5data.DungeonFightBeginReq(dungeonId, (dungeonFightResp) =>
                    {
                        if (dungeonFightResp != null && !string.IsNullOrEmpty(dungeonFightResp.ReportId))
                        {
                            battle5v5data.DoBattleRecordReadReq(dungeonFightResp.ReportId, (battleRecordReadResp) =>
                            {
                                if (battleRecordReadResp != null)
                                {
                                    Report message =
                                        Report.Descriptor.Parser.ParseFrom(battleRecordReadResp.Report) as Report;
                                    if (m_BattleFiled.PlayRecord(message))
                                    {
                                        Close();
                                    }
                                }
                            });
                        }
                    });
                });
            }
        }

        private void OnBtnAirSupportClick()
        {
            m_goAirSupport.SetActive(true);
            m_btnMask.gameObject.SetActive(true);
        }

        private void OnBtnUnknowClick()
        {
        }

        private void OnBtnMaskClick()
        {
            m_goPower.SetActive(false);
            m_goRelation.SetActive(false);
            m_goAirSupport.SetActive(false);
            m_goPopupAirSupportAttr.SetActive(false);
            m_btnMaskAirSupport.gameObject.SetActive(false);
            m_btnMask.gameObject.SetActive(false);
        }

        private void OnBtnMaskAirSupportClick()
        {
            m_goPopupAirSupportAttr.SetActive(false);
            m_btnMaskAirSupport.gameObject.SetActive(false);
        }

        private void OnHeroUpdate(object sender, GameEventArgs e)
        {
            OnUpdateInfo();
        }

        private void OnSelectBtn(int index)
        {
            if (selectType == index)
                return;

            Transform trans = m_transBtnList.GetChild(index);
            if (trans != null)
            {
                var rectTrans = trans.GetComponent<RectTransform>();
                var bg = trans.Find("bg").gameObject;
                var selectBg = trans.Find("selectBg").gameObject;
                // rectTrans.sizeDelta = new Vector2(260, 91);
                bg.SetActive(false);
                selectBg.SetActive(true);
            }

            if (selectType > -1)
            {
                trans = m_transBtnList.GetChild(selectType);
                if (trans != null)
                {
                    var rectTrans = trans.GetComponent<RectTransform>();
                    var bg = trans.Find("bg").gameObject;
                    var selectBg = trans.Find("selectBg").gameObject;
                    // rectTrans.sizeDelta = new Vector2(245, 91);
                    bg.SetActive(true);
                    selectBg.SetActive(false);
                }
            }

            selectType = index;
            OnUpdateInfo();

            var rect = m_transBtnList.GetComponent<RectTransform>();
            LayoutRebuilder.ForceRebuildLayoutImmediate(rect);
        }

        private void OnUpdateInfo()
        {
            List<HeroModule> heroList = GetHeroList();

            Transform rootTrans = m_scrollview.content;
            ToolScriptExtend.RecycleOrCreate(m_transHeroItem.gameObject, rootTrans, heroList.Count);
            for (int i = 0; i < heroList.Count; i++)
            {
                var item = rootTrans.GetChild(i);
                OnUpdateItem(item, heroList, i);
            }
        }

        private List<HeroModule> GetHeroList()
        {
            List<HeroModule> heroList;
            if (m_CurBattleType == EnumBattle5v5Type.Debug)
            {
                heroList = GameEntry.LogicData.HeroData.GetHeroModuleList((hero_services)selectType, true);
            }
            else
            {
                heroList = GameEntry.LogicData.HeroData.GetHeroModuleList((hero_services)selectType, true,
                    (heroModule) => heroModule.IsActive);
            }

            return heroList;
        }

        private void OnUpdateItem(Transform item, List<HeroModule> heroList, int index)
        {
            var heroVo = heroList[index];
            OnUpdateItem(item, heroVo);
        }

        private void OnUpdateItem(Transform item, HeroModule heroVo)
        {
            var heroItem = item.GetComponent<UIHeroItem>();
            heroItem.Refresh(heroVo);

            heroItem.SetSelected(IsInBattle((int)heroVo.id, m_CurOpSide));

            heroItem.RemoveAllClickListeners();
            heroItem.AddClickListener(() =>
            {
                var teamCtrl = m_BattleFiled.TeamCtrl;
                int heroId = (int)heroVo.id;
                if (teamCtrl.IsInBattle(heroId, m_CurOpSide))
                {
                    teamCtrl.RemoveHero(heroId, m_CurOpSide);
                    OnUpdateItem(item, heroVo);
                }
                else
                {
                    var emptyPos = teamCtrl.GetEmptyPosition(m_CurOpSide);
                    if (emptyPos != null)
                    {
                        teamCtrl.CreateHero(emptyPos.Value, heroId);
                        OnUpdateItem(item, heroVo);
                    }
                    else
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = "队伍人数已满",
                        });
                    }
                }
            });
        }

        private bool IsInBattle(int heroId, EnumBattleSide side)
        {
            return m_BattleFiled.TeamCtrl.IsInBattle(heroId, side);
        }

        private void RefreshAirportAttr()
        {
            foreach (Transform item in m_goAirSupportAttr.transform)
            {
                UIButton btn = item.GetComponent<UIButton>();
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    btn.AnchorUIToButton(m_goPopupAirSupportAttr.transform, new Vector2(0, 50f));
                    m_goPopupAirSupportAttr.SetActive(true);
                    m_btnMaskAirSupport.gameObject.SetActive(true);
                });
            }
        }

        private void TrySyncTeam(Action callBack)
        {
            if (!m_TeamDataDirty)
            {
                callBack?.Invoke();
                return;
            }

            m_TeamDataDirty = false;

            if (m_CurBattleType == EnumBattle5v5Type.Debug)
            {
                callBack?.Invoke();
                return;
            }
            else if (m_CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var teamData = m_BattleFiled.TeamCtrl.GetTeamHeroList(EnumBattleSide.Left);

                FormationTeam formationTeam = new FormationTeam();
                formationTeam.TeamType = TeamType.Dungeon;
                formationTeam.Heroes.AddRange(teamData);

                GameEntry.LogicData.TeamData.TeamModify(formationTeam, () => { callBack?.Invoke(); });
            }

            callBack?.Invoke();
        }


        #region touch 相关逻辑

        [CanBeNull] private BattleHero m_OpDragBattleHero;
        private EnumBattlePos? m_OpCurPos; //当前手指所在区域

        private void InitTouch()
        {
            var controller = GameEntry.Input;
            controller.tapped += OnTapped;
            controller.dragged += OnDragged;
            controller.released += OnReleased;
            controller.pressed += OnPressed;
        }

        private void UnInitTouch()
        {
            var controller = GameEntry.Input;
            controller.tapped -= OnTapped;
            controller.dragged -= OnDragged;
            controller.released -= OnReleased;
            controller.pressed -= OnPressed;
        }

        private void OnTapped(PointerActionInfo pointer)
        {
            if (pointer.startedOverUI) return;
            Vector3 worldPos =
                MapGridUtils.GetWorldPositionByScreenPos(pointer.currentPosition, GameEntry.Camera.BattleCamera);
            EnumBattlePos? pos = m_Battle5V5Component.IsInPosBounds(worldPos, m_CurOpSide);
            if (pos != null)
            {
                var hero = m_BattleFiled.TeamCtrl.GetBattleHero(pos.Value);
                if (hero != null)
                {
                    m_BattleFiled.TeamCtrl.RemoveHero(hero);
                    OnUpdateInfo();
                }
            }
        }

        private void OnDragged(PointerActionInfo pointer)
        {
            if (m_OpDragBattleHero == null)
                return;

            Vector3 worldPos =
                MapGridUtils.GetWorldPositionByScreenPos(pointer.currentPosition, GameEntry.Camera.BattleCamera);
            m_OpDragBattleHero.SetPosition(worldPos);
            EnumBattlePos? pos = m_Battle5V5Component.IsInPosBounds(worldPos, m_CurOpSide);
            if (pos == null)
            {
                //没有在任何区域内部
                MoveBackCache();
            }
            else
            {
                //移动到了新区域
                if (m_OpDragBattleHero.BattlePos == pos.Value)
                {
                    //新区域是我自己
                    MoveBackCache();
                }
                else
                {
                    if (m_OpCurPos == pos.Value)
                    {
                        //已经互换 什么也不做
                    }
                    else
                    {
                        MoveAndCache(pos.Value);
                    }
                }
            }
        }

        private void OnReleased(PointerActionInfo pointer)
        {
            if (m_OpCurPos != null && m_OpDragBattleHero != null)
            {
                var teamCtrl = m_BattleFiled.TeamCtrl;
                int heroIdA = m_OpDragBattleHero.HeroId;
                EnumBattlePos posA = m_OpDragBattleHero.BattlePos;
                int? heroIdB = null;
                EnumBattlePos posB = m_OpCurPos.Value;

                //删除原有英雄
                if (m_OpDragBattleHero != null)
                    teamCtrl.RemoveHero(m_OpDragBattleHero);
                //删除目标位置英雄
                var heroTarget = teamCtrl.GetBattleHero(m_OpCurPos.Value);
                if (heroTarget != null)
                {
                    heroIdB = heroTarget.HeroId;
                    teamCtrl.RemoveHero(heroTarget);
                }

                //创建新英雄
                teamCtrl.CreateHero(posB, heroIdA);
                if (heroIdB != null)
                    teamCtrl.CreateHero(posA, heroIdB.Value);

                m_OpCurPos = null;
                m_OpDragBattleHero = null;
            }
            else
            {
                if (m_OpDragBattleHero != null)
                    m_OpDragBattleHero.MoveBack();
                m_OpDragBattleHero = null;

                MoveBackCache();
            }
        }

        private void OnPressed(PointerActionInfo pointer)
        {
            Vector3 worldPos =
                MapGridUtils.GetWorldPositionByScreenPos(pointer.currentPosition, GameEntry.Camera.BattleCamera);
            EnumBattlePos? pos = m_Battle5V5Component.IsInPosBounds(worldPos, m_CurOpSide);

            if (pos != null)
            {
                var hero = m_BattleFiled.TeamCtrl.GetBattleHero(pos.Value);
                if (hero != null)
                {
                    m_OpDragBattleHero = hero;
                }
            }
        }

        private void MoveBackCache()
        {
            if (m_OpCurPos != null)
            {
                //将之前的英雄归位
                var tempHero = m_BattleFiled.TeamCtrl.GetBattleHero(m_OpCurPos.Value);
                if (tempHero != null)
                    tempHero.MoveBack();
                m_OpCurPos = null;
            }
        }

        private void MoveAndCache(EnumBattlePos pos)
        {
            if (m_OpDragBattleHero == null)
                return;

            var hero = m_BattleFiled.TeamCtrl.GetBattleHero(pos);
            if (hero != null)
            {
                hero.MoveTo(m_OpDragBattleHero.BattlePos);
            }

            m_OpCurPos = pos;
        }

        #endregion

        #region 战斗单位头部Hud

        public void InitHud()
        {
            m_HudsList = new Dictionary<EnumBattlePos, UIBattle5v5ChooseHUD>();

            foreach (EnumBattlePos pos in Enum.GetValues(typeof(EnumBattlePos)))
            {
                var hud = CreateHud(pos);
                var hero = m_BattleFiled.TeamCtrl.GetBattleHero(pos);
                if (hero != null)
                    hud.Show();
                else
                    hud.Hide();
            }
        }

        private void OnHeroDelete(object obj)
        {
            if (obj is EnumBattlePos pos)
            {
                if (m_HudsList.TryGetValue(pos, out UIBattle5v5ChooseHUD choose))
                {
                    choose.Hide();
                }
            }

            UpdatePower(EnumBattleSide.Left);
            UpdatePower(EnumBattleSide.Right);

            m_TeamDataDirty = true;
            TryResetRelationUI();
        }

        private void OnHeroCreate(object obj)
        {
            if (obj is EnumBattlePos pos)
            {
                if (m_HudsList.TryGetValue(pos, out UIBattle5v5ChooseHUD choose))
                {
                    choose.Show();
                }
            }

            UpdatePower(EnumBattleSide.Left);
            UpdatePower(EnumBattleSide.Right);

            m_TeamDataDirty = true;
            TryResetRelationUI();
        }

        public void UnInitHud()
        {
            foreach (var item in m_HudsList)
            {
                Destroy(item.Value.gameObject);
            }

            m_HudsList.Clear();
            m_HudsList = null;
        }

        private UIBattle5v5ChooseHUD CreateHud(EnumBattlePos pos)
        {
            var newItem = Instantiate(m_transHudItem);
            newItem.transform.parent = m_transHudParent;
            newItem.transform.localPosition = Vector3.one;
            newItem.transform.localScale = Vector3.one;

            var hud = newItem.gameObject.GetOrAddComponent<UIBattle5v5ChooseHUD>();
            hud.Init(m_BattleFiled, pos);
            m_HudsList.Add(pos, hud);
            return hud;
        }

        #endregion

        #region debug 相关

        private void InitDebug()
        {
            m_UIBattle5V5ChooseDebug = m_transDebug.GetComponent<UIBattle5v5ChooseDebug>();
            m_transDebug.gameObject.SetActive(m_IsDebug);
            if (!m_IsDebug)
                return;
            m_UIBattle5V5ChooseDebug.toggle.onValueChanged.AddListener(OnDebugToggleValueChange);
        }

        private void OnDebugToggleValueChange(bool value)
        {
            if (value)
                m_CurOpSide = EnumBattleSide.Right;
            else
                m_CurOpSide = EnumBattleSide.Left;
            OnUpdateInfo();
        }

        private void UnInitDebug()
        {
            if (!m_IsDebug)
                return;
            m_UIBattle5V5ChooseDebug.toggle.onValueChanged.RemoveListener(OnDebugToggleValueChange);
        }

        #endregion

        #region 羁绊相关

        private void TryResetRelationUI()
        {
            var list = m_BattleFiled.TeamCtrl.GetTeamArmyTypeList();
            EnumBattleRelation relation = ToolScriptExtend.GetRelation(list);
            if (m_CurRelation!=relation)
            {
                m_CurRelation = relation;
                SetRelationIcon();
                var relationItem = m_goRelation.GetComponent<UIRelationItem>();
                relationItem?.SetRelation(list, relation);
            }
        }

        private void SetRelationIcon()
        {
            m_btnLeftRelation.transform.DOPunchScale(Vector3.one * 0.5f, 0.3f);
            
            if (m_CurRelation == EnumBattleRelation.None)
            {
                m_imgLeftRelationA.gameObject.SetActive(false);
                m_imgLeftRelationB.gameObject.SetActive(false);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            } 
            else if (m_CurRelation == EnumBattleRelation.Relation3_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(false);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }
            else if (m_CurRelation == EnumBattleRelation.Relation3_2)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(true);
                
                m_imgLeftRelationB.SetImageGray(true);
                m_imgLeftRelationC.SetImageGray(true);
            }else if (m_CurRelation == EnumBattleRelation.Relation4_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(false);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }else if (m_CurRelation == EnumBattleRelation.Relation5_0)
            {
                m_imgLeftRelationA.gameObject.SetActive(true);
                m_imgLeftRelationB.gameObject.SetActive(true);
                m_imgLeftRelationC.gameObject.SetActive(true);
                
                m_imgLeftRelationB.SetImageGray(false);
                m_imgLeftRelationC.SetImageGray(false);
            }
        }
        
        #endregion
    }
}