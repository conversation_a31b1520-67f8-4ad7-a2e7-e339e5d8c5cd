using System;
using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Game.Hotfix.Config;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using Sirenix.Utilities;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionEditChangeForm : UGuiFormEx
    {
        private int showType;
        private int minVal;
        private int maxVal;
        private int costId;
        private int costNum;
        private bool btnState;

        private float deltaTime = 0;
        private string curStr;
        private string nextStr;
        private string checkStr;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_inputTxt.onValueChanged.AddListener(OnUpdateShow);
            m_inputTxt.onValidateInput += (text, charIndex, addedChar) =>
            {
                var showStr = text + addedChar;
                OnUpdateShow(showStr);
                return addedChar;
            };
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            showType = (int)userData;
            if (showType == 1)
            {
                var dic = GameEntry.LogicData.UnionData.GetUnionConst(3);
                minVal = dic != null ? int.Parse(dic[0]) : 0;
                maxVal = dic != null ? int.Parse(dic[1]) : 0;

                dic = GameEntry.LogicData.UnionData.GetUnionConst(16);
                costId = dic != null ? int.Parse(dic[0]) : 0;
                costNum = dic != null ? int.Parse(dic[1]) : 0;

                m_txtTitle.text = ToolScriptExtend.GetLang(80000085);
                m_txtName.text = ToolScriptExtend.GetLang(80000085);
                m_inputTxt.contentType = InputField.ContentType.Standard;
                m_inputTxt.characterLimit = maxVal;
            }
            else if (showType == 2)
            {
                var dic = GameEntry.LogicData.UnionData.GetUnionConst(4);
                minVal = dic != null ? int.Parse(dic[0]) : 0;
                maxVal = dic != null ? int.Parse(dic[1]) : 0;

                dic = GameEntry.LogicData.UnionData.GetUnionConst(17);
                costId = dic != null ? int.Parse(dic[0]) : 0;
                costNum = dic != null ? int.Parse(dic[1]) : 0;

                m_txtTitle.text = ToolScriptExtend.GetLang(80000088);
                m_txtName.text = ToolScriptExtend.GetLang(80000088);
                m_inputTxt.contentType = InputField.ContentType.Standard;
                m_inputTxt.characterLimit = maxVal;
            }
            else if (showType == 3)
            {
                var dic = GameEntry.LogicData.UnionData.GetUnionConst(5);
                minVal = 0;
                maxVal = dic != null ? int.Parse(dic[0]) : 0;
                costId = 0;
                costNum = 0;

                m_txtTitle.text = ToolScriptExtend.GetLang(80000126);
                m_txtName.text = ToolScriptExtend.GetLang(3000011);
                m_txtTip.text = ToolScriptExtend.GetLang(80000128);
                m_inputTxt.contentType = InputField.ContentType.IntegerNumber;
                m_inputTxt.characterLimit = ToolScriptExtend.GetStrLength(maxVal + "");
            }
            else if (showType == 4)
            {
                var dic = GameEntry.LogicData.UnionData.GetUnionConst(6);
                minVal = 0;
                maxVal = dic != null ? int.Parse(dic[0]) : 0;
                costId = 0;
                costNum = 0;

                m_txtTitle.text = ToolScriptExtend.GetLang(80000126);
                m_txtName.text = ToolScriptExtend.GetLang(1096);
                m_txtTip.text = ToolScriptExtend.GetLang(80000128);
                m_inputTxt.contentType = InputField.ContentType.IntegerNumber;
                m_inputTxt.characterLimit = ToolScriptExtend.GetStrLength(maxVal + "");
            }

            var isFree = costNum <= 0;
            if (!isFree)
            {
                var hasNum = GameEntry.LogicData.BagData.GetAmountById((itemid)costId);
                var colorStr = hasNum >= costNum ? "00ff00" : "FF1717";
                m_txtCost.text = $"<color=#{colorStr}>{costNum}</color>";
                m_imgCost.SetImage(ToolScriptExtend.GetItemIcon(costId));
            }

            var btnBg = m_btnSure.GetComponent<UIImage>();
            btnBg.SetImage(isFree ? "Sprite/ui_public/button2.png" : "Sprite/ui_public/button4.png");
            m_txtCost.gameObject.SetActive(!isFree);
            m_txtSure.gameObject.SetActive(isFree);

            curStr = "curStr";
            nextStr = "";
            checkStr = "";

            m_txtNum.text = "";
            m_inputTxt.text = "";
            OnUpdateShow("");
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            deltaTime += elapseSeconds;
            if (deltaTime >= 0.2f)
            {
                deltaTime = 0;

                if (!nextStr.IsNullOrWhitespace())
                {
                    OnUpdateShow(nextStr);
                    nextStr = "";
                }
            }
        }

        private void OnUpdateShow(string showStr)
        {
            if (curStr == showStr) return;
            curStr = showStr;

            var length = ToolScriptExtend.GetStrLength(showStr);
            var isMeet = false;
            if (showType == 1)
            {
                isMeet = length >= minVal && length <= maxVal;
                if (isMeet)
                {
                    GameEntry.LogicData.UnionData.OnReqUnionCheckName(showStr, 1, (resp) =>
                    {
                        var state = resp.Code; // 0-正常，1-重复，2-非法
                        if (state == 0)
                        {
                            checkStr = "";
                        }
                        else
                        {
                            if (state == 1) { checkStr = ToolScriptExtend.GetLang(80000090); }
                            else { checkStr = ToolScriptExtend.GetLang(80000091); }
                        }
                        m_txtTip.text = checkStr;
                        OnUpdateBtnShow(isMeet);
                    });
                }
                else
                {
                    checkStr = ToolScriptExtend.GetLang(80000087);
                    m_txtTip.text = checkStr;
                }
                m_txtNum.text = $"{length}/{maxVal}";
            }
            else if (showType == 2)
            {
                var isMatch = Regex.IsMatch(showStr, @"^[a-zA-Z0-9]*$");
                isMeet = length >= minVal && length <= maxVal && isMatch;
                if (isMeet)
                {
                    GameEntry.LogicData.UnionData.OnReqUnionCheckName(showStr, 2, (resp) =>
                    {
                        var state = resp.Code; // 0-正常，1-重复，2-非法
                        if (state == 0)
                        {
                            checkStr = "";
                        }
                        else
                        {
                            if (state == 1) { checkStr = ToolScriptExtend.GetLang(80000090); }
                            else { checkStr = ToolScriptExtend.GetLang(80000091); }
                        }
                        m_txtTip.text = checkStr;
                        OnUpdateBtnShow(isMeet);
                    });
                }
                else
                {
                    checkStr = ToolScriptExtend.GetLang(80000089);
                    m_txtTip.text = checkStr;
                }
            }
            else
            {
                if (long.TryParse(showStr, out long val))
                {
                    isMeet = length > 0 && val <= maxVal;
                    if (val > maxVal)
                    {
                        nextStr = maxVal + "";
                        m_inputTxt.text = nextStr;
                    }
                }
                else { isMeet = false; }
            }
            OnUpdateBtnShow(isMeet);
        }

        private void OnUpdateBtnShow(bool isMeet)
        {
            btnState = isMeet && checkStr.IsNullOrWhitespace();
            m_btnSure.interactable = btnState;
            ToolScriptExtend.SetGameObjectGrey(m_btnSure.transform, !btnState);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnSureClick()
        {
            var showStr = m_inputTxt.text;
            if (showType == 1)
            {
                if (showStr.IsNullOrWhitespace())
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = ToolScriptExtend.GetLang(1108),
                    });
                    return;
                }

                if (!checkStr.IsNullOrWhitespace())
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = checkStr,
                    });
                    return;
                }

                var hasNum = GameEntry.LogicData.BagData.GetAmountById((itemid)costId);
                if (hasNum < costNum)
                {
                    ItemModule itemModule = new((itemid)costId);
                    GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(costNum));
                    return;
                }

                GameEntry.LogicData.UnionData.OnReqUnionChangeName(showStr, (nameStr) =>
                {
                    ColorLog.Pink($"OnReqUnionChangeName Name:{nameStr}");
                    OnCloseFun();
                });
            }
            else if (showType == 2)
            {
                if (!checkStr.IsNullOrWhitespace())
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = checkStr,
                    });
                    return;
                }

                var hasNum = GameEntry.LogicData.BagData.GetAmountById((itemid)costId);
                if (hasNum < costNum)
                {
                    ItemModule itemModule = new((itemid)costId);
                    GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(costNum));
                    return;
                }
                GameEntry.LogicData.UnionData.OnReqUnionChangeShortName(showStr, (nameStr) =>
                {
                    ColorLog.Pink($"OnReqUnionChangeName ShortName:{nameStr}");
                    OnCloseFun();
                });
            }
            else
            {
                var val = Convert.ToInt32(showStr);
                var cond = new Union.UnionJoinCondition
                {
                    Type = showType == 3 ? 1 : 2,
                    Value = val
                };
                GameEntry.LogicData.UnionData.OnReqUnionChangeJoinCondition(cond, (condition) =>
                {
                    ColorLog.Pink($"OnReqUnionChangeName Type:{condition.Type} Value:{condition.Value}");
                    OnCloseFun();
                });
            }
        }

        private void OnCloseFun()
        {
            Close();
            GameEntry.UI.RefreshUIForm(EnumUIForm.UIUnionEditForm, new UnionParams
            {
                ShowType = 1
            });
        }
    }
}
