using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIUnionInfoForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnTranslate;
        [SerializeField] private UIButton m_btnMember;
        [SerializeField] private UIButton m_btnComment;
        [SerializeField] private UIButton m_btnContact;

        [SerializeField] private UIText m_txtName;
        [SerializeField] private UIText m_txtPower;
        [SerializeField] private UIText m_txtLeader;
        [SerializeField] private UIText m_txtLevel;
        [SerializeField] private UIText m_txtMember;
        [SerializeField] private UIText m_txtLang;
        [SerializeField] private UIText m_txtSlogan;

        [SerializeField] private UIImage m_imgFlag;

        [SerializeField] private ScrollRect m_scrollview;

        void InitBind()
        {
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnTranslate.onClick.AddListener(OnBtnTranslateClick);
            m_btnMember.onClick.AddListener(OnBtnMemberClick);
            m_btnComment.onClick.AddListener(OnBtnCommentClick);
            m_btnContact.onClick.AddListener(OnBtnContactClick);
        }
    }
}
