using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBattle5V5DungeonVictory : UGuiFormEx
    {
        private BattleFiled m_BattleFiled;
        private Battle5v5Component m_Battle5V5Component;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            HideDefault();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (GameEntry.LogicData.Battle5v5Data.CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                if (param != null)
                {
                    GameEntry.LogicData.Battle5v5Data.DungeonFightSettle(param.DungeonId);
                    m_txtCheckPoint.text = "关卡 " + param.DungeonId;
                }
            }
            
            m_Battle5V5Component = UnityGameFramework.Runtime.GameEntry.GetComponent<Battle5v5Component>();
            m_BattleFiled = m_Battle5V5Component.BattleFiled;

            ColorLog.Pink("m_Report.ReportId:" + m_Report.ReportId);
            
            RefreshHero();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {
            Close();
            GameEntry.LogicData.Battle5v5Data.GoBackMainCity();
        }

        private void OnBtnContinueClick()
        {
            Close();
            GameEntry.LogicData.Battle5v5Data.GoBackMainCity();
        }

        private void OnBtnDetailClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBattle5V5DungeonStatsForm, m_BattleFiled.RecordCtrl.Report);
        }

        void HideDefault()
        {
            m_transHeroItem.gameObject.SetActive(false);
        }

        void RefreshHero()
        {
            var attacker = m_BattleFiled.RecordCtrl.Report.Attacker;

            foreach (Transform item in m_transContentHero)
            {
                item.gameObject.SetActive(false);
            }

            for (int i = 0; i < attacker.Heroes.Count; i++)
            {
                var hero = attacker.Heroes[i];
                HeroModule heroModule = new((itemid)hero.Code)
                {
                    level = (int)hero.Level,
                    starLv = (int)hero.StarStage,
                    IsActive = true,
                };

                UIHeroItem item;
                if (i < m_transContentHero.childCount)
                {
                    item = m_transContentHero.GetChild(i).GetComponent<UIHeroItem>();
                }
                else
                {
                    Transform obj = Instantiate(m_transHeroItem, m_transContentHero);
                    item = obj.GetComponent<UIHeroItem>();
                }

                item.Refresh(heroModule);
                item.gameObject.SetActive(true);
            }
        }
    }
}
