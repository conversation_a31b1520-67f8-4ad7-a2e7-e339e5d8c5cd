using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Serialization;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIWorldMapGatherMenu : UIWorldMapMenuBase
    {
        [SerializeField] private WorldMapMenuItem worldMapMenuItem;

        private EL_WorldMapElement m_EntityLogic;

        private map_element m_Cfg;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is EL_WorldMapElement worldMapElement)
                m_EntityLogic = worldMapElement;

            ResetUI();
        }

        protected override void OnRefocus(object userData)
        {
            base.OnRefresh(userData);
            if (userData is EL_WorldMapElement worldMapElement)
            {
                if (m_EntityLogic == worldMapElement) return;
                m_EntityLogic = worldMapElement;
            }

            ResetUI();
        }

        private void ResetUI()
        {
            List<int> list = new List<int>();

            m_Cfg = m_EntityLogic.ElementCfg;

            m_txtTitle.text = m_Cfg.level + "_" + ToolScriptExtend.GetLang(m_Cfg.name);

            bool isWorking = IsWorking();

            m_goWorking.SetActive(isWorking);
            m_goNotWorking.SetActive(!isWorking);

            if (isWorking)
            {
                m_txtWorkerName.text = "采集者：XXX";
                if (IsMe())
                {
                    list.Add(2021);
                    list.Add(2022);
                }
                else
                {
                    if (IsFriend())
                    {
                    }
                    else
                    {
                        list.Add(2009);
                        list.Add(2007);
                    }
                }
            }
            else
            {
                list.Add(2011);

                m_txtName.text = "采集者：无";
                m_txtProgress.text = GetResourceMax() + "/" + GetResourceMax();

                var iconPath = GetItemIcon();
                if (!string.IsNullOrEmpty(iconPath))
                {
                    m_imgIcon.SetImage(iconPath);
                }
            }

            ResetMenuUI(m_EntityLogic, worldMapMenuItem, list, OnMenuBtnClick, m_rectMove, m_rectMenu);
        }

        private bool IsWorking()
        {
            return false;
        }

        private bool IsMe()
        {
            return false;
        }

        private bool IsFriend()
        {
            return false;
        }

        protected void OnMenuBtnClick(build_menubutton cfg)
        {
            // GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            // {
            //     Content = "功能暂未开放",
            // });
            GameEntry.UI.OpenUIForm(EnumUIForm.UIWorldMapTeamSelectForm);
            Close();
        }

        private string GetItemIcon()
        {
            if (m_Cfg.resource_quantity != null && m_Cfg.resource_quantity.Count >= 1)
            {
                var item = m_Cfg.resource_quantity[0];
                var itemConfig = GameEntry.LDLTable.GetTableById<item_config>(item.item_id);
                if (itemConfig != null)
                {
                    return itemConfig.icon;
                }
            }

            return string.Empty;
        }

        private long GetResourceMax()
        {
            if (m_Cfg.resource_quantity != null && m_Cfg.resource_quantity.Count >= 1)
            {
                var item = m_Cfg.resource_quantity[0];
                return item.num;
            }

            return 0;
        }

        private long GetResourceMaxNow()
        {
            return GetResourceMax(); //TODO
        }

        private void OnBtnShareClick()
        {
        }

        private void OnBtnFavoriteClick()
        {
        }
    }
}