using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UISwitchAccountForm : UGuiFormEx
    {
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnMaskClick()
        {
            this.Close();
        }

        private void OnBtnCloseClick()
        {
            this.Close();
        }

        private void OnBtnMailClick()
        {

        }

        private void OnBtnGoogleClick()
        {

        }
    }
}
