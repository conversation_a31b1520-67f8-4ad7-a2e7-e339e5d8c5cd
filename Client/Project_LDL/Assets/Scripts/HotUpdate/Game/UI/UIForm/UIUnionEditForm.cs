using System.Collections;
using System.Collections.Generic;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionEditForm : UGuiFormEx
    {
        private bool isAutoClean;
        private bool isUnionChange;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_togClean.onValueChanged.AddListener((isOn) =>
            {
                if (isOn == isAutoClean) return;

                GameEntry.LogicData.UnionData.OnReqUnionCleanInactive(isOn ? 1 : 0, (state) =>
                {
                    ColorLog.Pink($"OnReqUnionCleanInactive state:{state}");
                    isAutoClean = state;
                    OnUpdateInfo();
                });
            });
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            GameEntry.LogicData.UnionData.OnReqUnionGetCleanSwitch((state) =>
            {
                ColorLog.Pink($"OnReqUnionGetCleanSwitch state:{state}");
                isAutoClean = state;
                isUnionChange = false;
                OnUpdateInfo();
            });
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);

            if (isUnionChange)
            {
                isUnionChange = false;
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIUnionForm, 1);
            }
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            var param = (UnionParams)userData;
            if (param.ShowType == 1)
            {
                isUnionChange = true;
                OnUpdateInfo();
            }
            else if (param.ShowType == 2)
            {
                var langType = param.IntValue;
                GameEntry.LogicData.UnionData.OnReqUnionChangeLang(langType, (lang)=>
                {
                    ColorLog.Pink($"OnReqUnionChangeLang lang:{lang}");
                    isUnionChange = true;
                    OnUpdateInfo();
                });
            }
        }

        private void OnUpdateInfo()
        {
            var unionData = GameEntry.LogicData.UnionData;
            m_imgFlag.SetImage(unionData.GetFlagImgPath(unionData.Flag));
            m_txtName.text = unionData.Name;
            m_txtShortName.text = unionData.ShortName;
            m_txtLang.text = ToolScriptExtend.GetLangTypeName(unionData.Lang);
            m_txtPower.text = unionData.JoinPowerLimit + "";
            m_txtLevel.text = unionData.JoinLevelLimit + "";
            m_togClean.isOn = isAutoClean;
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnChooseClick()
        {
            // 打开旗帜选择界面
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionFlagForm, GameEntry.LogicData.UnionData.Flag);
        }

        private void OnBtnNameClick()
        {
            // 打开名字修改界面
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionEditChangeForm, 1);
        }

        private void OnBtnShortNameClick()
        {
            // 打开简称修改界面
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionEditChangeForm, 2);
        }

        private void OnBtnLangClick()
        {
            // 打开语言修改界面
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionLanguageForm, new UnionParams
            {
                ShowType = 2,
                IntValue = GameEntry.LogicData.UnionData.Lang
            });
        }

        private void OnBtnPowerClick()
        {
            // 打开战力限制修改界面
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionEditChangeForm, 3);
        }

        private void OnBtnLevelClick()
        {
            // 打开等级限制修改界面
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionEditChangeForm, 4);
        }
    }
}
