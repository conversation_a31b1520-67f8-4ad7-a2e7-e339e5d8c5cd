using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Serialization;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIWorldMapZombiesMenu : UIWorldMapMenuBase
    {
        [SerializeField] private WorldMapMenuItem worldMapMenuItem;

        private EL_WorldMapElement m_EntityLogic;

        private map_element m_Cfg;


        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is EL_WorldMapElement worldMapElement)
                m_EntityLogic = worldMapElement;

            ResetUI();
        }

        protected override void OnRefocus(object userData)
        {
            base.OnRefresh(userData);
            if (userData is EL_WorldMapElement worldMapElement)
            {
                if (m_EntityLogic == worldMapElement) return;
                m_EntityLogic = worldMapElement;
            }

            ResetUI();
        }

        private void ResetUI()
        {
            List<int> menuList = new List<int>();

            m_Cfg = m_EntityLogic.ElementCfg;

            var level = m_Cfg.level;
            var curLevel = GetCurLevel();
            m_txtName.text = ToolScriptExtend.GetLang(m_Cfg.name);
            m_txtLevel.text = "等级:" + level;

            var worldMapData = GameEntry.LogicData.WorldMapData;
            m_txtStamina.text = worldMapData.GetMapSettingStamina((WorldMapElementType)m_Cfg.big_type, (int)m_Cfg.small_type).ToString();
            m_txtDes.text = worldMapData.GetMapSettingDes((WorldMapElementType)m_Cfg.big_type, (int)m_Cfg.small_type);

            bool canKill = level <= curLevel + 1;
            bool hasKill = level <= curLevel;
            if (canKill)
            {
                //可以击杀过
                if (hasKill)
                {
                    //击杀过
                }
                else
                {
                    //没有击杀过
                }

                m_txtSuggestDesc.text = "推荐战力:";//+m_Cfg.recommend_power;
                if (m_Cfg.big_type == (map_element_big_type)WorldMapElementType.DoomElite)
                {
                    menuList.Add(2008);
                }
                else
                {
                    menuList.Add(2007);    
                }
                
            }
            else
            {
                m_txtSuggestDesc.text = "请先击杀" + (curLevel + 1) + "的怪物";
                menuList.Add(2010);
            }

            ResetDesMode(false);

            if (m_goFirst.activeSelf)
            {
                UpdateItem(m_goReward, m_transFirstRewards, m_Cfg.reward_frist);
            }

            if (m_goRewardFix.activeSelf)
            {
                UpdateItem(m_goReward, m_transFixRewards, m_Cfg.reward_fixed);
            }

            if (m_goRewardRandom.activeSelf)
            {
                UpdateItem(m_goReward, m_transRandomRewards, m_Cfg.reward_random);
            }


            ResetMenuUI(m_EntityLogic, worldMapMenuItem, menuList, OnMenuBtnClick, m_rectMove, m_rectMenu);
        }

        protected void OnMenuBtnClick(build_menubutton cfg)
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = "功能暂未开放",
            });
            Close();
        }


        private void UpdateItem(GameObject item, Transform rewardRoot, List<reward> rewards)
        {
            ToolScriptExtend.RecycleOrCreate(item, rewardRoot, rewards.Count);
            for (int i = 0; i < rewards.Count; i++)
            {
                var info = rewards[i];
                var rewardChild = rewardRoot.GetChild(i);
                var node = rewardChild.Find("node");
                node.localScale = Vector3.one * 0.55f;
                if (node.childCount == 0)
                {
                    BagManager.CreatItem(node, info.item_id, (int)info.num, (module) =>
                    {
                        module.SetClick(module.OpenTips);
                        ToolScriptExtend.SetItemObjTxtScale(module.gameObject, 1.3f);
                    });
                }
                else
                {
                    ToolScriptExtend.SetItemObjInfo(node, node.GetChild(0).gameObject, info.item_id, (int)info.num);
                }
            }
        }

        private int GetCurLevel()
        {
            return 1;
        }

        private void OnBtnShareClick()
        {
        }

        private void OnBtnFavoriteClick()
        {
        }

        private void OnBtnDesClick()
        {
            ResetDesMode(true);
        }

        private void OnBtnDesBackClick()
        {
            ResetDesMode(false);
        }

        private void ResetDesMode(bool desMode)
        {
            m_btnDes.gameObject.SetActive(!desMode);
            m_btnDesBack.gameObject.SetActive(desMode);
            m_goDes.SetActive(desMode);
            m_goBottomInfo.SetActive(!desMode);
            
            if (desMode)
            {
                m_goFirst.SetActive(false);
                m_goRewardFix.SetActive(false);
                m_goRewardRandom.SetActive(false);
            }
            else
            {
                var level = m_Cfg.level;
                var curLevel = GetCurLevel();
                bool canKill = level <= curLevel + 1;
                bool hasKill = level <= curLevel;
                if (canKill)
                {
                    //可以击杀过
                    if (hasKill)
                    {
                        //击杀过
                        m_goFirst.SetActive(false);
                        m_goRewardFix.SetActive(m_Cfg.reward_fixed.Count > 0);
                        m_goRewardRandom.SetActive(m_Cfg.reward_random.Count > 0);
                    }
                    else
                    {
                        //没有击杀过
                        m_goFirst.SetActive(m_Cfg.reward_frist.Count > 0);
                        m_goRewardFix.SetActive(m_Cfg.reward_fixed.Count > 0);
                        m_goRewardRandom.SetActive(false);
                    }
                }
                else
                {
                    m_goFirst.SetActive(m_Cfg.reward_frist.Count > 0);
                    m_goRewardFix.SetActive(m_Cfg.reward_fixed.Count > 0);
                    m_goRewardRandom.SetActive(false);
                }
            }
        }
    }
}