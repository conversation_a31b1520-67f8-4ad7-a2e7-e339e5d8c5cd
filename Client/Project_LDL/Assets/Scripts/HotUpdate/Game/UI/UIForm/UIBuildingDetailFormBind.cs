using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIBuildingDetailForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnExit;
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnDone;
        [SerializeField] private UIButton m_btnUpLevel;

        [SerializeField] private UIText m_txtBuildingName;
        [SerializeField] private UIText m_txtBuildingCurLevel;
        [SerializeField] private UIText m_txtBuildingNextLevel;
        [SerializeField] private UIText m_txtExp;
        [SerializeField] private UIText m_txtDoneValue;
        [SerializeField] private UIText m_txtOldTime;
        [SerializeField] private UIText m_txtUpLevelTime;

        [SerializeField] private UIImage m_imgBuildingIcon;

        [SerializeField] private ScrollRect m_scrollviewCost;

        [SerializeField] private GameObject m_goAttrItem;
        [SerializeField] private GameObject m_goCostItem;
        [SerializeField] private Mosframe.TableView m_TableViewVAttr;

        void InitBind()
        {
            m_btnExit.onClick.AddListener(OnBtnExitClick);
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnDone.onClick.AddListener(OnBtnDoneClick);
            m_btnUpLevel.onClick.AddListener(OnBtnUpLevelClick);
        }
    }
}
