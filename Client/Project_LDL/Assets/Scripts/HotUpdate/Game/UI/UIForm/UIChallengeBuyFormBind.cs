using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIChallengeBuyForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnBuy;
        [SerializeField] private UIButton m_btnDown;
        [SerializeField] private UIButton m_btnUp;

        [SerializeField] private UIText m_txtPrice;
        [SerializeField] private UIText m_txtDes;
        [SerializeField] private UIText m_txtDes2;
        [SerializeField] private UIText m_txtDes3;
        [SerializeField] private Text m_txtInput;

        [SerializeField] private UIImage m_imgCoin;

        [SerializeField] private Slider m_slider;

        void InitBind()
        {
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnBuy.onClick.AddListener(OnBtnBuyClick);
            m_btnDown.onClick.AddListener(OnBtnDownClick);
            m_btnUp.onClick.AddListener(OnBtnUpClick);
        }
    }
}
