using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIWorldMapDoomEliteMenu : UIWorldMapMenuBase
    {
        [SerializeField] private UIButton m_btnShare;
        [SerializeField] private UIButton m_btnFavorite;

        [SerializeField] private UIText m_txtTitle;
        [SerializeField] private UIText m_txtDes;

        [SerializeField] private UIImage m_imgIcon;

        [SerializeField] private RectTransform m_rectMove;
        [SerializeField] private RectTransform m_rectMenu;

        void InitBind()
        {
            m_btnShare.onClick.AddListener(OnBtnShareClick);
            m_btnFavorite.onClick.AddListener(OnBtnFavoriteClick);
        }
    }
}
