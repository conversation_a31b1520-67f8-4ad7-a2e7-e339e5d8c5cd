using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIUnionRankForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnExit;
        [SerializeField] private UIButton m_btnDonate;
        [SerializeField] private UIButton m_btnReward;

        [SerializeField] private UIText m_txtType;

        [SerializeField] private Transform m_transBtnList;
        [SerializeField] private RectTransform m_rectTitle;
        [SerializeField] private Transform m_transTogList;
        [SerializeField] private Mosframe.TableView m_TableViewV;
        [SerializeField] private GameObject m_goItem;
        [SerializeField] private GameObject m_goSelf;

        void InitBind()
        {
            m_btnExit.onClick.AddListener(OnBtnExitClick);
            m_btnDonate.onClick.AddListener(OnBtnDonateClick);
            m_btnReward.onClick.AddListener(OnBtnRewardClick);
        }
    }
}
