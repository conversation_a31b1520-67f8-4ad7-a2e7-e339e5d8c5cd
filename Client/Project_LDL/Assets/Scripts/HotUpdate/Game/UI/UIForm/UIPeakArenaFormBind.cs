using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIPeakArenaForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnRecords;
        [SerializeField] private UIButton m_btnRewards;
        [SerializeField] private UIButton m_btnShop;
        [SerializeField] private Button m_btnClose;
        [SerializeField] private UIButton m_btnChallenge;
        [SerializeField] private UIButton m_btnAddSoldierTime;
        [SerializeField] private UIButton m_btnDefense;

        [SerializeField] private UIText m_txtSoliderTitle;
        [SerializeField] private UIText m_txtTwoTitle;
        [SerializeField] private UIText m_txtTime;
        [SerializeField] private UIText m_txtChallenges;

        [SerializeField] private Image m_imgBg;

        [SerializeField] private ScrollRect m_scrollview;

        [SerializeField] private UIToggle m_tog1;
        [SerializeField] private UIToggle m_tog2;
        [SerializeField] private UIToggle m_tog3;
        [SerializeField] private UIToggle m_tog4;

        [SerializeField] private GameObject m_goTogList;
        [SerializeField] private GameObject m_goShopBg;
        [SerializeField] private Mosframe.CCTableViewController m_TableViewD;
        [SerializeField] private GameObject m_goPlayer1;
        [SerializeField] private GameObject m_goPlayer2;
        [SerializeField] private GameObject m_goPlayer3;
        [SerializeField] private GameObject m_goItem;
        [SerializeField] private GameObject m_goMyself;
        [SerializeField] private GameObject m_goSoldierNode;

        void InitBind()
        {
            m_btnRecords.onClick.AddListener(OnBtnRecordsClick);
            m_btnRewards.onClick.AddListener(OnBtnRewardsClick);
            m_btnShop.onClick.AddListener(OnBtnShopClick);
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnChallenge.onClick.AddListener(OnBtnChallengeClick);
            m_btnAddSoldierTime.onClick.AddListener(OnBtnAddSoldierTimeClick);
            m_btnDefense.onClick.AddListener(OnBtnDefenseClick);
        }
    }
}
