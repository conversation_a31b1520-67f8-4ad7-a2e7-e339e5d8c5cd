using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using GameFramework.Event;
using GameFramework.Resource;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class UIHeroDevelopPanel : MonoBehaviour
    {
        private bool isFristIn = true;
        public int Depth { get; set; }
        public Action<Vector3, Vector3, Vector3, float, float, float> OnShowFightEff;
        public virtual void OnCreate()
        {
            isFristIn = false;

            Text[] texts = GetComponentsInChildren<Text>(true);
            for (int i = 0; i < texts.Length; i++)
            {
                if (texts[i].name.StartsWith("m_txtAuto"))
                {
                    if (int.TryParse(texts[i].name.Substring(9), out int id))
                    {
                        var langCfg = GameEntry.LDLTable.GetTableById<Game.Hotfix.Config.language_config>(id);
                        if (langCfg != null)
                        {
                            texts[i].text = langCfg.GetLang();
                        }
                        else
                        {
                            Debug.LogError("langCfg null!!!  m_txtAuto id error: " + id);
                        }

                    }
                    else
                    {
                        Debug.LogError("m_txtAuto name error: " + texts[i].name);
                    }
                }
            }
        }
        public virtual void OnInit(itemid heroId) { if (isFristIn) { OnCreate(); } }
        public virtual void OnClose() { }
        public virtual void OnPause() { }
        public virtual void OnUpdate() { }
    }

    public partial class UIHeroDevelopForm : UGuiFormEx
    {
        public int HeroIndex => heroIndex;
        private List<HeroModule> heroList;
        private int heroIndex;
        private HeroModule heroVo;
        private Dictionary<int, GameObject> panelObjDic = new();
        private UIHeroDevelopPanel curPanel;
        private int selectIdx = -1;
        private int lastIdx = -1;
        private bool isPlayAni = false;
        private int timeCount = 0;
        private ObjectPool<GameObject> effectObjList;
        private string heroSpineName = "";
        private string[] spineAniName = { "idle", "xiuxian" };
        private int spineAniIdx = 0;
        private bool needUpdateAni = false;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitBind();

            effectObjList = new(() =>
            {
                var effectObj = m_transEffect.GetChild(0).gameObject;
                var obj = Instantiate(effectObj, m_transEffect);
                return obj;
            },
            (go) =>
            {
                go.SetActive(true);
            },
            (go) =>
            {
                var childCount = go.transform.childCount;
                for (int i = 0; i < childCount; i++)
                {
                    var obj = go.transform.GetChild(i).gameObject;
                    obj.SetActive(false);
                }
                go.SetActive(false);
            },
            (go) =>
            {
                Destroy(go);
            });
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            var arr = userData as ArrayList;
            heroList = arr[0] as List<HeroModule>;
            heroIndex = (int)arr[1];

            lastIdx = -1;
            OnSwitchHero(heroIndex);
            OnSwitchPanel(0);

            if (!GameEntry.Event.Check(HeroChangeEventArgs.EventId, OnHeroUpdate))
                GameEntry.Event.Subscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            if (!GameEntry.Event.Check(EquipmentChangeEventArgs.EventId, OnHeroUpdate))
                GameEntry.Event.Subscribe(EquipmentChangeEventArgs.EventId, OnHeroUpdate);
            if (!GameEntry.Event.Check(ItemChangeEventArgs.EventId, OnHeroUpdate))
                GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnHeroUpdate);
            if (!GameEntry.Event.Check(EquipmentSwitchEventArgs.EventId, OnEquipmentSwitch))
                GameEntry.Event.Subscribe(EquipmentSwitchEventArgs.EventId, OnEquipmentSwitch);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);

            if (GameEntry.Event.Check(HeroChangeEventArgs.EventId, OnHeroUpdate))
                GameEntry.Event.Unsubscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
            if (GameEntry.Event.Check(EquipmentChangeEventArgs.EventId, OnHeroUpdate))
                GameEntry.Event.Unsubscribe(EquipmentChangeEventArgs.EventId, OnHeroUpdate);
            if (GameEntry.Event.Check(ItemChangeEventArgs.EventId, OnHeroUpdate))
                GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnHeroUpdate);
            if (GameEntry.Event.Check(EquipmentSwitchEventArgs.EventId, OnEquipmentSwitch))
                GameEntry.Event.Unsubscribe(EquipmentSwitchEventArgs.EventId, OnEquipmentSwitch);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            var arr = userData as ArrayList;
            var t = (int)arr[0];
            if (t == 1)
            {
                var panelIndex = (int)arr[1];
                OnSelectBtn(panelIndex);
            }
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);

            var headEff = m_goHero.transform.Find("battle_peiyang_kejimianban").gameObject;
            ToolScriptExtend.SetParticleSystemSortingOrder(headEff, Depth);

            var canvas = m_goHero.transform.Find("canvas").GetComponent<Canvas>();
            canvas.sortingOrder = Depth + 10;

            var effectObj = m_transEffect.GetChild(0).gameObject;
            SetParticleSystemSortingOrder(effectObj, Depth);

            var trail = effectObj.transform.Find("battle_feixingguangdian/qianghua/Trail");
            if (trail != null)
            {
                var trailRenderer = trail.GetComponent<TrailRenderer>();
                trailRenderer.sortingOrder = Depth + 3;
            }
        }

        public new void Close()
        {
            base.Close();

            m_spuiRole.gameObject.SetActive(false);
            m_goHero.SetActive(false);

            foreach (var item in panelObjDic)
            {
                var panelObj = item.Value;
                if (panelObj != null)
                {
                    panelObj.transform.GetComponent<UIHeroDevelopPanel>().OnClose();
                }
            }
        }

        protected override void OnPause()
        {
            base.OnPause();

            foreach (var item in panelObjDic)
            {
                var panelObj = item.Value;
                if (panelObj != null)
                {
                    panelObj.transform.GetComponent<UIHeroDevelopPanel>().OnPause();
                }
            }
        }

        private void OnBtnLeftClick()
        {
            OnSwitchHero(heroIndex - 1);
            OnSwitchPanel(selectIdx);
        }

        private void OnBtnRightClick()
        {
            OnSwitchHero(heroIndex + 1);
            OnSwitchPanel(selectIdx);
        }

        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnHeroUpdate(object sender, GameEventArgs e)
        {
            OnUpdateShow();

            if (curPanel != null)
                curPanel.OnUpdate();
        }

        private void OnEquipmentSwitch(object sender, GameEventArgs e)
        {
            if (e is EquipmentSwitchEventArgs args)
            {
                int direction = args.Direction;
                int offset = args.Offset;
                if (direction == -1)
                {
                    OnSwitchHero(heroIndex - offset);
                    OnSwitchPanel(selectIdx);
                }
                else if (direction == 1)
                {
                    OnSwitchHero(heroIndex + offset);
                    OnSwitchPanel(selectIdx);
                }
            }
        }

        private void OnSwitchHero(int index)
        {
            var minIndex = 0;
            var maxIndex = heroList.Count - 1;
            if (index < minIndex)
            {
                index = minIndex;
            }
            if (index > maxIndex)
            {
                index = maxIndex;
            }
            m_btnLeft.gameObject.SetActive(index > minIndex);
            m_btnRight.gameObject.SetActive(index < maxIndex);

            heroIndex = index;
            heroVo = heroList[heroIndex];
        }

        private void OnSwitchPanel(int index)
        {
            if (GetTabState(index))
            {
                if (selectIdx == index && curPanel != null)
                {
                    curPanel.OnInit(heroVo.id);
                    OnUpdateShow();
                }
                else { OnSelectBtn(index); }
            }
            else
            {
                OnSelectBtn(0);
            }
            OnInitTab();
        }

        private void OnInitTab()
        {
            int showCount = 0;
            int childCount = m_transBtnList.childCount;
            for (int i = 0; i < childCount; i++)
            {
                var trans = m_transBtnList.GetChild(i);
                var btn = trans.GetComponent<Button>();

                int index = i;
                btn.onClick.RemoveAllListeners();
                btn.onClick.AddListener(() =>
                {
                    OnSelectBtn(index);
                });

                var showState = GetTabState(i);
                if (showState)
                    showCount += 1;
                trans.gameObject.SetActive(showState);
            }

            var layout = m_transBtnList.GetComponent<GridLayoutGroup>();
            layout.cellSize = new(900 / showCount, 151);
        }

        private bool GetTabState(int index)
        {
            if (index == 2)
            {
                return heroVo.IsActive;
            }
            return index > -1 && index < 3;
        }

        private void OnSelectBtn(int index)
        {
            if (selectIdx == index || !GetTabState(index))
                return;

            float time = 0;
            string aniName;
            if (index == 0 && lastIdx == 1)
            {
                time = 0.5f;
                isPlayAni = false;
                aniName = "heroBg_closed";
                OnUpdateHero(index);
                isPlayAni = true;
            }
            else if (index == 1 && lastIdx == 0)
            {
                time = 0.5f;
                isPlayAni = true;
                aniName = "heroBg_open";
            }
            else
            {
                time = 0;
                isPlayAni = false;
                aniName = "heroBg_normal";
            }
            lastIdx = index;

            var ani = m_goHero.GetComponent<Animation>();
            ani.Play(aniName);

            var canvasGroup = m_transPanel.GetComponent<CanvasGroup>();
            canvasGroup.DOKill();

            if (time > 0)
            {
                var closeIdx = selectIdx;
                var openIdx = index;

                canvasGroup.alpha = 1;
                canvasGroup.DOFade(0, 0.3f).SetEase(Ease.Unset).OnComplete(() =>
                {
                    if (closeIdx > -1)
                    {
                        GetPanelObj(closeIdx, false);
                    }
                });

                Timers.Instance.Remove("UIHeroDevelopPanel");
                Timers.Instance.Add("UIHeroDevelopPanel", time, (param) =>
                {
                    if (isPlayAni)
                    {
                        isPlayAni = false;

                        canvasGroup.alpha = 0.5f;
                        canvasGroup.DOFade(1, 0.3f).SetEase(Ease.Unset);

                        var panelObj = GetPanelObj(openIdx, true);
                        if (panelObj != null)
                        {
                            OnSelectPanel(panelObj);
                        }
                        OnUpdateHero(openIdx);
                        OnUpdateShow();
                    }
                });
            }
            else
            {
                canvasGroup.alpha = 1;

                var panelObj = GetPanelObj(index, true);
                if (panelObj != null)
                {
                    OnSelectPanel(panelObj);
                }

                if (selectIdx > -1)
                {
                    GetPanelObj(selectIdx, false);
                }
            }

            if (selectIdx == 0 || index == 0) // 防止动画不刷新
                needUpdateAni = true;

            var trans = m_transBtnList.GetChild(index);
            if (trans != null)
            {
                var btnBg = trans.Find("btnBg").gameObject;
                btnBg.SetActive(true);
            }

            if (selectIdx > -1)
            {
                trans = m_transBtnList.GetChild(selectIdx);
                if (trans != null)
                {
                    var btnBg = trans.Find("btnBg").gameObject;
                    btnBg.SetActive(false);
                }
            }
            selectIdx = index;

            if (time <= 0)
                OnUpdateShow();
        }

        private void OnSelectPanel(GameObject panelObj)
        {
            if (curPanel != null)
                curPanel.OnClose();

            curPanel = panelObj.GetComponent<UIHeroDevelopPanel>();
            curPanel.Depth = Depth;
            curPanel.OnInit(heroVo.id);
            curPanel.OnShowFightEff = (Vector3 startPos, Vector3 controlPos, Vector3 endPos, float moveTime, float width, float offsetX) =>
            {
                OnShowEff(startPos, controlPos, endPos, moveTime, width, offsetX);
            };
        }

        private GameObject GetPanelObj(int index, bool showState)
        {
            panelObjDic.TryGetValue(index, out GameObject panelObj);
            if (panelObj != null)
            {
                if (panelObj.activeSelf != showState)
                    panelObj.SetActive(showState);
            }
            else if (showState)
            {
                GameEntry.Resource.LoadAsset(GetPanelPath(index), typeof(GameObject), new LoadAssetCallbacks((assetName, asset, duration, userData) =>
                {
                    var prefab = asset as GameObject;
                    panelObj = Instantiate(prefab, m_transPanel);
                    panelObjDic.TryAdd(index, panelObj);
                    OnSelectPanel(panelObj);
                }));
            }
            return panelObj;
        }

        private string GetPanelPath(int index)
        {
            return index switch
            {
                1 => "Assets/ResPackage/Prefab/UI/UIHeroSkillPanel.prefab",
                2 => "Assets/ResPackage/Prefab/UI/UIHeroUpStarPanel.prefab",
                _ => "Assets/ResPackage/Prefab/UI/UIHeroAttrPanel.prefab",
            };
        }

        private void OnUpdateShow()
        {
            m_goBg_0.SetActive(selectIdx == 0);
            m_goBg_1.SetActive(selectIdx == 1);
            m_goBg_2.SetActive(selectIdx == 2);
            m_goMask_1.SetActive(selectIdx == 1);
            m_goMask_2.SetActive(selectIdx == 2);
            OnUpdateHero(selectIdx);
        }

        private void OnUpdateHero(int index)
        {
            var isShow = index > 0;
            var list = heroVo.HeroSpineList;
            if (list != null && list.Count >= 2)
            {
                if (list[1] != heroSpineName)
                {
                    heroSpineName = list[1];
                    GameEntry.Resource.LoadAsset(list[0], typeof(Material), new LoadAssetCallbacks((assetName, asset, duration, userData) =>
                    {
                        var material = asset as Material;
                        GameEntry.Resource.LoadAsset(list[1], typeof(Spine.Unity.SkeletonDataAsset), new LoadAssetCallbacks((assetName, asset, duration, userData) =>
                        {
                            var skeletonDataAsset = asset as Spine.Unity.SkeletonDataAsset;
                            m_spuiRole.material = material;
                            m_spuiRole.skeletonDataAsset = skeletonDataAsset;
                            m_spuiRole.Initialize(true);

                            m_spuiHero.material = material;
                            m_spuiHero.skeletonDataAsset = skeletonDataAsset;
                            m_spuiHero.Initialize(true);

                            OnResetSpine();
                            OnResetSpineAni();
                        }));
                    }));
                }
                else
                {
                    OnResetSpine();
                }
            }

            if (!isPlayAni)
            {
                m_spuiRole.gameObject.SetActive(isShow);
                m_goHero.SetActive(!isShow);

                if (needUpdateAni)
                {
                    needUpdateAni = false;
                    OnResetSpineAni();
                }
            }

            OnUpdateRedShow();
        }

        private void OnUpdateRedShow()
        {
            var childCount = m_transBtnList.childCount;
            for (int i = 0; i < childCount; i++)
            {
                var trans = m_transBtnList.GetChild(i);
                var redImg = trans.Find("redImg").gameObject;

                if (i == 0) { redImg.SetActive(heroVo.GetHeroUpgradeRed()); }
                else if (i == 1) { redImg.SetActive(heroVo.GetHeroSkillRed()); }
                else if (i == 2) { redImg.SetActive(heroVo.GetHeroStarRed()); }
                else { redImg.SetActive(false); }
            }
        }

        private void OnResetSpine()
        {
            var vec = heroVo.GetHeroSpineOffset(interfacetype.interfacetype_1);
            m_spuiHero.transform.SetLocalPosition(vec.x, vec.y, vec.z);

            vec = heroVo.GetHeroSpineScale(interfacetype.interfacetype_1);
            m_spuiHero.transform.SetLocalScale(vec.x, vec.y, vec.z);

            vec = heroVo.GetHeroSpineOffset(interfacetype.interfacetype_2);
            m_spuiRole.transform.SetLocalPosition(vec.x, vec.y, vec.z);
            m_goMask_1.transform.SetLocalPosition(0, vec.y + 140, 0);

            vec = heroVo.GetHeroSpineScale(interfacetype.interfacetype_2);
            m_spuiRole.transform.SetLocalScale(vec.x, vec.y, vec.z);
        }

        private void OnResetSpineAni()
        {
            spineAniIdx = 0;
            OnPlaySpineAni();
        }

        private void OnPlaySpineAni()
        {
            var aniName = spineAniName[spineAniIdx];
            spineAniIdx = (spineAniIdx + 1) % spineAniName.Length;

            var spui = selectIdx > 0 ? m_spuiRole : m_spuiHero;
            spui.Initialize(true);

            var trackEntry = spui.AnimationState.SetAnimation(0, aniName, false);
            trackEntry.Complete += OnSpineAniComplete;
        }

        private void OnSpineAniComplete(Spine.TrackEntry trackEntry)
        {
            OnPlaySpineAni();
        }

        private void OnShowEff(Vector3 startPos, Vector3 controlPos, Vector3 endPos, float moveTime, float width, float offsetX)
        {
            List<Vector3> pathPoints = new();
            int resolution = 10;
            for (int i = 0; i <= resolution; i++)
            {
                float t = i / (float)resolution;
                Vector3 point = (1 - t) * (1 - t) * startPos +
                                2 * t * (1 - t) * controlPos +
                                t * t * endPos;
                pathPoints.Add(point);
            }

            var effectObj = effectObjList.Get();
            var effect1 = effectObj.transform.GetChild(0);
            effect1.position = startPos;
            effect1.gameObject.SetActive(true);

            effect1.DOPath(pathPoints.ToArray(), moveTime).SetEase(Ease.InOutQuad).OnComplete(() =>
            {
                var effect2 = effectObj.transform.GetChild(1);
                effect2.position = endPos;
                effect2.gameObject.SetActive(true);
                effect1.gameObject.SetActive(false);

                var timerKey1 = "UIHeroDevelopForm_effect" + timeCount;
                timeCount += 1;

                Timers.Instance.Add(timerKey1, 0.2f, (param) =>
                {
                    Timers.Instance.Remove(timerKey1);
                    var effect3 = effectObj.transform.GetChild(2);
                    effect3.position = endPos;
                    effect3.localPosition += new Vector3(offsetX, 0, 0);
                    effect3.SetLocalScaleX(width / 2.8f);
                    effect3.gameObject.SetActive(true);

                    var timerKey2 = timerKey1 + "_2";
                    Timers.Instance.Add(timerKey1, 1.5f, (param) =>
                    {
                        Timers.Instance.Remove(timerKey2);
                        effectObjList.Release(effectObj);
                    });
                });
            });
        }

        /// <summary>
        /// 显示英雄战力拖尾特效
        /// </summary>
        /// <param name="depth">深度</param>
        public void ShowHeroPowerTrailEffect(int depth)
        {
            panelObjDic.TryGetValue(0, out GameObject panelObj);
            if (panelObj != null)
            {
                if (panelObj.TryGetComponent(out UIHeroAttrPanel panel))
                {
                    // 提高拖尾特效层级
                    var effectObj = m_transEffect.GetChild(0).gameObject;
                    GameObject battle_feixingguangdian = effectObj.transform.Find("battle_feixingguangdian").gameObject;
                    GameObject battle_feixingguangdian1 = effectObj.transform.Find("battle_feixingguangdian 1").gameObject;

                    SetParticleSystemSortingOrder(battle_feixingguangdian, depth);
                    SetParticleSystemSortingOrder(battle_feixingguangdian1, depth);

                    var trail = effectObj.transform.Find("battle_feixingguangdian/qianghua/Trail");
                    if (trail != null)
                    {
                        var trailRenderer = trail.GetComponent<TrailRenderer>();
                        trailRenderer.sortingOrder = depth + 3;
                    }

                    // 显示拖尾特效
                    panel.ShowFightEffect();
                }
            }
        }
    }
}
