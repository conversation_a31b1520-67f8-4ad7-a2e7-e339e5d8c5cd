using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using GameFramework.Resource;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIHeroAttrPanel : UIHeroDevelopPanel
    {
        private HeroModule heroVo;
        private List<GameObject> attrObjList;
        private ObjectPool<GameObject> upObjList;
        private string toyPathStr;
        private GameObject toyObj;
        private float rotateX;
        private float rotateSpeed = 0.5f;

        UIItemModule weapon;  // 武器
        UIItemModule armour;  // 装甲
        UIItemModule chip;    // 芯片
        UIItemModule radar;   // 雷达
        bool isInitWeapon = false; // 武器是否初始化过
        bool isInitArmour = false; // 装甲是否初始化过
        bool isInitChip = false;   // 芯片是否初始化过
        bool isInitRadar = false;  // 雷达是否初始化过
        bool hasAvailableQuickEquip = false;

        public override void OnCreate()
        {
            base.OnCreate();
            InitBind();

            m_txtLimit.text = ToolScriptExtend.GetLang(711346);
            m_txtQuickEquip.text = ToolScriptExtend.GetLang(711347);

            var count = m_transAttr.childCount;
            for (int i = 0; i < count; i++)
            {
                var childTrans = m_transAttr.GetChild(i);
                if (childTrans)
                {
                    int index = i + 1;
                    var btn = childTrans.GetComponent<UIButton>();
                    if (btn)
                    {
                        btn.onClick.RemoveAllListeners();
                        btn.onClick.AddListener(() =>
                        {
                            GameEntry.LogicData.HeroData.OnReqHeroQueryAttr(heroVo.id, (resp) =>
                            {
                                OnBtnAttrClick(childTrans, index, resp);
                            });
                        });
                    }
                }
            }

            var btn_1 = m_imgServices.GetComponent<UIButton>();
            if (btn_1)
            {
                btn_1.onClick.RemoveAllListeners();
                btn_1.onClick.AddListener(() =>
                {
                    var t = heroVo.Services;
                    int langId;
                    if (t == hero_services.hero_services_tank) { langId = 711301; }
                    else if (t == hero_services.hero_services_aircraft) { langId = 711302; }
                    else { langId = 711303; }
                    OnBtnPopClick(btn_1.transform, ToolScriptExtend.GetLang(langId));
                });
            }

            var btn_2 = m_imgPosition.GetComponent<UIButton>();
            if (btn_2)
            {
                btn_2.onClick.RemoveAllListeners();
                btn_2.onClick.AddListener(() =>
                {
                    var t = heroVo.Position;
                    int langId;
                    if (t == hero_position.hero_position_1) { langId = 711306; }
                    else if (t == hero_position.hero_position_2) { langId = 711304; }
                    else { langId = 711305; }
                    OnBtnPopClick(btn_2.transform, ToolScriptExtend.GetLang(langId));
                });
            }

            m_btnUpgrade.longPressInterval = 0.2f;
            m_btnUpgrade.SetLongPress(() =>
            {
                if (GameEntry.UI.GetUIForm(EnumUIForm.UIHeroSkillUnlockForm))
                    return;
                OnBtnUpgradeClick();
            });

            ToolScriptExtend.SetParticleSystemSortingOrder(m_goEffect, Depth);

            var canvas = m_goCanvas.GetComponent<Canvas>();
            canvas.sortingOrder = Depth + 11;

            upObjList = new(() =>
            {
                var prefab = m_transAttrUp.GetChild(0).gameObject;
                var obj = Instantiate(prefab, m_transAttrUp);
                return obj;
            },
            (go) =>
            {
                go.SetActive(true);
            },
            (go) =>
            {
                go.SetActive(false);
            },
            (go) =>
            {
                Destroy(go);
            });

            var uiDrag = m_transDrag.GetComponent<UIDragXYDir>();
            uiDrag.m_BeginDrag = (eventData, go, x, y) =>
            {
                rotateX = x;
            };
            uiDrag.m_OnDrag = (eventData, go, x, y) =>
            {
                if (toyObj != null && rotateX != x)
                {
                    toyObj.transform.Rotate(Vector3.forward, (rotateX - x) * rotateSpeed);
                    rotateX = x;
                }
            };
        }

        public override void OnInit(itemid heroId)
        {
            base.OnInit(heroId);

            m_transToy.gameObject.SetActive(true);

            heroVo = GameEntry.LogicData.HeroData.GetHeroModule(heroId);
            if (heroVo == null) { return; }

            OnUpdate();
            OnBtnTipMaskClick();
        }

        public override void OnClose()
        {
            m_goEffect.SetActive(false);
            m_transToy.gameObject.SetActive(false);
        }

        public override void OnUpdate()
        {
            var heroData = GameEntry.LogicData.HeroData;

            m_imgQuality.SetImage(heroData.GetQualityImgPath(heroVo.Quality), true);
            m_imgServices.SetImage(heroData.GetServicesImgPath(heroVo.Services), true);
            m_imgPosition.SetImage(heroData.GetPositionImgPath(heroVo.Position), true);

            m_txtNickname.text = heroVo.Nickname;
            m_txtName.text = heroVo.Name;
            m_txtTeam.text = heroVo.teamId + "";

            var isActive = heroVo.IsActive;
            var teamState = heroVo.IsTeam;
            if (!teamState)
            {
                var imgPath = heroVo.IsDrop ? "Sprite/ui_hero/heroshuxing_icon_guishu_guagou.png" : "Sprite/ui_hero/heroshuxing_icon_guishu_kongtouk.png";
                m_imgDrop.SetImage(imgPath, true);
            }
            m_goTeam.SetActive(isActive && teamState);
            m_btnDrop.gameObject.SetActive(isActive && !teamState);

            m_txtPower.text = (isActive ? heroVo.power : heroVo.GetCalculateFight()) + "";
            m_txtLevel.text = (isActive ? heroVo.level : heroVo.MaxLv) + "";
            m_txtAttr_1.text = heroVo.GetAttrByType(PbGameconfig.attributes_type.Attack) + "";
            m_txtAttr_2.text = heroVo.GetAttrByType(PbGameconfig.attributes_type.Hp) + "";
            m_txtAttr_3.text = heroVo.GetAttrByType(PbGameconfig.attributes_type.Defense) + "";
            m_txtAttr_4.text = heroVo.GetAttrByType(PbGameconfig.attributes_type.TroopCapacity) + "";

            if (isActive)
            {
                if (heroVo.level >= heroVo.MaxLv)
                {
                    m_txtTip.text = ToolScriptExtend.GetLang(711350);
                    m_txtCost.gameObject.SetActive(false);
                    m_txtLimit.gameObject.SetActive(false);
                    m_txtTip.gameObject.SetActive(true);
                    m_goRedDot.SetActive(false);

                    m_btnUpgrade.nextLongPressTime = 0;
                    m_btnUpgrade.enabled = false;
                    m_btnUpgrade.SetButtonGray(true);
                }
                else
                {
                    var isLimit = heroVo.GetLevelLimit();
                    var config = heroVo.GetHeroLvConfig(heroVo.level);
                    if (config != null)
                    {
                        if (isLimit)
                        {
                            var demandConfig = Game.GameEntry.LDLTable.GetTableById<demand_config>(config.level_up_demand);
                            if (demandConfig != null && demandConfig.build_demand != null)
                            {
                                var buildingLevelCfg = GameEntry.LogicData.BuildingData.GetBuildingLevelCfg(demandConfig.build_demand.build_type_demand, 1);
                                if (buildingLevelCfg != null && buildingLevelCfg.picture != null)
                                    m_imgBuild.SetImage(buildingLevelCfg.picture);
                            }
                            m_goRedDot.SetActive(false);
                        }
                        else
                        {
                            var costDic = config.hero_exp;
                            var curNum = GameEntry.LogicData.BagData.GetAmountById(costDic.item_id);
                            var isEnough = curNum >= costDic.num;
                            var colorStr = isEnough ? "00ff00" : "ff0101";
                            m_txtCost.text = string.Format("<color=#{0}>{1}</color>/{2}", colorStr, ToolScriptExtend.FormatNumberWithUnit(curNum), ToolScriptExtend.FormatNumberWithUnit(costDic.num));
                            m_imgCost.SetImage(ToolScriptExtend.GetItemIcon(costDic.item_id));
                            m_goRedDot.SetActive(isEnough);
                        }
                    }
                    m_txtCost.gameObject.SetActive(!isLimit);
                    m_txtLimit.gameObject.SetActive(isLimit);
                    m_txtTip.gameObject.SetActive(false);

                    m_btnUpgrade.enabled = !isLimit;
                    m_btnUpgrade.SetButtonGray(isLimit);
                }
                m_txtUpgrade.text = ToolScriptExtend.GetLang(711345);
            }
            else
            {
                m_txtTip.text = ToolScriptExtend.GetLang(711349);
                m_txtCost.gameObject.SetActive(false);
                m_txtLimit.gameObject.SetActive(false);
                m_txtTip.gameObject.SetActive(true);
                m_goRedDot.SetActive(false);

                m_txtUpgrade.text = ToolScriptExtend.GetLang(711348);
                m_btnUpgrade.enabled = true;
                m_btnUpgrade.SetButtonGray(false);
            }

            if (toyPathStr != heroVo.ToyPrefab)
            {
                toyPathStr = heroVo.ToyPrefab;
                if (toyObj != null)
                    Destroy(toyObj);

                GameEntry.Resource.LoadAsset(toyPathStr, new LoadAssetCallbacks((assetName, asset, duration, userData) =>
                {
                    var prefab = asset as GameObject;
                    toyObj = Instantiate(prefab, m_transToy.gameObject.transform);
                    OnResetToyObj();
                }));
            }
            else
            {
                OnResetToyObj();
            }

            RefreshEquipment();
        }

        private void OnResetToyObj()
        {
            if (toyObj == null) return;

            var list = heroVo.ToyOffsetList;
            toyObj.transform.SetLocalPosition(list[0], list[1], list[2]);

            list = heroVo.ToyScaleList;
            toyObj.transform.SetLocalScale(list[0], list[1], list[2]);

            list = heroVo.ToyRotList;
            toyObj.transform.eulerAngles = new Vector3(list[0], list[1], list[2]);
            toyObj.transform.SetLayer(6);
        }

        private void OnBtnAttrClick(Transform childTrans, int index, Hero.HeroQueryAttrBuildResp resp)
        {
            var attrs = resp.Builds;
            List<Common.AttrBuild> list = new();
            if (attrs != null)
            {
                if (attrs.Count <= 0)
                    return;

                var attrType = index;
                if (attrType == 2) { attrType = (int)PbGameconfig.attributes_type.Hp; }
                else if (attrType == 3) { attrType = (int)PbGameconfig.attributes_type.Defense; }
                else if (attrType == 4) { attrType = (int)PbGameconfig.attributes_type.TroopCapacity; }

                foreach (var item in attrs)
                {
                    if (item.AttrType == attrType)
                    {
                        var builds = item.Builds;
                        foreach (var data in builds)
                        {
                            list.Add(data);
                        }
                    }
                }
            }

            var tipBg = m_goTipAttr.transform.Find("tipBg");
            var titleTxt = tipBg.transform.Find("titleTxt").GetComponent<UIText>();
            var totalTxt = tipBg.transform.Find("titleTxt/totalTxt").GetComponent<UIText>();
            var prefab = tipBg.Find("attrBg").gameObject;
            attrObjList ??= new() { prefab };

            var count = list.Count;
            var num = attrObjList.Count;
            var len = count > num ? count : num;
            var config = GameEntry.LDLTable.GetTableById<attributes_detail>(index);
            float totalNum = 0;
            if (config != null)
            {
                for (int i = 0; i < len; i++)
                {
                    GameObject obj;
                    if (attrObjList.Count < i + 1)
                    {
                        obj = Instantiate(prefab, tipBg);
                        attrObjList.Add(obj);
                    }
                    else
                    {
                        obj = attrObjList[i];
                    }
                    if (i < count)
                    {
                        var bg = obj.transform.GetComponent<UIImage>();
                        var addTxt = obj.transform.Find("addTxt").GetComponent<UIText>();
                        var numTxt = obj.transform.Find("numTxt").GetComponent<UIText>();

                        bg.enabled = i % 2 == 0;

                        var data = list[i];
                        var sourceConfig = GameEntry.LDLTable.GetTableById<attributes_source>(data.Source);
                        if (sourceConfig != null)
                        {
                            addTxt.text = ToolScriptExtend.GetLang(sourceConfig.langid);
                            if (config.value_type == valuetype.valuetype_2)
                            {
                                numTxt.text = data.Value / 10000 + "%";
                            }
                            else
                            {
                                numTxt.text = ToolScriptExtend.FormatNumberWithUnit(data.Value);
                            }
                            totalNum += data.Value;
                        }
                    }
                    obj.SetActive(i < count);
                }

                titleTxt.text = ToolScriptExtend.GetLang(config.langid);
                if (config.value_type == valuetype.valuetype_2)
                {
                    totalTxt.text = (float)totalNum / 10000 + "%";
                }
                else
                {
                    totalTxt.text = ToolScriptExtend.FormatNumberWithUnit(totalNum);
                }
            }

            var halfWidth = Screen.width / 2;
            var halfBgWidth = 265;
            var posX = childTrans.localPosition.x;
            float bgPosX = 0;
            if (posX - halfBgWidth < -halfWidth)
            {
                bgPosX = Mathf.Abs(posX - halfBgWidth + halfWidth);
            }
            else if (posX + halfBgWidth > halfWidth)
            {
                bgPosX = -(posX + halfBgWidth - halfWidth);
            }
            tipBg.SetLocalPosition(bgPosX, 26, 0);

            m_goTipAttr.transform.position = childTrans.position;
            m_goTipAttr.SetActive(true);
            m_btnTipMask.gameObject.SetActive(true);

            var rectTrans = tipBg as RectTransform;
            LayoutRebuilder.ForceRebuildLayoutImmediate(rectTrans);
        }

        private void OnBtnPopClick(Transform btnTrans, string str)
        {
            var showTxt = m_goTip.transform.Find("showTxt").GetComponent<UIText>();
            if (showTxt)
            {
                showTxt.text = str;
            }
            m_goTip.transform.position = btnTrans.position;
            m_goTip.SetActive(true);
            m_btnTipMask.gameObject.SetActive(true);
        }

        private void OnBtnTipClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroAttrDetailForm, heroVo.id);
        }

        private void OnBtnDropClick()
        {
            GameEntry.LogicData.HeroData.OnReqHeroMapShow(heroVo.id, 1, (resp) =>
            {
                ColorLog.Pink(string.Format("英雄空投成功! id:{0} show_state:{1}", resp.Id, resp.ShowState));
            });
        }

        private void OnBtnGoClick()
        {
            var config = heroVo.GetHeroLvConfig(heroVo.level);
            if (config != null)
            {
                var demandconfig = Game.GameEntry.LDLTable.GetTableById<demand_config>(config.level_up_demand);
                if (demandconfig != null)
                {
                    var buildDamand = demandconfig.build_demand;
                    if (buildDamand != null)
                    {
                        GameEntry.UI.CloseUIForm(EnumUIForm.UIHeroForm);
                        GameEntry.UI.CloseUIForm(EnumUIForm.UIHeroDevelopForm);

                        GameEntry.LogicData.BuildingData.FindBuildingByType(buildDamand.build_type_demand, true, () =>
                        {
                            var buildCfg = GameEntry.LogicData.BuildingData.GetBuildingConfigByBuildType(buildDamand.build_type_demand);
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIMainFaceBuildForm, buildCfg);
                        });
                    }
                }
            }
        }

        private void OnBtnUpgradeClick()
        {
            if (heroVo.IsActive)
            {
                var config = heroVo.GetHeroLvConfig(heroVo.level);
                if (config != null)
                {
                    var costDic = config.hero_exp;
                    var curNum = GameEntry.LogicData.BagData.GetAmountById(costDic.item_id);
                    if (curNum >= costDic.num)
                    {
                        var curLevel = heroVo.level;
                        var starLv = heroVo.starLv;
                        var unlockIndex = heroVo.GetSkillUnlockIdx();
                        GameEntry.LogicData.HeroData.OnReqHeroUpgradeLevel(heroVo.id, (resp) =>
                        {
                            OnShowEff();
                            OnShowFightEff(new Vector3(0, 0, 0), m_btnUpgrade.transform.position, m_txtPower.transform.position, 0.8f, m_txtPower.preferredWidth, 0);
                            OnShowAttrUp(curLevel);
                            ColorLog.Pink(string.Format("英雄升级成功! id:{0} level:{1}", resp.Id, resp.Level));

                            if (unlockIndex > 0)
                            {
                                var damand = heroVo.GetSkillUnlock(unlockIndex);
                                if (damand != null)
                                {
                                    if (damand.hero_star_demand <= starLv && damand.hero_level_demand <= resp.Level)
                                    {
                                        var skillConfig = heroVo.GetHeroSkillConfig(unlockIndex);
                                        GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroSkillUnlockForm, skillConfig);
                                    }
                                }
                            }
                        });
                    }
                    else
                    {
                        // 打开道具获取途径弹窗
                        ItemModule itemModule = new(costDic.item_id);
                        GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(costDic.num));
                    }
                }
            }
            else
            {
                // 打开英雄获取途径弹窗
                ItemModule itemModule = new(heroVo.Piece);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(heroVo.Combind));
            }
        }

        private void OnBtnTipMaskClick()
        {
            if (m_goTip.activeSelf)
            {
                m_goTip.SetActive(false);
            }
            if (m_goTipAttr.activeSelf)
            {
                m_goTipAttr.SetActive(false);
            }
            if (m_btnTipMask.gameObject.activeSelf)
            {
                m_btnTipMask.gameObject.SetActive(false);
            }
        }

        private void OnBtnWeaponClick()
        {
            OpenEquipmentForm(weapon, equipposition.equipposition_weapon);
        }

        private void OnBtnArmourClick()
        {
            OpenEquipmentForm(armour, equipposition.equipposition_armor);
        }

        private void OnBtnChipClick()
        {
            OpenEquipmentForm(chip, equipposition.equipposition_chip);
        }

        private void OnBtnRadarClick()
        {
            OpenEquipmentForm(radar, equipposition.equipposition_radar);
        }

        private void OnBtnQuickEquipClick()
        {
            itemid heroID = heroVo == null ? 0 : heroVo.id;
            ColorLog.Pink("一键穿戴装备", "heroID", heroID);
            GameEntry.EquipmentData.RequestEquipmentTakeOn(heroID, 0, (result) =>
            {
                ColorLog.Pink("一键穿戴装备回调", result);
            });
        }

        /// <summary>
        /// 打开装备界面
        /// </summary>
        /// <param name="partItem">部位实例</param>
        void OpenEquipmentForm(UIItemModule partItem, equipposition part)
        {
            itemid heroID = heroVo == null ? 0 : heroVo.id;
            if (partItem == null || partItem.UID == 0)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIEquipmentReplaceForm, new EquipmentParams()
                {
                    HeroID = heroID,
                    Part = part
                });
            }
            else
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIEquipmentDetailForm, new EquipmentParams()
                {
                    ID = partItem.UID,
                    HeroID = heroID,
                    Part = part
                });
            }
        }

        /// <summary>
        /// 刷新装备栏
        /// </summary>
        void RefreshEquipment()
        {
            bool isLock = heroVo == null || !heroVo.IsActive;
            m_btnWeapon.gameObject.SetActive(!isLock);
            m_btnArmour.gameObject.SetActive(!isLock);
            m_btnChip.gameObject.SetActive(!isLock);
            m_btnRadar.gameObject.SetActive(!isLock);
            m_btnQuickEquip.gameObject.SetActive(!isLock);

            if (isLock) return;

            bool hasHigherPowerEquipment = GameEntry.EquipmentData.HasHigherPowerEquipment(heroVo.id);
            m_btnQuickEquip.gameObject.SetActive(hasHigherPowerEquipment);
            hasAvailableQuickEquip = false;

            RefreshEquipmentByPart(weapon, m_btnWeapon.transform, equipposition.equipposition_weapon, isInitWeapon);
            RefreshEquipmentByPart(armour, m_btnArmour.transform, equipposition.equipposition_armor, isInitArmour);
            RefreshEquipmentByPart(chip, m_btnChip.transform, equipposition.equipposition_chip, isInitChip);
            RefreshEquipmentByPart(radar, m_btnRadar.transform, equipposition.equipposition_radar, isInitRadar);

            m_goQuickEquipRedpoint.SetActive(hasAvailableQuickEquip);
        }

        /// <summary>
        /// 根据部位刷新装备
        /// </summary>
        /// <param name="partItem">部位实例</param>
        /// <param name="parent">装备栏父节点</param>
        void RefreshEquipmentByPart(UIItemModule partItem, Transform parent, equipposition part, bool isInit)
        {
            itemid heroID = heroVo == null ? 0 : heroVo.id;
            EquipmentModule equipment = GameEntry.EquipmentData.GetWearingByPart(heroID, part);
            bool hasEquipment = equipment != null;
            GameObject redpoint = parent.Find("redpoint").gameObject;

            if (hasEquipment)
            {
                if (partItem == null && !isInit)
                {
                    BagManager.CreatItem(parent, equipment.code, 1, (item) =>
                    {
                        CanvasGroup canvasGroup = item.gameObject.AddComponent<CanvasGroup>();
                        canvasGroup.interactable = false;
                        canvasGroup.blocksRaycasts = false;
                        item.transform.localScale = new Vector3(0.82f, 0.82f, 1f);
                        RectTransform rect = item.GetComponent<RectTransform>();
                        rect.anchoredPosition = new Vector2(0f, -10f);
                        item.IsShowCount(false);
                        item.SwitchQualityIcon(true);
                        item.UID = equipment.id;
                        item.txtEquipmentLevel.text = $"Lv.{equipment.equipment_level}";
                        item.txtEquipmentLevel.transform.localScale = new Vector3(1.2f, 1.2f, 1f);
                        item.RefreshEquipmentRedpoint(equipment.CanUpgrade);
                        item.RefreshEquipmentStar(equipment.PromotionPhase, equipment.CanPromote || equipment.PromoteMax);
                        item.RefreshEquipmentQuality(equipment.EquipmentQuality);
                        if (parent == m_btnWeapon.transform) weapon = item;
                        else if (parent == m_btnArmour.transform) armour = item;
                        else if (parent == m_btnChip.transform) chip = item;
                        else if (parent == m_btnRadar.transform) radar = item;
                    });
                    if (parent == m_btnWeapon.transform) isInitWeapon = true;
                    else if (parent == m_btnArmour.transform) isInitArmour = true;
                    else if (parent == m_btnChip.transform) isInitChip = true;
                    else if (parent == m_btnRadar.transform) isInitRadar = true;
                }
                else if (partItem != null)
                {
                    partItem.UID = equipment.id;
                    partItem.itemModule.ItemId = equipment.code;
                    partItem.txtEquipmentLevel.text = $"Lv.{equipment.equipment_level}";
                    partItem.InitConfigData();
                    partItem.DisplayInfo();
                    partItem.RefreshEquipmentRedpoint(equipment.CanUpgrade);
                    partItem.RefreshEquipmentStar(equipment.PromotionPhase, equipment.CanPromote || equipment.PromoteMax);
                    partItem.RefreshEquipmentQuality(equipment.EquipmentQuality);
                    partItem.gameObject.SetActive(true);
                }

                redpoint.SetActive(false);
            }
            else
            {
                if (partItem != null)
                {
                    partItem.UID = 0;
                    partItem.txtEquipmentLevel.text = string.Empty;
                    partItem.gameObject.SetActive(false);
                }

                bool hasEquipmentWithPart = GameEntry.EquipmentData.HasEquipmentWithPart(part);
                redpoint.SetActive(hasEquipmentWithPart);

                if (hasEquipmentWithPart)
                {
                    hasAvailableQuickEquip = true;
                }
            }
        }

        private void OnShowEff()
        {
            m_goEffect.SetActive(false);
            m_goEffect.SetActive(true);
        }

        public void ShowFightEffect()
        {
            OnShowFightEff(new Vector3(0, 0, 0), m_btnUpgrade.transform.position, m_txtPower.transform.position, 0.8f, m_txtPower.preferredWidth, 0);
        }

        private void OnShowAttrUp(int level)
        {
            var curConfig = heroVo.GetHeroLvConfig(level);
            var nextConfig = heroVo.GetHeroLvConfig(level + 1);
            if (curConfig != null && nextConfig != null)
            {
                var obj = upObjList.Get();
                var canvasGroup = obj.GetComponent<CanvasGroup>();
                var attrTxt_1 = obj.transform.Find("attrTxt_1").GetComponent<UIText>();
                var attrTxt_2 = obj.transform.Find("attrTxt_2").GetComponent<UIText>();
                var attrTxt_3 = obj.transform.Find("attrTxt_3").GetComponent<UIText>();

                attrTxt_1.text = ToolScriptExtend.GetLang(100001) + $"+{nextConfig.atk - curConfig.atk}";
                attrTxt_2.text = ToolScriptExtend.GetLang(100003) + $"+{nextConfig.life - curConfig.life}";
                attrTxt_3.text = ToolScriptExtend.GetLang(100002) + $"+{nextConfig.defense - curConfig.defense}";

                Sequence sequence = DOTween.Sequence();
                sequence.Append(obj.transform.DOLocalMoveY(80, 1f));
                sequence.Insert(0.5f, canvasGroup.DOFade(0, 0.5f));
                sequence.OnComplete(() =>
                {
                    canvasGroup.alpha = 1;
                    obj.transform.SetLocalPosition(0, 0, 0);
                    upObjList.Release(obj);
                });
            }
        }
    }
}
