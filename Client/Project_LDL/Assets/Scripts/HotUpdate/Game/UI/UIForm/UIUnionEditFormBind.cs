using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIUnionEditForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnChoose;
        [SerializeField] private UIButton m_btnName;
        [SerializeField] private UIButton m_btnShortName;
        [SerializeField] private UIButton m_btnLang;
        [SerializeField] private UIButton m_btnPower;
        [SerializeField] private UIButton m_btnLevel;

        [SerializeField] private UIText m_txtName;
        [SerializeField] private UIText m_txtShortName;
        [SerializeField] private UIText m_txtLang;
        [SerializeField] private UIText m_txtPower;
        [SerializeField] private UIText m_txtLevel;

        [SerializeField] private UIImage m_imgFlag;

        [SerializeField] private UIToggle m_togClean;

        void InitBind()
        {
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnChoose.onClick.AddListener(OnBtnChooseClick);
            m_btnName.onClick.AddListener(OnBtnNameClick);
            m_btnShortName.onClick.AddListener(OnBtnShortNameClick);
            m_btnLang.onClick.AddListener(OnBtnLangClick);
            m_btnPower.onClick.AddListener(OnBtnPowerClick);
            m_btnLevel.onClick.AddListener(OnBtnLevelClick);
        }
    }
}
