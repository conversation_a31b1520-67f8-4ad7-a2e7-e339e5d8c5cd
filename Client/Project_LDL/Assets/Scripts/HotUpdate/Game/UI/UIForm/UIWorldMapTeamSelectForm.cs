using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIWorldMapTeamSelectForm : UGuiFormEx
    {
        public List<Color> Colors = new List<Color>()
        {
            new Color(151, 149, 156, 1),//97959c
            new Color(12, 191, 121, 1),//0cbf79
            new Color(20, 165, 210, 1),//14a5d2
            new Color(174, 81, 227, 1),//ae51e3
            new Color(244, 161, 64, 1),//f4a140
            new Color(232, 91, 81, 1),//e85b51
        };
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnInfoClick()
        {

        }

        private void OnBtnConfirmClick()
        {

        }

        private void OnBtnSettingClick()
        {

        }

        private void OnBtnStaminaAddClick()
        {

        }
    }
}
