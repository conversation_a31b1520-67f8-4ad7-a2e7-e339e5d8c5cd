using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class UITeamFormNormalTeamSwitch : MonoBehaviour
    {
        public ToggleGroup toggleGroup;
        public List<UIToggle> toggles;


        private List<UIToggle> m_ToggleList;
        
        private void Awake()
        {
            for (int i = 0; i < m_ToggleList.Count; i++)
            {
                var toggle = m_ToggleList[i];
                toggle.onValueChanged.RemoveAllListeners();
                toggle.onValueChanged.AddListener((value) =>
                {
                    
                });
            }
            
        }
        
        public void Switch(int index)
        {
            if (index < m_ToggleList.Count)
            {
                m_ToggleList[index].isOn = true;
            }
        }
        
    }
}
