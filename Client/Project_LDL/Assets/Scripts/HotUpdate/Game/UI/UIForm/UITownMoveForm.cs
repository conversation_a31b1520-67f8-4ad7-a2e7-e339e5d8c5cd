using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using Vector2 = System.Numerics.Vector2;

namespace Game.Hotfix
{
    public class UITownMoveFormParam
    {
        public int TargetUid;
    }

    public partial class UITownMoveForm : UGuiFormEx
    {
        private EL_TownMovePreview m_Target;
        private UITownMoveFormParam m_UserData;
        private int m_Uid = 0;
        private RectTransform m_MenuParent;
        private UnityEngine.Vector2 tempV2;
        private Camera m_MainCamera;
        private Vector3 m_PosFollowOffset;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            m_UserData = userData as UITownMoveFormParam;
            m_Uid = m_UserData.TargetUid;
            m_MenuParent = m_rectMenu.parent.GetComponent<RectTransform>();
            m_MainCamera = GameEntry.Camera.CurUseCamera;
            m_Target = null;

            m_PosFollowOffset = new Vector3(0.5f, 0, 0);
            
            GameEntry.HUD.HideHUDGroup();
            
            m_rectMenu.localScale = Vector3.zero;
            m_rectMenu.DOScale(Vector3.one, 0.2f);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            GameEntry.HUD.ShowHUDGroup();
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnYesClick()
        {   
            GameEntry.WorldMap.MoveTownConfirmed();
            Close();
        }

        private void OnBtnNoClick()
        {
            GameEntry.WorldMap.MoveTownCanceled();
            Close();
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);
            if (!m_Target)
            {
                if (m_Uid != 0)
                {
                    var entity = Game.GameEntry.Entity.GetEntity(m_Uid);
                    if (entity)
                    {
                        m_Target = entity.Logic as EL_TownMovePreview;
                    }
                }
            }
            else
            {
                var screenPoint =
                    RectTransformUtility.WorldToScreenPoint(m_MainCamera,
                        m_Target.transform.position + m_PosFollowOffset);
                RectTransformUtility.ScreenPointToLocalPointInRectangle(m_MenuParent, screenPoint,
                    GameEntry.Camera.UICamera, out tempV2);
                m_rectMenu.anchoredPosition = tempV2;

                bool canUse = m_Target.IsGridCanPut();
                if (m_btnYes.enabled != canUse)
                {
                    m_btnYes.enabled = canUse;
                    m_btnYes.SetButtonGray(!canUse);
                }
            }
        }
    }
}
