using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using DG.Tweening;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public partial class UITradeTruckPlunderForm : UGuiFormEx
    {
        UITruckItem curSelectedTruck;
        UITrainItem curSelectedTrain;

        CanvasGroup truckInfo;
        CanvasGroup truckDesc;
        CanvasGroup trainInfo;
        CanvasGroup trainDesc;
        readonly float fadeDuration = 0.2f;

        bool isShowDesc;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            truckInfo = m_goTruckInfo.GetComponent<CanvasGroup>();
            truckDesc = m_goTruckDesc.GetComponent<CanvasGroup>();
            trainInfo = m_goTrainInfo.GetComponent<CanvasGroup>();
            trainDesc = m_goTrainDesc.GetComponent<CanvasGroup>();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is UITruckItem truck)
            {
                RefreshTruck(truck);
            }
            else if (userData is UITrainItem train)
            {
                RefreshTrain(train);
            }
            isShowDesc = false;

            RefreshPanel();

            m_txtTruckDesc.text = ToolScriptExtend.GetLang(1223);
            ToolScriptExtend.AdjustLineSpacing(m_txtTruckDesc);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            RefreshPanel();
        }

        private void OnBtnTipClick()
        {
            if (curSelectedTruck != null)
            {
                isShowDesc = !isShowDesc;
                truckInfo.DOComplete();
                truckDesc.DOComplete();

                if (isShowDesc)
                {
                    truckInfo.DOFade(0f, fadeDuration).OnComplete(() =>
                    {
                        m_goTruckInfo.SetActive(false);
                        m_goTruckDesc.SetActive(true);
                        truckDesc.alpha = 0f;
                        truckDesc.DOFade(1f, fadeDuration);
                    });
                }
                else
                {
                    truckDesc.DOFade(0f, fadeDuration).OnComplete(() =>
                    {
                        m_goTruckDesc.SetActive(false);
                        m_goTruckInfo.SetActive(true);
                        truckInfo.alpha = 0f;
                        truckInfo.DOFade(1f, fadeDuration);
                    });
                }

                m_goTipDesc.SetActive(!isShowDesc);
                m_goTipBack.SetActive(isShowDesc);
            }
            else if (curSelectedTrain != null)
            {
                isShowDesc = !isShowDesc;
                trainInfo.DOComplete();
                trainDesc.DOComplete();
                if (isShowDesc)
                {
                    m_btnTeamPower.isEnable = false;
                    trainInfo.DOFade(0f, fadeDuration).OnComplete(() =>
                    {
                        m_goTrainInfo.SetActive(false);
                        m_goTrainDesc.SetActive(true);
                        trainDesc.alpha = 0f;
                        trainDesc.DOFade(1f, fadeDuration);
                    });
                }
                else
                {
                    m_btnTeamPower.isEnable = true;
                    trainDesc.DOFade(0f, fadeDuration).OnComplete(() =>
                    {
                        m_goTrainDesc.SetActive(false);
                        m_goTrainInfo.SetActive(true);
                        trainInfo.alpha = 0f;
                        trainInfo.DOFade(1f, fadeDuration);
                    });
                }

                m_goTipDesc.SetActive(!isShowDesc);
                m_goTipBack.SetActive(isShowDesc);
            }
        }

        private void OnBtnShareClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = "功能暂未开放",
            });
        }

        private void OnBtnCollectClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = "功能暂未开放",
            });
        }

        private void OnBtnTruckDetailsClick()
        {
            Close();
            GameEntry.TradeTruckData.RequestTruckDetail(curSelectedTruck.truckData.Id, (result) =>
            {
                ColorLog.Pink("货车详情", result);
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckDetailForm, new UITradeTruckDetailFormParams()
                {
                    type = TruckDetailType.Plunder,
                    truckDetail = result
                });
            });
        }

        private void OnBtnTrainDetailsClick()
        {
            Close(true);
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainDetailForm, curSelectedTrain);
        }

        private void OnBtnPlunderClick()
        {
            if (curSelectedTruck != null)
            {
                if (curSelectedTruck.truckData.RobTimes >= 2)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = "每辆货车最多被劫掠2次！",
                    });
                    return;
                }

                if (GameEntry.TradeTruckData.TruckPlunderTodayCount >= 4)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        Content = "今日已达掠夺次数限制！",
                    });
                    return;
                }

                ColorLog.Pink("掠夺货车", curSelectedTruck.truckData);
                GameEntry.TradeTruckData.RequestTruckRob(curSelectedTruck.truckData.Id, (result) =>
                {
                    ColorLog.Pink("掠夺货车回调", result);
                    // 打开领奖界面
                    List<reward> rewards = new();
                    foreach (var item in result.Article)
                    {
                        rewards.Add(new reward()
                        {
                            item_id = (itemid)item.Code,
                            num = item.Amount
                        });
                    }
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);

                    GameEntry.TradeTruckData.RequestTruckDetail(curSelectedTruck.truckData.Id, (result) =>
                    {
                        ColorLog.Pink("货车详情", result);
                        curSelectedTruck.truckData = result.CargoTransport;
                        GameEntry.UI.RefreshUIForm(EnumUIForm.UITradeTruckPlunderForm);
                    });
                });
            }
            else if (curSelectedTrain != null)
            {
                Close(true);
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainBattlePlanForm);
            }
        }

        private void OnBtnTeamPowerClick()
        {
            OnBtnTipClick();
        }

        void RefreshTruck(UITruckItem truck)
        {
            m_rectBg.anchoredPosition = new Vector2(0f, -112f);
            m_txtTitle.text = ToolScriptExtend.GetLang(1217);
            curSelectedTruck = truck;
            curSelectedTrain = null;
            m_goTruckInfo.SetActive(true);
            truckInfo.alpha = 1f;
            m_goTruckDesc.SetActive(false);
            m_goTrainInfo.SetActive(false);
            m_goTrainDesc.SetActive(false);
            m_goTruckItem.SetActive(true);
            m_goTrainItem.SetActive(false);
            UITruckItem truckItem = m_goTruckItem.GetComponent<UITruckItem>();
            truckItem.isOther = true;
            truckItem.quality = truck.quality;
            truckItem.Refresh();
            m_imgQuality.SetImage(ToolScriptExtend.GetQualityIcon(truck.quality), true);

            if (truck.isOther)
            {
                m_goPlunder.SetActive(true);
            }
            else
            {
                m_goPlunder.SetActive(false);
            }
        }

        void RefreshTrain(UITrainItem train)
        {
            m_rectBg.anchoredPosition = new Vector2(0f, -272f);
            m_txtTitle.text = ToolScriptExtend.GetLang(1222);
            curSelectedTruck = null;
            curSelectedTrain = train;
            m_goTruckInfo.SetActive(false);
            m_goTruckDesc.SetActive(false);
            m_goTrainInfo.SetActive(true);
            trainInfo.alpha = 1f;
            m_goTrainDesc.SetActive(false);
            m_goTruckItem.SetActive(false);
            m_goTrainItem.SetActive(true);
            m_imgQuality.SetImage(ToolScriptExtend.GetQualityIcon(5), true);

            if (train.isOther)
            {
                m_goPlunder.SetActive(true);
            }
            else
            {
                m_goPlunder.SetActive(false);
            }
        }

        void RefreshPanel()
        {
            if (curSelectedTruck != null)
            {
                int robTimes = curSelectedTruck.truckData.RobTimes;
                if (robTimes >= 2)
                {
                    robTimes = 2;
                    ColorUtility.TryParseHtmlString("#f53d3d", out Color color);
                    m_txtTruckPlunderCount.color = color;
                }
                else
                {
                    ColorUtility.TryParseHtmlString("#0d9b49", out Color color);
                    m_txtTruckPlunderCount.color = color;
                }
                m_txtTruckPlunderCount.text = $"{robTimes}/2";
                int todayCount = GameEntry.TradeTruckData.TruckPlunderTodayCount;
                m_txtTruckPlunderTodayCount.text = ToolScriptExtend.GetLang(1115) + $"{todayCount}/4";

                if (curSelectedTruck.truckData.Boxcar.Count > 0)
                {
                    List<Trade.TradeGoods> rewards = new(curSelectedTruck.truckData.Boxcar[0].Goods);
                    List<Trade.TradeGoods> filter = new();
                    for (int i = 0; i < rewards.Count; i++)
                    {
                        if (!rewards[i].Rob)
                        {
                            filter.Add(rewards[i]);
                        }
                    }
                    RefreshReward(filter, m_transContentRewardTruck, m_transRewardTruck);
                }
            }
            else if (curSelectedTrain != null)
            {
                int todayCount = GameEntry.TradeTruckData.TruckPlunderTodayCount;
                m_txtTrainPlunderTodayCount.text = ToolScriptExtend.GetLang(1115) + $"{todayCount}/4";
            }
        }

        void RefreshReward(List<Trade.TradeGoods> rewards, Transform m_transContentReward, Transform m_transReward)
        {
            foreach (Transform item in m_transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            rewards.Sort((a, b) => a.Rob.CompareTo(b.Rob));

            for (int i = 0; i < rewards.Count; i++)
            {
                bool rob = rewards[i].Rob;
                if (i < m_transContentReward.childCount)
                {
                    m_transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = m_transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                    uiItemModule.plunder.SetActive(rob);
                }
                else
                {
                    Transform item = Instantiate(m_transReward, m_transContentReward);
                    BagManager.CreatItem(item, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.plunder.SetActive(rob);
                    });
                }
            }
        }
    }
}
