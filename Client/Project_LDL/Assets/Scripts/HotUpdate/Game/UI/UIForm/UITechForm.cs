using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using System.Linq;
using DG.Tweening;
using Game.Hotfix.Config;
using UnityEngine.Rendering;

namespace Game.Hotfix
{
    public partial class UITechForm : UGuiFormEx
    {

        private List<GameObject> m_techItems = new List<GameObject>();
        private Dictionary<int, TechItemData> m_techDataDict = new Dictionary<int, TechItemData>();
        public Dictionary<int, TechDetailParamsModel> curShowPanelList = new Dictionary<int, TechDetailParamsModel>();
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            // 初始化科技数据
            InitTechData();

            // 生成科技项
            GenerateTechItems();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            // 刷新科技项显示
            RefreshTechItems();
        }

        private void OnBtnCloseClick()
        {
            GameEntry.UI.CloseUIForm(this);
        }
        private void OnBtnReturnClick()
        {
            m_goDetailPanel.SetActive(false);
        }
        private void OnBtnTestClick()
        {
           curShowPanelList.Values.ToList().ForEach(x => x.ClearLine());
        }
        /// <summary>
        /// 初始化科技数据
        /// </summary>
        private void InitTechData()
        {
            m_techDataDict.Clear();

            // 获取科技类型配置
            List<tech_type> techTypes = GameEntry.LDLTable.GetTable<tech_type>();
            if (techTypes == null || techTypes.Count == 0)
            {
                Debug.LogError("[UITechForm] 获取科技类型配置失败");
                return;
            }

            // 获取科技等级配置
            List<tech_config> techLevels = GameEntry.LDLTable.GetTable<tech_config>();
            if (techLevels == null || techLevels.Count == 0)
            {
                Debug.LogError("[UITechForm] 获取科技等级配置失败");
                return;
            }

            //获取玩家科技数据
            //Dictionary<int, int> playerTechLevels = GameEntry.LogicData.TechData.GetAllTechLevels();

            //初始化科技数据
            foreach (var techType in techTypes)
            {
                // 获取玩家当前科技等级
                int currentLevel = 0;
                //playerTechLevels.TryGetValue(techType.id, out currentLevel);

                // 获取科技最大等级
                int maxLevel = 10;//techLevels.Where(l => l.tech_id == techType.id).Max(l => l.level);

                // 获取当前等级的配置
                //tech_config currentLevelConfig = techLevels.FirstOrDefault(l => l.tech_id == techType.id && l.level == currentLevel);

                // 获取下一级的配置
                //tech_config nextLevelConfig = techLevels.FirstOrDefault(l => l.tech_id == techType.id && l.level == currentLevel + 1);

                // 创建科技数据
                TechItemData techData = new TechItemData
                {
                    Id = techType.id,
                    type = techType.id,
                    Name = ToolScriptExtend.GetLang(techType.tech_type_title),
                    Icon = techType.tech_type_picture,
                    CurrentLevel = currentLevel,
                    MaxLevel = maxLevel,
                    // Description = ToolScriptExtend.GetLang(techType.desc_lang),
                    IsLocked = false, //techType.unlock_level > GameEntry.LogicData.RoleData.Level,
                    // UnlockLevel = techType.unlock_level,
                    // CurrentEffect = currentLevelConfig != null ? ToolScriptExtend.GetLang(currentLevelConfig.effect_lang) : "",
                    // NextEffect = nextLevelConfig != null ? ToolScriptExtend.GetLang(nextLevelConfig.effect_lang) : "",
                    // UpgradeCost = nextLevelConfig != null ? nextLevelConfig.cost : null
                };

                m_techDataDict.Add(techType.id, techData);
            }
        }

        /// <summary>
        /// 生成科技项
        /// </summary>
        private void GenerateTechItems()
        {
            // 清空现有的科技项
            foreach (var item in m_techItems)
            {
                Destroy(item);
            }
            m_techItems.Clear();

            // 获取content容器
            Transform contentTransform = m_scrollview.transform.Find("view/content");
            if (contentTransform == null)
            {
                Debug.LogError("[UITechForm] 找不到content容器");
                return;
            }

            // 按照ID排序
            var sortedTechData = m_techDataDict.Values.OrderBy(t => t.Id).ToList();

            // 生成新的科技项
            foreach (var techData in sortedTechData)
            {
                GameObject techItem = Instantiate(m_goItem, contentTransform);
                techItem.SetActive(true);

                // 设置科技项数据
                UpdateTechItem(techItem, techData);

                m_techItems.Add(techItem);
            }

            // 更新布局
            LayoutRebuilder.ForceRebuildLayoutImmediate(contentTransform.GetComponent<RectTransform>());
        }

        /// <summary>
        /// 刷新科技项显示
        /// </summary>
        private void RefreshTechItems()
        {
            // 更新科技数据
            InitTechData();

            // 更新科技项显示
            for (int i = 0; i < m_techItems.Count; i++)
            {
                var techData = m_techDataDict.Values.OrderBy(t => t.Id).ElementAt(i);
                UpdateTechItem(m_techItems[i], techData);
            }
        }

        /// <summary>
        /// 更新单个科技项
        /// </summary>
        private void UpdateTechItem(GameObject techItem, TechItemData techData)
        {
            // 根据提供的预制体结构获取组件
            Image bgImage = techItem.transform.Find("bg").GetComponent<Image>();
            Image iconImage = techItem.transform.Find("icon").GetComponent<Image>();
            Text nameText = techItem.transform.Find("name").GetComponent<Text>();
            Text pre = techItem.transform.Find("pre").GetComponent<Text>();
            GameObject caidaiObj = techItem.transform.Find("caidai").gameObject;
            Text txtCaidai = techItem.transform.Find("caidai/txtcaidai")?.GetComponent<Text>();
            GameObject tuijian = techItem.transform.Find("tuijian")?.gameObject;
            GameObject line = techItem.transform.Find("line")?.gameObject;

            // 设置图标
            iconImage.SetImage(techData.Icon);

            // 设置名称
            nameText.text = techData.Name;

            // 设置进度文本
            if (pre != null)
            {
                float progress = techData.MaxLevel > 0 ? (float)techData.CurrentLevel / techData.MaxLevel * 100 : 0;
                pre.text = $"{progress:0}%";
            }

            // 设置锁定状态
            bool isLocked = techData.IsLocked;
            bool isMaxLevel = techData.CurrentLevel >= techData.MaxLevel;

            // 设置背景
            if (isLocked)
            {
                // 锁定状态
                bgImage.color = new Color(0.7f, 0.7f, 0.7f, 1f); // 灰色


                // 设置解锁文本
                if (txtCaidai != null)
                {
                    txtCaidai.text = $"Lv.{techData.UnlockLevel} Unlock";
                }
            }
            else if (isMaxLevel)
            {
                // 最大等级
                bgImage.color = Color.white;


                // 设置最大等级文本
                if (txtCaidai != null)
                {
                    txtCaidai.text = "MAX";
                }
            }
            else
            {
                // 正常状态
                bgImage.color = Color.white;


                // 设置开发中文本
                if (txtCaidai != null)
                {
                    txtCaidai.text = "Development";
                }
            }

            // 设置彩带显示（用于特殊标记，如推荐升级）
            bool showCaidai = !isLocked && !isMaxLevel && techData.Id == GetRecommendTechId();
            caidaiObj.SetActive(showCaidai);

            // 添加点击事件
            Button itemButton = techItem.GetComponent<Button>();
            if (itemButton == null)
            {
                itemButton = techItem.AddComponent<Button>();
            }

            itemButton.onClick.RemoveAllListeners();
            itemButton.onClick.AddListener(() => OnTechItemClick(techData));
        }

        /// <summary>
        /// 获取推荐升级的科技ID
        /// </summary>
        private int GetRecommendTechId()
        {
            // 这里可以根据游戏逻辑实现推荐算法
            // 简单示例：返回第一个未达到最大等级且已解锁的科技
            foreach (var tech in m_techDataDict.Values.OrderBy(t => t.Id))
            {
                if (!tech.IsLocked && tech.CurrentLevel < tech.MaxLevel)
                {
                    return tech.Id;
                }
            }
            return 0; // 没有推荐
        }

        /// <summary>
        /// 科技项点击事件
        /// </summary>
        private void OnTechItemClick(TechItemData techData)
        {

            // if (techData.IsLocked)
            // {
            //     // 显示未解锁提示
            //     GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            //     {
            //         Content = $"Unlock at player level {techData.UnlockLevel}"
            //     });
            //     return;
            // }

            // if (techData.CurrentLevel >= techData.MaxLevel)
            // {
            //     // 显示已达最大等级提示
            //     GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            //     {
            //         Content = "Already at max level"
            //     });
            //     return;
            // }

            // 打开科技详情界面
            OpenTechDetailUI(techData);
        }

        /// <summary>
        /// 打开科技详情界面
        /// </summary>
        private void OpenTechDetailUI(TechItemData techData)
        {
            // 这里应该打开科技详情界面，显示详细信息和升级按钮
            // 示例代码，实际实现需要根据游戏UI系统调整
            // GameEntry.UI.OpenUIForm(EnumUIForm.UITechDetailForm, new TechDetailParams()
            // {
            //     TechId = techData.Id,
            //     Name = techData.Name,
            //     Icon = techData.Icon,
            //     Description = techData.Description,
            //     CurrentLevel = techData.CurrentLevel,
            //     MaxLevel = techData.MaxLevel,
            //     CurrentEffect = techData.CurrentEffect,
            //     NextEffect = techData.NextEffect,
            //     UpgradeCost = techData.UpgradeCost,
            //     OnUpgradeSuccess = RefreshTechItems
            // });
            curShowPanelList.Clear();
            m_scrollviewDetail.verticalNormalizedPosition = 1;
            var count = GameEntry.LogicData.TechData.GetTypeLine(techData.Id);
            for (int i = 0; i < m_transDetailContent.childCount; i++)
            {
                var obj = m_transDetailContent.GetChild(i).gameObject;
                obj.SetActive(false);
            }
            for (int i = 0; i < count; i++)
            {
                if (i >= m_transDetailContent.childCount)
                {
                    var obj = Instantiate(m_goDetailItem, m_transDetailContent);
                    TechDetailParamsModel model = new TechDetailParamsModel();
                    model.Line = i;
                    model.obj = obj;
                    if (!curShowPanelList.ContainsKey(i))
                    {
                        curShowPanelList.Add(i, model);
                    }
                    model.type = techData.type;
                    model.Init();
                    model.OnRefresh();
                }
                else
                {
                    var obj = m_transDetailContent.GetChild(i).gameObject;
                    TechDetailParamsModel model = new TechDetailParamsModel();
                    model.obj = obj;
                    if (!curShowPanelList.ContainsKey(i))
                    {
                        curShowPanelList.Add(i, model);
                    }
                    model.SetData(techData.type, i);
                    model.Init();
                    model.OnRefresh();
                    m_transDetailContent.GetChild(i).gameObject.SetActive(true);
                }

            }
            //刷新线条
            OnRefreshLine();

            //Debug.LogError(count);
            m_goDetailPanel.SetActive(true);
        }
      
        public void OnRefreshLine()
        {
            curShowPanelList.Values.ToList().ForEach(x => x.ClearLine());
            foreach (var item in curShowPanelList)
            {
                item.Value.LineToNext(curShowPanelList);
            }

        }
    }
    
    /// <summary>
    /// 一层科技链
    /// </summary>
    public class TechItemData
    {
        public int Id { get; set; }
        public int type { get; set; }
        public string Name { get; set; }
        public string Icon { get; set; }
        public int CurrentLevel { get; set; }
        public int MaxLevel { get; set; }
        public string Description { get; set; }
        public bool IsLocked { get; set; }
        public int UnlockLevel { get; set; }
        public string CurrentEffect { get; set; }
        public string NextEffect { get; set; }
        //public List<item_produce> UpgradeCost { get; set; }
    }

    /// <summary>
    /// 二层科技详情参数
    /// </summary>
    public class TechDetailParamsModel
    {
        public int Line { get; set; }
        public GameObject obj { get; set; }
        public int type { get; set; }
        //public List<item_produce> UpgradeCost { get; set; }
        public System.Action OnUpgradeSuccess { get; set; }
        public TechSingleModel leftItem { get; set; }
        public TechSingleModel rightItem { get; set; }
        public TechSingleModel midItem { get; set; }
        public Transform underMid { get; set; }
        public Transform underLeft { get; set; }
        public Transform underRight { get; set; }
        public void SetLeftItem(TechSingleModel model)
        {
            leftItem = model;
        }
        public void ClearLine()
        {
            if (underLeft != null)
            {
                for (int i = 0; i < underLeft.childCount; i++)
                {
                    var child = underLeft.GetChild(i);
                    child.gameObject.SetActive(false);
                }
            }
            if (underRight != null)
            {
                for (int i = 0; i < underRight.childCount; i++)
                {
                    var child = underRight.GetChild(i);
                    child.gameObject.SetActive(false);
                }
            }
            if (underMid != null)
            {
                for (int i = 0; i < underMid.childCount; i++)
                {
                    var child = underMid.GetChild(i);
                    child.gameObject.SetActive(false);
                }
            }
        }
        public void ShowLine(List<GameObject> lis, int TechId)
        {

        }
        public void SetRightItem(TechSingleModel model)
        {
            rightItem = model;
        }
        public void SetMidItem(TechSingleModel model)
        {
            midItem = model;
        }
        public void SetData(int _type, int line)
        {
            Line = line;
            type = _type;
        }
        public void Init()
        {
            underMid = obj.transform.Find("underMid");
            underLeft = obj.transform.Find("underLeft");
            underRight = obj.transform.Find("underRight");
        }
        public void OnRefresh()
        {
            var data = GameEntry.LogicData.TechData.GetTechShowConfig(type, Line + 1);

            if (data == null) return;
            leftItem = data.leftItem;
            rightItem = data.rightItem;
            midItem = data.midItem;
            if (obj != null)
            {
                underMid = obj.transform.Find("underMid");
                underLeft = obj.transform.Find("underLeft");
                underRight = obj.transform.Find("underRight");

                Transform leftObj = obj.transform.Find("itemLeft");
                Transform rightObj = obj.transform.Find("itemRight");
                Transform midObj = obj.transform.Find("itemMid");
                UIButton leftBtn = leftObj.Find("bg").GetComponent<UIButton>();
                UIButton rightBtn = rightObj.Find("bg").GetComponent<UIButton>();
                UIButton midBtn = midObj.Find("bg").GetComponent<UIButton>();
                leftBtn.onClick.RemoveAllListeners();
                rightBtn.onClick.RemoveAllListeners();
                midBtn.onClick.RemoveAllListeners();
                leftBtn.onClick.AddListener(() =>
                {
                    if (leftItem != null)
                    {
                        OnClickSingleTech(leftItem.techId);
                    }
                });
                rightBtn.onClick.AddListener(() =>
                {
                    if (rightItem != null)
                    {
                        OnClickSingleTech(rightItem.techId);
                    }
                });
                midBtn.onClick.AddListener(() =>
                {
                    if (midItem != null)
                    {
                        OnClickSingleTech(midItem.techId);
                    }
                });
                leftObj.gameObject.SetActive(leftItem != null);
                rightObj.gameObject.SetActive(rightItem != null);
                midObj.gameObject.SetActive(midItem != null);
                if (leftItem != null) leftItem.OnRefresh2(leftObj.gameObject);
                if (rightItem != null) rightItem.OnRefresh2(rightObj.gameObject);
                if (midItem != null) midItem.OnRefresh2(midObj.gameObject);
            }
        }
        public void OnClickSingleTech(int techId)
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITechUpDetailForm,techId);
        }
        public void LineToNext(Dictionary<int, TechDetailParamsModel> dic)
        {
            List<int> targetList = new List<int>();
            int curPos = 0;
            for (int i = 1; i <= 3; i++)
            {
                int TechID = 0;
                switch (i)
                {
                    case 1:
                        TechID = leftItem?.techId ?? 0;
                        break;
                    case 2:
                        TechID = midItem?.techId ?? 0;
                        break;
                    case 3:
                        TechID = rightItem?.techId ?? 0;
                        break;
                    default:
                        break;
                }
                if (TechID > 0)
                {
                    var config = GameEntry.LogicData.TechData.GetTechShowByTechId(TechID);
                    if (config != null)
                    {
                        curPos = int.Parse(config.offset.Split("|")[1]);
                        Line = int.Parse(config.offset.Split("|")[0]);
                        string demand_tech_id = config.demand_tech_id;

                        if (!string.IsNullOrEmpty(demand_tech_id))
                        {
                            var arr = demand_tech_id.Split('|');
                            for (int ii = 0; ii < arr.Length; ii++)
                            {
                                int targetTechId = int.Parse(arr[ii]);
                                targetList.Add(targetTechId);
                            }
                        }
                        else
                        {
                            continue;
                        }
                        //Debug.LogError(Line);
                        switch (curPos)
                        {
                            case 1:
                                for (int i1 = 0; i1 < targetList.Count; i1++)
                                {
                                    int targetLine = GameEntry.LogicData.TechData.GetTechLineByTechId(targetList[i1]);
                                    //相邻
                                    if (targetLine == Line + 1)
                                    {
                                        int targetPos = GameEntry.LogicData.TechData.GetTechPosByTechId(targetList[i1]);
                                        string name = "normal" + (curPos - targetPos).ToString();
                                        var showLine = underLeft.transform.Find(name);
                                        showLine.gameObject.SetActive(true);

                                        //Debug.LogError(name);
                                    }
                                    else //不相邻
                                    {

                                    }

                                }
                                break;
                            case 2:
                                for (int i2 = 0; i2 < targetList.Count; i2++)
                                {
                                    int targetLine = GameEntry.LogicData.TechData.GetTechLineByTechId(targetList[i2]);
                                    //相邻
                                    if (targetLine == Line + 1)
                                    {
                                        int targetPos = GameEntry.LogicData.TechData.GetTechPosByTechId(targetList[i2]);
                                        string name = "normal" + (curPos - targetPos).ToString();
                                        var showLine = underMid.transform.Find(name);
                                        showLine.gameObject.SetActive(true);
                                        //Debug.LogError(name);
                                    }
                                    else //不相邻
                                    {

                                    }

                                }
                                break;
                            case 3:
                                for (int i3 = 0; i3 < targetList.Count; i3++)
                                {
                                    int targetLine = GameEntry.LogicData.TechData.GetTechLineByTechId(targetList[i3]);
                                    //相邻
                                    if (targetLine == Line + 1)
                                    {
                                        int targetPos = GameEntry.LogicData.TechData.GetTechPosByTechId(targetList[i3]);
                                        string name = "normal" + (curPos - targetPos).ToString();
                                        var showLine = underRight.transform.Find(name);
                                        showLine.gameObject.SetActive(true);
                                        //Debug.LogError(name);
                                    }
                                    else //不相邻
                                    {

                                    }

                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }
    }
    //单个科技
    public class TechSingleModel
    {
        public int techId { get; set; }
        public string icon { get; set; }
        public int currentLevel { get; set; }
        public int maxLevel { get; set; }
        public bool isLocked { get; set; }
        public TechSingleModel(int techId)
        {
            this.techId = techId;
        }
        public void OnRefresh2(GameObject obj)
        {
            if (techId == 0) return;
            UIText progress = obj.transform.Find("progress").GetComponent<UIText>();
            if (progress != null)
            {
                progress.text = techId + "";
            }
        }
    }
    
}
