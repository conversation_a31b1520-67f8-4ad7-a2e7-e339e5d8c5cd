using Game.Hotfix.Config;
using GameFramework;
using GameFramework.Resource;
using Mosframe;
using UnityEngine;

namespace Game.Hotfix
{
    public class UITeamFormHero : IReference
    {
        public int HeroId => (int)m_HeroModule.id;
        public hero_services Services => m_HeroModule.Services;
        public EnumBattlePos BattlePos => m_BattlePos;
        public ulong Power => m_HeroModule.power;

        private EnumBattlePos m_BattlePos;

        private HeroModule m_HeroModule;
        private Transform m_ParentTrans;

        private GameObject m_heroGo;

        public UITeamFormHero()
        {
        }

        public void Init(int heroId, EnumBattlePos battlePos, Transform parentTrans)
        {
            m_HeroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroId);
            m_BattlePos = battlePos;

            m_ParentTrans = parentTrans;
            Load();
        }

        public void UnInit()
        {
            if (m_heroGo != null)
            {
                GameObject.Destroy(m_heroGo);
                m_heroGo = null;
            }

            m_HeroModule = null;
            m_ParentTrans = null;
        }

        private void Load()
        {
            var battleRole = GameEntry.LDLTable.GetTableById<battle_role>(HeroId);

            if (m_heroGo != null)
            {
                GameObject.Destroy(m_heroGo);
                m_heroGo = null;
            }

            GameEntry.Resource.LoadAsset(battleRole.res_location, new LoadAssetCallbacks(
                (assetName, asset, duration, userData) =>
                {
                    if (m_ParentTrans != null)
                    {
                        var prefab = asset as GameObject;
                        m_heroGo = GameObject.Instantiate(prefab, m_ParentTrans);
                        m_heroGo.setLayer(6);    
                    }
                }));
        }

        public Vector3 GetPosition()
        {
            return Vector3.zero;
        }

        public void SetPosition(Vector3 worldPos)
        {
            m_heroGo.transform.position = worldPos;
        }

        public void MoveBack()
        {
        }

        public void MoveTo(EnumBattlePos pos)
        {
        }

        public void OnDestroy()
        {
        }


        public void Clear()
        {
        }
    }
}