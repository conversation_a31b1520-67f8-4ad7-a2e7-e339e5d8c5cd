using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITechForm : UGuiFormEx
    {
        [SerializeField] private Button m_btnReturn;
        [SerializeField] private Button m_btnClose;

        [SerializeField] private ScrollRect m_scrollviewDetail;
        [SerializeField] private ScrollRect m_scrollview;

        [SerializeField] private GameObject m_goDetailPanel;
        [SerializeField] private GameObject m_goItem;
        [SerializeField] private GameObject m_goLine1;
        [SerializeField] private GameObject m_goLine2;
        [SerializeField] private GameObject m_goDetailItem;

        void InitBind()
        {
            m_btnReturn.onClick.AddListener(OnBtnReturnClick);
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
        }
    }
}
