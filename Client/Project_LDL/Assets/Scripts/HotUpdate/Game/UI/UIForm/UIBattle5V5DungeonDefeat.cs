using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBattle5V5DungeonDefeat : UGuiFormEx
    {
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitToggle();
            HideDefault();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            
            if (GameEntry.LogicData.Battle5v5Data.CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var param = GameEntry.LogicData.Battle5v5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                if (param != null)
                {
                    m_txtCheckPoint.text = "关卡 " + param.DungeonId;
                }
            }
            
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {
            Close();
            GameEntry.LogicData.Battle5v5Data.GoBackMainCity();
        }

        private void OnBtnContinueClick()
        {
            Close();
            GameEntry.LogicData.Battle5v5Data.GoBackMainCity();
        }

        void InitToggle()
        {
            m_togStrengthen.onValueChanged.AddListener((isOn) =>
            {
                m_scrollviewStrengthen.gameObject.SetActive(isOn);
                m_scrollviewHeroData.gameObject.SetActive(!isOn);
            });

            m_togOutput.onValueChanged.AddListener((isOn) =>
            {
                m_scrollviewStrengthen.gameObject.SetActive(!isOn);
                m_scrollviewHeroData.gameObject.SetActive(isOn);
            });

            m_togBear.onValueChanged.AddListener((isOn) =>
            {
                m_scrollviewStrengthen.gameObject.SetActive(!isOn);
                m_scrollviewHeroData.gameObject.SetActive(isOn);
            });
        }

        void HideDefault()
        {
            m_scrollviewHeroData.gameObject.SetActive(false);
        }
    }
}
