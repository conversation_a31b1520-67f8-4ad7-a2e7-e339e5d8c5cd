using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.Events;

namespace Game.Hotfix
{
    public partial class UIGeneralBuyForm : UGuiFormEx
    {
        private int buyCount;
        private int minCount;
        private int maxCount;
        private int unitPrice;
        private itemid coinId;
        
        private int sumCount;
        private int curIndex;
        private UnityAction<int> callback;//外部逻辑回调
        
        private int goodsId;

        private storetype curStoreType;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            
            InitBind();
            
            m_sliderSum.onValueChanged.AddListener((value) =>
            {
                buyCount = (int)value;
                UpdateBuyInfo();
            });
            
            m_inputSum.onEndEdit.AddListener((str) =>
            {
                if (int.TryParse(str, out var value))
                {
                    if (IsSatisfy(value) && value != buyCount)
                    {
                        buyCount = value;
                        UpdateBuyInfo();
                        return;
                    }
                }
                m_inputSum.text = buyCount.ToString();
            });
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            goodsId = (int)userData;
            var goodsConfig = GameEntry.LogicData.GeneralShopData.GetGoodsConfigById(goodsId);
            if (goodsConfig == null) return;
            
            var productId = goodsConfig.goods.item_id;
            var productNum = goodsConfig.goods.num;
            coinId = goodsConfig.store_cost.item_id;
            unitPrice = (int)goodsConfig.store_cost.num;
            maxCount = goodsConfig.purchase_number;
            m_txtName.text = ToolScriptExtend.GetItemName(productId);
            curStoreType = goodsConfig.store_type;
            if (maxCount == 0)
            {
                //可无限购买
                maxCount = 99;
            }
            
            buyCount = 1;
            minCount = 1;
            m_sliderSum.minValue = minCount;
            m_sliderSum.maxValue = maxCount;
            
            m_sliderSum.value = 1;
            UpdateBuyInfo();
            ToolScriptExtend.ClearAllChild(m_transNode);
            m_transNode.localScale = Vector3.one * 0.8f;
            BagManager.CreatItem(m_transNode,productId,productNum, (module) =>
            {
                ToolScriptExtend.SetItemObjTxtScale(module.gameObject,1.3f);
            });
        }
        
        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnBuyClick()
        {
            var enough = GameEntry.LogicData.GeneralShopData.CheckCoinEnough(coinId, GetSumCost(), out var hex);
            if (enough)
            {
                GameEntry.LogicData.GeneralShopData.C2SShopBuyGoods(goodsId,buyCount, (msg) =>
                {
                    List<reward> rewards = new();
                    foreach (var item in msg.Rewards)
                    {
                        rewards.Add(new reward()
                        {
                            item_id = (itemid)item.Code,
                            num = item.Amount
                        });
                    }                
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);   
                    Close();
                });
            }
            else
            {
                if (curStoreType == storetype.storetype_expedition || curStoreType == storetype.storetype_season)
                {
                    //远征和赛季商店，点击提示飘字
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                    {
                        //道具不足
                        Content = ToolScriptExtend.GetLang(10100)
                    });
                }
                else
                {
                    //钻石、Vip商店、同盟商店、荣誉商店，点击不足弹出获取途径
                    //道具不够，打开道具获取途径界面
                    ItemModule itemModule = new(coinId);
                    GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(unitPrice));
                }
            }
        }

        private void OnBtnDownClick()
        {
            var temp = buyCount - 1;
            if (!IsSatisfy(temp)) return;
            buyCount--;
            UpdateBuyInfo();
        }

        private void OnBtnUpClick()
        {
            var temp = buyCount + 1;
            if (!IsSatisfy(temp)) return;
            buyCount++;
            UpdateBuyInfo();
        }

        private void OnBtnSumClick()
        {
            var result = 1;
            var ownCount = GameEntry.LogicData.BagData.GetAmountById(coinId);
            for (var i = maxCount; i >= 1; i--)
            {
                var needCount = unitPrice * i;
                if (ownCount >= needCount)
                {
                    result = i;
                    break;
                }
            }
            
            if (buyCount == result) return;
            buyCount = result;
            UpdateBuyInfo();
        }

        //判断是否符合要求
        private bool IsSatisfy(int value)
        {
            return value >= minCount && value <= maxCount;
        }
        
        private void UpdateBuyInfo()
        {
            var sumCost = GetSumCost();
            m_txtUnit.text = ToolScriptExtend.FormatNumberWithUnit(unitPrice);
            
            m_inputSum.text = buyCount.ToString();
            m_sliderSum.value = buyCount;

            GameEntry.LogicData.GeneralShopData.CheckCoinEnough(coinId, sumCost, out var hex);
            m_txtSum.text =  $"<color={hex}>{ToolScriptExtend.FormatNumberWithUnit(sumCost)}</color>";
            m_txtPrice.text = $"<color={hex}>{ToolScriptExtend.FormatNumberWithUnit(sumCost)}</color>";
            m_imgCoin.SetImage(ToolScriptExtend.GetItemIcon(coinId));
        }

        private int GetSumCost()
        {
            return unitPrice * buyCount;
        }
        
    }
}
