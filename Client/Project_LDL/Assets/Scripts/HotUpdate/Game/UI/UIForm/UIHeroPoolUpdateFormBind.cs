using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIHeroPoolUpdateForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnBack;
        [SerializeField] private UIButton m_btnType;
        [SerializeField] private UIButton m_btnAttr;
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnSwitch;

        [SerializeField] private UIText m_txtName;
        [SerializeField] private UIText m_txtDesc;
        [SerializeField] private UIText m_txtCheckTip;
        [SerializeField] private UIText m_txtLevelTip;

        [SerializeField] private UIImage m_imgIcon;
        [SerializeField] private UIImage m_imgQuality;

        [SerializeField] private ScrollRect m_scrollviewTag;

        [SerializeField] private GameObject m_goContent;
        [SerializeField] private Transform m_transSkillRoot;
        [SerializeField] private GameObject m_goTagItem;
        [Serial<PERSON>Field] private GameObject m_goSkillItem;
        [SerializeField] private GameObject m_goTagRoot;

        void InitBind()
        {
            m_btnBack.onClick.AddListener(OnBtnBackClick);
            m_btnType.onClick.AddListener(OnBtnTypeClick);
            m_btnAttr.onClick.AddListener(OnBtnAttrClick);
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnSwitch.onClick.AddListener(OnBtnSwitchClick);
        }
    }
}
