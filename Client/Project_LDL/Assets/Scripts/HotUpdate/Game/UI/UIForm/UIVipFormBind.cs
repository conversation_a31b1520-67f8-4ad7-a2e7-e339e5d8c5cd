using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIVipForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnExit;
        [SerializeField] private UIButton m_btnStore;
        [SerializeField] private UIButton m_btnVipPoint;
        [SerializeField] private UIButton m_btnFree;
        [SerializeField] private UIButton m_btnUnlock;
        [SerializeField] private UIButton m_btnLeft;
        [SerializeField] private UIButton m_btnRight;

        [SerializeField] private UIText m_txtTitleDesc;
        [SerializeField] private UIText m_txtVip2;
        [SerializeField] private UIText m_txtVip1;
        [SerializeField] private UIText m_txtProgress;
        [SerializeField] private UIText m_txtType;

        [SerializeField] private ScrollRect m_scrollviewList;

        [SerializeField] private Slider m_sliderProgress;

        [SerializeField] private GameObject m_goGetEffect;
        [SerializeField] private GameObject m_goFreeOff;
        [SerializeField] private GameObject m_goFreeOn;
        [SerializeField] private GameObject m_goColserEffect;
        [SerializeField] private GameObject m_goUnlockDot;
        [SerializeField] private GameObject m_goFreeGift;
        [SerializeField] private GameObject m_goExclusiveGift;
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goPriviligeItem;
        [SerializeField] private GameObject m_goRewardItem;

        void InitBind()
        {
            m_btnExit.onClick.AddListener(OnBtnExitClick);
            m_btnStore.onClick.AddListener(OnBtnStoreClick);
            m_btnVipPoint.onClick.AddListener(OnBtnVipPointClick);
            m_btnFree.onClick.AddListener(OnBtnFreeClick);
            m_btnUnlock.onClick.AddListener(OnBtnUnlockClick);
            m_btnLeft.onClick.AddListener(OnBtnLeftClick);
            m_btnRight.onClick.AddListener(OnBtnRightClick);
        }
    }
}
