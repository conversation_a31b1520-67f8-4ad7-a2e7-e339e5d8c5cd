using System;
using System.Collections;
using System.Collections.Generic;
using Activity;
using Game.Hotfix.Config;
using GameFramework.Event;
using Unity.Plastic.Newtonsoft.Json;
using UnityEngine;
using UnityEngine.UI;
using Object = System.Object;

namespace Game.Hotfix
{
    public partial class UIChaoZhiActivityForm : UGuiFormEx
    {
        private UISwitchPage switchPage;
        private Dictionary<int, string> prefabList;
        
        private List<ActivityTime> activityList;
        private ChaoZhiData Manager;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            Manager = GameEntry.LogicData.ChaoZhiData;
            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            var targetIndex = 0;
            
            if (prefabList == null)
            {
                prefabList = new Dictionary<int, string>();
            }
            else
            {
                prefabList.Clear();
            }
            
            var list = new List<ValueTuple<int,string, string>>();//唯一id，名称，预制体
            activityList = Manager.GetActivityList();
            
            var useList = new List<ActivityTime>();
            foreach (var activity in activityList)
            {
                var uniqueId = (int)activity.Template;
                var config = Manager.GetActivityConfig(uniqueId);
                if (config != null)
                {
                    var prefab = Manager.GetTemplatePrefab(uniqueId);
                    list.Add((uniqueId,ToolScriptExtend.GetLang(config.name),prefab));
                    useList.Add(activity);
                }
            }
            
            switchPage = m_goSwitchPage.GetComponent<UISwitchPage>();
            for (var i = 0; i < useList.Count; i++)
            {
                switchPage.BindDataByIndex(i,useList[i]);
            }
            switchPage.OnInit(m_goTagItem, m_goTagRoot.transform, m_goPanelRoot.transform, list, OnSelectLogic);
            switchPage.SwitchTagGroup.ScrollToFirst();
           
            switchPage.SelectPageByIndex(targetIndex);
            switchPage.UseTimer = true;
            
            GameEntry.Event.Subscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            prefabList.Clear();
            GameEntry.Event.Unsubscribe(HeroChangeEventArgs.EventId, OnHeroUpdate);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            switchPage.OnRefresh(userData);
        }

        private void OnBtnExitClick()
        {
             Close();
        }
        
        private void OnSelectLogic(int index)
        {
            
            
        }
        
        private void OnHeroUpdate(object sender, GameEventArgs e)
        {
            switchPage.OnRefresh(("ChaoZhi_RoleUpStar",1));
        }
        
    }
}
