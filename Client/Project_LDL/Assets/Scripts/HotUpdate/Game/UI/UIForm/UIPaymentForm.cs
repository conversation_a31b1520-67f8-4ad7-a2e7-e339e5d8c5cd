using System.Collections.Generic;
using System;

namespace Game.Hotfix
{
    public partial class UIPaymentForm : UGuiFormEx
    {
        public float delayTime = 0.2f;
        float curTime;
        bool isFinished = false;
        readonly Queue<PaymentParams> paymentParamsQueue = new();
        Action onClickConfirm;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is PaymentParams param)
            {
                // 设置提示文本
                m_txtTip.text = param.Tip;
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            if (userData is PaymentParams param)
            {
                paymentParamsQueue.Enqueue(param);
            }
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            if (curTime > delayTime)
            {
                curTime = 0f;
                if (isFinished)
                {
                    isFinished = false;
                    paymentParamsQueue.Clear();
                    Close();
                }

                if (paymentParamsQueue.Count > 0)
                {
                    PaymentParams param = paymentParamsQueue.Dequeue();
                    m_txtTip.text = param.Tip;

                    // 设置弹出框的显示和隐藏
                    m_goContainer.SetActive(param.ShowPopup);

                    // 设置回调函数和用户数据
                    onClickConfirm = param.OnClickConfirm;

                    if (param.IsFinished)
                    {
                        isFinished = true;
                    }
                }
            }
            else
            {
                curTime += elapseSeconds;
            }
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnConfirmClick()
        {
            onClickConfirm?.Invoke();
            m_goContainer.SetActive(false);
            m_txtTip.text = "支付中...";
        }

        private void OnBtnCancelClick()
        {
            Close();
            m_goContainer.SetActive(false);
            m_txtTip.text = "支付取消";
        }
    }
}
