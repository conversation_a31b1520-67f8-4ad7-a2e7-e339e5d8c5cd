using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Game.Hotfix.Config;
using Google.Protobuf.Collections;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Logical;
using Sirenix.Utilities;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionCreateForm : UGuiFormEx
    {
        private int minCountName;
        private int maxCountName;
        private int minCountShortName;
        private int maxCountShortName;
        private int costId;
        private int costNum;

        private int selectIdx = -1;
        private int flagId;
        private int langType;
        private ulong recommendId;
        private float deltaTime = 0;

        private string nameStr;
        private string checkNameStr;
        private bool isCheckName;
        private string nextCheckName;

        private string shortNameStr;
        private string checkShortNameStr;
        private bool isCheckShortName;
        private string nextCheckShortName;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            var config = GameEntry.LogicData.UnionData.GetUnionConst(3);
            minCountName = config != null ? int.Parse(config[0]) : 0;
            maxCountName = config != null ? int.Parse(config[1]) : 0;

            config = GameEntry.LogicData.UnionData.GetUnionConst(4);
            minCountShortName = config != null ? int.Parse(config[0]) : 0;
            maxCountShortName = config != null ? int.Parse(config[1]) : 0;

            config = GameEntry.LogicData.UnionData.GetUnionConst(1);
            costId = config != null ? int.Parse(config[0]) : 0;
            costNum = config != null ? int.Parse(config[1]) : 0;
            m_imgCost.SetImage(ToolScriptExtend.GetItemIcon(costId));

            var count = m_transTogList.childCount;
            for (int i = 0; i < count; i++)
            {
                var toggle = m_transTogList.GetChild(i).GetComponent<UIToggle>();
                var index = i;
                toggle.onValueChanged.AddListener((isOn) =>
                {
                    if (isOn)
                    {
                        OnSelectTog(index);
                    }
                });
            }

            m_inputName.onValueChanged.AddListener(OnUpdateName);
            m_inputShortName.onValueChanged.AddListener(OnUpdateShortName);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            flagId = 1;
            langType = GameEntry.RoleData.Lang;

            nameStr = "nameStr";
            checkNameStr = "";
            isCheckName = false;
            nextCheckName = "";

            shortNameStr = "shortNameStr";
            checkShortNameStr = "";
            isCheckShortName = false;
            nextCheckShortName = "";

            m_inputName.text = "";
            m_inputShortName.text = "";

            var index = userData != null ? (int)userData : 0;
            var toggle = m_transTogList.GetChild(index).GetComponent<UIToggle>();
            toggle.isOn = true;
            OnSelectTog(index);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            var param = (UnionParams)userData;
            if (param.ShowType == 1)
            {
                flagId = param.IntValue;
                OnUpdateCreate();
            }
            else if (param.ShowType == 2)
            {
                langType = param.IntValue;
                OnUpdateCreate();
            }
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            deltaTime += elapseSeconds;
            if (deltaTime >= 0.2f)
            {
                deltaTime = 0;
                
                if (!isCheckName && !nextCheckName.IsNullOrWhitespace())
                {
                    OnUpdateName(nextCheckName);
                    nextCheckName = "";
                }

                if (!isCheckShortName && !nextCheckShortName.IsNullOrWhitespace())
                {
                    OnUpdateShortName(nextCheckShortName);
                    nextCheckShortName = "";
                }
            }
        }

        private void OnSelectTog(int index)
        {
            if (selectIdx != index)
            {
                var rectTrans = m_transTogList.GetChild(index).GetComponent<RectTransform>();
                rectTrans.sizeDelta = new Vector2(463, 96);

                if (selectIdx >= 0)
                {
                    rectTrans = m_transTogList.GetChild(selectIdx).GetComponent<RectTransform>();
                    rectTrans.sizeDelta = new Vector2(402, 90);
                }
            }

            selectIdx = index;
            m_goJoin.SetActive(selectIdx == 0);
            m_goCreate.SetActive(selectIdx == 1);

            if (selectIdx == 0) { OnUpdateJoin(); }
            else { OnUpdateCreate(); }
        }

        private void OnUpdateJoin()
        {
            GameEntry.LogicData.UnionData.OnReqUnionList((resp) =>
            {
                recommendId = resp.RecommendUnionId;
                OnUpdateList(resp.ShortInfo);
            });
        }

        private void OnUpdateList(RepeatedField<Union.UnionShortInfo> list)
        {
            var count = list.Count;
            m_TableViewV.GetItemCount = () => count;
            m_TableViewV.GetItemGo = () => m_goItem;
            m_TableViewV.UpdateItemCell = (index, go) =>
            {
                var rootTrans = go.transform;
                var flagSp = rootTrans.Find("flagBg/flagSp").GetComponent<UIImage>();
                var flagBtn = flagSp.transform.GetComponent<UIButton>();
                var recommendSp = rootTrans.Find("recommendSp");
                var nameTxt = rootTrans.Find("nameTxt").GetComponent<UIText>();
                var langTxt = rootTrans.Find("langTxt").GetComponent<UIText>();
                var memberTxt = rootTrans.Find("memberTxt").GetComponent<UIText>();
                var powerTxt = rootTrans.Find("powerTxt").GetComponent<UIText>();
                var limitTxt = rootTrans.Find("limitTxt").GetComponent<UIText>();
                var joinBtn = rootTrans.Find("joinBtn").GetComponent<UIButton>();
                var btnBg = rootTrans.Find("joinBtn/btnBg").GetComponent<UIImage>();
                var btnTxt = rootTrans.Find("joinBtn/btnTxt").GetComponent<UIText>();

                var data = list[index];
                nameTxt.text = $"[{data.ShortName}]{data.Name}";
                langTxt.text = ToolScriptExtend.GetLangTypeName(data.Lang);
                memberTxt.text = $"{data.MemberNum}/{GameEntry.LogicData.UnionData.GetMaxMember()}";
                powerTxt.text = ToolScriptExtend.FormatNumberWithUnit(data.Power);
                flagSp.SetImage(GameEntry.LogicData.UnionData.GetFlagImgPath(data.Flag));
                recommendSp.gameObject.SetActive(recommendId == data.UnionId);

                var isCanJoin = true;
                var conditions = data.Conditions;
                string showStr = "";
                foreach (var item in conditions)
                {
                    var isMeet = true;
                    if (item.Type == 1) // 战力
                    {
                        isMeet = item.Value <= (long)GameEntry.RoleData.Power;
                        if (item.Value > 0)
                        {
                            var colorStr = isMeet ? "009c04" : "ff3535";
                            var tipStr = ToolScriptExtend.GetLang(80000074) + $"<color=#{colorStr}>{item.Value}</color>";
                            if (showStr.IsNullOrWhitespace()) { showStr = tipStr; }
                            else { showStr = tipStr + "\n" + showStr; }
                        }
                    }
                    else // 总部等级
                    {
                        var buildingModule = GameEntry.LogicData.BuildingData.FindBuildingById(101);
                        isMeet = item.Value <= (buildingModule?.LEVEL ?? 0);
                        if (item.Value > 0)
                        {
                            var colorStr = isMeet ? "009c04" : "ff3535";
                            var tipStr = ToolScriptExtend.GetLang(80000075) + $"<color=#{colorStr}>{item.Value}</color>";
                            if (showStr.IsNullOrWhitespace()) { showStr = tipStr; }
                            else { showStr += "\n" + tipStr; }
                        }
                    }

                    if (!isMeet)
                        isCanJoin = isMeet;
                }
                limitTxt.text = showStr;

                var isApply = data.Status == 0;
                btnBg.SetImage(isApply ? "Sprite/ui_public/button2.png" : "Sprite/ui_public/button3.png");
                btnTxt.text = isApply ? ToolScriptExtend.GetLang(80000070) : ToolScriptExtend.GetLang(80000071);

                joinBtn.onClick.RemoveAllListeners();
                joinBtn.onClick.AddListener(() =>
                {
                    if (isApply)
                    {
                        GameEntry.LogicData.UnionData.OnReqUnionJoinApply(data.UnionId, (UnionId) =>
                        {
                            if (data.UnionId == UnionId)
                            {
                                isApply = false;
                                btnBg.SetImage("Sprite/ui_public/button3.png");
                                btnTxt.text = ToolScriptExtend.GetLang(80000071);
                            }
                        });
                    }
                });

                limitTxt.gameObject.SetActive(!isCanJoin);
                joinBtn.gameObject.SetActive(isCanJoin);

                flagBtn.onClick.RemoveAllListeners();
                flagBtn.onClick.AddListener(() =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionInfoForm, data.UnionId);
                });
            };
            m_TableViewV.InitTableViewByIndex(0);

            m_goNone.SetActive(count <= 0);
        }

        private void OnUpdateCreate()
        {
            m_imgFlag.SetImage(GameEntry.LogicData.UnionData.GetFlagImgPath(flagId));
            m_txtLanguage.text = ToolScriptExtend.GetLangTypeName(langType);

            var hasNum = GameEntry.LogicData.BagData.GetAmountById((itemid)costId);
            var colorStr = hasNum >= costNum ? "00ff00" : "FF1717";
            m_txtCost.text = $"<color=#{colorStr}>{costNum}</color>";

            OnUpdateName(m_inputName.text);
            OnUpdateShortName(m_inputShortName.text);
        }

        private void OnUpdateName(string showStr)
        {
            if (nameStr == showStr) return;
            if (isCheckName)
            {
                nextCheckName = showStr;
                return;
            }
            nameStr = showStr;

            var length = ToolScriptExtend.GetStrLength(showStr);
            m_txtNum.text = $"{length}/{maxCountName}";

            var isMeet = length >= minCountName && length <= maxCountName;
            if (isMeet)
            {
                isCheckName = true;
                GameEntry.LogicData.UnionData.OnReqUnionCheckName(showStr, 1, (resp) =>
                {
                    var state = resp.Code; // 0-正常，1-重复，2-非法
                    if (state == 0)
                    {
                        checkNameStr = "";
                        m_imgNameState.SetImage("Assets/ResPackage/Sprite/ui_public/icon_tishi_yes.png");
                    }
                    else
                    {
                        if (state == 1) { checkNameStr = ToolScriptExtend.GetLang(80000090); }
                        else { checkNameStr = ToolScriptExtend.GetLang(80000091); }
                        m_imgNameState.SetImage("Assets/ResPackage/Sprite/ui_public/icon_tishi_no.png");
                    }
                    m_txtNameState.text = checkNameStr;
                    isCheckName = false;
                });
            }
            else
            {
                checkNameStr = ToolScriptExtend.GetLang(80000087);
                m_txtNameState.text = checkNameStr;
                m_imgNameState.SetImage("Assets/ResPackage/Sprite/ui_public/icon_tishi_no.png");
            }
        }

        private void OnUpdateShortName(string showStr)
        {
            if (shortNameStr == showStr) return;
            if (isCheckShortName)
            {
                nextCheckShortName = showStr;
                return;
            }
            shortNameStr = showStr;

            var length = ToolScriptExtend.GetStrLength(showStr);
            var isMatch = Regex.IsMatch(showStr, @"^[a-zA-Z0-9]*$");
            var isMeet = length >= minCountShortName && length <= maxCountShortName && isMatch;
            if (isMeet)
            {
                isCheckShortName = true;
                GameEntry.LogicData.UnionData.OnReqUnionCheckName(showStr, 2, (resp) =>
                {
                    var state = resp.Code; // 0-正常，1-重复，2-非法
                    if (state == 0)
                    {
                        checkShortNameStr = "";
                        m_imgShortNameState.SetImage("Assets/ResPackage/Sprite/ui_public/icon_tishi_yes.png");
                    }
                    else
                    {
                        if (state == 1) { checkShortNameStr = ToolScriptExtend.GetLang(80000090); }
                        else { checkShortNameStr = ToolScriptExtend.GetLang(80000091); }
                        m_imgShortNameState.SetImage("Assets/ResPackage/Sprite/ui_public/icon_tishi_no.png");
                    }
                    m_txtShortNameState.text = checkShortNameStr;
                    isCheckShortName = false;
                });
            }
            else
            {
                checkShortNameStr = ToolScriptExtend.GetLang(80000089);
                m_txtShortNameState.text = checkShortNameStr;
                m_imgShortNameState.SetImage("Assets/ResPackage/Sprite/ui_public/icon_tishi_no.png");
            }
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnSearchClick()
        {
            GameEntry.LogicData.UnionData.OnReqUnionSearch(m_inputSearch.text, (resp) =>
            {
                OnUpdateList(resp.List);
            });
        }

        private void OnBtnJoinClick()
        {
            GameEntry.LogicData.UnionData.OnReqUnionOneKeyJoin(recommendId, () =>
            {
                Close();
            });
        }

        private void OnBtnRankClick()
        {
            // 打开排行榜
        }

        private void OnBtnChangeClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionFlagForm, flagId);
        }

        private void OnBtnShortNameClick()
        {
            GameEntry.LogicData.UnionData.OnReqUnionRandomShortName((shortName) =>
            {
                m_inputShortName.text = shortName;
                OnUpdateShortName(shortName);
            });
        }

        private void OnBtnLanguageClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIUnionLanguageForm, new UnionParams
            {
                ShowType = 1,
                IntValue = langType
            });
        }

        private void OnBtnCreateClick()
        {
            var nameStr = m_inputName.text;
            if (nameStr.IsNullOrWhitespace())
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1108),
                });
                return;
            }

            var bCheckName = !checkNameStr.IsNullOrWhitespace();
            var checkShortName = !checkShortNameStr.IsNullOrWhitespace();
            if (bCheckName || checkShortName)
            {
                var tipStr = bCheckName ? checkNameStr : checkShortNameStr;
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = tipStr,
                });
                return;
            }

            var hasNum = GameEntry.LogicData.BagData.GetAmountById((itemid)costId);
            if (hasNum < costNum)
            {
                ItemModule itemModule = new((itemid)costId);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(costNum));
                return;
            }

            GameEntry.LogicData.UnionData.OnReqUnionCreate(m_inputName.text, m_inputShortName.text, langType, flagId, () =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1093),
                });
                GameEntry.LogicData.UnionData.CloseAllUnionForm();
                GameEntry.LogicData.UnionData.GoToUnion();
            });
        }
    }
}
