using System.Collections;
using Battle;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class UIBattle5V5FightHeroItem : MonoBehaviour
    {
        private EnumBattlePos m_Pos;

        private UIHeroItem m_HeroItem;
        
        private Transform m_TransHeroItem;
        private UIText m_TxtSkillCd;
        private UIImage m_ImgSkillCd;
        private UIImage m_ImgSkillIcon;
        private UIImage m_ImgBloodBar;

        private BattleFiled m_BattleFiled;
        private BattleHero m_BattleHero;
        private HeroBattleSKill m_HeroBattleSKill;
        private TeamHero m_TeamHero;

        private float m_CurSkillDuration;
        private float m_TotalSkillDuration;

        private long m_CurHp;
        private long m_MaxHP;

        private bool m_IsWorking = false;

        public void Init(EnumBattlePos pos)
        {
            m_Pos = pos;

            m_TransHeroItem = transform.Find("heroItem");
            m_TxtSkillCd = transform.Find("txtSkillCd")?.GetComponent<UIText>();
            m_ImgSkillCd = transform.Find("imgSkillCd")?.GetComponent<UIImage>();
            m_ImgSkillIcon = transform.Find("skillIcon")?.GetComponent<UIImage>();
            m_ImgBloodBar = transform.Find("bloodBar/progress")?.GetComponent<UIImage>();

            m_HeroItem = m_TransHeroItem.GetComponent<UIHeroItem>();
        }

        /// <summary>
        /// 重置外观
        /// </summary>
        public void Reset(BattleFiled battleFiled)
        {
            m_BattleFiled = battleFiled;
            var teamHero = m_BattleFiled.RecordCtrl.GetTeamHero(m_Pos);

            m_IsWorking = teamHero != null;
            gameObject.SetActive(m_IsWorking);

            if (!m_IsWorking)
                return;

            var heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)teamHero.Code);
            UpdateItem(heroModule, teamHero);

            m_BattleHero = m_BattleFiled.TeamCtrl.GetBattleHero(m_Pos);
            m_HeroBattleSKill = m_BattleHero.HeroBattleSkillCtrl.GetMainSkill();

            if (m_HeroBattleSKill is { SkillConfig: { icon: not null } })
                m_ImgSkillIcon.SetImage(m_HeroBattleSKill.SkillConfig.icon);

            SetIsDie(m_BattleHero.IsDie);
        }

        public void SetIsDie(bool isDie)
        {
            m_HeroItem.SetGrey(isDie);
        }
        
        private void Update()
        {
            if (!m_IsWorking)
                return;

            m_HeroBattleSKill?.GetSkillCD(out m_CurSkillDuration, out m_TotalSkillDuration);

            if (m_TotalSkillDuration > 0)
                m_ImgSkillCd.fillAmount = 1-m_CurSkillDuration / m_TotalSkillDuration;
            else
                m_ImgSkillCd.fillAmount = 0;

            if (m_CurSkillDuration >= m_TotalSkillDuration)
                m_TxtSkillCd.text = string.Empty;
            else
                m_TxtSkillCd.text = (m_TotalSkillDuration - m_CurSkillDuration).ToString("F1") + "s";
            
            m_BattleHero?.GetHPState(out m_CurHp, out m_MaxHP);

            if (m_MaxHP > 0)
                m_ImgBloodBar.fillAmount = (float)m_CurHp / m_MaxHP;
            else
                m_ImgBloodBar.fillAmount = 0;
        }

        public void PlaySkillCastEffect()
        {
            // StartCoroutine(PlaySkillCastEffectE());
        }
        
        IEnumerator PlaySkillCastEffectE()
        {
            m_ImgSkillIcon.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.5f);
            m_ImgSkillIcon.gameObject.SetActive(false);
        }
        
        #region 头像Icon 相关

        private void UpdateItem(HeroModule heroVo, TeamHero teamHero)
        {
            m_HeroItem.Refresh(heroVo);
        }

        #endregion
    }
}