using System;
using System.Collections.Generic;
using System.Net;
using Game.Hotfix;
using Game.Hotfix.Config;
using GameFramework.Event;
using JetBrains.Annotations;
using Mosframe;
using UnityEngine;

namespace Game.Hotfix
{
    public class TaskParam{
        public GameObject obj;
        public int index;
    }
    public partial class UITaskForm : UGuiFormEx
    {
        long unixTimestampSeconds = DateTimeOffset.UtcNow.ToUnixTimeSeconds(); 
        public List<UIToggle> tabToggList;
        public TableView[] arrView;
        public GameObject[] arrPanel;
        public GameObject[] arrBox;
        public TableView curViewV;
        public List<task_main> configs;
        public List<task_daily> dailyConfigs;
        public List<Task.Task> curShowData;
        public Dictionary<int,List<Task.Task>> curTaskDic = new Dictionary<int, List<Task.Task>>();
        public int clickTabIndex = 0;
        //public bool firstLoad = true;
        //public int lastTabIndex = -1;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            configs = GameEntry.LDLTable.GetTable<task_main>();
            dailyConfigs = GameEntry.LDLTable.GetTable<task_daily>();
            tabToggList = new List<UIToggle> { m_tog1, m_tog2, m_tog3 };

            arrBox = new GameObject[] { m_goBox1, m_goBox2, m_goBox3, m_goBox4, m_goBox5 };
            arrPanel = new GameObject[] { m_goPanel1, m_goPanel2, m_goPanel3 };
            arrView = new TableView[] { m_TableViewV1, m_TableViewV2, m_TableViewV3 };
            foreach (var item in tabToggList)
            {
                item.onValueChanged.AddListener((isOn) =>
                {
                    if (isOn)
                    {
                        //ckTabIndex;
                        clickTabIndex = item.toggleType;
                        //if(lastTabIndex != clickTabIndex){
                        ClickTab(true);
                        //}                                                                                
                    }
                });
            }

            for (int i = 0; i < arrBox.Length; i++)
            {
                int index = i;
                UIButton btn = arrBox[i].transform.GetComponent<UIButton>();
                if (btn != null)
                {
                    btn.onClick.RemoveAllListeners();
                    btn.onClick.AddListener(() =>
                    {
                        ClickDailyBox(index);
                    });
                }
            }
        }
        public void OnrefreshItem(){
            
        }
        public void ShowBoxTips(int _index){
            //Debug.LogError("展示宝箱tips");
            TaskParam param = new TaskParam(){obj = arrBox[_index],index = _index};
            GameEntry.UI.OpenUIForm(EnumUIForm.UITaskRewardForm,param);
        }
        public void ClickDailyBox(int index){
            var data = GameEntry.LogicData.TaskData;
            int openCount = data.GetTaskBoxCount();
            if(index < openCount){
                ShowBoxTips(index);
            }else
            {
             if(clickTabIndex == 2){
                var taskdatas = GameEntry.LogicData.TaskData.GetTaskListByModule(3);
                int score = 0;
                foreach (var item in taskdatas)
                {
                    if(item.Status == Common.RewardStatus.Received){
                        score += (int)GetTaskConfig(item.Id).task_reward[0].num;
                    }
                }
                var config = dailyConfigs[index];
                if(score >= config.reward_level){
                    //领取
                    data.OnTaskReceiveScoreRewardsReq();
                }
                else{
                    //
                    ShowBoxTips(index);
                }
             }
            }
            //OnTaskReceiveScoreRewardsReq();           
        }
        public void UpdateLine(int index, GameObject itemObj){
            Task.Task data;
            if(index == -1){
                data = curTaskDic[4][0];
            }else
            {
                data = curTaskDic[clickTabIndex+1][index];
            }
            
            var config = GameEntry.LogicData.TaskData.GetTaskMainById(data.Id);
           
            string process = string.Format("({0}/{1})",data.Process,config.task_value[0]);
            UIText title = itemObj.transform.Find("itemDes").GetComponent<UIText>();
            title.text = ToolScriptExtend.GetLang(config.task_desc) + process;
            UIButton btn = itemObj.transform.Find("btn").GetComponent<UIButton>();
            UIImage bg = itemObj.transform.Find("Image").GetComponent<UIImage>();
            Transform rewardNode = itemObj.transform.Find("rewardNode");
            UIText txtBtn = btn.transform.Find("txtBtn").GetComponent<UIText>();
            UIImage imgBtn = btn.transform.GetComponent<UIImage>();
            txtBtn.text = ToolScriptExtend.GetLang(711376);
            if (clickTabIndex == 0)
            {
                UIImage icon = itemObj.transform.Find("icon").GetComponent<UIImage>();
                icon?.SetImage(config.task_icon);
            }
            if(data.Status == Common.RewardStatus.Receivable)
            {
                txtBtn.text = ToolScriptExtend.GetLang(1100273);
                bg.SetImage("Sprite/ui_jingjichang/win2_small_dikuang2_2ziji.png",false);
                imgBtn.SetImage("Sprite/ui_public/button2.png",false);

            }else
            {
                imgBtn.SetImage("Sprite/ui_public/button3.png",false); 
                if(clickTabIndex == 1 || clickTabIndex == 0)
                {
                    bg.SetImage("Sprite/ui_jingjichang/win2_small_dikuang2_2green.png",false);
                }
                else
                {
                    bg.SetImage("Sprite/ui_public/win2_small_dikuang2_2.png",false);
                }
                if(data.Status == Common.RewardStatus.Received){                  
                   txtBtn.text = ToolScriptExtend.GetLang(1100273);
                }
                if(data.Status == Common.RewardStatus.Receivable){
                    txtBtn.text = ToolScriptExtend.GetLang(711376);
                }  
            } 
            btn.gameObject.SetActive(data.Status != Common.RewardStatus.Received);
            
            
             // 先清除所有子物体的显示状态
            for (int i = 0; i < rewardNode.childCount; i++)
            {
                rewardNode.GetChild(i).gameObject.SetActive(false);  
            }
            // 使用现有物体或创建新物体
            for (int i = 0; i < config.task_reward.Count; i++)
            {          
                if(i < rewardNode.childCount)
                {
                    // 使用已有的物体
                    GameObject rewardObj = rewardNode.GetChild(i).gameObject;
                    rewardObj.SetActive(true);
                    
                    UIItemModule uIItemModule = rewardObj.GetComponent<UIItemModule>();
                    if (uIItemModule != null)
                    {
                        uIItemModule.SetData(config.task_reward[i].item_id, config.task_reward[i].num);
                        uIItemModule.DisplayInfo();
                        uIItemModule.GetComponent<UIButton>().useTween = false;
                        uIItemModule.SetClick(()=>
                        {
                            uIItemModule.OpenTips();   
                        }); 
                    }
                }
                else
                {
                    // 只有在没有足够物体时才创建新物体
                    BagManager.CreatItem(rewardNode, config.task_reward[i].item_id, config.task_reward[i].num, (item)=>
                    {                                
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetScale(0.5f);
                        item.SetClick(()=>
                        {
                            item.OpenTips();   
                        });                      
                    }); 
                }
            }
           
            btn.onClick.RemoveAllListeners();
            btn.onClick.AddListener(() =>
            {
                if(data.Status == Common.RewardStatus.Receivable){
                    GameEntry.LogicData.TaskData.OnTaskReceiveReq((PbGameconfig.taskmodule)config.task_module,config.task_id,(List<reward> rewards)=>
                    {
                        foreach (var item in rewards)
                        {
                            if(item.item_id == itemid.itemid_1200002){
                                var form = GameEntry.UI.GetUIForm(EnumUIForm.UIResFlyForm)as UIResFlyForm;
                                Vector2 starPos = form.GetPosByTrans(btn.transform);
                                Vector2 endPos = form.GetPosByTrans(m_txtTotalScore.gameObject.transform);
                                FlyResManager.UIFly(item.item_id,(int)item.num,starPos,endPos);
                            }else{
                                FlyResManager.FlyResByItemId(item.item_id,(int)item.num,btn.transform);
                            }
                           
                        }
                    });
                }else{
                    Debug.LogError("前往");
                }
               
            });

            Transform complete = itemObj.transform.Find("complete");
            if(clickTabIndex == 2){
                complete?.gameObject.SetActive(data.Status == Common.RewardStatus.Received);
            }else{
                complete?.gameObject.SetActive(false);
            }
        }

        private int GetTabCount(int module){
            return curTaskDic[module+1].Count;
        }
        private void ClickTab(bool reload = true)
        {
            // if (!firstLoad)
            // {
            //     firstLoad = false;
            //     return;
            // }
            foreach (var item in tabToggList)
            {
                if (clickTabIndex == item.toggleType)
                {
                    item.isOn = true;
                    break;
                }
            }
            arrView[clickTabIndex].GetItemCount = () => { return GetTabCount(clickTabIndex); };

            if (arrView[clickTabIndex].itemPrototype == null)
            {
                arrView[clickTabIndex].InitTableViewByIndex(0, 12);
            }

            //if(GetTabCount(clickTabIndex)>0){
            if (reload)
            {
                arrView[clickTabIndex].ReloadData();
            }

            //}

            for (int i = 0; i < arrPanel.Length; i++)
            {
                arrPanel[i].SetActive(clickTabIndex == i);
            }

            OnRefreshPanel();
        }
         private void ClickTab2(bool reload = true){
            arrView[clickTabIndex].GetItemCount = () => { return GetTabCount(clickTabIndex);};
           
            //if(GetTabCount(clickTabIndex)>0){
                if(reload){
                    arrView[clickTabIndex].ReloadData();
                }
                
            //}
            
            for (int i = 0; i < arrPanel.Length; i++)
            {
              arrPanel[i].SetActive(clickTabIndex==i);  
            }

            OnRefreshPanel();
        }
        
        public void OnRefreshPanel(){
            if(clickTabIndex == 2){
                var data = GameEntry.LogicData.TaskData.GetTaskListByModule(clickTabIndex+1);
                int score = 0;
                foreach (var item in data)
                {
                    if(item.Status == Common.RewardStatus.Received){
                        score += (int)GetTaskConfig(item.Id).task_reward[0].num;
                    }
                }
                m_txtTotalScore.text = score.ToString();
                int OpenBoxCount = GameEntry.LogicData.TaskData.GetTaskBoxCount();
                for (int i = 0; i < arrBox.Length; i++)
                {
                    int openScore = dailyConfigs[i].reward_level;
                    UIImage imgBox = arrBox[i].transform.Find("imgBox").GetComponent<UIImage>();
                    Transform canOpen = arrBox[i].transform.Find("canOpen").GetComponent<Transform>();
                    Animation anim = arrBox[i].transform.GetComponent<Animation>();

                    if(score>= openScore){
                        if(OpenBoxCount >= i+1){
                            imgBox.SetImage("Sprite/ui_task/task_jindutiao_box2.png");
                            canOpen.gameObject.SetActive(false);
                            anim.Stop();
                        }else{
                            imgBox.SetImage("Sprite/ui_task/task_jindutiao_box1.png");
                            canOpen.gameObject.SetActive(true);
                            anim.Play("m_goBox");
                        }
                    }else{
                        imgBox.SetImage("Sprite/ui_task/task_jindutiao_box1.png");
                        canOpen.gameObject.SetActive(false);
                        anim.Stop();
                    }
                }
                m_goBoxReward.SetActive(OpenBoxCount != arrBox.Length);
                //Debug.LogError(dailyConfigs[dailyConfigs.Count-1].reward_level);
                m_slider.value = (float)score/dailyConfigs[dailyConfigs.Count-1].reward_level;
            }
            if(clickTabIndex == 1){
                UpdateLine(-1,m_goMainItem);
            }
        }
        public task_main GetTaskConfig(int taskId){
            foreach (var item in configs)
            {
                if(item.task_id == taskId){
                    return item;
                }
            }
            return null;
        }
        public bool IsCanScrollTog()
        {
            int sub = 0;
            for (int i = 0; i < m_goTogList.transform.childCount; i++)
            {
                if (m_goTogList.transform.GetChild(i).gameObject.activeSelf == true)
                {
                    sub = sub + 1;
                }
            }
            if (sub <= 2)
            {
                return false;
            }
            return true;
        }
        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            //firstLoad = true;

            clickTabIndex = 0;
            curTaskDic[1] = GameEntry.LogicData.TaskData.GetTaskListByModule(1);
            curTaskDic[2] = GameEntry.LogicData.TaskData.GetTaskListByModule(2);
            curTaskDic[3] = GameEntry.LogicData.TaskData.GetTaskListByModule(3);
            //main task
            curTaskDic[4] = new List<Task.Task>
            {
                GameEntry.LogicData.TaskData.GetMainTask(true)
            };
            m_tog3.gameObject.SetActive(GameEntry.LogicData.TaskData.isOpenDailyTask());
            m_tog1.gameObject.SetActive(GameEntry.LogicData.TaskData.isOpenChapterTask());
            m_scrollview.enabled = IsCanScrollTog();
            for (int i = 0; i < 3; i++)
            {
                arrView[i].GetItemCount = () => { return GetTabCount(clickTabIndex); };
                arrView[i].GetItemGo = () =>
                {
                    switch (i)
                    {
                        case 0:
                            return m_goItem1;
                        case 1:
                            return m_goItem2;
                        case 2:
                            return m_goItem3;
                        default:
                            return m_goItem1;
                    }
                };
                arrView[i].UpdateItemCell = UpdateLine;

                if (arrView[i].itemPrototype == null)
                {
                    arrView[i].InitTableViewByIndex(0, 12);
                }
                else
                {
                    arrView[i].ReloadData();
                }
            }
            JumpToCompletedTaskTab();
            GameEntry.Event.Subscribe(TaskChangeEventArgs.EventId, OnTaskChangeUpdate);
            m_scrollview.horizontalNormalizedPosition = 0;

            DateTime now = DateTime.Now;
            // 获取明天0点的时间
            DateTime tomorrow = now.Date.AddDays(1);
            // 转换为Unix时间戳（秒）
            long timestamp = (long)(tomorrow - new DateTime(1970, 1, 1)).TotalSeconds;
            //1100266
            Timers.Instance.Add("UITaskTimer", 1f, (param) =>
            {
                //Debug.LogError("xxx");
                // Debug.LogError(TimeComponent.Now);
                //TimeHelper.FormatGameTimeWithDays((int)timestamp-(int)TimeComponent.Now);
                //ToolScriptExtend.FormatTime((int)timestamp-(int)TimeComponent.Now)
                m_txtTime.text = ToolScriptExtend.GetLang(1100266) + TimeHelper.FormatGameTimeWithDays((int)timestamp - (int)TimeComponent.Now);
            }, -1);
        }

        void OnTaskChangeUpdate(object sender, GameEventArgs e){
            curTaskDic[1] = GameEntry.LogicData.TaskData.GetTaskListByModule(1);
            curTaskDic[2] = GameEntry.LogicData.TaskData.GetTaskListByModule(2);
            curTaskDic[3] = GameEntry.LogicData.TaskData.GetTaskListByModule(3);
            //main task
            curTaskDic[4] = new List<Task.Task>
            {
                GameEntry.LogicData.TaskData.GetMainTask(true)
            };
            ClickTab2(true);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);

            clickTabIndex=0;
            curTaskDic.Clear();
            GameEntry.Event.Unsubscribe(TaskChangeEventArgs.EventId, OnTaskChangeUpdate);
            Timers.Instance.Remove("UITaskTimer");
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnBoxRewardClick()
        {

        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        /// <summary>
        /// 跳转到有任务完成的页签
        /// </summary>
        public void JumpToCompletedTaskTab()
        {
            // 获取有已完成任务的模块ID
            int completedModule = GameEntry.LogicData.TaskData.GetCompletedTaskModule();

            // 根据模块ID设置页签索引
            for (int i = 0; i < m_goTogList.transform.childCount; i++)
            {
                if (m_goTogList.transform.GetChild(i).gameObject.activeSelf == true)
                {
                    clickTabIndex = i;
                    break;
                }
            }
            switch (completedModule)
            {
                case 1: // 支线任务
                    clickTabIndex = 0;
                    break;
                case 2: // 主线任务
                    clickTabIndex = 1;
                    break;
                case 3: // 每日任务
                    if (m_tog3.gameObject.activeSelf)
                    {
                        clickTabIndex = 2;
                        break;
                    }
                    // 如果每日任务页签未激活，则回退到主线任务
                    clickTabIndex = 1;
                    break;
                default: // 没有已完成的任务，默认显示主线任务
                    //clickTabIndex = 1;
                    break;
            }
            
            // 切换到选定的页签
            ClickTab(true);
        }
    }
}
