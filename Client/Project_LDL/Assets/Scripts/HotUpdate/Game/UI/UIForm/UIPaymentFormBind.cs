using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIPaymentForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnCancel;
        [SerializeField] private UIButton m_btnConfirm;

        [SerializeField] private UIText m_txtTip;
        [SerializeField] private UIText m_txtTitle;
        [SerializeField] private UIText m_txtContent;
        [SerializeField] private UIText m_txtCancel;
        [SerializeField] private UIText m_txtConfirm;

        [SerializeField] private UIImage m_imgMask;

        [SerializeField] private GameObject m_goContainer;

        void InitBind()
        {
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnCancel.onClick.AddListener(OnBtnCancelClick);
            m_btnConfirm.onClick.AddListener(OnBtnConfirmClick);
        }
    }
}
