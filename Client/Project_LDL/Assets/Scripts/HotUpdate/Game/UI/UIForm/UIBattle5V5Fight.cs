using System;
using System.Collections;
using System.Collections.Generic;
using Battle;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class UIBattle5V5FightParam
    {
        public float BattleDelay;

        public UIBattle5V5FightParam(float battleDelay)
        {
            BattleDelay = battleDelay;
        }
    }
    
    public partial class UIBattle5V5Fight : UGuiFormEx
    {
        
        private BattleFiled m_BattleFiled;
        private BattleRecordCtrl m_BattleRecordCtrl;
        private Battle5v5Component m_Battle5V5Component;
        private Dictionary<EnumBattlePos, UIBattle5V5FightHeroItem> m_HeroItems;
        private Dictionary<EnumBattlePos, UIBattle5V5FightHeroBlood> m_HeroBloodBars;

        private List<UIBattle5V5FightHurtNumber> m_HurtNumbers;
        private List<UIBattle5V5FightHurtNumber> m_HurtNumbersPool;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            m_HurtNumbers = new List<UIBattle5V5FightHurtNumber>();
            m_HurtNumbersPool = new List<UIBattle5V5FightHurtNumber>();
            
            m_HeroItems = new Dictionary<EnumBattlePos, UIBattle5V5FightHeroItem>();
            m_HeroItems.Add(EnumBattlePos.PosL1, CreateHeroItem(EnumBattlePos.PosL1,m_goPos1.transform));
            m_HeroItems.Add(EnumBattlePos.PosL2, CreateHeroItem(EnumBattlePos.PosL2,m_goPos2.transform));
            m_HeroItems.Add(EnumBattlePos.PosL3, CreateHeroItem(EnumBattlePos.PosL3,m_goPos3.transform));
            m_HeroItems.Add(EnumBattlePos.PosL4, CreateHeroItem(EnumBattlePos.PosL4,m_goPos4.transform));
            m_HeroItems.Add(EnumBattlePos.PosL5, CreateHeroItem(EnumBattlePos.PosL5,m_goPos5.transform));

            m_HeroBloodBars = new Dictionary<EnumBattlePos, UIBattle5V5FightHeroBlood>();
            foreach (EnumBattlePos pos in Enum.GetValues(typeof(EnumBattlePos)))
            {
                m_HeroBloodBars.Add(pos, CreateBloodBar(pos,m_transHudParent));    
            }
        }

        private UIBattle5V5FightHeroItem CreateHeroItem(EnumBattlePos pos,Transform parentTrans)
        {
            var item = Instantiate(m_transHeroItem,parentTrans);
            item.localPosition = Vector3.zero;
            
            var itemLogic = item.gameObject.GetOrAddComponent<UIBattle5V5FightHeroItem>();
            itemLogic.Init(pos);
            return itemLogic;
        }

        private UIBattle5V5FightHeroBlood CreateBloodBar(EnumBattlePos pos,Transform parentTrans)
        {
            var item = Instantiate(m_transHudBlood, parentTrans);
            item.localPosition = Vector3.zero;
            var itemLogic = item.gameObject.GetOrAddComponent<UIBattle5V5FightHeroBlood>();
            itemLogic.Init(pos);
            return itemLogic;
        }

        private UIBattle5V5FightHurtNumber GetOrCreateHurtNumber()
        {
            if (m_HurtNumbersPool.Count > 0)
            {
                var temp = m_HurtNumbersPool[0];
                m_HurtNumbersPool.Remove(temp);
                
                temp.transform.SetParent(m_transHurtNumParent);
                m_HurtNumbers.Add(temp);
                
                return temp;
            }
            
            var item = Instantiate(m_transHurtNum, m_transHurtNumParent);
            item.localPosition = Vector3.zero;
            var itemLogic = item.gameObject.GetOrAddComponent<UIBattle5V5FightHurtNumber>();
            itemLogic.Init();
            m_HurtNumbers.Add(itemLogic);
            return itemLogic;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            float battleDelay = 0;
            if (userData is UIBattle5V5FightParam param)
                battleDelay = param.BattleDelay;
            
            m_Battle5V5Component = UnityGameFramework.Runtime.GameEntry.GetComponent<Battle5v5Component>();
            m_BattleFiled = m_Battle5V5Component.BattleFiled;
            m_BattleRecordCtrl = m_BattleFiled.RecordCtrl;

            m_BattleFiled.AddEventListener(BattleFiledEvent.OnHeroAttrChange,OnHeroAttrChangeEvent);
            m_BattleFiled.AddEventListener(BattleFiledEvent.OnHeroDie,OnHeroDieEvent);
            
            ResetUI();

            StartCoroutine(BattlePrepare(battleDelay));
        }

        private IEnumerator BattlePrepare(float battleDelay)
        {
            m_transBattleInfo.gameObject.SetActive(true);
            yield return new WaitForSeconds(battleDelay);
            m_transBattleInfo.gameObject.SetActive(false);
        }
        
        private void OnHeroDieEvent(object obj)
        {
            if (obj is EnumBattlePos pos)
            {
                if (m_HeroItems.TryGetValue(pos,out var heroItem))
                {
                    heroItem.SetIsDie(true);
                }
                
                if (m_HeroBloodBars.TryGetValue(pos,out var bloodBarItem))
                {
                    bloodBarItem.SetIsDie(true);
                }
                
            }
        }

        private void OnHeroAttrChangeEvent(object obj)
        {
            if (obj is BattleFiledOnHeroAttrChangeParam param)
            {
                if (param.ChangeType == ChangeType.AttrHp)
                {
                    var type = param.Value > 0
                        ? UIBattle5V5FightHurtNumberType.NormalQueue
                        : UIBattle5V5FightHurtNumberType.NormalDmg;
                    
                    var hurtNumber = GetOrCreateHurtNumber();

                    hurtNumber.Play(m_BattleFiled,type, param.BattlePos, param.Value);
                }
            }
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            m_BattleFiled?.RemoveEventListener(BattleFiledEvent.OnHeroDie,OnHeroDieEvent);
            m_BattleFiled?.RemoveEventListener(BattleFiledEvent.OnHeroAttrChange,OnHeroAttrChangeEvent);
            
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            
            float battleDelay = 0;
            if (userData is UIBattle5V5FightParam param)
                battleDelay = param.BattleDelay;
            
            ResetUI();
            StartCoroutine(BattlePrepare(battleDelay));
        }

        private void Update()
        {
            for (int i = m_HurtNumbers.Count-1; i >=0; i--)
            {
                var hurtNumber = m_HurtNumbers[i];
                if (hurtNumber.IsFinish)
                {
                    m_HurtNumbers.RemoveAt(i);
                    m_HurtNumbersPool.Add(hurtNumber);
                    hurtNumber.transform.SetParent(m_transHurtNumCacheParent);
                }
            }
            
            if (!m_BattleRecordCtrl.Running)
                return;
            m_txtTimer.text =
                TimeHelper.FormatGameTime((int)(m_BattleRecordCtrl.TotalDuration - m_BattleRecordCtrl.Duration), true);
        }

        private void ResetUI()
        {
            foreach (var item in m_HeroItems)
                item.Value.Reset(m_BattleFiled);
            
            foreach (var item in m_HeroBloodBars)
                item.Value.Reset(m_BattleFiled);

            m_txtTimer.text =
                TimeHelper.FormatGameTime((int)(m_BattleRecordCtrl.TotalDuration - m_BattleRecordCtrl.Duration), true);
        }
        
        private void OnBtnSpeedUpClick()
        {
            m_BattleFiled.SkipBattle();
        }

        private void OnBtnAirSupportClick()
        {
            
        }
    }
}
