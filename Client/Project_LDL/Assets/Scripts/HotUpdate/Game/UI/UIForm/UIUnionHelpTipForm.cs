using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUnionHelpTipForm : UGuiFormEx
    {
        private float duration = 0.2f;                // 动画时长
        private float delay = 1f;                     // 消失动画延迟
        
        private float initPos = 290;                        // 初始位置
        private float targetPos = 320;                      // 目标位置
        public float targetScale = 1.03f;                   // 目标缩放
        private float endPos = 380;                         // 结束位置

        private bool isPlay = false;
        private ObjectPool<GameObject> tipObjList;
        private List<Sequence> sequenceList = new();
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            tipObjList = new(() =>
            {
                return Instantiate(m_goTip, transform);
            },
            (go) =>
            {
                go.transform.SetLocalPositionY(initPos);
                go.SetActive(true);
            },
            (go) =>
            {
                go.SetActive(false);
            },
            (go) =>
            {
                Destroy(go);
            });
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            OnShowTip();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            isPlay = false;
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }
        
        protected override void OnRefocus(object userData)
        {
            base.OnRefocus(userData);

            OnShowTip();
        }

        private void OnShowTip()
        {
            if (isPlay) return;

            var list = GameEntry.LogicData.UnionData.helpTipList;
            if (list.Count <= 0)
            {
                Close();
                return;
            }
            
            var tipObj = tipObjList.Get();
            var rectTrans = tipObj.GetComponent<RectTransform>();
            var canvasGroup = tipObj.GetComponent<CanvasGroup>();
            var headBg = tipObj.transform.Find("headBg").GetComponent<UIImage>();
            var headSp = tipObj.transform.Find("headBg/headSp").GetComponent<UIImage>();
            var showTxt = tipObj.transform.Find("showTxt").GetComponent<UIText>();

            var data = list[0];
            GameEntry.LogicData.RoleData.RequestRoleQueryLocalSingle(data.RoleId, (roleBrief) =>
            {
                // headBg.SetImage(roleBrief.HeadBorder);
                // headSp.SetImage(roleBrief.IsCustomAvatar ? roleBrief.HeadSystemAvatar : roleBrief.HeadCustomAvatar);

                var param = data.Args;
                var args0 = param.Count > 0 ? param[0] : 0;
                if (data.Type == Build.QueueType.BuildUpgrade)
                {
                    var buildingCfg = Game.GameEntry.LDLTable.GetTableById<build_config>((int)data.BuildId);
                    showTxt.text = ToolScriptExtend.GetLangFormat(80400013, new Dictionary<string, object>
                    {
                        {"player_name", roleBrief.Name}, {"build_lv", args0}, {"build_name", ToolScriptExtend.GetLang(buildingCfg.name)},
                        {"number_now", data.HelpTimes}, {"number_max", data.MaxHelpTimes}
                    });
                }
                else if (data.Type == Build.QueueType.BuildTech)
                {
                    var techConfig = Game.GameEntry.LDLTable.GetTableById<tech_config>((int)args0);
                    showTxt.text = ToolScriptExtend.GetLangFormat(80400014, new Dictionary<string, object>
                    {
                        {"player_name", roleBrief.Name}, {"tech_name", techConfig.tech_title},
                        {"number_now", data.HelpTimes}, {"number_max", data.MaxHelpTimes}
                    });
                }
                else if (data.Type == Build.QueueType.BuildSoldierTreatment)
                {
                    showTxt.text = ToolScriptExtend.GetLangFormat(80400015, new Dictionary<string, object>
                    {
                        {"player_name", roleBrief.Name}, {"number_now", data.HelpTimes}, {"number_max", data.MaxHelpTimes}
                    });
                }
            });

            // 创建新动画序列
            Sequence sequence = DOTween.Sequence();
            sequenceList.Add(sequence);
            // 飘字出现
            sequence.Insert(0, rectTrans.DOLocalMoveY(targetPos, duration).SetEase(Ease.Linear));
            sequence.Insert(0, canvasGroup.DOFade(1, duration).SetEase(Ease.Linear));
            sequence.Insert(0, rectTrans.DOScale(1f, duration).SetEase(Ease.Linear));
            // 飘字消失
            sequence.Insert(delay, rectTrans.DOLocalMoveY(endPos, duration).SetEase(Ease.Linear));
            sequence.Insert(delay, canvasGroup.DOFade(0.5f, duration).SetEase(Ease.Linear));
            sequence.Insert(delay, rectTrans.DOScale(targetScale, duration).SetEase(Ease.Linear));
            // 动画完成后回收对象
            sequence.OnComplete(() =>
            {
                canvasGroup.alpha = 0.5f;
                sequenceList.Remove(sequence);
                tipObjList.Release(tipObj);

                list.RemoveAt(0);
                OnShowTip();
            });
        }
    }
}
