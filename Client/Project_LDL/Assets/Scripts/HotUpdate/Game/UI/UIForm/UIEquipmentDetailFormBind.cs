using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIEquipmentDetailForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnTip;
        [SerializeField] private UIButton m_btnClose;
        [SerializeField] private UIButton m_btnReplace;
        [SerializeField] private UIButton m_btnStrengthen;
        [SerializeField] private UIButton m_btnUpgrade;
        [SerializeField] private UIButton m_btnPromote;
        [SerializeField] private UIButton m_btnRemove;
        [SerializeField] private UIButton m_btnLeft;
        [SerializeField] private UIButton m_btnRight;

        [SerializeField] private UIText m_txtName;
        [SerializeField] private UIText m_txtLevel;
        [SerializeField] private UIText m_txtPower;
        [SerializeField] private UIText m_txtNoAttributes;
        [SerializeField] private UIText m_txtFactoryLevel;
        [SerializeField] private UIText m_txtLevelTip;

        [SerializeField] private ScrollRect m_scrollviewExtra;

        [SerializeField] private Slider m_sliderFactoryLevel;

        [SerializeField] private Transform m_transEquipment;
        [SerializeField] private Transform m_transBasic;
        [SerializeField] private Transform m_transContentExtra;
        [SerializeField] private Transform m_transCost;
        [SerializeField] private GameObject m_goCost;
        [SerializeField] private GameObject m_goStrengthenRedpoint;
        [SerializeField] private GameObject m_goPromoteRedpoint;
        [SerializeField] private GameObject m_goExtraAttributeItem;

        void InitBind()
        {
            m_btnTip.onClick.AddListener(OnBtnTipClick);
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
            m_btnReplace.onClick.AddListener(OnBtnReplaceClick);
            m_btnStrengthen.onClick.AddListener(OnBtnStrengthenClick);
            m_btnUpgrade.onClick.AddListener(OnBtnUpgradeClick);
            m_btnPromote.onClick.AddListener(OnBtnPromoteClick);
            m_btnRemove.onClick.AddListener(OnBtnRemoveClick);
            m_btnLeft.onClick.AddListener(OnBtnLeftClick);
            m_btnRight.onClick.AddListener(OnBtnRightClick);
        }
    }
}
