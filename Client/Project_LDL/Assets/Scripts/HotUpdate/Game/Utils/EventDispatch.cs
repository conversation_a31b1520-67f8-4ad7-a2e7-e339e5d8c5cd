using System;
using System.Collections.Generic;
using GameFramework;

namespace Game.Hotfix
{
    public delegate void EventCallBack(object obj);
    
    public class EventDispatch<T> where T:struct 
    {

        Dictionary<T, List<EventCallBack>> event_handlers = new Dictionary<T, List<EventCallBack>>();

        public void RegisterEvent(T eventType, EventCallBack eventCallBack)
        {
            if (!event_handlers.ContainsKey(eventType))
            {
                event_handlers.Add(eventType, new List<EventCallBack>());
            }

            List<EventCallBack> handlers = event_handlers[eventType];

            handlers.Add(eventCallBack);
        }

        public void UnRegisterEvent(T eventType, EventCallBack eventHander)
        {
            if (event_handlers.ContainsKey(eventType))
            {
                List<EventCallBack> handlers = event_handlers[eventType];

                for (int i = 0; i < handlers.Count; ++i)
                {
                    if (handlers[i] == eventHander)
                    {
                        handlers.RemoveAt(i);
                        break;
                    }
                }
            }
        }

        public void PostEvent(T eventType, object obj)
        {
            if (event_handlers.ContainsKey(eventType))
            {
                List<EventCallBack> handlers = event_handlers[eventType];

                for (int i = 0; i < handlers.Count; ++i)
                {
                    handlers[i].Invoke(obj);
                }
                //引用回收
                if (obj is IReference reference)
                    ReferencePool.Release(reference);
            }
        }

        public void Destroy()
        {
            event_handlers.Clear();
        }
    }
}