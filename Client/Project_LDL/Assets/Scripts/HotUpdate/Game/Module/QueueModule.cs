using System;
using Build;
using UnityEngine;

namespace Game.Hotfix
{
    public enum QueueState
    {
        Running,
        Finished
    }
    
    public class QueueModule
    {
        protected uint m_QueueUid = 0; //队列Uid
        protected QueueType m_QueueType = QueueType.BuildNil; //队列类型 1:建造队列 2:升级队列 3:资源队列
        protected uint m_BindBuildNo = 0; //绑定某个建筑上面
        protected uint m_Help = 0; //是否帮助 0:未帮助 1:已帮助
        protected ulong m_CreateTime = 0; // 创建时间戳,秒
        protected ulong m_FinishTime = 0; // 完成时间戳,秒
        protected ulong m_AccelerateTime = 0; //加速时间,秒

        protected QueueState curQueueState = QueueState.Running;
        
        public QueueModule(Build.Queue queue)
        {
            m_QueueUid = queue.QueueUID;
            m_QueueType = (QueueType)queue.QueueType;
            m_BindBuildNo = queue.BuildNo;
            m_Help = queue.Help;
            m_CreateTime = queue.CreateTime;
            m_FinishTime = queue.FinishTime;
            m_AccelerateTime = queue.AccelerateTime;

            CurQueueState = GetState();
        }
        
        public virtual void Init()
        {
        }
        
        private QueueState GetState()
        {
            if (CurQueueState == QueueState.Running)
            {
                if (TimeComponent.Now >= (m_FinishTime - AccelerateTime))
                {
                    return QueueState.Finished;
                } 
            }
            return CurQueueState;
        }
        
        public uint QueueUid
        {
            get => m_QueueUid;
            set => m_QueueUid = value;
        }

        public QueueType QueueType
        {
            get => m_QueueType;
            set => m_QueueType = value;
        }

        /// <summary>
        /// 绑定的建筑物
        /// </summary>
        public uint BindBuildNo
        {
            get => m_BindBuildNo;
            set => m_BindBuildNo = value;
        }

        public uint Help
        {
            get => m_Help;
            set => m_Help = value;
        }

        protected ulong CreateTime
        {
            get => m_CreateTime;
            set => m_CreateTime = value;
        }

        protected ulong FinishTime
        {
            get => m_FinishTime;
            set => m_FinishTime = value;
        }

        protected ulong AccelerateTime
        {
            get => m_AccelerateTime;
            set => m_AccelerateTime = value;
        }

        protected QueueState CurQueueState
        {
            get => curQueueState;
            set
            {
                if (curQueueState == value)
                    return;
                curQueueState = value;
                //是否发送事件
            }
        }

        /// <summary>
        /// 每秒更新一次
        /// </summary>
        /// <param name="dt"></param>
        public void UpdatePerSecond(float dt)
        {
            UpdateState();
        }

        public void UpdateState()
        {
            var newState = CurQueueState;
            if (CurQueueState != QueueState.Finished)
            {
                newState = GetState();
            }

            if (CurQueueState != newState)
            {
                CurQueueState = newState;
                OnQueueStateChange();
            }
        }

        public virtual void UpdateInfo(Queue queue)
        {
            m_QueueUid = queue.QueueUID;
            m_QueueType = (QueueType)queue.QueueType;
            m_BindBuildNo = queue.BuildNo;
            m_Help = queue.Help;
            m_CreateTime = queue.CreateTime;
            m_FinishTime = queue.FinishTime;
            m_AccelerateTime = queue.AccelerateTime;

            CurQueueState = QueueState.Running;
            
            UpdatePerSecond(0);
        }
        
        public virtual void OnQueueStateChange()
        {
            
        }

        public void Accelerate(ulong accelerateTime)
        {
            AccelerateTime += accelerateTime;
        }

        public float GetTotalTime()
        {
            return (FinishTime - AccelerateTime) - CreateTime;
        }
        
        public float GetRemainTime()
        {
            var remain = (long)(FinishTime - AccelerateTime) - (long)TimeComponent.Now;
            return Math.Max(0, remain);
        }

        public float GetHasWorkTime()
        {
            return GetTotalTime() - GetRemainTime();
        }
        
        public virtual void UnInit()
        {
        }
        
        /// <summary>
        /// 队列是否完成
        /// </summary>
        /// <returns></returns>
        public bool IsFinish()
        {
            return CurQueueState == QueueState.Finished;
        }

        public BuildingModule GetBindBuilding()
        {
            var buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById(m_BindBuildNo);
            return buildingModule;
        }
    }
}