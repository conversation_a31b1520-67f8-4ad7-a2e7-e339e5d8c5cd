using System.Collections.Generic;
using Game.Hotfix.Config;
using Google.Protobuf.Collections;
using Uav;

namespace Game.Hotfix
{
    public class UAVModule
    {
        private uint m_Id;
        private uint m_Exp;
        private uint m_Skin;
        private int m_SkillStar;
        private Dictionary<uint, uint> m_ComponentDic;
        private Dictionary<componenttype, uint> m_ComponentTypeDic;
        private uav_level m_UavLevelConfig;
        
        public UAVModule(UAV uav)
        {
            m_Id = uav.Code;
            m_Exp = uav.Exp;
            m_Skin = uav.Skin;
            m_ComponentDic = new Dictionary<uint, uint>();
            m_ComponentTypeDic = new Dictionary<componenttype, uint>();
            for (int i = 0; i < uav.Components.Count; i++)
            {
                Component uavComponent = uav.Components[i];
                m_ComponentDic[uavComponent.Code] = uavComponent.Exp;
                component_level levelCfg = GameEntry.LDLTable.GetTableById<component_level>((int)uavComponent.Code);
                if (levelCfg != null)
                {
                    m_ComponentTypeDic[levelCfg.component_type] = uavComponent.Code;
                }
                
            }
            m_UavLevelConfig = GameEntry.LDLTable.GetTableById<uav_level>((int)m_Id);
            m_SkillStar = m_UavLevelConfig?.skill_star ?? 0;
        }

        public void UpdateUAVInfo(UAV uav)
        {
            m_Id = uav.Code;
            m_Exp = uav.Exp;
            m_Skin = uav.Skin;
            for (int i = 0; i < uav.Components.Count; i++)
            {
                Component uavComponent = uav.Components[i];
                m_ComponentDic[uavComponent.Code] = uavComponent.Exp;
            }
            m_UavLevelConfig = GameEntry.LDLTable.GetTableById<uav_level>((int)m_Id);
            m_SkillStar = m_UavLevelConfig.skill_star;
        }        
        
        public void UpdateUAVInfo(uint code,uint exp)
        {
            m_Id = code;
            m_Exp = exp;
            m_UavLevelConfig = GameEntry.LDLTable.GetTableById<uav_level>((int)m_Id);
            m_SkillStar = m_UavLevelConfig.skill_star;
        }        
        
        public void UpdateUAVInfo(RepeatedField<Component> components)
        {
            for (int i = 0; i < components.Count; i++)
            {
                Component uavComponent = components[i];
                m_ComponentDic[uavComponent.Code] = uavComponent.Exp;
                component_level levelCfg = GameEntry.LDLTable.GetTableById<component_level>((int)uavComponent.Code);
                if (levelCfg != null)
                {
                    m_ComponentTypeDic[levelCfg.component_type] = uavComponent.Code;
                }
            }
        }

        public uint Id
        {
            get => m_Id;
            set => m_Id = value;
        }
        
        public uint Exp
        {
            get => m_Exp;
            set => m_Exp = value;
        }
        
        public uint Skin
        {
            get => m_Skin;
            set => m_Skin = value;
        }
        
        public int Level
        {
            get
            {
                return m_UavLevelConfig?.level ?? 0;
            }
        }          
        
        public int SkillStar
        {
            get
            {
                return m_SkillStar;
            }
        }        
        
        public int Stage
        {
            get
            {
                
                if (m_UavLevelConfig != null)
                {
                    return m_UavLevelConfig.stage;
                }
                return 0;
            }
        }

        public long Power
        {
            get
            {
                if (m_UavLevelConfig != null)
                {
                    return m_UavLevelConfig.power;
                }

                return 0;
            }
        }

        public Dictionary<uint, uint> UavComponents => m_ComponentDic;

        /// <summary>
        /// 是否在升级阶段
        /// </summary>
        /// <returns></returns>
        public bool GetIsInUpgradeStage()
        {
            var uavNextLevelcfg = GameEntry.LDLTable.GetTableById<uav_level>((int)m_Id + 1);
            if (uavNextLevelcfg != null)
            {
                return uavNextLevelcfg.stage != 0;
            }

            return false;
        }

        public string GetUAVDisplayAssetPath()
        {
            int skinId = m_Skin != 0 ? (int)m_Skin : 1;
            uav_skin uavSkinConfig = GameEntry.LDLTable.GetTableById<uav_skin>(skinId);
            if (uavSkinConfig != null)
            {
                return uavSkinConfig.model;
            }
            return string.Empty;
        }

        // 根据组件部位获取装备的itemid
        public uint GetComponentByType(componenttype cType)
        {
            if ( m_ComponentTypeDic.ContainsKey(cType))
            {
                return m_ComponentTypeDic[cType];
            }

            return 0;
        }
    }
}