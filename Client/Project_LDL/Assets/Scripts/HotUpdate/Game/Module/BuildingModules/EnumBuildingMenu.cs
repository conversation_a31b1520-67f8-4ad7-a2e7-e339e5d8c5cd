using UnityEngine;

namespace Game.Hotfix
{
    public enum EnumBuildingMenu : int
    {
        Btn1001 = 1001, //关卡战斗
        Btn1002 = 1002, //幸存者
        Btn1003 = 1003, //建筑详情
        Btn1004 = 1004, //城门城防
        Btn1005 = 1005, //增益效果
        Btn1006 = 1006, //总部外观
        Btn1007 = 1007, //新手剧情
        Btn1008 = 1008, //队伍配置
        Btn1009 = 1009, //训练士兵
        Btn1010 = 1010, //校场存兵情况
        Btn1011 = 1011, //医院伤兵情况
        Btn1012 = 1012, //酒馆抽卡
        Btn1013 = 1013, //购买建筑队列
        Btn1014 = 1014, //装备制造
        Btn1015 = 1015, //联盟招募
        Btn1016 = 1016, //联盟战争
        Btn1017 = 1017, //无人机
        Btn1018 = 1018, //荣耀远征
        Btn1019 = 1019, //科技界面
        Btn1020 = 1020, //竞技场
        Btn1021 = 1021, //建筑回收
        Btn1022 = 1022, //荣誉墙
        Btn1023 = 1023, //建筑升级
        Btn1024 = 1024, //升级加速
    }
}