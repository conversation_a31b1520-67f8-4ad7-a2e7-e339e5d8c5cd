using System;
using System.Collections.Generic;
using Game.Hotfix.Config;
using Sirenix.Utilities;
using UnityEngine;

namespace Game.Hotfix
{
    public class HeroModule
    {
        public itemid id;
        public int level;
        public int teamId;
        public int starLv;
        public int honorLv;
        public ulong power;
        public int dropState;

        private Dictionary<PbGameconfig.attributes_type, long> attrDic;
        private List<PbGameconfig.attributes_type> attrTypeList;
        private Dictionary<int, Hero.HeroSkill> skillDic;

        public HeroModule(itemid id)
        {
            this.id = id;
            ClearData();
        }

        public void ClearData()
        {
            level = 1;
            teamId = 0;
            starLv = 0;
            honorLv = 0;
            power = 0;
            dropState = 0;
            isActive = false;
        }

        public void SetData(Hero.Hero hero)
        {
            level = (int)hero.Level;
            teamId = (int)hero.TeamId;
            starLv = (int)hero.StarStage;
            honorLv = (int)hero.HonorLevel;
            power = hero.Power;
            dropState = (int)hero.ShowState;

            var attr = hero.Attr;
            if (attr != null)
            {
                if (attrDic == null)
                {
                    attrDic = new();
                    attrTypeList = new();
                }
                else
                {
                    attrDic.Clear();
                    attrTypeList.Clear();
                }

                foreach (var item in attr.Attrs)
                {
                    attrDic.TryAdd(item.Type, item.Value);
                    attrTypeList.Add(item.Type);
                }
            }

            var skills = hero.Skills;
            if (skills != null)
            {
                if (skillDic == null) { skillDic = new(); }
                else { skillDic.Clear(); }

                foreach (var item in skills)
                {
                    skillDic.TryAdd((int)item.Id, item);

                }
            }
            isActive = true;
        }

        /// <summary>
        /// 英雄是否激活了
        /// </summary>
        private bool isActive = false;
        public bool IsActive
        {
            get { return isActive; }
            set { isActive = value; }
        }

        /// <summary>
        /// 英雄是否能展示，不被屏蔽
        /// </summary>
        private bool isShow = true;
        public bool IsShow
        {
            get
            {
                if (!IsActive && ShowDay > GameEntry.LDLNet.GetOpenServiceDays())
                    return false;

                return isShow;
            }
        }

        /// <summary>
        /// 未激活的英雄是否能被合成
        /// </summary>
        public bool IsCombind
        {
            get
            {
                var chipCount = GameEntry.LogicData.BagData.GetAmountById(Piece);
                return !isActive && chipCount >= Combind;
            }
        }

        /// <summary>
        /// 是否空投状态
        /// </summary>
        public bool IsDrop
        {
            get { return dropState == 2; }
        }

        /// <summary>
        /// 是否在队伍中
        /// </summary>
        public bool IsTeam
        {
            get { return teamId > 0; }
        }

        /// <summary>
        /// 星数
        /// </summary>
        public int StarNum
        {
            get { return (int)Mathf.Floor(starLv / 5); }
        }

        /// <summary>
        /// 星阶
        /// </summary>
        public int StarOrder
        {
            get { return starLv % 5; }
        }

        /// <summary>
        /// 英雄最大等级
        /// </summary>
        private int maxLv = -1;
        public int MaxLv
        {
            get
            {
                if (maxLv == -1)
                {
                    int level = 0;
                    var list = GameEntry.LDLTable.GetTable<hero_level>();
                    foreach (var item in list)
                    {
                        if (item.hero_id == id && level < item.level)
                        {
                            level = item.level;
                        }
                    }
                    maxLv = level;
                }
                return maxLv;
            }
        }

        /// <summary>
        /// 英雄最大星级等级
        /// </summary>
        private int maxStarLv = -1;
        public int MaxStarLv
        {
            get
            {
                if (maxStarLv == -1)
                {
                    int level = 0;
                    var list = GameEntry.LDLTable.GetTable<hero_star>();
                    foreach (var item in list)
                    {
                        if (item.hero_id == id && level < item.star_stage)
                        {
                            level = item.star_stage;
                        }
                    }
                    maxStarLv = level;
                }
                return maxStarLv;
            }
        }

        /// <summary>
        /// 英雄最大荣誉等级
        /// </summary>
        private int maxHonorLv = -1;
        public int MaxHonorLv
        {
            get
            {
                if (maxHonorLv == -1)
                {
                    int level = 0;
                    var list = GameEntry.LDLTable.GetTable<hero_promotion>();
                    foreach (var item in list)
                    {
                        if (item.hero_id == id && level < item.promotion_lv)
                        {
                            level = item.promotion_lv;
                        }
                    }
                    maxHonorLv = level;
                }
                return maxHonorLv;
            }
        }

        /// <summary>
        /// 英雄属性列表
        /// </summary>
        public List<PbGameconfig.attributes_type> AttrTypeList
        {
            get
            {
                return attrTypeList;
            }
        }

        /// <summary>
        /// 根据属性类型获取当前属性值
        /// </summary>
        /// <param name="t"></param>
        /// <returns></returns>
        public long GetAttrByType(PbGameconfig.attributes_type t)
        {
            long value = 0;
            if (IsActive)
            {
                attrDic?.TryGetValue(t, out value);
            }
            else
            {
                var levelConfig = GetHeroLvConfig(MaxLv);
                if (levelConfig != null)
                {
                    if (t == PbGameconfig.attributes_type.Attack) { value = levelConfig.atk; }
                    else if (t == PbGameconfig.attributes_type.Defense) { value = levelConfig.defense; }
                    else if (t == PbGameconfig.attributes_type.Hp) { value = levelConfig.life; }
                    else if (t == PbGameconfig.attributes_type.TroopCapacity) { value = Soldier; }
                }

                var starConfig = GetHeroStarConfig(MaxStarLv);
                if (starConfig != null)
                {
                    if (t == PbGameconfig.attributes_type.Attack) { value += starConfig.atk; }
                    else if (t == PbGameconfig.attributes_type.Defense) { value += starConfig.defense; }
                    else if (t == PbGameconfig.attributes_type.Hp) { value += starConfig.life; }
                }
            }
            return value;
        }

        /// <summary>
        /// 判断当前英雄等级是否达到上限
        /// </summary>
        /// <returns></returns>
        public bool GetLevelLimit()
        {
            var config = GetHeroLvConfig(level);
            if (config != null)
            {
                return !ToolScriptExtend.GetDemandUnlock(config.level_up_demand);
            }
            return false;
        }

        /// <summary>
        /// 获取技能等级
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public int GetSkillLevel(int index)
        {
            var level = 0;
            var group = GetSkillGroup(index);
            if (skillDic != null && group > 0)
            {
                skillDic.TryGetValue(group, out Hero.HeroSkill skillInfo);
                if (skillInfo != null)
                {
                    level = (int)skillInfo.Level;
                }
            }
            return level;
        }

        /// <summary>
        /// 获取技能cd减少比例
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public int GetSkillCdRate(int index)
        {
            var rate = 0;
            if (skillDic != null)
            {
                var group = GetSkillGroup(index);
                skillDic.TryGetValue(group, out Hero.HeroSkill skillInfo);
                if (skillInfo != null)
                {
                    rate = (int)skillInfo.CdRate;
                }
            }
            return rate;
        }

        /// <summary>
        /// 获取技能第一个未解锁的下标
        /// </summary>
        /// <returns></returns>
        public int GetSkillUnlockIdx()
        {
            var index = 0;
            for (int i = 0; i < HeroData.SKILL_COUNT; i++)
            {
                var level = GetSkillLevel(i + 1);
                if (level <= 0)
                {
                    index = i + 1;
                    break;
                }
            }
            return index;
        }


        /// <summary>
        /// 获取技能星数上限
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        private Dictionary<int, int> skillStarDic;
        public int GetSkillStarLimit(int index)
        {
            skillStarDic ??= new();
            skillStarDic.TryGetValue(index, out int starNum);
            if (starNum == 0)
            {
                starNum = -1;
                var config = GameEntry.LDLTable.GetTable<skill_config>();
                var skillGroup = GetSkillGroup(index);
                foreach (var item in config)
                {
                    if (item.skill_type == skilltpye.skilltype_4) break;
                    if (item.skill_group == skillGroup && starNum < item.hero_stars)
                    {
                        starNum = item.hero_stars;
                    }
                }
                skillStarDic.TryAdd(index, starNum);
            }
            starNum = starNum == -1 ? 0 : starNum;
            return starNum;
        }

        /// <summary>
        /// 获取技能等级上限
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public int GetSkillLvLimit(int index)
        {
            var maxStar = GetSkillStarLimit(index);
            var skillConfig = GetHeroSkillConfig(index, maxStar);
            if (skillConfig != null)
            {
                return skillConfig.skill_lv_limit;
            }
            return 0;
        }

        /// <summary>
        /// 获取未激活英雄满级满星的战斗力
        /// </summary>
        /// <returns>战力值</returns>
        public long GetCalculateFight()
        {
            var atkVal = ToolScriptExtend.GetFightByAttrbuteType(attributes_type.attributes_type_attack);
            var defVal = ToolScriptExtend.GetFightByAttrbuteType(attributes_type.attributes_type_defense);
            var hpVal = ToolScriptExtend.GetFightByAttrbuteType(attributes_type.attributes_type_hp);
            var fightVal = 0f;

            var levelConfig = GetHeroLvConfig(MaxLv);
            if (levelConfig != null)
            {
                fightVal = levelConfig.atk * atkVal + levelConfig.defense * defVal + levelConfig.life * hpVal;
            }

            var starConfig = GetHeroStarConfig(MaxStarLv);
            if (starConfig != null)
            {
                fightVal = starConfig.atk * atkVal + starConfig.defense * defVal + starConfig.life * hpVal;
            }
            return (long)fightVal;
        }

        /// <summary>
        /// 获取英雄升级红点状态
        /// </summary>
        /// <returns></returns>
        public bool GetHeroUpgradeRed()
        {
            if (!IsActive || !IsShow) return false;

            if (level < MaxLv && !GetLevelLimit())
            {
                var config = GetHeroLvConfig(level);
                if (config != null)
                {
                    var costDic = config.hero_exp;
                    var curNum = GameEntry.LogicData.BagData.GetAmountById(costDic.item_id);
                    return curNum >= costDic.num;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取英雄技能红点状态
        /// </summary>
        /// <returns></returns>
        public bool GetHeroSkillRed()
        {
            if (!IsActive || !IsShow) return false;

            for (int i = 1; i <= HeroData.SKILL_COUNT; i++)
            {
                if (GetHeroSkillSingleRed(i))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取英雄单个技能红点状态
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public bool GetHeroSkillSingleRed(int index)
        {
            if (!IsActive || !IsShow) return false;

            var skillLv = GetSkillLevel(index);
            if (skillLv > 0)
            {
                var skillConfig = GetHeroSkillConfig(index, StarNum);
                if (skillLv < skillConfig.skill_lv_limit)
                {
                    var skillLvConfig = GetHeroSkillLvConfig(index, skillLv);
                    var costDic = skillLvConfig.skill_cost;
                    var curNum = GameEntry.LogicData.BagData.GetAmountById(costDic.item_id);
                    return curNum >= costDic.num;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取英雄升星红点状态
        /// </summary>
        /// <returns></returns>
        public bool GetHeroStarRed()
        {
            if (!IsActive || !IsShow) return false;

            if (starLv < MaxStarLv)
            {
                var config = GetHeroStarConfig(starLv);
                if (config != null)
                {
                    var costDic = config.piece;
                    var curNum = GameEntry.LogicData.BagData.GetAmountById(costDic.item_id);
                    return curNum >= costDic.num;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取英雄荣誉墙升级红点状态
        /// </summary>
        /// <returns></returns>
        public bool GetHeroHonorRed()
        {
            if (!IsActive || !IsShow) return false;

            if (honorLv <= 0)
            {
                var unlockConfig = GetHeroHonorUnlockConfig();
                if (unlockConfig != null)
                {
                    var demand = unlockConfig.unlock;
                    var isMeet = GameEntry.LogicData.BuildingData.GetBuildingDemandIsUnlock((buildtype)demand.build_type_demand, demand.build_level_demand, 1);
                    if (!isMeet)
                        return false;
                }
            }

            if (honorLv < MaxHonorLv)
            {
                var config = GetHeroHonorConfig(honorLv);
                if (config != null)
                {
                    var costDic = config.promotion_cost;
                    var curNum = GameEntry.LogicData.BagData.GetAmountById(costDic.item_id);
                    return curNum >= costDic.num;
                }
            }
            return false;
        }

        #region 配置读取

        /// <summary>
        /// 获取英雄配置表
        /// </summary>
        /// <returns></returns>
        public hero_config GetHeroConfig()
        {
            return GameEntry.LDLTable.GetTableById<hero_config>(id);
        }

        /// <summary>
        /// 获取英雄等级配置表
        /// </summary>
        /// <param name="level"></param>
        /// <returns></returns>
        public hero_level GetHeroLvConfig(int level)
        {
            int configId = (int)id * 1000 + level;
            return GameEntry.LDLTable.GetTableById<hero_level>(configId);
        }

        /// <summary>
        /// 获取英雄星级配置表
        /// </summary>
        /// <param name="starLv"></param>
        /// <returns></returns>
        public hero_star GetHeroStarConfig(int starLv)
        {
            int configId = (int)id * 1000 + starLv;
            return GameEntry.LDLTable.GetTableById<hero_star>(configId);
        }

        /// <summary>
        /// 获取英雄荣誉墙配置
        /// </summary>
        /// <param name="level"></param>
        /// <returns></returns>
        public hero_promotion GetHeroHonorConfig(int level)
        {
            int configId = (int)id * 1000 + level;
            return GameEntry.LDLTable.GetTableById<hero_promotion>(configId);
        }

        /// <summary>
        /// 获取英雄荣誉墙解锁配置
        /// </summary>
        /// <returns></returns>
        public hero_promotion_unlock GetHeroHonorUnlockConfig()
        {
            int configId = (int)services;
            return GameEntry.LDLTable.GetTableById<hero_promotion_unlock>(configId);
        }

        /// <summary>
        /// 获取英雄技能配置
        /// </summary>
        /// <param name="index"></param>
        /// <param name="star"></param>
        /// <returns></returns>
        public skill_config GetHeroSkillConfig(int index, int star = 0)
        {
            var starLimit = GetSkillStarLimit(index);
            star = star < starLimit ? star : starLimit;
            int configId = GetSkillGroup(index) * 100 + star;
            return GameEntry.LDLTable.GetTableById<skill_config>(configId);
        }

        /// <summary>
        /// 获取英雄技能等级配置
        /// </summary>
        /// <param name="index"></param>
        /// <param name="level"></param>
        /// <returns></returns>
        public hero_skill_level GetHeroSkillLvConfig(int index, int level = 1)
        {
            level = level < 0 ? 1 : level;
            int configId = GetSkillGroup(index) * 100 + level;
            return GameEntry.LDLTable.GetTableById<hero_skill_level>(configId);
        }

        /// <summary>
        /// 获取英雄Spine配置表
        /// </summary>
        /// <returns></returns>
        public hero_spine_config GetHeroSpineConfig()
        {
            return GameEntry.LDLTable.GetTableById<hero_spine_config>(id);
        }

        /// <summary>
        /// 英雄名
        /// </summary>
        private string name;
        public string Name
        {
            get
            {
                if (name.IsNullOrWhitespace())
                {
                    var config = ToolScriptExtend.GetItemConfig(id);
                    if (config != null)
                    {
                        name = ToolScriptExtend.GetLang(config.name);
                    }
                }
                return name;
            }
        }

        /// <summary>
        /// 英雄外号
        /// </summary>
        private string nickname;
        public string Nickname
        {
            get
            {
                if (nickname.IsNullOrWhitespace())
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        nickname = ToolScriptExtend.GetLang(config.hero_nickname);
                    }
                }
                return nickname;
            }
        }

        /// <summary>
        /// 分类
        /// </summary>
        private int heroType = -1;
        public int HeroType
        {
            get
            {
                if (heroType == -1)
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        heroType = config.hero_type;
                    }
                }
                return heroType;
            }
        }

        /// <summary>
        /// 军种
        /// </summary>
        private int services = -1;
        public hero_services Services
        {
            get
            {
                if (services == -1)
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        services = (int)config.services;
                    }
                }
                return (hero_services)services;
            }
        }

        /// <summary>
        /// 品质
        /// </summary>
        private int quality = -1;
        public int Quality
        {
            get
            {
                if (quality == -1)
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        quality = config.quality;
                    }
                }
                return quality;
            }
        }

        /// <summary>
        /// 定位
        /// </summary>
        public int position = -1;
        public hero_position Position
        {
            get
            {
                if (position == -1)
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        position = (int)config.position;
                    }
                }
                return (hero_position)position;
            }
        }

        /// <summary>
        /// 头像
        /// </summary>
        private string heroHead;
        public string HeroHead
        {
            get
            {
                if (heroHead.IsNullOrWhitespace())
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        heroHead = config.hero_head;
                    }
                }
                return heroHead;
            }
        }

        /// <summary>
        /// 形象
        /// </summary>
        private string heroPic;
        public string HeroPic
        {
            get
            {
                if (heroPic.IsNullOrWhitespace())
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        heroPic = config.hero_pic;
                    }
                }
                return heroPic;
            }
        }

        /// <summary>
        /// 角色Spine
        /// </summary>
        private List<string> heroSpineList = null;
        public List<string> HeroSpineList
        {
            get
            {
                if (heroSpineList == null)
                {
                    var config = GetHeroSpineConfig();
                    if (config != null)
                    {
                        heroSpineList = config.role_resources;
                    }
                }
                return heroSpineList;
            }
        }

        /// <summary>
        /// 角色Spine偏移
        /// </summary>
        private Dictionary<interfacetype, Vector3> heroSpineOffsetDic = null;
        public Vector3 GetHeroSpineOffset(interfacetype _type)
        {
            if (heroSpineOffsetDic == null)
            {
                heroSpineOffsetDic = new();
                var config = GetHeroSpineConfig();
                if (config != null)
                {
                    var list = config.role_offset;
                    for (int i = 0; i < list.Count; i++)
                    {
                        var data = list[i];
                        float.TryParse(data.pos_x, out float posX);
                        float.TryParse(data.pos_y, out float posY);
                        float.TryParse(data.pos_z, out float posZ);
                        heroSpineOffsetDic.TryAdd(data.interface_type, new Vector3(posX, posY, posZ));
                    }
                }
            }
            heroSpineOffsetDic.TryGetValue(_type, out Vector3 pos);
            return pos;
        }

        /// <summary>
        /// 角色Spine缩放
        /// </summary>
        private Dictionary<interfacetype, Vector3> heroSpineScaleDic = null;
        public Vector3 GetHeroSpineScale(interfacetype _type)
        {
            if (heroSpineScaleDic == null)
            {
                heroSpineScaleDic = new();
                var config = GetHeroSpineConfig();
                if (config != null)
                {
                    var list = config.role_scale;
                    for (int i = 0; i < list.Count; i++)
                    {
                        var data = list[i];
                        float.TryParse(data.pos_x, out float posX);
                        float.TryParse(data.pos_y, out float posY);
                        float.TryParse(data.pos_z, out float posZ);
                        heroSpineScaleDic.TryAdd(data.interface_type, new Vector3(posX, posY, posZ));
                    }
                }
            }
            heroSpineScaleDic.TryGetValue(_type, out Vector3 pos);
            return pos;
        }

        /// <summary>
        /// 玩具预制体
        /// </summary>
        private string toyPrefab;
        public string ToyPrefab
        {
            get
            {
                if (toyPrefab.IsNullOrWhitespace())
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        toyPrefab = config.resources;
                    }
                }
                return toyPrefab;
            }
        }

        private List<float> toyOffsetList = null;
        public List<float> ToyOffsetList
        {
            get
            {
                if (toyOffsetList == null)
                {
                    toyOffsetList = new();
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        var list = config.offset;
                        float.TryParse(list[0], out float posX);
                        float.TryParse(list[1], out float posY);
                        float.TryParse(list[2], out float posZ);
                        toyOffsetList = new() { posX, posY, posZ };
                    }
                }
                return toyOffsetList;
            }
        }

        private List<float> toyScaleList = null;
        public List<float> ToyScaleList
        {
            get
            {
                if (toyScaleList == null)
                {
                    toyScaleList = new();
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        var list = config.scale;
                        float.TryParse(list[0], out float posX);
                        float.TryParse(list[1], out float posY);
                        float.TryParse(list[2], out float posZ);
                        toyScaleList = new() { posX, posY, posZ };
                    }
                }
                return toyScaleList;
            }
        }

        private List<float> toyRotList = null;
        public List<float> ToyRotList
        {
            get
            {
                if (toyRotList == null)
                {
                    toyRotList = new();
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        var list = config.rotate;
                        float.TryParse(list[0], out float posX);
                        float.TryParse(list[1], out float posY);
                        float.TryParse(list[2], out float posZ);
                        toyRotList = new() { posX, posY, posZ };
                    }
                }
                return toyRotList;
            }
        }

        /// <summary>
        /// 带兵数
        /// </summary>
        public int Soldier
        {
            get
            {
                int lv = isActive ? level : MaxLv;
                var config = GetHeroLvConfig(lv);
                if (config != null)
                {
                    return config.soldier;
                }
                return 0;
            }
        }

        /// <summary>
        /// 对应碎片道具id
        /// </summary>
        private itemid piece = itemid.itemid_nil;
        public itemid Piece
        {
            get
            {
                if (piece == itemid.itemid_nil)
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        piece = config.piece;
                    }
                }
                return piece;
            }
        }

        /// <summary>
        /// 碎片合成需求
        /// </summary>
        private int combind = -1;
        public int Combind
        {
            get
            {
                if (combind == -1)
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        combind = config.combind;
                    }
                }
                return combind;
            }
        }

        /// <summary>
        /// 角色分解碎片
        /// </summary>
        private int split = -1;
        public int Split
        {
            get
            {
                if (split == -1)
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        split = config.split;
                    }
                }
                return split;
            }
        }

        /// <summary>
        /// 开服n天显示
        /// </summary>
        private int showDay = -1;
        public int ShowDay
        {
            get
            {
                if (showDay == -1)
                {
                    var config = GetHeroConfig();
                    if (config != null)
                    {
                        showDay = config.show_day;
                    }
                }
                return showDay;
            }
        }


        /// <summary>
        /// 获取技能组id
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public int GetSkillGroup(int index)
        {
            var config = GetHeroConfig();
            if (config != null)
            {
                if (index == 1) { return config.skill1; }
                else if (index == 2) { return config.skill2; }
                else if (index == 3) { return config.skill3; }
                else if (index == 4) { return config.skill4; }
            }
            return 0;
        }

        /// <summary>
        /// 获取技能解锁条件
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public herodemand GetSkillUnlock(int index)
        {
            var config = GetHeroConfig();
            if (config != null)
            {
                int configId = 0;
                if (index == 2) { configId = config.unlock_skill2; }
                else if (index == 3) { configId = config.unlock_skill3; }
                else if (index == 4) { configId = config.unlock_skill4; }

                if (configId > 0)
                {
                    var demandConfig = Game.GameEntry.LDLTable.GetTableById<demand_hero>(configId);
                    if (demandConfig.hero_demand != null)
                    {
                        return demandConfig.hero_demand;
                    }
                }
            }
            return null;
        }

        #endregion
    }
}
