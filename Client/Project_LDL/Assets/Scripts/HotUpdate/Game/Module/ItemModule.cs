using System;
using UnityEngine;
using UnityEngine.UI;
using Game.Hotfix.Config;
using System.Collections.Generic;
using UnityEngine.Rendering;

namespace Game.Hotfix
{
    // 物品类型枚举
    public enum ItemType
    {
        Normal,
        Special,
        Resourse,
        TimeAdd,
        Hero,
        Equiment,
        Gift
    }

    // Item类
    public class ItemModule
    {
        // 属性
        public int UID { get; set; } // 物品唯一标识符
        public string Name { get; set; } // 物品名称
        public string Description { get; set; } // 物品描述
        public ItemType Type { get; set; } // 物品类型
        public int Quality { get; set; } // 物品质量
        public long Count { get; set; } // 物品数量
        public int ItemType {get;set;}//物品类型
        public GameObject Select {get;set;} //选中
        public int level  = 0;//等级
        //public 
        public int ChooseId = 0; //自选宝箱 item_c 表id
        public long ChooseCount = 0;//自选宝箱选用数量
        public long GetWayCount = 0;//获取途径需要数量
        public GameObject levelNode;
        public Action clickFun;
        public itemid ItemId {get;set;}

        public GameObject itemObj = null;

        public ItemModule(itemid id,long count){
            ItemId = id;
            Count = count;
        }
        public ItemModule(itemid id){
            ItemId = id;
        }
        public ItemModule(){
            
        }
        public virtual void Init(Transform parent,itemid itemId,long count)
        {
            ItemId = itemId;
            Count = count;
            InitConfigData();
            itemObj.transform.SetParent(parent,false);
            itemObj.transform.localScale = Vector3.one;
            UIButton uiButton = itemObj.GetComponent<UIButton>();
            uiButton.onClick.AddListener(ItemClick);
            levelNode = itemObj.transform.Find("levelNode").gameObject;
        } 
        public virtual void IsSelect(bool value){
            if (Select == null){
                Select = itemObj.transform.Find("select").gameObject;
            }
            if (value)
            {
                Select.SetActive(true);
            }
            else
            {
                Select.SetActive(false);
            }
        }
        public virtual void SetData(itemid id,long count)
        {
            ItemId = id;
            Count = count;
        }
        public virtual void SetChoose(int id){
            ChooseId = id;
        }
        public virtual void SetChooseCount(long id){
            ChooseCount = id;
        }
        public ItemModule SetGetWayCount(long count){
            GetWayCount = count;
            return this;
        }
        public void SetScale(float size){
            if(itemObj){
                itemObj.transform.localScale = new Vector3(size,size,1);
            }
        }
        public virtual void SetIconScale(Vector3 scale){
            Image icon = itemObj.transform.Find("icon").GetComponent<Image>();
            icon.transform.SetLocalScale(scale.x,scale.y,scale.z);
        }
        
        public virtual void IsShowCount(bool value)
        {
            Transform t_count = itemObj.transform.Find("count");
            t_count.gameObject.SetActive(value);
        }
        public string GetItemName(){
            item_config config = GetItemConfig();
            if(config !=null){
                return ToolScriptExtend.GetLang(config.name);
            }
            return "";
        }
        public virtual void SetLevel(int _level){
            level = _level;
            levelNode.SetActive(level >1);
        }
        public virtual void ShowPro(float value)
        {
            Transform ratio = itemObj.transform.Find("ratio");
            ratio.gameObject.SetActive(true);
            UIText txt = ratio.transform.GetComponent<UIText>();
            txt.text = value.ToString()+"%";
        }
        public virtual void SetClick(Action fun){
            clickFun = fun;
        }
        // 使用物品的方法
        public virtual void Use()
        {
            Console.WriteLine($"使用了物品: {Name}");
            
        }
         public virtual void ItemClick()
        {
            Debug.Log("ItemClick");
           // Text count = itemObj.transform.Find("count").GetComponent<Text>();
           // count.text =(Count*2).ToString();
            clickFun?.Invoke();    
        }

        public virtual void OpenTxtTips()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIItemTextTip,this);
        }
        public virtual void OpenTips()
        {
            OpenTxtTips();
        }
        // 显示物品信息的方法
        public virtual void DisplayInfo()
        {
            Text count = itemObj.transform.Find("count").GetComponent<Text>();
            count.text = Count.ToString();
            item_config config = GetItemConfig();
            Image qualityIcon = itemObj.transform.Find("quality").GetComponent<Image>();
            qualityIcon.SetImage(GetItemPath(this.Quality));
            Image icon = itemObj.transform.Find("icon").GetComponent<Image>();
            icon.SetImage(config.icon,false);
        }

        public virtual void InitConfigData()
        {
            item_config config = GetItemConfig();
            Quality = (int)config.quality;
            Name = ToolScriptExtend.GetLang(config.name);
            Description =ToolScriptExtend.GetLang(config.desc);
            ItemType = (int)config.item_type;
        }

        public string GetItemPath(int _quality)
        {
            var t = (quality)_quality;
            return t switch
            {
                Config.quality.quality_white => "Sprite/ui_public/pingzhikuang_grey.png",
                Config.quality.quality_green => "Sprite/ui_public/pingzhikuang_green.png",
                Config.quality.quality_blue => "Sprite/ui_public/pingzhikuang_blue.png",
                Config.quality.quality_purple => "Sprite/ui_public/pingzhikuang_purple.png",
                Config.quality.quality_orange => "Sprite/ui_public/pingzhikuang_orange.png",
                Config.quality.quality_red => "Sprite/ui_public/pingzhikuang_red.png",
                _ => "Sprite/ui_public/pingzhikuang_blue.png",
            };
        }
        public string GetItemPath()
        {
            var t = GetItemConfig().quality;
            return t switch
            {
                Config.quality.quality_white => "Sprite/ui_public/pingzhikuang_grey.png",
                Config.quality.quality_green => "Sprite/ui_public/pingzhikuang_green.png",
                Config.quality.quality_blue => "Sprite/ui_public/pingzhikuang_blue.png",
                Config.quality.quality_purple => "Sprite/ui_public/pingzhikuang_purple.png",
                Config.quality.quality_orange => "Sprite/ui_public/pingzhikuang_orange.png",
                Config.quality.quality_red => "Sprite/ui_public/pingzhikuang_red.png",
                _ => "Sprite/ui_public/pingzhikuang_blue.png",
            };
        }

         public item_config GetItemConfig()
        {
            return GameEntry.LDLTable.GetTableById<item_config>(ItemId);
        }
        //
        public string ITM_GetUseTotalNum(long useCount){
            item_config config = GetItemConfig();           
            if(config.use_value.Count >= 2){
                foreach (var item in config.use_value)
                {
                    if(long.TryParse(config.use_value[1],out long number))
                    {
                        return ToolScriptExtend.GetLang(1100062) + ToolScriptExtend.FormatNumberWithUnit(number*useCount).ToString();
                    }
                    else
                    {
                        return " ";
                    }                   
                }
            }
            if(config.item_subtype ==itemsubtype.itemsubtype_levelchest){
               var build = GameEntry.LogicData.BuildingData.GetBuildingModuleById(101);
               List<item_levelreward> configs = GameEntry.LDLTable.GetTable<item_levelreward>();
               foreach (var item in configs)
               {
                if(config.id == item.item_id && build.LEVEL == item.headquarterslevel){
                   
                    return ToolScriptExtend.GetLang(1100062) + ToolScriptExtend.FormatNumberWithUnit(item.item_produce[0].num*useCount);
                }
               }
            }
            return " ";
        }
        public int GetAutoUseNum(){
            long useGet = ITM_GetUseTotalNumLong(1);
            if(GetWayCount > 0){
                long bagNum = GameEntry.LogicData.BagData.GetAmountById(ItemId);
                item_config config = GetItemConfig();
                if(config.use_value.Count > 1){
                    long targetId = int.Parse(config.use_value[0]);
                    long targetNum = GameEntry.LogicData.BagData.GetAmountById((itemid)targetId);

                    double number = (float)(GetWayCount-targetNum)/useGet;
                    int autoCount = (int)Math.Ceiling(number);
                    if(autoCount <= bagNum){
                        return autoCount;
                    }else{
                        return (int)bagNum;
                    } 
                }               
            }
            return 1;
        }
        public long ITM_GetUseTotalNumLong(long useCount){
            item_config config = GetItemConfig();
            if(config.use_value.Count == 1){             
                if(long.TryParse(config.use_value[0],out long number))
                {
                    return number*useCount;
                }
                else
                {
                    return 0;
                }                   
            }           
            if(config.use_value.Count >= 2){
                foreach (var item in config.use_value)
                {
                    if(long.TryParse(config.use_value[1],out long number))
                    {
                        return number*useCount;
                    }
                    else
                    {
                        return 0;
                    }                   
                }
            }
            return 0;
        }
        public string GetItemDes(){
            var itemConfig = GetItemConfig();
            if(itemConfig.item_subtype ==itemsubtype.itemsubtype_levelchest){
               var build = GameEntry.LogicData.BuildingData.GetBuildingModuleById(101);
               List<item_levelreward> configs = GameEntry.LDLTable.GetTable<item_levelreward>();
               foreach (var config in configs)
               {
                if(itemConfig.id == config.item_id && build.LEVEL == config.headquarterslevel){
                    return ToolScriptExtend.GetLangFormat(itemConfig.desc,ToolScriptExtend.FormatNumberWithUnit(config.item_produce[0].num).ToString());                   
                }
               }
            }
            return ToolScriptExtend.GetLang(itemConfig.desc);
        }
    }
}