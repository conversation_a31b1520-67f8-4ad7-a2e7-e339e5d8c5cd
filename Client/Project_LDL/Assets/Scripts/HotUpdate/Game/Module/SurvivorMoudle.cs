using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class SurvivorMoudle
    {
        private uint m_Id;
        private uint m_starStage;
        private uint m_buildNo;
        private uint m_pos;
        private survivor_list survivorCfg;

        public SurvivorMoudle(Survivor.Survivor survivor)
        {
            m_Id = survivor.Code;
            m_starStage = survivor.StarStage;
            m_buildNo = survivor.BuildNo;
            m_pos = survivor.Pos;
            
            survivorCfg = Game.GameEntry.LDLTable.GetTableById<survivor_list>((itemid)m_Id);
            HalfIcon = survivorCfg.picture;
            IsDispatch = m_buildNo != 0;
        }
        
        public uint Id
        {
            get => m_Id;
            set => m_Id = value;
        }
        
        public uint StarStage
        {
            get => m_starStage;
            set => m_starStage = value;
        }
        
        public uint BuildingId
        {
            get => m_buildNo;
            set => m_buildNo = value;
        }
        
        public uint Pos
        {
            get => m_pos;
            set => m_pos = value;
        }

        public quality Quality
        {
            get
            {
                if (survivorCfg != null)
                {
                    return survivorCfg.quality;
                }
                return quality.quality_nil;
            }
        } 
        
        public int Power
        {
            get
            {
                List<survivor_star> survivorStars = GameEntry.LDLTable.GetTable<survivor_star>();
                if (survivorStars != null)
                {
                    for (int i = 0; i < survivorStars.Count; i++)
                    {
                        survivor_star survivorStarCfg = survivorStars[i];
                        if (survivorStarCfg.profession_id == survivorCfg.profession_id && survivorStarCfg.quality == Quality && survivorStarCfg.star_stage == m_starStage)
                        {
                            return survivorStarCfg.star_power;
                        }
                    }
                }
                return 0;
            }
        }

        public string Name
        {
            get
            {
                return ToolScriptExtend.GetLang(survivorCfg.name);
            }
        }

        // 职业名称
        public string ProfessionName
        {
            get
            {
                survivor_config survivorConfig = GameEntry.LDLTable.GetTableById<survivor_config>(survivorCfg.profession_id);
                string professionName = ToolScriptExtend.GetLang(survivorConfig.profession_name);
                return professionName;
            }
        }

        public string HeadIcon
        {
            get
            {
                return survivorCfg.icon;
            }
        }

        //立绘
        public string HalfIcon { get; set; }
        //是否派遣
        public bool IsDispatch { get; set; }

        //星级
        public int StarNum
        {
            get { return (int)Mathf.Floor(m_starStage / 5); }
        }
        
        /// <summary>
        /// 星阶
        /// </summary>
        public int StarOrder
        {
            get { return (int)m_starStage % 5; }
        }
        
        public survivor_list SurvivorCfg => survivorCfg;
        
        public void UpdateInfo(Survivor.Survivor survivor)
        {
            m_Id = survivor.Code;
            m_starStage = survivor.StarStage;
            m_buildNo = survivor.BuildNo;
            m_pos = survivor.Pos;
        }
        
        public void UpdateInfo(uint star)
        {
            m_starStage = star;
        }

        public buildtype GetMatchBuildtype()
        {
            survivor_config survivorConfig = GameEntry.LDLTable.GetTableById<survivor_config>(survivorCfg.profession_id);
            return  survivorConfig.build_type;
        }

        // 是否可升星
        public bool GetCanUpGrade()
        {
            survivor_star survivorStarConfigNext = GameEntry.LogicData.SurvivorData.GetSurvivorStarConfig(survivorCfg.profession,
                 1,this.Quality);
            if (StarStage == 0 && survivorStarConfigNext == null)
            {
                return false;
            }
            return true;
        }
        
        // 是否满星
        public bool GetIsMaxStar()
        {
            survivor_star survivorStarConfigNext = GameEntry.LogicData.SurvivorData.GetSurvivorStarConfig(survivorCfg.profession,
                (int)this.StarStage + 1,this.Quality);
            return survivorStarConfigNext == null;
        }

        //可升星
        public bool GetCanIncreaseStar()
        {
            var canUpGrade = GetCanUpGrade();
            if (!canUpGrade)
            {
                return false;
            }

            bool isMaxStar = GetIsMaxStar();
            if (isMaxStar)
            {
                return false;
            }
            survivor_star survivorStarConfigNext = GameEntry.LogicData.SurvivorData.GetSurvivorStarConfig(survivorCfg.profession,
                (int)this.StarStage + 1,this.Quality);
            cost costs = survivorStarConfigNext.cost;
            itemid itemId = costs.item_id;
            long itemNum = costs.num;
            long count = GameEntry.LogicData.BagData.GetAmountById(itemId);

            itemid fragmengId = survivorCfg.peace;
            long fragmengCount = GameEntry.LogicData.BagData.GetAmountById(fragmengId);
            
            return count >= itemNum && fragmengCount >= survivorStarConfigNext.star_cost;
        }

    }
}