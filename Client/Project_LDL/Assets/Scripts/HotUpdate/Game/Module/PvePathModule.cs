using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using Google.Protobuf.Collections;
using UnityEngine;

namespace Game.Hotfix
{
    public enum PvePathModuleEvent
    {
        //逻辑上的 到达
        OnArriveLogic,
        //显示上的 到达
        OnArriveDisplay,
        //逻辑上离开
        OnLeaveLogic,
        //显示上的 到达
        OnLeaveDisplay,
        //已经到达前一个
        OnPreviousArriveDisplay
    }
    
    public class PvePathModule
    {
        public int? Uid => m_Uid;
        public int Id => m_Config.id;
        public bool InShowGround => m_ShowGround;
        public bool InShowObj => m_ShowObj;
        public int? BeginIndex => m_BeginIndex;

        public int? EntityIdObj => m_EntityIdObj;
        public int? EntityIdGround => m_EntityIdGround;

        public int? AreaId => m_Config.from_area_id;

        public RepeatedField<global::Article.Article> CacheRewards;
        
        private innercity_path m_Config;

        private Vector3? m_DirFrom;
        private Vector3? m_DirTo;

        private bool m_ShowObj;
        private bool m_ShowGround;
        private int? m_BeginIndex = null;

        private int? m_Uid;
        private int? m_EntityIdObj;
        private int? m_EntityIdGround;
        
        private EventDispatch<PvePathModuleEvent> m_EventDispatch = new EventDispatch<PvePathModuleEvent>();
        
        public PvePathModule(innercity_path config,bool showGround,bool showObj)
        {
            m_Config = config;
            m_ShowGround = showGround;
            m_ShowObj = showObj;
        }

        public Vector3? DirFrom
        {
            get => m_DirFrom;
            set => m_DirFrom = value;
        }

        public Vector3? DirTo
        {
            get => m_DirTo;
            set => m_DirTo = value;
        }

        public bool IsDestination()
        {
            return m_Config.is_destination;
        }

        public bool HasNext()
        {
            return m_Config.next_id > 0;
        }
        
        public int GetNext()
        {
            return m_Config.next_id;
        }

        public innercity_path GetConfig()
        {
            return m_Config;
        }

        public ground_eventtype GetEventType()
        {
            return m_Config.event_type.types;
        }

        public Vector3 GetPosition(bool withRoadHeight = false)
        {
            return new Vector3(m_Config.ground_location.x, withRoadHeight?GameDefine.PvePathRoadHeight:0, m_Config.ground_location.y);
        }

        public Vector3 GetPositionCenter(bool withRoadHeight = false)
        {
            return new Vector3(m_Config.ground_location.x+1, withRoadHeight?GameDefine.PvePathRoadHeight:0, m_Config.ground_location.y+1);
        }

        public List<int> GetArriveShow()
        {
            return m_Config.arrive_show;
        }
        
        public void ShowGround(bool show)
        {
            if (m_ShowGround != show)
            {
                m_ShowGround = show;

                if (show)
                {
                    var pvePath = GetElPath<EL_PvePath>(m_Uid);
                    pvePath?.ShowGroundDisplay();
                }
                else
                {
                    var pvePath = GetElPath<EL_PvePath>(m_Uid);
                    pvePath?.HideGroundDisplay();
                }
            }
        }

        public void ShowObj(bool show)
        {
            if (m_ShowObj != show)
            {
                m_ShowObj = show;

                if (show)
                {
                    var pvePath = GetElPath<EL_PvePath>(m_Uid);
                    pvePath?.ShowObjDisplay();
                }
                else
                {
                    var pvePath = GetElPath<EL_PvePath>(m_Uid);
                    pvePath?.HideObjDisplay();
                }
            }
        }

        public void SetBeginIndex(int? beginIndex)
        {
            m_BeginIndex = beginIndex;
        }

        public void SetUid(int? uid)
        {
            m_Uid = uid;
        }
        
        public void SetEntityIdGround(int? id)
        {
            m_EntityIdGround = id;
        }
        
        public void SetEntityIdObj(int? id)
        {
            m_EntityIdObj= id;
        }

        public bool CanShow()
        {
            return m_ShowObj || m_ShowGround;
        }

        
        public bool CanTriggerEvent()
        {
            if (m_Config.build_condition != null)
            {
                var buildingId = m_Config.build_condition.build_id;
                var buildingLevel = m_Config.build_condition.build_lv;
                var buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById((uint)buildingId);
                if (buildingModule != null && buildingModule.LEVEL >= buildingLevel)
                    return true;
                return false;
            }
            return true;
        }

        /// <summary>
        /// 播放死亡或者消失动画
        /// </summary>
        /// <returns></returns>
        private float PlayExitAnimation()
        {
            if (m_EntityIdObj!=null)
            {
                var elPvePath = GetElPath<EL_PvePathObjDisplay>(m_EntityIdObj.Value);
                if (elPvePath != null)
                    return elPvePath.PlayExitAnimation();    
            }
            return 0;
        }

        public float PlayGroundExitAnimation()
        {
            if (m_EntityIdGround != null)
            {
                var elPvePath = GetElPath<EL_PvePathGroundDisplay>(m_EntityIdGround.Value);
                if (elPvePath != null)
                    return elPvePath.PlayGroundExitAnimation();    
            }
            return 0;
        }

        public int? GetUnlockArea()
        {
            if (m_Config.unlock_area_id > 0)
                return m_Config.unlock_area_id;
            return null;
        }
        
        public T GetElPath<T>(int? uid)
        {
            if (uid != null)
            {
                var entity = GameEntry.Entity.GetEntity(uid.Value);
                if (entity != null && entity.Logic != null && entity.Logic is T elPvePath)
                {
                    return elPvePath;
                }    
            }
            return default;
        }

        public Bounds GetBounds()
        {
            Bounds bounds = new Bounds(GetPositionCenter(), new Vector3(1, 0, 1));
            
            return bounds;
        }

        public int? GetEffectId()
        {
            if (m_Config.event_type.types == ground_eventtype.ground_eventtype_1)
                return 100005;
            else if(m_Config.event_type.types == ground_eventtype.ground_eventtype_2)
                return 100006;
            return 100006;
        }

        public void OnPreviousArriveDisplay()
        {
            SendEvent(PvePathModuleEvent.OnPreviousArriveDisplay, this);
        }
        
        /// <summary>
        /// 逻辑上的 到达
        /// </summary>
        public void OnArriveLogic()
        {
            SendEvent(PvePathModuleEvent.OnArriveLogic, this);
        }

        /// <summary>
        /// 显示上的 到达
        /// </summary>
        public IEnumerator OnArriveDisplay()
        {
            yield return new WaitForEndOfFrame();
            SendEvent(PvePathModuleEvent.OnArriveDisplay, this);
        }

        /// <summary>
        /// 逻辑上离开
        /// </summary>
        public void OnLeaveLogic()
        {
            SendEvent(PvePathModuleEvent.OnLeaveLogic, this);
        }

        /// <summary>
        /// 显示上 离开
        /// </summary>
        public IEnumerator OnLeaveDisplay()
        {
            var waitDelay = PlayExitAnimation();
            yield return new WaitForSeconds(waitDelay);
            SendEvent(PvePathModuleEvent.OnLeaveDisplay, this);
        }
        
        
        #region 事件

        public void AddEventListener(PvePathModuleEvent eventType, EventCallBack eventHandler)
        {
            m_EventDispatch.RegisterEvent(eventType, eventHandler);
        }

        public void RemoveEventListener(PvePathModuleEvent eventType, EventCallBack eventHanlder)
        {
            m_EventDispatch.UnRegisterEvent(eventType, eventHanlder);
        }

        protected void SendEvent(PvePathModuleEvent eventType, object obj)
        {
            m_EventDispatch.PostEvent(eventType, obj);
        }

        #endregion
        
    }
}
