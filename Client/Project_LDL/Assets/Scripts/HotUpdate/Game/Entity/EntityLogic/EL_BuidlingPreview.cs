using System;
using Game.Hotfix;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class EL_BuidlingPreview : Entity
    {
        private ED_BuidlingPreview m_Data;
        private BuildingModule m_BuildingModule;
        private EL_Building m_Building;

        private GameObject m_BuildingContainer;
        private bool m_GridCanPut;
        
        //提示相关
        private eBuildingPlacementRejectionReason m_LastRejectReason = eBuildingPlacementRejectionReason.Unknown;
        private ulong m_LastShowReasonTime = 0;
        private const ulong ShowReasonInterval = 1;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            
        }

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            
            m_Data = (ED_BuidlingPreview)userData;
            if (m_Data.PreviewType == BuidlingPreviewType.Build)
            {
                m_BuildingModule = m_Data.buildingData;
            }
            else
            {
                m_BuildingModule = m_Data.m_building.GetBuildingModule();
                m_Building = m_Data.m_building;
            }

            var pos = m_BuildingModule.GetGridPos();
            SetGridPos(pos);

            BuildingGridDisplay gridDisplay = gameObject.GetOrAddComponent<BuildingGridDisplay>();
            gridDisplay.SetSize(m_BuildingModule.GetGridAreaL(),m_BuildingModule.GetGridAreaW());

            m_BuildingContainer = transform.Find("BuildingContainer").gameObject;

            if (m_Data.PreviewType == BuidlingPreviewType.Build)
            {
                BuildingModule buildingModule = BuildingModule.Create(m_BuildingModule.BuildingId, m_BuildingModule.LEVEL,
                    Game.GameEntry.Entity.GenerateSerialId());
                //如果是建造-创建一个 building Entity
                ED_Building edBuilding = new ED_Building(buildingModule.UID);
                
                edBuilding.parentEntityId = this.Entity.Id;
                edBuilding.parentTransformPath = "BuildingContainer";
                edBuilding.buildingData = buildingModule;
                
                Game.GameEntry.Entity.ShowBuilding(edBuilding);
            }
            else if(m_Data.PreviewType == BuidlingPreviewType.Move)
            {
                //如果是移动-把建筑物的entity attach 过来
                Game.GameEntry.Entity.AttachEntity(m_Building.Entity.Id, this.Entity.Id, m_BuildingContainer);
                
                m_Building.transform.localPosition = Vector3.zero;
            }
            
            ValidateGridCanPut();
        }

        protected override void OnRecycle()
        {
            base.OnRecycle();
        }

        public void SetGridPos(Vector2Int pos)
        {   
            transform.SetPositionX(pos.x);
            transform.SetPositionZ(pos.y);

            ValidateGridCanPut();
        }

        public bool IsInGrid(int x,int y)
        {
            var tempPos = MapGridUtils.WorldToGrid(transform.position);
            return m_BuildingModule.IsInArea(x, y, tempPos.x, tempPos.y);
        }

        public void ValidateGridCanPut()
        {
            //检测是否可以建造
            bool canBuild = GameEntry.CityMap.CanBuild(transform.position, m_BuildingModule,out var reason);
            BuildingGridDisplay gridDisplay = gameObject.GetOrAddComponent<BuildingGridDisplay>();
            gridDisplay.SetEnable(canBuild);
            m_GridCanPut = canBuild;

            if (!canBuild)
            {
                TryShowReason(reason);
            }
        }

        private void TryShowReason(eBuildingPlacementRejectionReason reason)
        {
            if (reason == eBuildingPlacementRejectionReason.Unknown)
                return;
            
            if (TimeComponent.Now - m_LastShowReasonTime > ShowReasonInterval)
            {
                m_LastRejectReason = reason;
                m_LastShowReasonTime = TimeComponent.Now;

                int longId = 1100191;
                if (reason == eBuildingPlacementRejectionReason.OnTheRoad)
                {
                    longId = 1100190;
                }
                
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(longId)
                });
            }
        }
        
        public bool IsGridCanPut()
        {
            return m_GridCanPut;
        }
    }
}