using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class ED_BattleHero:ED_BattleUnitBase
    {
        public ED_BattleHero(int entityId) : base(entityId)
        {
        }
    }
    
    public class EL_BattleHero:Entity
    {
        private BattleSlotHandler m_BattleSlotHandler;
        private Dictionary<slot, Transform> m_SlotsDic;

        private Animator m_Animator;
        private BattleLookAt m_LookAtAble;
        
        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            Hide(false);
            m_SlotsDic = new Dictionary<slot, Transform>();
            m_BattleSlotHandler = CachedTransform.GetComponent<BattleSlotHandler>();
            if (m_BattleSlotHandler && m_BattleSlotHandler.slots!=null)
            {
                foreach (var slotData in m_BattleSlotHandler.slots)
                {
                    m_SlotsDic.TryAdd(slotData.Slot, slotData.Transform);
                }
            }

            m_LookAtAble = CachedTransform.GetComponent<BattleLookAt>();
            m_Animator = CachedTransform.GetComponentInChildren<Animator>();
        }

        public Vector3 GetSlotPosition(slot slot)
        {
            return GetSlot(slot).position;
        }

        public Quaternion GetSlotRotation(slot slot)
        {
            return GetSlot(slot).rotation;
        }

        public Transform GetSlot(slot slot)
        {
            if (m_SlotsDic.TryGetValue(slot, out var value))
            {
                return value;
            }
            return CachedTransform;
        }
        
        public void PlayAnimation(string animationName,Vector3? targetPosition = null)
        {
            if (targetPosition != null && m_LookAtAble != null)
            {
                m_LookAtAble.LookAt(targetPosition.Value);
            }
            m_Animator.SetTrigger(animationName);
        }

        public void Hide(bool hide)
        {
            CachedTransform.gameObject.SetActive(!hide);
        }
        
        
        
        
    }    
}

