using UnityEngine;

namespace Game.Hotfix
{
    public class ED_WorldMapTroop : EntityData
    {
        public ulong TroopUid;
        public ED_WorldMapTroop(int entityId,ulong troopUid) : base(entityId)
        {
            TroopUid = troopUid;
        }
    }

    public class EL_WorldMapTroop : EL_WorldMapDisplay
    {
        public GameObject goBeginPoint;
        public GameObject goEndPoint;
        public LineRenderer lineRenderer;
        public GameObject lineContainer;

        private ulong m_TroopUid;
        private TroopData m_TroopData;
        
        protected override void OnShow(object userData)
        {
            base.OnShow(userData);

            if(userData is ED_WorldMapTroop param)
            {
                m_TroopUid = param.TroopUid;
                m_TroopData = GameEntry.LogicData.WorldMapData.GetTroopData(m_TroopUid);
            }
            
            if (goBeginPoint == null)
                goBeginPoint = transform.Find("beginPoint")?.gameObject;
            
            if(goEndPoint == null)
                goEndPoint = transform.Find("endPoint")?.gameObject;
            
            if(lineContainer == null)
                lineContainer = transform.Find("lineContainer")?.gameObject;
            
            if(lineRenderer == null)
                lineRenderer = transform.Find("line")?.GetComponent<LineRenderer>();
            
         
            
            
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);
        }
    }

}