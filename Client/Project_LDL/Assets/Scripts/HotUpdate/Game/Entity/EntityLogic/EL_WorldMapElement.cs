using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class ED_WorldMapElement:EntityData
    {
        public int ElementId;
        
        public ED_WorldMapElement(int entityId,int id) : base(entityId)
        {
            ElementId = id;
        }
    }
    
    public class EL_WorldMapElement : EL_WorldMapDisplay
    {
        public map_element ElementCfg => m_ElementCfg;
        
        private int m_ElementId;
        private map_element m_ElementCfg;
        
        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            if (userData is ED_WorldMapElement param)
            {
                m_ElementId = param.ElementId;
                m_ElementCfg = GameEntry.LDLTable.GetTableById<map_element>(m_ElementId);
            }
        }

        public override void OnClick()
        {
            base.OnClick();
            if (m_ElementCfg?.big_type == (map_element_big_type)WorldMapElementType.Gather)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIWorldMapGatherMenu, this);    
            }else if (m_ElementCfg?.big_type == (map_element_big_type)WorldMapElementType.Zombies)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIWorldMapZombiesMenu, this);
            }else if (m_ElementCfg?.big_type == (map_element_big_type)WorldMapElementType.DoomElite)
            {
                // GameEntry.UI.OpenUIForm(EnumUIForm.UIWorldMapDoomEliteMenu, this);
                GameEntry.UI.OpenUIForm(EnumUIForm.UIWorldMapZombiesMenu, this);
            }
        }
    }
}