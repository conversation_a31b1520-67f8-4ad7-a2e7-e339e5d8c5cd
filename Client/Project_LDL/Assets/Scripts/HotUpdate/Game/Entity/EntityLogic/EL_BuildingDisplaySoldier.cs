using System.Collections.Generic;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class EL_BuildingDisplaySoldier : EL_BuildingDisplay
    {
        private Dictionary<int, List<int>> m_SoldierDictionary;
        private List<int> m_SoldierDisplayIdList;
        List<Vector3> m_Offset = new List<Vector3>()
        {
            new Vector3(-0.55f,0,0.45f),
            new Vector3(0.55f,0,0.45f),
            new Vector3(-0.55f,0,-0.6f),
            new Vector3(0.55f,0,-0.6f),
        };
        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            m_SoldierDictionary = new Dictionary<int, List<int>>();
            m_SoldierDisplayIdList = new List<int>();
            ComputeAndShowSoldierDisplay();
        }

        protected void ComputeAndShowSoldierDisplay()
        {
          
            var soldierModules = GetAndSortSoldoerList();
            //所有容量
            int allCapacity = GameEntry.LogicData.BuildingData.GetAllBuildingGroundCapacity();
            int oneSoldierCount = allCapacity / 32;
            //int soldierAreaIndex = 1;
            for (var i = 0; i < soldierModules.Count; i++)
            {
                SoldierModule soldierModule = soldierModules[i];
                int count = 0;
                if (oneSoldierCount != 0)
                {
                    count = Mathf.FloorToInt((float)soldierModule.Num / oneSoldierCount);
                }
                ComputeDisplayList(count, (int)soldierModule.Id);
            }
        }

        public void ResetDisplayList()
        {
            for (var i = 0; i < m_SoldierDisplayIdList.Count; i++)
            {
                int soldierDisplayId = m_SoldierDisplayIdList[i];
                Game.GameEntry.Entity.HideEntity(soldierDisplayId);
            }
            m_SoldierDisplayIdList.Clear();
            m_SoldierDictionary.Clear();
            ComputeAndShowSoldierDisplay();
        }

        protected void ComputeDisplayList(int count,int soldierLevel)
        {
            for (int i = 0; i < count; i++)
            {
                var curSoldierList = new List<int>();
                int soldierAreaIndex = 1;
                for (int j = 1; j < 9; j++)
                {
                    List<int> soldierList;
                    bool isExist = m_SoldierDictionary.TryGetValue(j, out soldierList);
                    if (!isExist)
                    {
                        soldierList = new List<int>();
                    }
                    if (soldierList.Count < 4)
                    {
                        soldierAreaIndex = j;
                        curSoldierList = soldierList;
                        break;
                    }
                }

                if (curSoldierList.Count < 4)
                {
                    curSoldierList.Add(soldierLevel);
                    int posIndex = curSoldierList.Count;
                    m_SoldierDictionary[soldierAreaIndex] = curSoldierList;
                    ShowSoldierDisplay(soldierLevel, soldierAreaIndex, m_Offset[posIndex - 1]);
                }
            }
        }
        
        protected List<SoldierModule> GetAndSortSoldoerList()
        {
            List<SoldierModule> soldierModules = new List<SoldierModule>();
            for (int i = 0; i < GameEntry.LogicData.SoliderData.SoldierList.Count; i++)
            {
                var hSoldier = GameEntry.LogicData.SoliderData.SoldierList[i];
                if (hSoldier.Num != 0)
                {
                    soldierModules.Add(hSoldier);
                }
            }
            soldierModules.Sort((a,b) =>
            {
                return b.Id.CompareTo(a.Id);
            });
            return soldierModules;
        }

        protected void ShowSoldierDisplay(int soldierLevel,int areaIndex,Vector3 offset)
        {
            string soldierDisplayPath = GameEntry.LogicData.SoliderData.GetSoldierDisplayPath(soldierLevel);
            if (!string.IsNullOrEmpty(soldierDisplayPath))
            {
                int displayId = Game.GameEntry.Entity.ShowSoldierDisplay(soldierDisplayPath, this.Id,areaIndex,0,null,offset);
                m_SoldierDisplayIdList.Add(displayId);
            }
        }
        
        public void AddSoldierDisplay(int soldierLevel,int addNum)
        {
            //所有容量
            int allCapacity = GameEntry.LogicData.BuildingData.GetAllBuildingGroundCapacity();
            int oneSoldierCount = allCapacity / 32;
            int count = Mathf.FloorToInt(addNum / oneSoldierCount);
            for (int i = 0; i < count; i++)
            {
                var curSoldierList = new List<int>();
                int soldierAreaIndex = 1;
                for (int j = 1; j < 9; j++)
                {
                    List<int> soldierList;
                    bool isExist = m_SoldierDictionary.TryGetValue(j, out soldierList);
                    if (!isExist)
                    {
                        soldierList = new List<int>();
                    }
                    if (soldierList.Count < 4)
                    {
                        soldierAreaIndex = j;
                        curSoldierList = soldierList;
                        break;
                    }
                }

                if (curSoldierList.Count < 4)
                {
                    curSoldierList.Add(soldierLevel);
                    int posIndex = curSoldierList.Count;
                    m_SoldierDictionary[soldierAreaIndex] = curSoldierList;
                    ShowSoldierDisplay(soldierLevel, soldierAreaIndex, m_Offset[posIndex - 1]);
                }
            }
        }
    }
}