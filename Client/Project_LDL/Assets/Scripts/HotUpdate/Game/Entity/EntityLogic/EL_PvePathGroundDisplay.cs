using DG.Tweening;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class ED_PvePathGroundDisplay:EntityData
    {
        public int ParentId;
        public string HideAnimation;
        public ED_PvePathGroundDisplay(int entityId, int parentId,string hideAnimation) : base(entityId)
        {
            ParentId = parentId;
            HideAnimation = hideAnimation;
        }
    }
    
    public class EL_PvePathGroundDisplay : Entity
    {
        private string m_HideAnimation = string.Empty;
        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            ED_PvePathGroundDisplay data = userData as ED_PvePathGroundDisplay;
            if (data != null)
            {
                GameEntry.Entity.AttachEntity(this.Id, data.ParentId);
                m_HideAnimation = data.HideAnimation;
            }
            
        }

        public void PlayHideEffect()
        {
            var animation = CachedTransform.GetComponentInChildren<Animation>();
            if (animation && !string.IsNullOrEmpty(m_HideAnimation))
            {
                animation.Play(m_HideAnimation);
                float progress = 1;
                DOTween.To(() => progress, x => progress = x, 0, 1f).OnComplete(() =>
                {
                    GameEntry.Entity.HideEntity(this);    
                });
            }
            else
            {
                GameEntry.Entity.HideEntity(this);
            }
        }
        
        protected override void OnAttachTo(EntityLogic parentEntity, Transform parentTransform, object userData)
        {
            base.OnAttachTo(parentEntity, parentTransform, userData);
            ResetCachedTransform();
        }

        public float PlayGroundExitAnimation()
        {
            return 0.3f;
        }
    }
}