using DG.Tweening;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class EL_SoldierDisplay : Entity
    {
        private Animator m_Animator;
        private static readonly int Run = Animator.StringToHash("run");

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            m_Animator = CachedTransform.GetComponentInChildren<Animator>();
            ED_SoldierDisplay data = userData as ED_SoldierDisplay;
            if (data != null)
            {
                if (data.SoldierAreaIndex != 0)
                {
                    GameEntry.Entity.AttachEntity(this.Id, data.ParentId,$"soldierArea_{data.SoldierAreaIndex}");
                    if (data.Position != null)
                    {
                        transform.localPosition = data.Position;
                        transform.localRotation = Quaternion.Euler(0, 180, 0);
                    }
                    transform.localScale = new Vector3(1.2f,1.2f,1.2f);
                }
                else if(data.MoveTargetId != 0)
                {
                    GameEntry.Entity.AttachEntity(this.Id, data.ParentId);
                    transform.localPosition = data.Position;
                    MoveTo(data.MoveTargetId,data.TargetPos);
                }
      
            }
        }
        
        public void MoveTo(int moveTargetId,Vector3 movePos)
        {
            var targetEntity = GameEntry.Entity.GetEntity(moveTargetId);
            Vector3 targetPos = movePos;
            m_Animator?.SetTrigger(Run);
            // 计算移动方向
            if (targetEntity.Logic is EL_Building building)
            {
                int offsetZ = building.GetBuildingModule().GetGridAreaL();
                targetPos = new Vector3(targetPos.x, 0, targetPos.z + offsetZ);
            }

            Vector3 direction = targetPos - transform.position;
            float duration = Vector3.Magnitude(direction) / 1f;
            // 计算目标旋转
            Quaternion targetRotation = Quaternion.LookRotation(direction.normalized);
            SetRotation(targetRotation);
            transform.DOMove(targetPos, duration).SetEase(Ease.Linear).OnComplete(()=>{
                GameEntry.Effect.CreateEffect(100013,null,transform.position);
                GameEntry.Entity.HideEntity(this);
            });
        }
    }
}