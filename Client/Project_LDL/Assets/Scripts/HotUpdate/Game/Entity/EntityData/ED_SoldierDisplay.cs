using UnityEngine;

namespace Game.Hotfix
{
    public class ED_SoldierDisplay : EntityData
    {
        public int ParentId;
        public int SoldierAreaIndex;
        public int MoveTargetId;
        public Vector3 TargetPos;

        public ED_SoldierDisplay(int entityId, int parentId, int areaIndex, int moveTargetId = 0,Vector3? targetPos = null) : base(entityId)
        {
            ParentId = parentId;
            SoldierAreaIndex = areaIndex;
            MoveTargetId = moveTargetId;
            if (targetPos != null)
            {
                TargetPos = targetPos.Value;
            }
        }
    }
}