using System;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class TimeComponent : GameFrameworkComponent
    {
        private static ulong? _serverTime = null;
        private static string _location = "";
        private static float m_UpdatePerSecond;
        public static ulong Now
        {
            get
            {
                if (_serverTime == null)
                {
                    TimeSpan timeSpan = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0);
                    _serverTime = (ulong)timeSpan.TotalSeconds;
                    m_UpdatePerSecond = 0;
                }
                return _serverTime.Value;
            }
        }

        public void SetServerTime(Gate.HeartResp heartResp)
        {
            _serverTime = (ulong)heartResp.Time;
            _location = heartResp.Location;
        }

        private void Update()
        {
            if (_serverTime != null)
            {
                m_UpdatePerSecond += Time.deltaTime;
                if (m_UpdatePerSecond >= 1)
                {
                    _serverTime += 1;
                    m_UpdatePerSecond -= 1;
                }

            }
        }

        //传入的时间是否小于当前时间
        public static bool IsEnd(long timeValue)
        {
            return timeValue <= (long)Now;
        }

        /// <summary>
        /// 获取第二天零点的时间戳
        /// </summary>
        /// <param name="timestamp"></param>
        /// <returns></returns>
        public static long GetNextDayTimestamp(long timestamp)
        {
            var curTime = DateTimeOffset.FromUnixTimeSeconds(timestamp).UtcDateTime;
            var nextDayTime = curTime.AddDays(1);
            return new DateTimeOffset(nextDayTime.Date).ToUnixTimeSeconds();
        }
        
        /// <summary>
        /// 获取下周一零点的时间戳
        /// </summary>
        /// <param name="timestamp"></param>
        /// <returns></returns>
        public static long GetNextWeekTimestamp(long timestamp)
        {
            var curTime = DateTimeOffset.FromUnixTimeSeconds(timestamp).UtcDateTime;
            var addDay = ((int)DayOfWeek.Monday - (int)curTime.DayOfWeek + 7) % 7;
            var nextWeekTime = curTime.AddDays(addDay == 0 ? 7 : addDay);
            return new DateTimeOffset(nextWeekTime.Date).ToUnixTimeSeconds();
        }
    }
}
