// 此文件由工具自动生成，请勿直接修改。
// 生成时间：2021-01-02 19:09:07.680
//------------------------------------------------------------

namespace Game.Hotfix
{
    public enum EnumUIForm : int
    {
        /// <summary>
        /// 无
        /// </summary>
        None = 0,

        /// <summary>
        /// 弹出框。
        /// </summary>
        DialogForm = 1,

        /// <summary>
        /// 主菜单。
        /// </summary>
        MenuForm = 100,

        /// <summary>
        /// 设置。
        /// </summary>
        SettingForm = 101,

        /// <summary>
        /// 关于。
        /// </summary>
        AboutForm = 102,
        /// <summary>
        /// 主菜单
        /// </summary>
        UIMainMenuForm = 1001,

        /// <summary>
        /// 加载界面
        /// </summary>
        UILoadingForm = 1002,

        /// <summary>
        /// 游戏主界面
        /// </summary>
        UIMainFaceForm = 1003,

        /// <summary>
        /// 背包界面
        /// </summary>
        UIBagForm = 1004,

        /// <summary>
        /// 邮件界面
        /// </summary>
        UIEmailForm = 1005,

        /// <summary>
        /// 联盟界面
        /// </summary
        UIUnionForm = 1006,

        /// <summary>
        /// 建造界面
        /// </summary>
        UIMainFaceBuildForm = 1007,

        /// <summary>
        /// 英雄界面
        /// </summary
        UIHeroForm = 1008,

        /// <summary>
        /// 建筑物移动或者建造交互
        /// </summary>
        UIBuildingMoveForm = 1009,

        /// <summary>
        /// 建筑物菜单
        /// </summary>
        UIBuildingMenuForm = 1010,

        /// <summary>
        /// 通用确认弹窗
        /// </summary>
        UICommonConfirmForm = 1011,

        /// <summary>
        /// 资源飞行动画界面
        /// </summary>
        UIResFlyForm = 1012,

        /// <summary>
        /// 名片界面
        /// </summary>
        UIPlayerInfoForm = 1013,

        /// <summary>
        /// 更改绑定信箱
        /// </summary>
        UIChangeMailForm = 1014,

        /// <summary>
        /// 切换账号界面
        /// </summary>
        UISwitchAccountForm = 1015,

        /// <summary>
        /// 玩家设置界面
        /// </summary>
        UIPlayerSettingsForm = 1016,

        /// <summary>
        /// 玩家详情界面
        /// </summary>
        UIPlayerDetailForm = 1017,

        /// <summary>
        /// 玩家账号管理界面
        /// </summary>
        UIAccountManageForm = 1018,

        /// <summary>
        /// 玩家记录界面
        /// </summary>
        UIPlayerRecordForm = 1019,

        /// <summary>
        /// 飘字界面
        /// </summary>
        UIFlyTextForm = 1020,

        /// <summary>
        /// 登录
        /// </summary>
        UILoginForm = 1021,

        /// <summary>
        /// 服务器列表
        /// </summary>
        UIServerListForm = 1022,

        /// <summary>
        /// 英雄培养界面
        /// </summary>
        UIHeroDevelopForm = 1023,

        /// <summary>
        /// 奖励获取界面
        /// </summary>
        UIRewardGetForm = 1024,

        /// <summary>
        /// 通用说明界面
        /// </summary>
        UIComDescForm = 1025,

        /// <summary>
        /// 邮件主界面
        /// </summary>
        UIMailForm = 1026,

        /// <summary>
        /// 邮件消息列表界面
        /// </summary>
        UIMailMsgListForm = 1027,

        /// <summary>
        /// 邮件消息内容界面
        /// </summary>
        UIMailDetailForm = 1028,

        /// <summary>
        /// 装备工厂界面
        /// </summary>
        UIEquipmentFactoryForm = 1029,

        /// <summary>
        /// 建造升级界面
        /// </summary>
        UIBuildingDetailForm = 1030,

        /// <summary>
        /// 英雄属性详情界面
        /// </summary>
        UIHeroAttrDetailForm = 1031,

        /// <summary>
        /// 物品描述
        /// </summary>
        UIItemTextTip = 1032,

        /// <summary>
        /// 酒馆招募界面
        /// </summary>
        UIRecruitForm = 1033,

        /// <summary>
        /// 酒馆招募动画界面
        /// </summary>
        UIRecruitAnimForm = 1034,

        /// <summary>
        /// 选择保底英雄界面
        /// </summary>
        UISelectFinalHeroForm = 1035,

        /// <summary>
        /// 卡池更新界面
        /// </summary>
        UIHeroPoolUpdateForm = 1036,

        /// <summary>
        /// 招募概率说明界面
        /// </summary>
        UIRecruitRatioForm = 1037,

        /// <summary>
        /// 建筑派遣界面
        /// </summary>
        UIBuildingDispatchForm = 1038,
        /// <summary>
        /// 背包统计界面
        /// </summary>
        UIBagStatistics = 1039,

        /// <summary>
        /// 建筑加速界面
        /// </summary>
        UIBuildingSpeedUpForm = 1040,

        /// <summary>
        /// 英雄荣誉墙界面
        /// </summary>
        UIHeroHonorForm = 1041,

        /// <summary>
        /// 英雄荣誉墙晋升界面
        /// </summary>
        UIHeroHonorUpgradeForm = 1042,

        /// <summary>
        /// 招募百抽动画界面
        /// </summary>
        UIRecruitHundredForm = 1043,

        /// <summary>
        /// 宝箱概率界面
        /// </summary>
        UIItemBoxProForm = 1044,

        /// <summary>
        /// 建造成功界面
        /// </summary>
        UIBuildingCompleteForm = 1045,

        /// <summary>
        /// 提示框
        /// </summary>
        UICommonTipForm = 1046,

        /// <summary>
        /// 建筑队列界面
        /// </summary>
        UIBuildingQueueForm = 1047,

        /// <summary>
        /// 建筑使用加速道具界面
        /// </summary>
        UIUseSpeedUpItemForm = 1048,

        /// <summary>
        /// 装备详情界面
        /// </summary>
        UIEquipmentDetailForm = 1049,

        /// <summary>
        /// 装备替换界面
        /// </summary>
        UIEquipmentReplaceForm = 1050,

        /// <summary>
        /// 选择宝箱界面
        /// </summary>
        UIChooseBoxForm = 1051,

        /// <summary>
        /// 建造小屋购买界面
        /// </summary>
        UIUnLockBuildingQueueForm = 1052,

        /// <summary>
        /// 建造小屋购买界面
        /// </summary>
        UIBattle5v5Debug = 1053,

        /// <summary>
        /// 商店界面
        /// </summary>
        UIGeneralShopForm = 1054,

        /// <summary>
        /// 英雄技能解锁弹窗
        /// </summary>
        UIHeroSkillUnlockForm = 1055,

        /// <summary>
        /// 商店购买界面
        /// </summary>
        UIGeneralBuyForm = 1056,

        /// <summary>
        /// 英雄技能Tips弹窗
        /// </summary>
        UIHeroSkillTipForm = 1057,

        /// <summary>
        /// 校场详情界面
        /// </summary>
        UIBuildingGround = 1058,

        /// <summary>
        /// 装备晋升界面
        /// </summary>
        UIEquipmentPromoteForm = 1059,

        /// <summary>
        /// 锁定屏幕界面
        /// </summary>
        UILockForm = 1060,

        /// <summary>
        /// 月卡、周卡跳转购买界面
        /// </summary>
        UIMonthWeekCardForm = 1061,

        /// <summary>
        /// 医院治疗界面
        /// </summary>
        UITreatSoldiersForm = 1062,

        /// <summary>
        /// 获取途径
        /// </summary>
        UIGetWayForm = 1063,

        /// <summary>
        /// 联盟创建界面
        /// </summary>
        UIUnionCreateForm = 1064,

        /// <summary>
        /// 联盟旗帜界面
        /// </summary>
        UIUnionFlagForm = 1065,

        /// <summary>
        /// 联盟语言界面
        /// </summary>
        UIUnionLanguageForm = 1066,

        /// <summary>
        /// 商城界面
        /// </summary>
        UIMallForm = 1067,

        /// <summary>
        /// 战斗出战界面
        /// </summary>
        UIBattle5v5Choose = 1068,

        /// <summary>
        /// 使用加速道具公用界面
        /// </summary>
        UICommonSpeedUpItemForm = 1069,

        /// <summary>
        /// 战斗UI
        /// </summary>
        UIBattle5V5Fight = 1070,

        /// <summary>
        /// 关卡战斗胜利
        /// </summary>
        UIBattle5V5DungeonVictory = 1071,

        /// <summary>
        /// 关卡战斗失败
        /// </summary>
        UIBattle5V5DungeonDefeat = 1072,

        /// <summary>
        /// 支付界面
        /// </summary>
        UIPaymentForm = 1073,

        /// <summary>
        /// 任务界面
        /// </summary>
        UITaskForm = 1074,

        /// <summary>
        /// 兵营训练界面
        /// </summary>
        UITrainingSoldiersForm = 1075,

        /// 联盟成员界面
        /// </summary>
        UIUnionMemberForm = 1076,

        /// <summary>
        /// 联盟成员管理界面
        /// </summary>
        UIUnionManageForm = 1077,

        /// <summary>
        /// 联盟成员职位设置界面
        /// </summary>
        UIUnionStepUpForm = 1078,

        /// <summary>
        /// 联盟记录界面
        /// </summary>
        UIUnionRecordForm = 1079,

        /// <summary>
        /// 联盟设置界面
        /// </summary>
        UIUnionSettingForm = 1080,

        /// <summary>
        /// 联盟申请界面
        /// </summary>
        UIUnionApplyForm = 1081,

        /// <summary>
        /// 联盟列表界面
        /// </summary>
        UIUnionListForm = 1082,

        /// <summary>
        /// 联盟权限查看界面
        /// </summary>
        UIUnionPermissionForm = 1083,

        /// <summary>
        /// 联盟分享界面
        /// </summary>
        UIUnionShareForm = 1084,

        /// <summary>
        /// 联盟分享确认界面
        /// </summary>
        UIUnionShareSureForm = 1085,

        /// <summary>
        /// 联盟编辑界面
        /// </summary>
        UIUnionEditForm = 1086,

        /// <summary>
        /// 士兵晋升界面
        /// </summary>
        UISoldiersTrainUpForm = 1087,

        /// <summary>
        /// 城内路点信息界面
        /// </summary>
        UIPvePathInfoForm = 1088,

        /// <summary>
        /// 关卡战斗统计界面
        /// </summary>
        UIBattle5V5DungeonStatsForm = 1089,

        /// <summary>
        /// 成长基金合购界面
        /// </summary>
        UIGrowthFundBuyForm = 1090,

        /// <summary>
        /// 联盟编辑变更界面
        /// </summary>
        UIUnionEditChangeForm = 1091,

        /// <summary>
        /// 选择每日特惠英雄界面
        /// </summary>
        UISelectDailyDealForm = 1092,

        /// <summary>
        /// 战力变化界面
        /// </summary>
        UIFightChangeForm = 1093,

        /// <summary>
        /// 建筑满级界面
        /// </summary>
        UIBuildingMaxLevelForm = 1094,

        /// <summary>
        /// 屏幕点击特效界面
        /// </summary>
        UIClickEffectForm = 1095,

        /// <summary>
        /// 联盟首次加入界面
        /// </summary>
        UIUnionFristForm = 1096,

        /// <summary>
        /// 联盟退出警告界面
        /// </summary>
        UIUnionExitWarnForm = 1097,

        /// <summary>
        /// 联盟信息界面
        /// </summary>
        UIUnionInfoForm = 1098,

        /// <summary>
        /// Vip界面
        /// </summary>
        UIVipForm = 1099,

        /// <summary>
        /// 任务宝箱奖励界面
        /// </summary>
        UITaskRewardForm = 1100,

        /// <summary>
        /// 人才大厅
        /// </summary>
        UIBuildingSurvivorForm = 1101,

        /// <summary>
        /// 联盟科技界面
        /// </summary>
        UIUnionTechForm = 1102,

        /// <summary>
        /// 联盟科技升级界面
        /// </summary>
        UIUnionTechUpForm = 1103,

        /// <summary>
        /// 幸存者列表
        /// </summary>
        UIDispatchListForm = 1104,

        /// <summary>
        /// 贸易货车界面
        /// </summary>
        UITradeTruckForm = 1105,

        /// <summary>
        /// 竞技场界面
        /// </summary>
        UIPeakArenaForm = 1106,

        /// <summary>
        /// 沙盘 预览
        /// </summary>
        UIWorldMapPreview = 1107,

        /// <summary>
        /// 货车出发界面
        /// </summary>
        UITradeTruckDepartForm = 1108,

        /// <summary>
        /// 沙盘 预览 底层
        /// </summary>
        UIWorldMapPreviewLow = 1109,

        /// <summary>
        /// Vip升级界面
        /// </summary>
        UIVipUpgradeForm = 1110,

        /// <summary>
        /// Vip免费点数领取提示界面
        /// </summary>
        UIVipPointTipForm = 1111,

        /// <summary>
        /// Vip点数购买界面
        /// </summary>
        UIVipPointBuyForm = 1112,

        /// <summary>
        /// Vip时间购买界面
        /// </summary>
        UIVipTimeBuyForm = 1113,

        /// <summary>
        /// 贸易历史记录界面
        /// </summary>
        UITradeTruckHistoryForm = 1114,

        /// <summary>
        /// 竞技场挑战界面
        /// </summary>
        UIArenaChallengeForm = 1115,

        /// <summary>
        /// 幸存者升星界面
        /// </summary>
        UISurvivorUpStarGradeForm = 1116,

        /// <summary>
        /// 竞技场挑战券购买界面
        /// </summary>
        UIChallengeBuyForm = 1117,

        /// <summary>
        /// 货车详情界面
        /// </summary>
        UITradeTruckDetailForm = 1118,

        /// <summary>
        /// 竞技场新兵奖励
        /// </summary>
        UINovicRewardForm = 1119,

        /// <summary>
        /// 多重选择确认窗口
        /// </summary>
        UIWorldMapSelectionConfirm = 1120,

        /// <summary>
        /// 超值活动
        /// </summary>
        UIChaoZhiActivityForm = 1121,

        /// <summary>
        /// 联盟里程碑界面
        /// </summary>
        UIUnionMileStoneForm = 1122,

        /// <summary>
        /// 货车掠夺界面
        /// </summary>
        UITradeTruckPlunderForm = 1123,

        /// <summary>
        /// 采集菜单
        /// </summary>
        UIWorldMapGatherMenu = 1124,

        /// <summary>
        /// 打野菜单
        /// </summary>
        UIWorldMapZombiesMenu = 1125,

        /// <summary>
        /// 末日精英
        /// </summary>
        UIWorldMapDoomEliteMenu = 1126,

        /// <summary>
        /// 玩家城镇菜单
        /// </summary>
        UIWorldMapTownMenu = 1127,

        /// <summary>
        /// 新兵挑战记录
        /// </summary>
        UINovicReportForm = 1128,

        /// <summary>
        /// 火车详情界面
        /// </summary>
        UITradeTrainDetailForm = 1129,

        /// <summary>
        /// 联盟排行榜界面
        /// </summary>
        UIUnionRankForm = 1130,

        /// <summary>
        /// 联盟排行榜奖励界面
        /// </summary>
        UIUnionRankRewardForm = 1131,

        /// <summary>
        /// 火车作战计划界面
        /// </summary>
        UITradeTrainBattlePlanForm = 1132,

        /// <summary>
        /// 玩家城市移动交互
        /// </summary>
        UITownMoveForm = 1133,

        /// <summary>
        /// 火车乘客列表界面
        /// </summary>
        UITradeTrainPassengerForm = 1134,

        /// <summary>
        /// 火车战斗结算界面
        /// </summary>
        UITradeTrainBattleResultForm = 1135,

        /// <summary>
        /// 火车战斗记录界面
        /// </summary>
        UITradeTrainBattleRecordForm = 1136,

        /// <summary>
        /// 无人机界面
        /// </summary>
        UIBuildingUAVForm = 1137,

        /// <summary>
        /// 无人机属性加成tip界面
        /// </summary>
        UIUAVAttributeTipForm = 1138,

        /// <summary>
        /// 巅峰竞技场奖励界面
        /// </summary>
        UIPeakRewardForm = 1139,

        /// <summary>
        /// 货车说明界面
        /// </summary>
        UITradeTruckDescForm = 1140,

        /// <summary>
        /// 联盟礼物界面
        /// </summary>
        UIUnionGiftForm = 1141,

        /// <summary>
        /// 联盟帮助界面
        /// </summary>
        UIUnionHelpForm = 1142,

        /// <summary>
        /// 无人机预览界面
        /// </summary>
        UIUAVLevelUpPreViewForm = 1143,

        /// <summary>
        /// 联盟礼包概率界面
        /// </summary>
        UIUnionGiftRatioForm = 1144,

        /// <summary>
        /// 无人机技能提示界面
        /// </summary>
        UIUAVSkillTip = 1145,

        /// <summary>
        /// 火车站台界面
        /// </summary>
        UITradeTrainStationPlatformForm = 1146,

        /// <summary>
        /// 无人机组件提升界面
        /// </summary>
        UIUAVComponentUpForm = 1147,

        /// <summary>
        /// 研究中心界面
        /// </summary>
        UITechForm = 1148,

        /// <summary>
        /// 列车运行守则界面
        /// </summary>
        UITradeTrainDescForm = 1149,

        /// <summary>
        /// 乘车感谢界面
        /// </summary>
        UITradeTrainThanksForm = 1150,

        /// <summary>
        /// 联盟帮助提示界面
        /// </summary>
        UIUnionHelpTipForm = 1151,

        /// <summary>
        /// VIP 乘客界面
        /// </summary>
        UITradeTrainVIPForm = 1152,

        /// <summary>
        /// VIP 乘客说明界面
        /// </summary>
        UITradeTrainVIPDescForm = 1153,

        /// <summary>
        /// VIP 邀请界面
        /// </summary>
        UITradeTrainVIPInviteForm = 1154,

        /// <summary>
        /// 组件预览界面
        /// </summary>
        UIUAVComponentPreviewForm = 1155,

        /// <summary>
        /// 邀请列车长界面
        /// </summary>
        UITradeTrainInviteConductorForm = 1156,
        
        /// <summary>
        /// 火车合约购买界面
        /// </summary>
        UITradeTrainContractForm = 1158,
    }
}