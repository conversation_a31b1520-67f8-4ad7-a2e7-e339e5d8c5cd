namespace Network
{
    public class NetworkEvent_ReceivedMessage : NetworkPassiveEvent
    {
        public uint m_cid = 0;

        public ushort m_eCode = 0;
        
        public ushort m_iProtoID = 0;

        public byte[] m_msg = null;

        public NetworkEvent_ReceivedMessage(TCPConnect conn, uint cid,ushort eCode,ushort iProtoID, byte[] msg)
            :base(NetworkEventType.NetworkEvent_ReceivedMessage,conn)
        {
            m_cid = cid;
            m_eCode = eCode;
            m_iProtoID = iProtoID;
            m_msg = msg;
        }

        public override void RunLuaCallback()
        {
            DoLuaCallback(m_cid, m_eCode, m_iProtoID, m_msg);
        }
    }
}
