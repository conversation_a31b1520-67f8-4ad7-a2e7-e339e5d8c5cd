using System;
using System.Collections.Generic;
using Network;
public class NetworkEventMgr
{
    private static NetworkEventMgr m_instance;
    public static NetworkEventMgr Instance
    {
        get
        {
            if (m_instance == null)
            {
                m_instance = new NetworkEventMgr();
            }
            return m_instance;
        }
    }

    private long m_globalConnectID = 0;

    private long m_connectTimtout = 5000;

    private Queue<NetworkActiveEvent> m_activeEvents = new Queue<NetworkActiveEvent>();

    private Queue<NetworkPassiveEvent> m_passiveEvents = new Queue<NetworkPassiveEvent>();

    private HashSet<long> m_closingConnection = new HashSet<long>();

    private HashSet<long> m_connectedConnection = new HashSet<long>();

    static public void Start()
    {
        NetworkEventMgr ins = NetworkEventMgr.Instance;
        TCPConnectMgr.Instance.Start();
    }

    static public void Stop()
    {
        TCPConnectMgr.Instance.Stop();
    }

    public long AssignUniqueConnectID()
    {
        return ++m_globalConnectID;
    }

    public long ConnectTimeOut
    {
        get { return m_connectTimtout; }
        set { m_connectTimtout = value; }
    }

    public void AddPassiveEvent(NetworkPassiveEvent ev)
    {
        lock (m_passiveEvents)
        {
            m_passiveEvents.Enqueue(ev);
        }
    }

    public List<NetworkPassiveEvent> PopAllPassiveEvent()
    {
        List<NetworkPassiveEvent> list = null;
        lock (m_passiveEvents)
        {
            list = new List<NetworkPassiveEvent>(m_passiveEvents);
            m_passiveEvents.Clear();
        }
        return list;
    }

    public void AddActiveEvent(NetworkActiveEvent ev)
    {
        lock (m_activeEvents)
        {
            m_activeEvents.Enqueue(ev);
        }
    }

    public List<NetworkActiveEvent> PopAllActiveEvent()
    {
        List<NetworkActiveEvent> list = null;
        lock (m_activeEvents)
        {
            list = new List<NetworkActiveEvent>(m_activeEvents);
            m_activeEvents.Clear();  
        }
        return list;
    }

    static public void Update()
    {
        List<NetworkPassiveEvent> eventList = NetworkEventMgr.Instance.PopAllPassiveEvent();

        for (int i = 0, m = eventList.Count; i < m; ++i)
        {
            NetworkPassiveEvent curEvent = eventList[i];

            if (curEvent.EventType == NetworkBaseEvent.NetworkEventType.NetworkEvent_SocketClosed)
            {
                NetworkEventMgr.Instance.m_closingConnection.Remove(curEvent.ConnectID);
                curEvent.RunLuaCallback();
                continue;
            }

            if (NetworkEventMgr.Instance.m_closingConnection.Contains(curEvent.ConnectID) == true)
            {
                continue;
            }

            if (curEvent.EventType == NetworkBaseEvent.NetworkEventType.NetworkEvent_ConnectOK)
            {
                NetworkEventMgr.Instance.m_connectedConnection.Add(curEvent.ConnectID);
            }
            else if (curEvent.EventType == NetworkBaseEvent.NetworkEventType.NetworkEvent_ConnectionLost)
            {
                NetworkEventMgr.Instance.m_connectedConnection.Remove(curEvent.ConnectID);
            }

            curEvent.RunLuaCallback();
        }
    }

    static public bool IsConnected(long iConnID)
    {
        return NetworkEventMgr.Instance.m_connectedConnection.Contains(iConnID);
    }

    static public long StartConnect(string host, int port, Action<int, long, uint ,ushort ,ushort , byte[]> luaCallback)
    {
        long newConnID = NetworkEventMgr.Instance.AssignUniqueConnectID();

        NetworkActiveEvent newEvent = new NetworkEvent_StartConnect(newConnID, host, port, luaCallback);
        NetworkEventMgr.Instance.AddActiveEvent(newEvent);

        return newConnID;
    }

    static public long StartConnectSLG(string host, int port, Action<int, long, uint, ushort, ushort, byte[]> luaCallback)
    {
        long newConnID = NetworkEventMgr.Instance.AssignUniqueConnectID();

        NetworkActiveEvent newEvent = new NetworkEvent_StartConnectSLG(newConnID, host, port, luaCallback);
        NetworkEventMgr.Instance.AddActiveEvent(newEvent);

        return newConnID;
    }

    static public void Disconnect(long iConnID)
    {
        NetworkEventMgr.Instance.m_closingConnection.Add(iConnID);
        NetworkEventMgr.Instance.m_connectedConnection.Remove(iConnID);

        NetworkActiveEvent newEvent = new NetworkEvent_Disconnect(iConnID);
        NetworkEventMgr.Instance.AddActiveEvent(newEvent);
    }

    static public void SendMessage(long iConnID,uint cid, ushort iProtoID, byte[] msg)
    {
        NetworkActiveEvent newEvent = new NetworkEvent_SendMessage(iConnID, cid, iProtoID, msg);
        NetworkEventMgr.Instance.AddActiveEvent(newEvent);
    }
}

