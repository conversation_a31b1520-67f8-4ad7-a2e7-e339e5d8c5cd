
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GameFramework.Event;
using GameFramework;

namespace Game.Hotfix
{
    public class OnSceneStartEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(OnSceneStartEventArgs).GetHashCode();

        public OnSceneStartEventArgs()
        {
            SceneId = 0;
        }

        public override int Id
        {
            get
            {
                return EventId;
            }
        }

        public int SceneId
        {
            get;
            private set;
        }

        public object UserData
        {
            get;
            private set;
        }

        public static OnSceneStartEventArgs Create(int sceneId, object userData = null)
        {
            OnSceneStartEventArgs args = ReferencePool.Acquire<OnSceneStartEventArgs>();
            args.SceneId = sceneId;
            args.UserData = userData;
            return args;
        }

        public override void Clear()
        {
            SceneId = 0;
        }
    }

}

