using System.Collections.Generic;
using GameFramework;
using GameFramework.Event;

namespace Game.Hotfix
{
    public sealed class UnionHelpChangeEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(UnionHelpChangeEventArgs).GetHashCode();

        public UnionHelpChangeEventArgs()
        {
            
        }

        public override int Id
        {
            get
            {
                return EventId;
            }
        }

        public int RefreshType
        {
            get;
            private set;
        }

        public static UnionHelpChangeEventArgs Create(int refreshType)
        {
            UnionHelpChangeEventArgs UnionHelpChangeEventArgs = ReferencePool.Acquire<UnionHelpChangeEventArgs>();
            UnionHelpChangeEventArgs.RefreshType = refreshType;
            return UnionHelpChangeEventArgs;
        }

        public override void Clear()
        {
            
        }
    }
}
