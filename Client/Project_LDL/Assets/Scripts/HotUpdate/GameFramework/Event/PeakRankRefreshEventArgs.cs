using GameFramework;
using GameFramework.Event;

namespace Game.Hotfix
{
    /// <summary>
    /// 巅峰竞技场刷新事件
    /// </summary>
    public class PeakRankRefreshEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(PeakRankRefreshEventArgs).GetHashCode();

        public override int Id
        {
            get { return EventId; }
        }

        /// <summary>
        /// 刷新结果
        /// </summary>
        public PeakRankRefreshEventArgs RefreshResult { get; private set; }

         public static PeakRankRefreshEventArgs Create()
        {
            PeakRankRefreshEventArgs peakRankRefreshEventArgs = ReferencePool.Acquire<PeakRankRefreshEventArgs>();
            return peakRankRefreshEventArgs;
        }

        public override void Clear()
        {
            RefreshResult = null;
        }
    }
}