using GameFramework;
using GameFramework.Event;

namespace Game.Hotfix
{
    public sealed class OnWorldMapCameraMoveArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(OnWorldMapCameraMoveArgs).GetHashCode();

        public OnWorldMapCameraMoveArgs()
        {
            
        }

        public override int Id
        {
            get
            {
                return EventId;
            }
        }

        public bool IsManual;
        

        public static OnWorldMapCameraMoveArgs Create(bool isManual)
        {
            OnWorldMapCameraMoveArgs e = ReferencePool.Acquire<OnWorldMapCameraMoveArgs>();
            e.IsManual = isManual;
            return e;
        }

        public override void Clear()
        {
            
        }
    }
}
