using System.Collections.Generic;
using GameFramework;
using GameFramework.Event;

namespace Game.Hotfix
{
    public sealed class BagChangeEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(BagChangeEventArgs).GetHashCode();

        public BagChangeEventArgs()
        {
            
        }

        public override int Id
        {
            get
            {
                return EventId;
            }
        }

        public static BagChangeEventArgs Create()
        {
            BagChangeEventArgs bagChangeEventArgs = ReferencePool.Acquire<BagChangeEventArgs>();
            return bagChangeEventArgs;
        }

        public override void Clear()
        {
            
        }
    }
}
