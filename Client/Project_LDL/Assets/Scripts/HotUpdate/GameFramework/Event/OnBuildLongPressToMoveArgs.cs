using GameFramework;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class OnBuildLongPressToMoveArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(OnBuildLongPressToMoveArgs).GetHashCode();
        
        public override int Id
        {
            get
            {
                return EventId;
            }
        }

        public bool IsPressing { get; set; }
        public Vector3? WorldPosition { get; set; }

        public static OnBuildLongPressToMoveArgs Create(bool isPressing,Vector3? worldPosition)
        {
            OnBuildLongPressToMoveArgs args = ReferencePool.Acquire<OnBuildLongPressToMoveArgs>();
            args.IsPressing = isPressing;
            args.WorldPosition = worldPosition;
            return args;
        }
        
        public override void Clear()
        {
            IsPressing = false;
            WorldPosition = null;
        }
        

    }
}