using GameFramework;
using GameFramework.Event;
using UnityEngine;

namespace Game.Hotfix
{
    public class OnAreaStateChangeEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(OnAreaStateChangeEventArgs).GetHashCode();


        public override int Id
        {
            get { return EventId; }
        }

        public MainCityAreaModule MainCityAreaModule;
        public MainCityAreaState NewState;

        public static OnAreaStateChangeEventArgs Create(MainCityAreaModule module,MainCityAreaState newState)
        {
            OnAreaStateChangeEventArgs args = ReferencePool.Acquire<OnAreaStateChangeEventArgs>();
            args.MainCityAreaModule = module;
            args.NewState = newState;
            return args;
        }
        
        public override void Clear()
        {
            MainCityAreaModule = null;
        }
    }
}