using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GameFramework.Event;
using GameFramework;

namespace Game.Hotfix
{
    public class PaymentFinishEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(PaymentFinishEventArgs).GetHashCode();

        public PaymentFinishEventArgs()
        {
            
        }

        public override int Id
        {
            get
            {
                return EventId;
            }
        }

        public static PaymentFinishEventArgs Create()
        {
            PaymentFinishEventArgs paymentFinishEventArgs = ReferencePool.Acquire<PaymentFinishEventArgs>();
            return paymentFinishEventArgs;
        }

        public override void Clear()
        {

        }
    }
}
