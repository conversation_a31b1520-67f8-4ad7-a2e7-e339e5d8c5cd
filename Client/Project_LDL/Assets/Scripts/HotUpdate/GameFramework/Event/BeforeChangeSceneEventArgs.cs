using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using GameFramework.Event;
using GameFramework;

namespace Game.Hotfix
{
    public class BeforeChangeSceneEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(BeforeChangeSceneEventArgs).GetHashCode();

        public BeforeChangeSceneEventArgs()
        {
            CurSceneId = 0;
            NextSceneId = 0;
        }

        public override int Id
        {
            get { return EventId; }
        }

        public int CurSceneId { get; private set; }

        public int NextSceneId { get; private set; }

        public static BeforeChangeSceneEventArgs Create(int curSceneId, int nextSceneId)
        {
            BeforeChangeSceneEventArgs eArgs = ReferencePool.Acquire<BeforeChangeSceneEventArgs>();
            eArgs.CurSceneId = curSceneId;
            eArgs.NextSceneId = nextSceneId;
            return eArgs;
        }

        public override void Clear()
        {
            CurSceneId = 0;
            NextSceneId = 0;
        }
    }
}