using System.Collections.Generic;
using GameFramework;
using GameFramework.Event;

namespace Game.Hotfix
{
    public sealed class TechChangeEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(TechChangeEventArgs).GetHashCode();

        public TechChangeEventArgs()
        {
            
        }

        public override int Id
        {
            get
            {
                return EventId;
            }
        }

        public static TechChangeEventArgs Create()
        {
            TechChangeEventArgs TechChangeEventArgs = ReferencePool.Acquire<TechChangeEventArgs>();
            return TechChangeEventArgs;
        }

        public override void Clear()
        {
            
        }
    }
}
