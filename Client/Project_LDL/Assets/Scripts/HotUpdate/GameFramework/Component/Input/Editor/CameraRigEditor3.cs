#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;

namespace Game.Hotfix
{
    [CustomEditor(typeof(CameraRig3))]
    public class CameraRigEditor3 : UnityEditor.Editor
    {
        CameraRig3 m_CameraRig;
        Rect m_MapSize;
        SerializedProperty m_SerializedPropertyMapSize;

        Rect m_MapSize2;
        SerializedProperty m_SerializedPropertyMapSize2;

        void OnSceneGUI()
        {
            m_MapSize = Draw(m_MapSize, m_CameraRig.color1);
            if (m_SerializedPropertyMapSize.rectValue != m_MapSize)
            {
                m_SerializedPropertyMapSize.rectValue = m_MapSize;
                serializedObject.ApplyModifiedProperties();
            }

            m_MapSize2 = Draw(m_MapSize2, m_CameraRig.color2);
            if (m_SerializedPropertyMapSize2.rectValue != m_MapSize2)
            {
                m_SerializedPropertyMapSize2.rectValue = m_MapSize2;
                serializedObject.ApplyModifiedProperties();
            }
        }

        Rect Draw(Rect m_MapSize, Color color)
        {
            Handles.color = color;

            float y = m_CameraRig.floorY;
            Plane floor = new Plane(Vector3.up, y);

            float middleX = (m_MapSize.xMax + m_MapSize.xMin) * 0.5f;
            float middleY = (m_MapSize.yMax + m_MapSize.yMin) * 0.5f;

            Vector3 bottomPosition = new Vector3(middleX, y, m_MapSize.yMin),
                topPosition = new Vector3(middleX, y, m_MapSize.yMax),
                leftPosition = new Vector3(m_MapSize.xMin, y, middleY),
                rightPosition = new Vector3(m_MapSize.xMax, y, middleY);

            // Draw handles to resize map rect
            float size = HandleUtility.GetHandleSize(m_CameraRig.transform.position) * 0.125f;
            Vector3 snap = Vector3.one * 0.5f;
            var fmh_53_65_638756633231036017 = Quaternion.LookRotation(Vector3.up);
            Vector3 bottom = Handles.FreeMoveHandle(bottomPosition, size, snap,
                Handles.RectangleHandleCap);
            var fmh_55_59_638756633231048222 = Quaternion.LookRotation(Vector3.up);
            Vector3 top = Handles.FreeMoveHandle(topPosition, size, snap,
                Handles.RectangleHandleCap);
            var fmh_57_61_638756633231049530 = Quaternion.LookRotation(Vector3.up);
            Vector3 left = Handles.FreeMoveHandle(leftPosition, size, snap,
                Handles.RectangleHandleCap);
            var fmh_59_63_638756633231050447 = Quaternion.LookRotation(Vector3.up);
            Vector3 right = Handles.FreeMoveHandle(rightPosition, size, snap,
                Handles.RectangleHandleCap);


            // Draw a box to represent the map rect
            Vector3 topLeft = new Vector3(m_MapSize.x, y, m_MapSize.y),
                topRight = topLeft + new Vector3(m_MapSize.width, 0, 0),
                bottomLeft = topLeft + new Vector3(0, 0, m_MapSize.height),
                bottomRight = bottomLeft + new Vector3(m_MapSize.width, 0, 0);
            Handles.DrawLine(topLeft, topRight);
            Handles.DrawLine(topRight, bottomRight);
            Handles.DrawLine(bottomRight, bottomLeft);
            Handles.DrawLine(bottomLeft, topLeft);

            m_MapSize.xMin = left.x;
            m_MapSize.xMax = right.x;
            m_MapSize.yMin = bottom.z;
            m_MapSize.yMax = top.z;

            return m_MapSize;
        }


        void OnEnable()
        {
            m_CameraRig = target as CameraRig3;
            m_SerializedPropertyMapSize = serializedObject.FindProperty("moveRegion1");
            m_MapSize = m_SerializedPropertyMapSize.rectValue;

            m_SerializedPropertyMapSize2 = serializedObject.FindProperty("moveRegion2");
            m_MapSize2 = m_SerializedPropertyMapSize2.rectValue;
        }
    }
}

#endif