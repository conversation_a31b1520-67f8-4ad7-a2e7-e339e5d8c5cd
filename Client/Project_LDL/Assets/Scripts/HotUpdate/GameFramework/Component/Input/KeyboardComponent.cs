using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    [DisallowMultipleComponent]
    [AddComponentMenu("GameCustom/KeyboardComponent")]
    public class KeyboardComponent : GameFrameworkComponent
    {
        void Start()
        {
            
        }

        void Update()
        {
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                GameEntry.UI.CloseTopUIFormByEsc();
            }
        }
    }
}
