using System;
using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Hotfix
{
    public class MainCityFog : MonoBehaviour
    {
        private static bool IsDirty = false;
        
        [Header("可见区域纹理")]
        [Tooltip("可见区域纹理的大小 (宽度, 高度)")]
        public Vector2Int m_TexSize = new Vector2Int(128, 128);
        [Tooltip("表示可见区域的纹理")]
        public Texture2D m_VisualTex;
        [Tooltip("用于模糊处理的渲染纹理的大小 (宽度, 高度)")]
        public Vector2Int m_RTSize = new Vector2Int(1024, 1024);
        [Tooltip("用于模糊处理的渲染纹理")]
        public RenderTexture m_VisualRT;
        [Tooltip("可见区域的刷新率 (每秒更新次数)")]
        public float m_RefreshRate = 10;

        [Header("优化")]
        [Tooltip("是否启用模糊效果?")]
        public bool m_Blur = true;
        [Tooltip("用于模糊处理的材质")]
        public Material m_BlurMat;
        [Tooltip("模糊偏移量 (控制模糊强度)")]
        public float m_BlurOffset = 1;
        [Tooltip("是否启用线性插值 (平滑帧之间的过渡)?")]
        public bool m_Lerp = true;
        [Tooltip("用于线性插值的纹理")]
        public Texture2D m_VisualTexForLerp;
        [Tooltip("线性插值速度 (控制平滑速度)")]
        public float m_LerpSpeed = 1;

        [Header("测试")]
        [Tooltip("用于应用可见区域的材质 (例如, 平面或其他对象)")]
        public Material m_ApplyMaterial; //将最终的视觉结果应用到平面或其他可渲染对象
        [ShowInInspector]
        public List<Bounds> DefaultBounds = new List<Bounds>();
        /// <summary>
        /// 用于控制可见区域更新频率的计时器.
        /// </summary>
        private float m_RefreshRateTimer = 0;

        /// <summary>
        /// 包含用于重置视觉纹理的清除颜色的数组.
        /// </summary>
        private Color[] m_ClearColor;

        private bool m_UpdateAble = false;
        
        private void OnEnable()
        {
            // 初始化清除颜色数组
            m_ClearColor = new Color[m_TexSize.x * m_TexSize.y];
            for (int i = 0; i < m_TexSize.x; i++)
            {
                for (int j = 0; j < m_TexSize.y; j++)
                {
                    m_ClearColor[i * m_TexSize.y + j] = Color.clear;
                }
            }

            // 创建视觉纹理
            m_VisualTex = new Texture2D(m_TexSize.x, m_TexSize.y, TextureFormat.RHalf, false, false);
            m_VisualTex.wrapMode = TextureWrapMode.Clamp;
            m_VisualTex.SetPixels(m_ClearColor);

            for (int i = 0; i < DefaultBounds.Count; i++)
            {
                AddBound(DefaultBounds[i],false);    
            }
            m_UpdateAble = DefaultBounds.Count>0;
            
            // 根据是否启用模糊处理设置材质的 _MaskTex 属性
            if (m_Blur)
            {
                m_VisualRT = RenderTexture.GetTemporary(m_RTSize.x, m_RTSize.y, 0, RenderTextureFormat.RHalf);
                m_ApplyMaterial.SetTexture("_MaskTex", m_VisualRT);
            }
            else
            {
                m_ApplyMaterial.SetTexture("_MaskTex", m_VisualTex);
            }

            // 如果启用, 创建用于线性插值的纹理
            if (m_Lerp)
            {
                m_VisualTexForLerp = new Texture2D(m_TexSize.x, m_TexSize.y, TextureFormat.RHalf, false, false);
                m_VisualTexForLerp.wrapMode = TextureWrapMode.Clamp;
            }
        }

        private void OnDisable()
        {
            // 清理资源
            Destroy(m_VisualTex);
            if (m_Blur)
                RenderTexture.ReleaseTemporary(m_VisualRT);
            if (m_Lerp)
                Destroy(m_VisualTexForLerp);
        }

        private void Start()
        {
            // 如果启用, 将视觉纹理复制到插值纹理
            if (m_Lerp)
                Graphics.CopyTexture(m_VisualTex, m_VisualTexForLerp);
        }

        private void UpdateDirty()
        {
            if (!IsDirty) return;
            IsDirty = false;
            if (m_Blur)
            {
                m_VisualRT = RenderTexture.GetTemporary(m_RTSize.x, m_RTSize.y, 0, RenderTextureFormat.RHalf);
                m_ApplyMaterial.SetTexture("_MaskTex", m_VisualRT);
            }
            else
            {
                m_ApplyMaterial.SetTexture("_MaskTex", m_VisualTex);
            }

            m_UpdateAble = true;
        }

        private void Update()
        {
            UpdateDirty();
            
            if(!m_UpdateAble)
                return;
            m_UpdateAble = false;

            // 根据是否启用线性插值来更新可见区域
            if (m_Lerp)
            {
                m_RefreshRateTimer += Time.deltaTime;
                if (m_RefreshRateTimer < 1 / m_RefreshRate)
                {
                    // 使用线性插值平滑可见区域
                    SmoothVisual();
                    return;
                }
                else
                {
                    m_RefreshRateTimer -= 1 / m_RefreshRate;
                }
                SmoothVisual();
            }

            // 应用模糊效果
            if (m_Blur)
            {
                // 将视觉纹理 (或插值纹理) Blit 到渲染纹理
                Graphics.Blit(m_Lerp ? m_VisualTexForLerp : m_VisualTex, m_VisualRT);

                // 分两个 Pass 应用模糊效果 (水平和垂直)
                RenderTexture temp = RenderTexture.GetTemporary(m_RTSize.x, m_RTSize.y, 0, RenderTextureFormat.RHalf);
                m_BlurMat.SetVector("_Offsets", new Vector2(0, m_BlurOffset / m_VisualRT.height));
                Graphics.Blit(m_VisualRT, temp, m_BlurMat);
                m_BlurMat.SetVector("_Offsets", new Vector2(m_BlurOffset / m_VisualRT.width, 0));
                Graphics.Blit(temp, m_VisualRT, m_BlurMat);
                RenderTexture.ReleaseTemporary(temp);
            }
        }

        public void AddBound(Bounds boundsParam,bool update = true)
        {
            Bounds bounds = new Bounds(boundsParam.center,boundsParam.size);
            bounds.center += new Vector3(1, 0, -1);//视角差
            bounds.extents += new Vector3(5, 0, 5);
                
            int cellSize = 1;
            Vector3 minBounds = bounds.min;
            Vector3 maxBounds = bounds.max;
            Vector2Int minCell = new Vector2Int(
                Mathf.FloorToInt(minBounds.x / cellSize),
                Mathf.FloorToInt(minBounds.z / cellSize)
            );
            Vector2Int maxCell = new Vector2Int(
                Mathf.FloorToInt(maxBounds.x / cellSize),
                Mathf.FloorToInt(maxBounds.z / cellSize)
            );
            
            for (int x = minCell.x; x <= maxCell.x; x++)
            {
                for (int y = minCell.y; y <= maxCell.y; y++)
                {
                    m_VisualTex.SetPixel(x, y, new Color(1, 0, 0, 0)); // 将像素设置为可见 (白色)
                }
            }
            
            m_VisualTex.SetPixel(70, 70, new Color(1, 0, 0, 0)); // 将像素设置为可见 (白色)
            m_VisualTex.Apply();

            if (update)
                m_UpdateAble = true;    
        }

        /// <summary>
        /// 使用线性插值平滑可见区域.
        /// </summary>
        private void SmoothVisual()
        {
            // 迭代纹理中的每个像素
            for (int r = 0; r < m_TexSize.x; r++)
            {
                for (int c = 0; c < m_TexSize.y; c++)
                {
                    // 从插值纹理和视觉纹理中获取像素颜色
                    Color bufferPixel = m_VisualTexForLerp.GetPixel(r, c);
                    Color targetPixel = m_VisualTex.GetPixel(r, c);

                    // 在两种颜色之间进行线性插值
                    m_VisualTexForLerp.SetPixel(r, c, Color.Lerp(bufferPixel, targetPixel, m_LerpSpeed * Time.deltaTime));
                }
            }

            // 将更改应用到插值纹理
            m_VisualTexForLerp.Apply();
        }

        [HorizontalGroup("Split", 0.5f)]
        [Button(ButtonSizes.Large), GUIColor(0.4f, 0.8f, 1)]
        public void SetDirty()
        {
            IsDirty = true;
        }
        
        [HorizontalGroup("Split", 0.5f)]
        [Button(ButtonSizes.Large), GUIColor(0.4f, 0.8f, 1)]
        public void SetUpdate()
        {
            this.m_UpdateAble = true;
        }
        
#if UNITY_EDITOR
        class ModelPostprocessor : UnityEditor.AssetPostprocessor
        {

            static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets, string[] movedFromAssetPaths)
            {
                IsDirty = true;
            }
        }
#endif
        
    }
}