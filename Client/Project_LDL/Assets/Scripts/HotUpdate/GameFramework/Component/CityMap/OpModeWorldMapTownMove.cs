using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class OpModeWorldMapTownMove : OpModeBase
    {
        private bool m_InTouchPreview = false;

        private int m_PreviewId;

        private int m_TargetEntityUid;
        private Vector2Int m_TargetGridPos;
        private Vector2Int m_TargetGridNewPos;

        private bool m_HasMoveCamera = false;
        private bool m_HasZoomCamera = false;

        public OpModeWorldMapTownMove()
        {
        }

        public override void OnDestroy()
        {
        }

        public override MapOpType GetOpType()
        {
            return MapOpType.TOWN_MOVE_MODE;
        }

        protected override void OnEnableStateChange(bool enable)
        {
            base.OnEnableStateChange(enable);
            if (!enable)
            {
                if (m_PreviewId != 0)
                {
                    Game.GameEntry.Entity.HideEntity(m_PreviewId);
                }
            }
        }

        public int SetData(Vector2Int targetGridPos)
        {
            m_TargetGridPos = targetGridPos;
            m_TargetGridNewPos = targetGridPos;

            var path = "Assets/ResPackage/Prefab/Building/townMovePreview.prefab";
            ED_TownMovePreview param =
                new ED_TownMovePreview(Game.GameEntry.Entity.GenerateSerialId(), targetGridPos);

            m_PreviewId = Game.GameEntry.Entity.ShowTownMovePreview(path, param, typeof(EL_TownMovePreview));
            return m_PreviewId;
        }

        public override void Update(float dt)
        {
            if (m_HasMoveCamera)
            {
                m_HasMoveCamera = false;
                GameEntry.Event.Fire(OnWorldMapCameraMoveArgs.EventId, OnWorldMapCameraMoveArgs.Create(true));
            }

            if (m_HasZoomCamera)
            {
                m_HasZoomCamera = false;
                GameEntry.Event.Fire(OnWorldMapCameraZoomArgs.EventId,
                    OnWorldMapCameraZoomArgs.Create());
            }
        }

        /// <summary>
        /// 移动取消
        /// </summary>
        public void MoveTownCanceled()
        {
        }

        /// <summary>
        /// 移动结束
        /// </summary>
        public void MoveTownConfirmed()
        {
            //     Build.Build build = m_Data.ToBuild();
            //     var preview = Game.GameEntry.Entity.GetEntity(m_BuildingPreviewId);
            //     var tempPos = MapGridUtils.WorldToGrid(preview.transform.position);
            //     build.X = (uint)tempPos.x;
            //     build.Y = (uint)tempPos.y;
            //
            //     var oldPosX = m_Data.GetGridPos().x;
            //     var oldPosY = m_Data.GetGridPos().y;
            //     
            //     GameEntry.LogicData.BuildingData.BuildMoveReq(build,(buildBack =>
            //     {
            //         Game.GameEntry.Entity.DetachEntity(m_Building.Entity.Id);
            //         m_Building.transform.position = preview.transform.position;
            //
            //         GameEntry.LogicData.GridData.RemoveBuilding(oldPosX, oldPosY, m_Data.GetGridAreaL(),
            //             m_Data.GetGridAreaW());
            //         m_Data.SetGridPos((int)build.X, (int)build.Y);
            //         GameEntry.LogicData.GridData.AddBuilding(m_Data);
            //     
            //         Game.GameEntry.Entity.HideEntity(m_BuildingPreviewId);
            //
            //         // m_Building.SendEvent(EL_BuildingEvent.OnResetPosition, null);
            //     }));
        }

        protected override void OnTap(PointerActionInfo pointer)
        {
            base.OnTap(pointer);
            if (pointer.startedOverUI)
            {
                return;
            }
            
            Vector3 worldPos =
                MapGridUtils.GetWorldPositionByScreenPos(pointer.currentPosition, GameEntry.Camera.CurUseCamera);
            var gridPos = MapGridUtils.WorldToGrid(worldPos);
            var entity = Game.GameEntry.Entity.GetEntity(m_PreviewId);

            var preview = entity.Logic as EL_TownMovePreview;
            preview.SetGridPos(gridPos);
            m_TargetGridNewPos = gridPos;
        }

        protected override void OnPressed(PointerActionInfo pointer)
        {
            base.OnPressed(pointer);
            if (pointer.startedOverUI)
            {
                return;
            }

            Vector3 worldPos =
                MapGridUtils.GetWorldPositionByScreenPos(pointer.currentPosition, GameEntry.Camera.CurUseCamera);
            var gridPos = MapGridUtils.WorldToGrid(worldPos);

            var entity = Game.GameEntry.Entity.GetEntity(m_PreviewId);
            var preview = entity.Logic as EL_TownMovePreview;
            if (preview.IsInGrid(gridPos.x, gridPos.y))
            {
                Debug.Log("IN ");
                m_InTouchPreview = true;
            }
            else
            {
                Debug.Log("out ");
                m_InTouchPreview = false;
            }
        }

        protected override void OnReleased(PointerActionInfo pointer)
        {
            base.OnReleased(pointer);
            m_InTouchPreview = false;
        }

        protected override void OnDragged(PointerActionInfo pointer)
        {
            if (pointer.startedOverUI)
            {
                return;
            }

            if (m_InTouchPreview)
            {
                Vector3 worldPos =
                    MapGridUtils.GetWorldPositionByScreenPos(pointer.currentPosition, GameEntry.Camera.CurUseCamera);
                var gridPos = MapGridUtils.WorldToGrid(worldPos);
                var entity = Game.GameEntry.Entity.GetEntity(m_PreviewId);
                var preview = entity.Logic as EL_TownMovePreview;
                preview.SetGridPos(gridPos);
                m_TargetGridNewPos = gridPos;
            }
            else
            {
                base.OnDragged(pointer);
                m_HasMoveCamera = true;
            }
        }

        protected override void OnSpunWheel(WheelInfo obj)
        {
            base.OnSpunWheel(obj);
            m_HasZoomCamera = true;
        }

        public void GetPreviewPos(out Vector2Int pos)
        {
            pos = m_TargetGridNewPos;
        }

        public int? GetPreviewUid()
        {
            if (m_PreviewId == 0)
                return null;
            return m_PreviewId;
        }
    }
}