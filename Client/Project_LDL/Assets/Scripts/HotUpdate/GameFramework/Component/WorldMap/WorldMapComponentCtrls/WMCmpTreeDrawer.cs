using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Game.Hotfix
{
    public class WMCmpTreeDrawer : IWMCmp
    {
        private const ushort GROUND_SIZE = 20;

        private Dictionary<Vector2Int, List<int>> m_ShowListDic = new Dictionary<Vector2Int, List<int>>();
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();

        private WorldMapComponent m_WorldMapComponent;

        public WMCmpTreeDrawer(WorldMapComponent component)
        {
            m_WorldMapComponent = component;
        }

        public void Init()
        {
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
        }

        public void UnInit()
        {
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
        }

        private void OnCameraMove()
        {
            m_WorldMapComponent.GetInShowLIstByGrid(GROUND_SIZE, 1, WorldMapLOD.Level1,
                WorldMapLOD.Level4, out List<Vector2Int> showListNew);
            
            List<Vector2Int> add = showListNew.Except(m_ShowList).ToList();
            List<Vector2Int> remove = m_ShowList.Except(showListNew).ToList();

            foreach (var t in remove)
            {
                Remove(t);
            }

            foreach (var t in add)
            {
                Add(t);
            }
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                if (!m_ShowListDic.ContainsKey(grid))
                {
                    var list = GameEntry.LogicData.WorldMapData.GetTreeList(grid);
                    if (list != null)
                    {
                        var treeIdList = new List<int>();
                        for (int i = 0; i < list.Count; i++)
                        {
                            Vector3Int treeData = list[i];
                            var path = GetTreePathById(treeData.z);
                            int id = GameEntry.Entity.ShowWorldMapDisplay(path, new Vector3(treeData.x, 0, treeData.y));
                            treeIdList.Add(id);
                        }

                        m_ShowListDic.Add(grid, treeIdList);
                    }
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (m_ShowListDic.TryGetValue(grid, out var treeIdList))
                {
                    for (int i = 0; i < treeIdList.Count; i++)
                    {
                        var id = treeIdList[i];
                        GameEntry.Entity.HideEntity(id);
                    }

                    m_ShowListDic.Remove(grid);
                }
            }
        }

        private string GetTreePathById(int id)
        {
            int rId = id % 5 + 1;
            return "Assets/ResPackage/WorldMap/Prefab/WroldMapTree" + rId + ".prefab";
        }
    }
}