using UnityEngine;

namespace Game.Hotfix
{
    public class WorldMapOperationCtrl
    {

        public MapOpType? CurOpMode => m_CurOpMode?.GetOpType();
        
        private OpModeWorldMapDefault m_OpModeDefault;
        private OpModeWorldMapTownMove m_OpModeTownMove;
        
        private OpModeBase m_CurOpMode = null;
        
        public WorldMapOperationCtrl()
        {
            m_OpModeDefault = new OpModeWorldMapDefault();
            m_OpModeTownMove = new OpModeWorldMapTownMove();

            SwitchOp(MapOpType.DEFAULT_MODE);
        }
        
        private void SwitchOp(MapOpType type)
        {
            var old = m_CurOpMode?.GetOpType();
            
            m_CurOpMode?.SetEnable(false);
            
            if (type == MapOpType.DEFAULT_MODE)
            {
                m_CurOpMode = m_OpModeDefault;
            }else if (type == MapOpType.TOWN_MOVE_MODE)
            {
                m_CurOpMode = m_OpModeTownMove;
            }

            if (m_CurOpMode != null) m_CurOpMode.SetEnable(true);
            
            GameEntry.Event.Fire(this, OnOpModeChangeEventArgs.Create(old, m_CurOpMode?.GetOpType()));
        }
        
        public void Update()
        {
            if (m_CurOpMode != null)
            {
                m_CurOpMode.Update(Time.deltaTime);
            }
        }
        
        public void LateUpdate()
        {
            if (m_CurOpMode != null)
            {
                m_CurOpMode.LateUpdate(Time.deltaTime);
            }
        }
        
        public void Destroy()
        {
            m_OpModeDefault.Destroy();
        }

        public bool TryEnterMoveTownMode(Vector2Int targetGridPos)
        {
            if (m_CurOpMode.GetOpType() == MapOpType.TOWN_MOVE_MODE) return false;

            int previewId = m_OpModeTownMove.SetData(targetGridPos);
            SwitchOp(MapOpType.TOWN_MOVE_MODE);
            
            var formParam = new UITownMoveFormParam
            {
                TargetUid = previewId,
            };
            GameEntry.UI.OpenUIForm(EnumUIForm.UITownMoveForm, formParam);

            return true;
        }
        
        public void MoveTownCanceled()
        {
            if (m_CurOpMode.GetOpType() == MapOpType.TOWN_MOVE_MODE)
            {
                m_OpModeTownMove.MoveTownCanceled();
            }
            SwitchOp(MapOpType.DEFAULT_MODE);
        }

        public void MoveTownConfirmed()
        {
            if (m_CurOpMode.GetOpType() == MapOpType.TOWN_MOVE_MODE)
            {
                m_OpModeTownMove.MoveTownConfirmed();
            }
            SwitchOp(MapOpType.DEFAULT_MODE);
        }
        
        public void GetPreviewPos(out Vector2Int pos)
        {
            m_OpModeTownMove.GetPreviewPos(out pos);
        }

        public int? GetPreviewUid()
        {
            return m_OpModeTownMove.GetPreviewUid();
        }
    }
}