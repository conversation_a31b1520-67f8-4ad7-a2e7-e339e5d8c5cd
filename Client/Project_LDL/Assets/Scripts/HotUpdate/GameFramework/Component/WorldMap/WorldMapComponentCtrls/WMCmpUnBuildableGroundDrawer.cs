using System.Collections.Generic;
using System.Linq;
using GameFramework.Event;
using Sirenix.Utilities;
using UnityEngine;

namespace Game.Hotfix
{
    struct UnBuildableData
    {
        public int EntityId;
        public Rect Rect;
    }
    
    public class WMCmpUnBuildableGroundDrawer : IWMCmp
    {
        private const ushort GROUND_SIZE = 20;
        private const string PATH = "Assets/ResPackage/WorldMap/Prefab/WorldMapUnbuildGround.prefab";

        private Dictionary<Vector2Int, List<UnBuildableData>> m_ShowListDic = new Dictionary<Vector2Int, List<UnBuildableData>>();
        private HashSet<Vector2Int> m_ShowList = new HashSet<Vector2Int>();

        private WorldMapComponent m_WorldMapComponent;

        private Vector2Int m_GridMaxSize;

        private bool m_IsWorking = false;

        private List<Vector2Int> m_ShowListNew;

        private Dictionary<Vector2Int, int> m_BuildBlockedData = new Dictionary<Vector2Int, int>();
        
        public WMCmpUnBuildableGroundDrawer(WorldMapComponent component)
        {
            m_WorldMapComponent = component;
        }

        public void Init()
        {
            m_ShowListNew = new List<Vector2Int>();
            
            m_GridMaxSize = new Vector2Int(m_WorldMapComponent.WorldMapSize.x / GROUND_SIZE,
                m_WorldMapComponent.WorldMapSize.y / GROUND_SIZE);
            
            m_IsWorking = m_WorldMapComponent.GetMapOpType() == MapOpType.TOWN_MOVE_MODE;
            
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
            
            GameEntry.Event.Subscribe(OnOpModeChangeEventArgs.EventId,OnOpModeChangeEventArgsHandler);
        }

        private void OnOpModeChangeEventArgsHandler(object sender, GameEventArgs e)
        {
            if (e is OnOpModeChangeEventArgs args)
            {
                if(args.newOp == MapOpType.TOWN_MOVE_MODE)
                {
                    m_IsWorking = true;
                    OnCameraMove();
                }
                else
                {
                    m_IsWorking = false;
                    OnCameraMove();
                }
            }
        }

        public void UnInit()
        {
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
            GameEntry.Event.Unsubscribe(OnOpModeChangeEventArgs.EventId,OnOpModeChangeEventArgsHandler);
        }

        private void OnCameraMove()
        {
            if (m_IsWorking)
            {
                m_WorldMapComponent.GetInShowLIstByGrid(GROUND_SIZE, 1, WorldMapLOD.Level1,
                    WorldMapLOD.Level3, out m_ShowListNew);
            }
            else
            {
                m_ShowListNew.Clear();
            }

            List<Vector2Int> add = m_ShowListNew.Except(m_ShowList).ToList();
            List<Vector2Int> remove = m_ShowList.Except(m_ShowListNew).ToList();

            foreach (var t in remove)
            {
                Remove(t);
            }

            foreach (var t in add)
            {
                Add(t);
            }
        }

        private void Add(Vector2Int grid)
        {
            if (m_ShowList.Add(grid))
            {
                if (!m_ShowListDic.ContainsKey(grid))
                {
                    WorldMapGridData gridData = GameEntry.LogicData.WorldMapData.GetGridDataByPos(grid);
                    List<Rect> list = gridData.GetUnBuildArea();
                    if (list != null && list.Count > 0)
                    {
                        var idList = new List<UnBuildableData>();
                        for (int i = 0; i < list.Count; i++)
                        {
                            Rect data = list[i];
                            ED_WorldMapUnBuildArea param =
                                new ED_WorldMapUnBuildArea(Game.GameEntry.Entity.GenerateSerialId(), data);
                            param.Position = new Vector3(data.x, 0.015f, data.y) + GameDefine.HalfOffset;
                            param.Scale = new Vector3(data.width, 0, data.height);
                            int id = GameEntry.Entity.ShowWorldMapDisplay(PATH, param, typeof(EL_WorldMapUnBuildArea));
                            
                            UnBuildableData unBuildableData = new UnBuildableData()
                            {
                                EntityId = id,
                                Rect = data,
                            };
                            idList.Add(unBuildableData);
                            OnAreaAdd(unBuildableData);
                        }
                        m_ShowListDic.Add(grid, idList);
                    }
                    
                }
            }
        }

        private void Remove(Vector2Int grid)
        {
            if (m_ShowList.Remove(grid))
            {
                if (m_ShowListDic.TryGetValue(grid, out var idList))
                {
                    for (int i = 0; i < idList.Count; i++)
                    {
                        UnBuildableData data = idList[i];
                        GameEntry.Entity.HideEntity(data.EntityId);
                        OnAreaRemove(data);
                    }

                    m_ShowListDic.Remove(grid); 
                }
            }
        }
        
        private void OnAreaAdd(UnBuildableData data)
        {
            var rect = data.Rect; 
            
            var xL = Mathf.FloorToInt(rect.xMin- (rect.width - 1)/2);
            var xR = Mathf.FloorToInt(rect.xMax- (rect.width - 1)/2);
            var yB = Mathf.FloorToInt(rect.yMin- (rect.height - 1)/2);
            var yT = Mathf.FloorToInt(rect.yMax- (rect.height - 1)/2);
            
            for (int i = xL; i < xR; i++)
            {
                for (int j = yB; j < yT; j++)
                {
                    var pos = new Vector2Int(i, j);
                    m_BuildBlockedData.TryAdd(pos, 0);                      
                    m_BuildBlockedData[pos]++;
                }
            }
        }
        
        private void OnAreaRemove(UnBuildableData data)
        {
            var rect = data.Rect;
            var xL = Mathf.FloorToInt(rect.xMin- (rect.width - 1)/2);
            var xR = Mathf.FloorToInt(rect.xMax- (rect.width - 1)/2);
            var yB = Mathf.FloorToInt(rect.yMin- (rect.height - 1)/2);
            var yT = Mathf.FloorToInt(rect.yMax- (rect.height - 1)/2);
            
            for (int i = xL; i < xR; i++)
            {
                for (int j = yB; j < yT; j++)
                {
                    var pos = new Vector2Int(i, j);
                    if (m_BuildBlockedData.ContainsKey(pos))
                    {
                        m_BuildBlockedData[pos]--;
                        if (m_BuildBlockedData[pos] < 0)
                            m_BuildBlockedData[pos] = 0;
                    }
                }
            }
        }

        public bool CanPut(Vector2Int pos)
        {
            if (m_BuildBlockedData.ContainsKey(pos) && m_BuildBlockedData[pos] >0)
            {
                return false;
            }
            return true;
        }
    }
}