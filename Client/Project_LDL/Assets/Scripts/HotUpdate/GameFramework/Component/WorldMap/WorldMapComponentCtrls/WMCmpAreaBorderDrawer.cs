using GameFramework.Event;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class WMCmpAreaBorderDrawer : IWMCmp
    {
        private const string PATH = "Assets/ResPackage/WorldMap/Prefab/AreaBorder/areaBorder_allInOne.prefab";

        private AreaBorderDataHolder m_AreaBorderDataHolder;
        private int m_EntityUid = 0;
        private EL_WorldMapDisplay m_EntityLogic;
        
        private WorldMapComponent m_WorldMapComponent;
        public WMCmpAreaBorderDrawer(WorldMapComponent component)
        {
            m_WorldMapComponent = component;
        }
        
        public void Init()
        {
            m_EntityUid = GameEntry.Entity.ShowWorldMapDisplay(PATH, Vector3.zero);
            Game.GameEntry.Event.Subscribe(ShowEntitySuccessEventArgs.EventId,Loaded);
            
            m_WorldMapComponent.OnCameraMoveCall += OnCameraMove;
            m_WorldMapComponent.OnCameraZoomCall += OnCameraZoom;
        }

        private void Loaded(object sender, GameEventArgs e)
        {
            if (e is ShowEntitySuccessEventArgs showEntitySuccessEventArgs)
            {
                if (showEntitySuccessEventArgs.Entity.Id ==m_EntityUid)
                {
                    // Debug.LogError("加载完毕" + Uid);
                    Game.GameEntry.Event.Unsubscribe(ShowEntitySuccessEventArgs.EventId,Loaded);
                    m_EntityLogic = showEntitySuccessEventArgs.Entity.Logic as EL_WorldMapDisplay;
                    if (m_EntityLogic != null)
                    {
                        m_AreaBorderDataHolder = m_EntityLogic.GetComponent<AreaBorderDataHolder>();

                        //TODO 测试代码
                        if (m_AreaBorderDataHolder != null)
                        {
                            m_AreaBorderDataHolder.SetColor(79, Color.red);
                        }
                        
                        Reset();
                    }
                } 
            }
        }

        public void UnInit()
        {
            m_WorldMapComponent.OnCameraMoveCall -= OnCameraMove;
            m_WorldMapComponent.OnCameraZoomCall -= OnCameraZoom;
            
            bool hasEvent = Game.GameEntry.Event.Check(ShowEntitySuccessEventArgs.EventId, Loaded);
            if (hasEvent)
            {
                Game.GameEntry.Event.Unsubscribe(ShowEntitySuccessEventArgs.EventId, Loaded);
            }
        }

        private void Reset()
        {
            m_AreaBorderDataHolder?.gameObject.SetActive(m_WorldMapComponent.CurLODLevel < WorldMapLOD.Level5);
        }
        
        private void OnCameraMove()
        {
            
        }

        private void OnCameraZoom()
        {
            Reset();
        }


    }
}
