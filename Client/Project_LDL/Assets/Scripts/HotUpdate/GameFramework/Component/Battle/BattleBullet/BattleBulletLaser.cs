using UnityEngine;

namespace Game.Hotfix
{
    public class BattleBulletLaser : BattleBullet
    {
        private SkillTargetData m_SkillTargetData;
        private Vector3 m_MoveBeginPos;
        private Vector3 m_TargetPos;

        private EditorBulletLaser m_Param;

        private BattleEffect m_BeginEffect;
        private BattleEffect m_EndEffect;

        protected override void OnInit()
        {
            if (!string.IsNullOrEmpty(SkillShowCfg.b_trajectory_param))
                m_Param = JsonUtility.FromJson<EditorBulletLaser>(SkillShowCfg.b_trajectory_param);

            m_SkillTargetData = GetFirstTarget();
            m_MoveBeginPos = GetPosition();
            m_TargetPos = m_SkillTargetData.TargetPos + GetRandomBombRange();

            int beginEffect = GetEffectBegin();
            if (beginEffect > 0)
            {
                m_BeginEffect = m_BattleFiled.EffectCtrl.GetEffect(beginEffect);
                m_BeginEffect.SetPosition(m_MoveBeginPos);
            }

            int endEffect = GetEffectEnd();
            if (endEffect > 0)
            {
                m_EndEffect = m_BattleFiled.EffectCtrl.GetEffect(endEffect);
                m_EndEffect.SetPosition(m_TargetPos);
            }
        }

        protected override void OnBulletTick(float dt)
        {
            if (AlivePct >= 1)
                m_BattleFiled.BulletCtrl.RemoveBullet(this, true);
        }

        protected override void OnUnInit()
        {
            if (m_BeginEffect != null)
            {
                m_BattleFiled.EffectCtrl.RemoveEffect(m_BeginEffect);
                m_BeginEffect = null;
            }

            if (m_EndEffect != null)
            {
                m_BattleFiled.EffectCtrl.RemoveEffect(m_EndEffect);
                m_BattleFiled = null;
            }
        }

        protected override void OnLoaded(Entity entity)
        {
            base.OnLoaded(entity);
            LineRenderer lineRenderer = entity.gameObject.GetComponentInChildren<LineRenderer>();
            if (lineRenderer != null)
            {
                lineRenderer.SetPosition(0, m_MoveBeginPos);
                lineRenderer.SetPosition(1, m_TargetPos);
            }
        }

        private int GetEffectBegin()
        {
            if (m_Param != null)
            {
                return m_Param.BeginEffectId;
            }

            return 0;
        }

        private int GetEffectEnd()
        {
            if (m_Param != null)
            {
                return m_Param.EndEffectId;
            }

            return 0;
        }
    }
}