using System;
using EasingCore;
using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public enum EnumBattleSide
    {
        Left,
        Right
    }


    public enum EnumBattlePos
    {
        PosL1 = 1,
        PosL2 = 2,
        PosL3 = 3,
        PosL4 = 4,
        PosL5 = 5,
        PosR1 = 11,
        PosR2 = 12,
        PosR3 = 13,
        PosR4 = 14,
        PosR5 = 15,
    }
    
    [System.Flags]
    public enum eSkillEditorBattlePosition
    {
        PosL1 = 1 << 1,
        PosL2 = 1 << 2,
        PosL3 = 1 << 3,
        PosL4 = 1 << 4,
        PosL5 = 1 << 5,
        PosR1 = 1 << 6,
        PosR2 = 1 << 7,
        PosR3 = 1 << 8,
        PosR4 = 1 << 9,
        PosR5 = 1 <<10,
        ALL = PosL1 | PosL2 | PosL3 | PosL4 |PosL5 | PosR1 | PosR2 | PosR3 | PosR4 | PosR5
    }

    public static class BATTLE_TIME_EASE
    {
        public static int None = 0;

        public static int InSine = 1;
        public static int OutSine = 2;
        public static int InOutSine = 3;

        public static int InQuad = 4;
        public static int OutQuad = 5;
        public static int InOutQuad = 6;

        public static int InCubic = 7;
        public static int OutCubic = 8;
        public static int InOutCubic = 9;

        public static int InQuart = 10;
        public static int OutQuart = 11;
        public static int InOutQuart = 12;

        public static int Bezier = 13;
    }

    public static class BattleDefine
    {
        /// <summary>
        /// 每秒多少帧
        /// </summary>
        public const int BATTLE_FRAME_SECONDS = 24;

        /// <summary>
        /// 每帧多少秒
        /// </summary>
        public const float BATTLE_FRAME_DURATION = 1f / BATTLE_FRAME_SECONDS;

        /// <summary>
        /// 战斗总时长
        /// </summary>
        // public const int BATTLE_TOTAL_SECONDS_Max = 300;
            
        /// <summary>
        /// 位置0-10 都是攻击位置
        /// </summary>
        public static int AttackTeamMaxPos = 10;

        
        public static Color GetColorByHeroQuality(quality quality)
        {
            switch (quality)
            {
                case quality.quality_nil:
                    return Color.gray;
                case quality.quality_white:
                    return Color.white;
                case quality.quality_green:
                    return Color.green;
                case quality.quality_blue:
                    return Color.blue;
                case quality.quality_purple:
                    return Color.magenta;
                case quality.quality_orange:
                    return new Color(255/255f, 153/255f, 51/255f);
                case quality.quality_red:
                    return Color.red;
                default:
                    return Color.gray;
            }
        }
    }
    
    
}