using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using Hero;
using UnityEngine;

namespace Game.Hotfix
{
    public class HeroBattleSkillCtrl
    {
        private BattleHero m_BattleHero;
        private List<HeroBattleSKill> m_HeroSkillList;

        public HeroBattleSkillCtrl(BattleHero battleHero)
        {
            m_BattleHero = battleHero;
            m_HeroSkillList = new List<HeroBattleSKill>();
        }

        public bool HasSkill(int showId)
        {
            return m_HeroSkillList.Any(skill => skill.ShowId == showId);
        }

        
        public void AddSkill(HeroBattleSKill heroBattleSKill)
        {
            if (m_HeroSkillList.Contains(heroBattleSKill))
            {
                Debug.LogError("技能已经存在" + heroBattleSKill.ShowId);
                return;
            }

            m_HeroSkillList.Add(heroBattleSKill);
        }

        public void RemoveSkill(int showId)
        {
            foreach (var skill in m_HeroSkillList)
            {
                if (skill.ShowId == showId)
                {
                    RemoveSkill(skill);
                    break;
                }
            }
        }
        
        public void RemoveSkill(HeroBattleSKill heroBattleSKill)
        {
            if (!m_HeroSkillList.Contains(heroBattleSKill))
            {
                return;
            }

            m_HeroSkillList.Remove(heroBattleSKill);
        }

        public void Init()
        {
        }

        public void CastSkillBySkillId(int skillId, List<EnumBattlePos> targetList)
        {
            var skill = GetSkillBySkillId(skillId);
            CastSkill(skill, targetList);
        }
        
        public void CastSkill(int skillShowId, List<EnumBattlePos> targetList)
        {
            var skill = GetSkill(skillShowId);
            CastSkill(skill,targetList);
        }

        private void CastSkill(HeroBattleSKill skill,List<EnumBattlePos> targetList)
        {
            if (skill == null)
            {
                Debug.LogError("未找到指定技能");
                return;
            }

            skill.OnCast(targetList);

            if (skill.IsMainSkill)
                m_BattleHero.BattleFiled.SendEvent(BattleFiledEvent.OnHeroMainSkillCast, skill);
        }

        public HeroBattleSKill GetMainSkill()
        {
            for (int i = 0; i < m_HeroSkillList.Count; i++)
            {
                var skill = m_HeroSkillList[i];
                if (skill.SkillConfig.skill_type == skilltpye.skilltype_2)
                {
                    return skill;
                }
            }
            
            return null;
        }
        
        public HeroBattleSKill GetSkillBySkillId(int skillId)
        {
            return m_HeroSkillList.FirstOrDefault(heroBattleSKill => heroBattleSKill.SkillId == skillId);
        }
        
        public HeroBattleSKill GetSkill(int skillShowId)
        {
            return m_HeroSkillList.FirstOrDefault(heroBattleSKill => heroBattleSKill.ShowId == skillShowId);
        }

        public void OnTick(float dt)
        {
            for (int i = 0; i < m_HeroSkillList.Count; i++)
            {
                m_HeroSkillList[i].OnTick(dt);
            }
        }

        public void ResetAllSkillCD()
        {
            foreach (var skill in m_HeroSkillList)
            {
                skill.ResetCD();
            }
        }
        
        public void UnInit()
        {
        }
    }
}