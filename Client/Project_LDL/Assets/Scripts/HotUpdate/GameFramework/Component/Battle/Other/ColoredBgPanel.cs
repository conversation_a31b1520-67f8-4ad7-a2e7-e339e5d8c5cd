using System;
using System.Drawing;
using UnityEditor;
using UnityEngine;
using Color = UnityEngine.Color;

namespace Game.Hotfix
{
    public class ColoredBgPanel : MonoBehaviour
    {
        public float Size;
        
        private Transform m_DebugGo;
        private Transform m_ColoredPanel;
        private Transform m_EmptyPanel;

        private Bounds m_Bounds;
        
        private void Awake()
        {
            m_DebugGo = transform.Find("DebugPlane");
            m_ColoredPanel = transform.Find("PanelColored");
            m_EmptyPanel = transform.Find("PanelEmpty");

            m_Bounds = new Bounds(transform.position, Vector3.one * Size);
        }

        public bool Contains(Vector3 worldPos)
        {
            return m_Bounds.Contains(worldPos);
        }
        
        public void ShowDebugPanel(bool show)
        {
            m_DebugGo.gameObject.SetActive(show);
        }
        
        public void SetColor(Color? color)
        {
            if (color == null)
            {
                m_ColoredPanel.gameObject.SetActive(false);
                m_EmptyPanel.gameObject.SetActive(true);
            }
            else
            {
                m_ColoredPanel.gameObject.SetActive(true);
                m_EmptyPanel.gameObject.SetActive(false);
                Renderer rend = m_ColoredPanel.GetComponent<Renderer>();
                if (rend != null)
                {
                    Material mat = rend.material;
                    mat.color = color.Value;
                }    
            }
            
        }

        public void ShowAll(bool show)
        {
            m_ColoredPanel.gameObject.SetActive(show);
            m_EmptyPanel.gameObject.SetActive(show);
        }

        private void OnDrawGizmos()
        {
            Color prevCol = Gizmos.color;
            Gizmos.color = Color.red;
            
            Matrix4x4 originalMatrix = Gizmos.matrix;
            Gizmos.matrix = transform.localToWorldMatrix;
            //===================================
            var p = new Vector3(0, 0, 0);
            Gizmos.DrawWireCube(p, new Vector3(Size, Size, Size));
            //===================================
            Gizmos.matrix = originalMatrix;
            Gizmos.color = prevCol;
        }
    }
}