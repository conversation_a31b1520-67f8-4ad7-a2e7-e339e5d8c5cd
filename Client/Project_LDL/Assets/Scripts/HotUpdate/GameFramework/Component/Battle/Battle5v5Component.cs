using System;
using System.Collections.Generic;
using Battle;
using DG.Tweening;
using Game.Hotfix.Config;
using Sirenix.OdinInspector;
using Team;
using UnityEngine;
using UnityEngine.Serialization;
using UnityGameFramework.Runtime;
using itemid = PbGameconfig.itemid;

namespace Game.Hotfix
{
    public class Battle5v5Component : BaseSceneComponent
    {
        [Title("进攻位置")]
        [LabelText("位置1")][FormerlySerializedAs("pos1")] public Transform pos1;
        [LabelText("位置2")][FormerlySerializedAs("pos2")] public Transform pos2;
        [LabelText("位置3")][FormerlySerializedAs("pos3")] public Transform pos3;
        [LabelText("位置4")][FormerlySerializedAs("pos4")] public Transform pos4;
        [LabelText("位置5")][FormerlySerializedAs("pos5")] public Transform pos5;
        [Title("防守位置")]
        [LabelText("位置1")][FormerlySerializedAs("pos11")] public Transform pos11;
        [LabelText("位置2")][FormerlySerializedAs("pos12")] public Transform pos12;
        [LabelText("位置3")][FormerlySerializedAs("pos13")] public Transform pos13;
        [LabelText("位置4")][FormerlySerializedAs("pos14")] public Transform pos14;
        [LabelText("位置5")][FormerlySerializedAs("pos15")] public Transform pos15;
        
        [Title("相机位置")]
        [LabelText("默认相机位置")][FormerlySerializedAs("")] public Transform cameraPosDefault;
        [LabelText("战斗相机位置")][FormerlySerializedAs("")] public Transform cameraPosBattle;

        public bool IsDebug = false;
        public BattleFiled BattleFiled => m_BattleBattleFiled;
        
        private Dictionary<EnumBattlePos,ColoredBgPanel> m_BattlePositions;


        private BattleFiled m_BattleBattleFiled;
        private Battle5v5Data m_Battle5V5Data;

        protected override int GetSceneId()
        {
            return (int)SceneDefine.Battle5v5Scene;
        }

        protected override void OnStart()
        {
            base.OnStart();
            m_BattlePositions = InitBattlePos();

            IsDebug = TempConnectHelper.Instance.ConnectTargetPath == TempConnectHelper.ConnectTarget.技能编辑;

            m_Battle5V5Data = GameEntry.LogicData.Battle5v5Data;
            
            m_BattleBattleFiled = new BattleFiled();
            m_BattleBattleFiled.IsDebug = IsDebug;
            BattleFiled.Init(m_BattlePositions, this);


            //创建初始敌人
            if (m_Battle5V5Data.CurBattleType == EnumBattle5v5Type.Debug)
            {
                m_BattleBattleFiled.TeamCtrl.CreateHero(EnumBattlePos.PosL1,11301);
                m_BattleBattleFiled.TeamCtrl.CreateHero(EnumBattlePos.PosR1,11301);
            }
            else if (m_Battle5V5Data.CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var param = m_Battle5V5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                if (param != null)
                {
                    foreach (var hero in param.Heros)
                    {
                        m_BattleBattleFiled.TeamCtrl.CreateHero((EnumBattlePos)hero.Pos, (int)hero.Code, hero);
                    }
                }
                else
                {
                    Debug.LogError("Battle5v5ParamDungeon not found");
                }
                //=================
                var team = GameEntry.LogicData.TeamData.GetTeam(TeamType.Dungeon);
                if (team != null)
                {
                    foreach (var hero in team)
                    {
                        var heroModule = GameEntry.LogicData.HeroData.GetHeroModule((Config.itemid)hero.HeroId);
                        TeamHero teamHero = new TeamHero();
                        teamHero.Code = (itemid)heroModule.id;
                        teamHero.Pos = hero.Pos;
                        teamHero.Level = teamHero.Level;
                        teamHero.StarStage = teamHero.StarStage;
                        m_BattleBattleFiled.TeamCtrl.CreateHero((EnumBattlePos)hero.Pos, (int)hero.HeroId, teamHero);
                    }
                }
            }

            MoveCameraToDefault(0);
        }

        protected override void OnUpdate(float dt)
        {
            base.OnUpdate(dt);
            BattleFiled?.OnUpdate(Time.deltaTime);
        }

        protected override void OnDestroy()
        {
            BattleFiled.UnInit();
            GameEntry.Battle5v5 = null;
            base.OnDestroy();
        }

        private Dictionary<EnumBattlePos, ColoredBgPanel> InitBattlePos()
        {
            var bp = new Dictionary<EnumBattlePos, ColoredBgPanel>();
            bp.Add(EnumBattlePos.PosL1,pos1.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosL2,pos2.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosL3,pos3.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosL4,pos4.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosL5,pos5.gameObject.GetOrAddComponent<ColoredBgPanel>());
            
            bp.Add(EnumBattlePos.PosR1,pos11.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosR2,pos12.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosR3,pos13.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosR4,pos14.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosR5,pos15.gameObject.GetOrAddComponent<ColoredBgPanel>());
            return bp;
        }

        public EnumBattlePos? IsInPosBounds(Vector3 worldPos, EnumBattleSide side)
        {
            foreach (var item in m_BattlePositions)
            {
                if (side == EnumBattleSide.Left && (int)item.Key > BattleDefine.AttackTeamMaxPos)
                    continue;
                if (side == EnumBattleSide.Right && (int)item.Key <= BattleDefine.AttackTeamMaxPos)
                    continue;

                if (item.Value.Contains(worldPos))
                    return item.Key;
            }

            return null; 
        }

        public void MoveCameraToBattle(float duration)
        {
            MoveCameraTo(cameraPosBattle,duration);
        }
        
        public void MoveCameraToDefault(float duration)
        {
            MoveCameraTo(cameraPosDefault,duration);
        }

        private void MoveCameraTo(Transform targetTrans,float duration)
        {
            var curCameraTrans = GameEntry.Camera.CurUseCamera.transform;
            if (duration > 0)
            {
                curCameraTrans.DOMove(targetTrans.position,duration);
                // curCameraTrans.DOLookAt(targetTrans.forward, duration);
            }
            else
            {
                curCameraTrans.position = targetTrans.position;
                // curCameraTrans.rotation = targetTrans.rotation;
            }
        }
    }
    
}