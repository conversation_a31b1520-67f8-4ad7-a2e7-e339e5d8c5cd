using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Game.Hotfix
{
    public class BattleBulletParticle : BattleBullet
    {
        private SkillTargetData m_SkillTargetData;
        private Vector3 m_MoveBeginPos;
        private Vector3 m_TargetPos;

        private EditorBulletParticle m_Param;

        private ParticleSystem[] m_PList;
        private float m_RemoveDelay;
        private bool m_HasStopPs;

        private float m_PsSpeedMin;
        private float m_PsSpeedMax;

        private float MinDistanceParam => GetMinDistance(1);
        private float MxnDistanceParam => GetMaxDistance(9999);
        private float MinSpeedParam => GetMinSpeed(1);
        private float MaxSpeedParam => GetMaxSpeed(9999);
        private float OffsetParam => GetOffset(0);


        protected override void OnInit()
        {
            if (!string.IsNullOrEmpty(SkillShowCfg.b_trajectory_param))
                m_Param = JsonUtility.FromJson<EditorBulletParticle>(SkillShowCfg.b_trajectory_param);

            m_SkillTargetData = GetFirstTarget();
            m_MoveBeginPos = GetPosition();
            m_TargetPos = m_SkillTargetData.TargetPos + GetRandomBombRange();

            SetBulletRotation(m_TargetPos, m_MoveBeginPos);

            m_PList = null;
            m_HasStopPs = false;
            m_RemoveDelay = 1.5f;

            var distance = Vector3.Magnitude(m_TargetPos - Position);
            m_PsSpeedMin = BattleUtils.MapValue(MinDistanceParam, MxnDistanceParam, MinSpeedParam, MaxSpeedParam,
                distance);
            m_PsSpeedMax = m_PsSpeedMin + OffsetParam;
        }

        protected override void OnBulletTick(float dt)
        {
            if (AlivePct >= 1)
            {
                if (!m_HasStopPs)
                {
                    m_HasStopPs = true;
                    PlayBombEffect(m_TargetPos);

                    foreach (var t in m_PList)
                    {
                        t.Stop();
                    }
                }

                if (m_RemoveDelay <= 0)
                    m_BattleFiled.BulletCtrl.RemoveBullet(this, true);
                else
                    m_RemoveDelay -= dt;
            }
        }

        protected override void OnLoaded(Entity entity)
        {
            base.OnLoaded(entity);
            m_PList = entity.CachedTransform.GetComponentsInChildren<ParticleSystem>();
            foreach (var t in m_PList)
            {
                var mainModule = t.main;
                mainModule.startSpeed = new ParticleSystem.MinMaxCurve(m_PsSpeedMin, m_PsSpeedMax);
            }
        }

        protected override void OnUnInit()
        {
        }

        private float GetMinSpeed(float defaultValue)
        {
            if (m_Param != null)
                return m_Param.MinSpeed;
            return defaultValue;
        }

        private float GetMaxSpeed(float defaultValue)
        {
            if (m_Param != null)
                return m_Param.MaxSpeed;
            return defaultValue;
        }

        private float GetMinDistance(float defaultValue)
        {
            if (m_Param != null)
                return m_Param.MinDistance;
            return defaultValue;
        }

        private float GetMaxDistance(float defaultValue)
        {
            if (m_Param != null)
                return m_Param.MaxDistance;
            return defaultValue;
        }

        private float GetOffset(float defaultValue)
        {
            if (m_Param != null)
                return m_Param.Offset;
            return defaultValue;
        }
    }
}