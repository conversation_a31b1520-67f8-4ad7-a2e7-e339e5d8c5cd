using System;
using System.Collections.Generic;
using System.Linq;
using Battle;
using Google.Protobuf.Collections;
using PbGameconfig;
using UnityEngine;
using Action = Battle.Action;

namespace Game.Hotfix
{
    public class BattleRecordCtrl : BattleBaseCtrl
    {
        public bool Running => m_Running;
        public float Duration => m_Duration;
        public float TotalDuration => m_TotalDuration;
        public string ReportId => m_Report.ReportId;
        public Report Report => m_Report;

        // private Team teamAttacker;
        // private battle_types BattleTypes;
        // private BattleResult result;
        private RepeatedField<Action> m_Actions => m_Report.Actions;

        // private BattleStats stats;
        // private string ext;
        private Report m_Report;


        private bool m_Running = false;
        private float m_Duration;
        private float m_TotalDuration;
        private float m_TotalFrameCnt;
        private float m_FrameAddTime;
        private int m_Frame;

        private int m_ActionIndex;
        private int m_ActionCnt;

        private Dictionary<long, int> m_SkillIdSet = new Dictionary<long, int>();
        
        public void Load(Report report)
        {
            m_Report = report;

            m_BattleFiled.TeamCtrl.CreateHero(m_Report.Attacker,EnumBattleSide.Left);
            m_BattleFiled.TeamCtrl.CreateHero(m_Report.Defender,EnumBattleSide.Right);

            m_TotalFrameCnt = m_Report.Stats.Duration;
            m_TotalDuration = m_TotalFrameCnt * BattleDefine.BATTLE_FRAME_DURATION;   
        }

        public BattleResult GetBattleResult()
        {
            return m_Report.Result;
        }
        
        public TeamHero GetTeamHero(EnumBattlePos pos)
        {
            Battle.Team team = null;
            if ((int)pos <= BattleDefine.AttackTeamMaxPos)
                team = m_Report.Attacker;
            else
                team = m_Report.Defender;
            
            foreach (var hero in team.Heroes)
            {
                if (hero.Pos == (int)pos)
                {
                    return hero;
                }
            }

            return null;
        }
        
        public void Run()
        {
            m_Duration = 0;
            m_FrameAddTime = 0;
            m_Frame = 0;
            m_ActionCnt = m_Actions.Count;
            m_ActionIndex = 0;

            m_Running = true;
        }

        public void Pause()
        {
            m_Running = false;
        }
        
        public override void OnTick(float dt)
        {
            if (!m_Running) return;

            m_Duration += dt;
            m_FrameAddTime += dt;
            if (m_FrameAddTime > BattleDefine.BATTLE_FRAME_DURATION)
            {
                m_Frame += 1;
                m_FrameAddTime -= BattleDefine.BATTLE_FRAME_DURATION;
                List<Action> actions = GetActionByFrame(m_Frame);
                if (actions.Count > 0)
                {
                    for (int i = 0; i < actions.Count; i++)
                    {
                        PlayAction(actions[i]);
                    }
                }
            }
        }

        private List<Action> GetActionByFrame(int frame)
        {
            List<Action> actions = new List<Action>();
            while (m_ActionIndex < m_ActionCnt)
            {
                var actionTemp = m_Actions[m_ActionIndex];
                if (actionTemp.Frame <= frame)
                {
                    actions.Add(actionTemp);
                    m_ActionIndex++;
                }
                else
                {
                    break;
                }
            }

            return actions;
        }

        private void PlayAction(Action action, bool isSkip = false)
        {
            
            switch (action.Action_)
            {
                case ActionType.ActionNil:
                    PlayActionActionNil();
                    break;
                case ActionType.Skill:
                    PlayActionSkill(action.Caster,action.Value,action.Target);
                    break;
                case ActionType.Attr:
                    foreach (var t in action.Target)
                    {
                        PlayActionAttr(t,action.Mask,action.Change,action.Value,isSkip);
                    }
                    break;
                case ActionType.Buff:
                    if (!isSkip)
                        foreach (var t in action.Target)
                        {
                            PlayActionBuff(t, action.Change, action.Value);
                        }
                    break;
                case ActionType.Special:
                    foreach (var t in action.Target)
                    {
                        PlayActionSpecial(t, action.Change, action.Value);
                    }
                    break;
                case ActionType.Plane:
                    PlayActionPlane();
                    break;
                case ActionType.Result:
                    PlayActionResult(action.Value);
                    break;
                default:
                    Debug.LogError("未知的Action类型" + action.Action_);
                    break;
            }
        }

        private void PlayActionActionNil()
        {
        }

        private void PlayActionSkill(int caster,long skillId,RepeatedField<int> targetList)
        {
            // var showId = SkillId2ShowId(skillId);
            var hero = m_BattleFiled.TeamCtrl.GetBattleHero((EnumBattlePos)caster);
            if (hero != null)
            {
                // hero.HeroBattleSkillCtrl.CastSkill(showId, RepeatedField2List(targetList));
                hero.HeroBattleSkillCtrl.CastSkillBySkillId((int)skillId, RepeatedField2List(targetList));
            }
        }

        private void PlayActionAttr(int target,int mask,ChangeType changeType,long value,bool isSkip)
        {
            var hero = m_BattleFiled.TeamCtrl.GetBattleHero((EnumBattlePos)target);
            if (hero != null)
            {
                hero.AddAttr(mask, changeType, value);
            }
        }

        private void PlayActionBuff(int target,ChangeType changeType,long value)
        {
            var hero = m_BattleFiled.TeamCtrl.GetBattleHero((EnumBattlePos)target);
            if (hero != null)
            {
                if (changeType == ChangeType.BuffAdd)
                    hero.HeroBattleBuffCtrl.AddBuff(value);
                else if (changeType == ChangeType.BuffRemove)
                    hero.HeroBattleBuffCtrl.RemoveBuff(value);
            }
        }

        private void PlayActionSpecial(int target,ChangeType changeType,long values)
        {
            var hero = m_BattleFiled.TeamCtrl.GetBattleHero((EnumBattlePos)target);
            if (hero != null)
            {
                if (changeType == ChangeType.Die)
                {
                    hero.Die();
                }else if (changeType == ChangeType.Resurgence)
                {
                    hero.Resurgence();
                }
            }
        }

        private void PlayActionPlane()
        {
            //TODO
        }

        private void PlayActionResult(long value)
        {
            m_BattleFiled.PlayRecordFinish((BattleResult)value);
        }

        public override void RemoveAll()
        {
        }

        public override void UnInit()
        {
        }

        private int SkillId2ShowId(long skillId)
        {
            if (m_SkillIdSet.TryGetValue(skillId, out var showId))
            {
                return showId;
            }

            var skillConfig = GameEntry.LDLTable.GetTableById<Config.skill_config>((int)skillId);
            if (skillConfig != null)
            {
                m_SkillIdSet.Add(skillId,skillConfig.show_id);
                return skillConfig.show_id;
            }

            Debug.LogError("skill not found:" + skillId);
            return 0;
        }

        private List<EnumBattlePos> RepeatedField2List(RepeatedField<int> repeatedField)
        {
            List<EnumBattlePos> list = new List<EnumBattlePos>();
            foreach (var item in repeatedField)
            {
                list.Add((EnumBattlePos)item);
            }
            return list;
        }
        
    }
}