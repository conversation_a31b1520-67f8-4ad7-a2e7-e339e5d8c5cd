using Game.Hotfix.Config;
using UnityEngine;

namespace Game.Hotfix
{
    public class HeroSkillActionParticle : HeroSkillActionBase
    {
        private int m_EffectId;
        private slot m_slot;

        public HeroSkillActionParticle(HeroBattle<PERSON>Kill heroBattleSKill, float startTime, int effectId, slot slot) :
            base(heroBattleSKill, startTime)
        {
            m_EffectId = effectId;
            m_slot = slot;
        }

        protected override void OnFinish()
        {
        }

        protected override float DoAction()
        {
            if (m_EffectId <= 0)
                return 0;
            
            var effectCtrl = m_HeroBattleSKill.BattleFiled.EffectCtrl;
            if (effectCtrl != null)
            {
                BattleEffect battleEffect = effectCtrl.GetEffect(m_EffectId);
                battleEffect.SetPosition(m_HeroBattleSKill.Caster.GetSlotPosition(m_slot));
                battleEffect.SetRotation(m_HeroBattleSKill.Caster.GetSlotRotation(m_slot));
            }

            return 0;
        }

        protected override void OnBegin()
        {
        }
    }
}