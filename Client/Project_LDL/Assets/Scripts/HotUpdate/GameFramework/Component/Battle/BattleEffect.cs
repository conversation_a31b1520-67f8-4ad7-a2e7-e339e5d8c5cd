using System;
using System.Runtime.CompilerServices;
using Game.Hotfix.Config;
using GameFramework;
using GameFramework.Event;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class BattleEffect:BattleUnitBase
    {
        private int m_Id;

        private float m_Duration;
        private float m_RemoveDelay;
        private BattleFiled m_BattleFiled;
        private battle_effect m_Config;

        public void Init(int id,BattleFiled battleFiled, Action<Entity> onLoaded, Vector3? p = null, Quaternion? r = null,
            Vector3? s = null)
        {
            base.Init(onLoaded, p, r, s);
            m_BattleFiled = battleFiled;
            m_Id = id;
            m_Config = Game.GameEntry.LDLTable.GetTableById<battle_effect>(m_Id);
            m_Duration = m_Config.time;
            
            if (!Mathf.Approximately(m_Config.scale, BattleDefine.BATTLE_WAIT_TIME))
                Scale *= m_Config.scale;
            
            Load();
        }

        protected override Type GetEntityLogicType()
        {
            return typeof(EL_BattleEffect);
        }

        protected override string GetPrefabPath()
        {
            return m_Config.res_location;
        }

        protected override ED_BattleUnitBase GetBattleUnitData()
        {
            ED_BattleEffect edBattleEffect =
                new ED_BattleEffect(Game.GameEntry.Entity.GenerateSerialId());
            edBattleEffect.Position = Position;
            edBattleEffect.Rotation = Rotation;
            edBattleEffect.Scale = Scale;
            return edBattleEffect;
        }

        protected override void OnTick(float dt)
        {
        }

        
        protected override void UnInit()
        {
        }

        protected override void OnLoaded(Entity entity)
        {
            
        }
        
        public void SetRemoveDelay(float value)
        {
            m_RemoveDelay = value;
        }

        public float GetRemoveDelay()
        {
            return m_RemoveDelay;
        }
        
        public void ReduceRemoveDelay(float value)
        {
            m_RemoveDelay -= value;
        }

        public float GetDuration()
        {
            return m_Duration;
        }
    }
}