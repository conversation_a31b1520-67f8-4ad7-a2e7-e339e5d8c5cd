using System;
using System.Collections.Generic;
using System.Linq;
using Battle;
using DG.Tweening;
using Fight;
using Game.Hotfix.Config;
using GameFramework;
using Google.Protobuf.Collections;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class BattleTeamCtrl : BattleBaseCtrl
    {
        public bool ShowGround
        {
            get { return m_ShowGround; }
            set
            {
                if (value != m_ShowGround)
                {
                    m_ShowGround = value;
                    UpdateGround(true);
                }
            }
        }

        private Dictionary<EnumBattlePos, BattleHero> m_BattleHeroDic;
        private Dictionary<EnumBattlePos, ColoredBgPanel> m_BattlePos;

        private bool m_ShowGround = false;

        public void Init(BattleFiled battleFiled, Dictionary<EnumBattlePos, ColoredBgPanel> battlePos)
        {
            base.Init(battleFiled);
            m_BattleHeroDic = new Dictionary<EnumBattlePos, BattleHero>();

            m_BattlePos = battlePos;

            ShowGround = false;
        }

        public override void RemoveAll()
        {
            var keys = m_BattleHeroDic.Keys.ToList();
            for (int i = keys.Count - 1; i >= 0; i--)
            {
                if (m_BattleHeroDic.TryGetValue(keys[i], out BattleHero hero))
                {
                    RemoveHero(hero);
                }
            }
        }

        public override void UnInit()
        {
            RemoveAll();
        }

        public override void OnTick(float dt)
        {
            //TODO 性能
            foreach (var kv in m_BattleHeroDic)
            {
                kv.Value.Tick(dt);
            }
        }

        public void RemoveHero(int heroId, EnumBattleSide side)
        {
            var hero = GetBattleHero(heroId, side);
            if (hero != null)
            {
                RemoveHero(hero);
            }
        }

        public void RemoveHero(BattleHero battleHero)
        {
            if (m_BattleHeroDic.ContainsKey(battleHero.TeamUid))
            {
                m_BattleHeroDic.Remove(battleHero.TeamUid);
                m_BattleFiled.SendEvent(BattleFiledEvent.OnHeroDelete, battleHero.BattlePos);
            }

            ReferencePool.Release(battleHero);

            UpdateGround();
        }

        public void CreateHero(Battle.Team team,EnumBattleSide side)
        {
            foreach (var hero in team.Heroes)
            {
                if (side == EnumBattleSide.Right)
                    CreateHero((EnumBattlePos)(hero.Pos + BattleDefine.AttackTeamMaxPos), (int)hero.Code, hero);
                else
                    CreateHero((EnumBattlePos)hero.Pos, (int)hero.Code, hero);
            }
        }

        public void CreateHero(EnumBattlePos teamUid, int heroId, TeamHero teamHero = null)
        {
            if (m_BattleHeroDic.ContainsKey(teamUid))
            {
                Debug.LogError("The team already exists:" + teamUid);
                return;
            }

            var battleTeam = ReferencePool.Acquire<BattleHero>();
            battleTeam.Init(teamUid, GetBattlePos(teamUid), heroId, m_BattleFiled, null, teamHero);

            m_BattleHeroDic[teamUid] = battleTeam;
            m_BattleFiled.SendEvent(BattleFiledEvent.OnHeroCreate, teamUid);
            UpdateGround();
        }

        private Transform GetBattlePos(EnumBattlePos teamUid)
        {
            return m_BattlePos[teamUid].transform;
        }

        public BattleHero GetBattleHero(EnumBattlePos teamUid)
        {
            return m_BattleHeroDic.GetValueOrDefault(teamUid);
        }

        public BattleHero GetBattleHero(int heroId, EnumBattleSide side)
        {
            foreach (var item in m_BattleHeroDic)
            {
                if (side == EnumBattleSide.Left && (int)item.Key > BattleDefine.AttackTeamMaxPos)
                    continue;
                if (side == EnumBattleSide.Right && (int)item.Key <= BattleDefine.AttackTeamMaxPos)
                    continue;

                if (item.Value.HeroId == heroId)
                {
                    return item.Value;
                }
            }

            return null;
        }

        /// <summary>
        /// 该方法只在编辑器使用（预计）
        /// </summary>
        /// <param name="entityLogic"></param>
        /// <returns></returns>
        public BattleHero GetBattleHero(EntityLogic entityLogic)
        {
            foreach (var data in m_BattleHeroDic)
            {
                if (data.Value.EntityLogic == entityLogic)
                {
                    return data.Value;
                }
            }

            return null;
        }

        /// <summary>
        ///  英雄是否在战斗中
        /// </summary>
        /// <param name="heroId"></param>
        /// <returns></returns>
        public bool IsInBattle(int heroId, EnumBattleSide side)
        {
            if (GetBattleHero(heroId, side) != null)
            {
                return true;
            }

            return false;
        }

        public EnumBattlePos? GetEmptyPosition(EnumBattleSide side)
        {
            foreach (EnumBattlePos pos in Enum.GetValues(typeof(EnumBattlePos)))
            {
                if (side == EnumBattleSide.Left && (int)pos > BattleDefine.AttackTeamMaxPos)
                    continue;
                if (side == EnumBattleSide.Right && (int)pos <= BattleDefine.AttackTeamMaxPos)
                    continue;

                if (!m_BattleHeroDic.ContainsKey(pos))
                {
                    return pos;
                }
            }

            return null;
        }

        public void ResetAllSkillCD()
        {
            foreach (var hero in m_BattleHeroDic)
            {
                hero.Value.HeroBattleSkillCtrl.ResetAllSkillCD();
            }   
        }

        /// <summary>
        /// 更新地面
        /// </summary>
        /// <param name="force"></param>
        private void UpdateGround(bool force = false)
        {
            if (m_ShowGround || force)
            {
                foreach (var item in m_BattlePos)
                {
                    if (!item.Value) continue;

                    item.Value.ShowAll(m_ShowGround);
                    if (m_ShowGround)
                    {
                        var hero = GetBattleHero(item.Key);
                        if (hero != null)
                        {
                            var heroData = GameEntry.LogicData.HeroData;
                            var heroVo = heroData.GetHeroModule((itemid)hero.HeroId);
                            if (heroVo != null)
                            {
                                var color = BattleDefine.GetColorByHeroQuality((quality)heroVo.Quality);
                                item.Value.SetColor(color);
                            }
                        }
                        else
                            item.Value.SetColor(null);
                    }
                }
            }
        }

        public DebugTeam GetDebugTeam(EnumBattleSide side)
        {
            DebugTeam debugTeam = new DebugTeam();

            foreach (EnumBattlePos pos in Enum.GetValues(typeof(EnumBattlePos)))
            {
                if (side == EnumBattleSide.Left && (int)pos > BattleDefine.AttackTeamMaxPos)
                    continue;
                if (side == EnumBattleSide.Right && (int)pos <= BattleDefine.AttackTeamMaxPos)
                    continue;

                var hero = GetBattleHero(pos);
                if (hero != null)
                {
                    DebugHero debugHero = new DebugHero();
                    debugHero.Code = (PbGameconfig.itemid)hero.HeroId;
                    debugHero.Pos = (int)hero.RelativeBattlePos;
                    debugTeam.Heroes.Add(debugHero);
                }
            }

            return debugTeam;
        }
        
        public List<FormationHero> GetTeamHeroList(EnumBattleSide side)
        {
            List<FormationHero> list = new();
            foreach (EnumBattlePos pos in Enum.GetValues(typeof(EnumBattlePos)))
            {
                if (side == EnumBattleSide.Left && (int)pos > BattleDefine.AttackTeamMaxPos)
                    continue;
                if (side == EnumBattleSide.Right && (int)pos <= BattleDefine.AttackTeamMaxPos)
                    continue;

                if (m_BattleHeroDic.TryGetValue(pos, out var hero))
                {
                    list.Add(new FormationHero()
                    {
                        HeroId = (PbGameconfig.itemid)hero.HeroId,
                        Pos = (int)hero.RelativeBattlePos,
                    });
                }
            }

            return list;
        }

        public List<hero_services> GetTeamArmyTypeList()
        {
            var list = new List<hero_services>();
            for (int i = 1; i <= 5; i++)
            {
                var hero = GetBattleHero((EnumBattlePos)i);
                if(hero!=null)
                {
                    var heroConfig = GameEntry.LDLTable.GetTableById<hero_config>(hero.HeroId);
                    if(heroConfig!=null)
                        list.Add(heroConfig.services);
                }
            }
            return list;
        }
        
        /// <summary>
        /// 获取战力（heroModule）
        /// </summary>
        /// <param name="side"></param>
        /// <returns></returns>
        public double GetPowerFromHeroModule(EnumBattleSide side)
        {
            double power = 0;
            var heroData = GameEntry.LogicData.HeroData;
            foreach (EnumBattlePos pos in Enum.GetValues(typeof(EnumBattlePos)))
            {
                if (side == EnumBattleSide.Left && (int)pos > BattleDefine.AttackTeamMaxPos)
                    continue;
                if (side == EnumBattleSide.Right && (int)pos <= BattleDefine.AttackTeamMaxPos)
                    continue;

                if (m_BattleHeroDic.TryGetValue(pos,out var hero))
                {
                    var heroModule = heroData.GetHeroModule((itemid)hero.HeroId);
                    power += heroModule.power;
                }
            }

            return power;
        }
    }
}