using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace Game.Hotfix
{
    public class UISwitchPage : MonoBehaviour
    {
        private Transform panelRoot;
        private UISwitchTagGroup switchTagGroup;
        public UISwitchTagGroup SwitchTagGroup => switchTagGroup;

        private UnityAction<int> callback;
        private class PanelNode
        {
            public int uniqueId;//唯一id
            public string name;
            public string objPath;
        }

        //缓存已加载过的面板
        private Dictionary<int, SwitchPanelLogic> panelCache;
        private List<PanelNode> panelDataList;

        [HideInInspector]
        public bool UseTimer = false;//是否开启定时器逻辑
        #region 外部调用接口
        /// <summary>
        /// 组件初始化
        /// </summary>
        /// <param name="tagObj">标签预制体</param>
        /// <param name="tagRoot">标签实例化父节点</param>
        /// <param name="_panelRoot">子界面实例化父节点</param>
        /// <param name="pageList">将标签名和界面预制体名用|分割符拼接传入</param>
        public void OnInit(GameObject tagObj,
            Transform tagRoot, 
            Transform _panelRoot,
            List<ValueTuple<int,string, string>> pageList,
            UnityAction<int> _callback = null)
        {
            Release();
            panelRoot = _panelRoot;
            switchTagGroup = GetComponentInChildren<UISwitchTagGroup>();
            panelDataList = TurnToNodeList(pageList);
            panelCache = new Dictionary<int, SwitchPanelLogic>();
            callback += _callback;
            
            var nameList = panelDataList.Select(x => x.name).ToList();
            var idList = panelDataList.Select(x => x.uniqueId).ToList();
            
            switchTagGroup.Init(tagObj,tagRoot,nameList, OnSwitchPage);
            switchTagGroup.BindUniqueId(idList);
            
            BindTimerLogic();
        }

        //选择激活指定索引（从0开始）的界面
        public void SelectPageByIndex(int index)
        {
            switchTagGroup.OnSelectLogic(index);
        }
        
        //释放资源
        public void Release()
        {   
            Timers.Instance.Remove(GetInstanceID().ToString());
            if (switchTagGroup != null)
            {
                switchTagGroup.Release();
            }
            if (panelCache != null)
            {
                foreach (var cache in panelCache)
                {
                    cache.Value.Release();
                }
            }
            callback = null;
            panelCache?.Clear();
            panelDataList?.Clear();
            if (panelRoot != null)
            {
                ToolScriptExtend.ClearAllChild(panelRoot);
            }
        }

        //获取当前展示的界面索引
        public int GetCurPageIndex()
        {
            return switchTagGroup.CurSelectIndex;
        }
        
        //获取当前展示的界面绑定的唯一id
        public int GetCurUniqueId()
        {
            return switchTagGroup.CurSelectUniqueId;
        }

        //刷新逻辑
        public void OnRefresh(object userData)
        {
            foreach (var cache in panelCache)
            {
                cache.Value.OnRefresh(userData);
            }
        }

        //触发指定索引的界面的更新函数
        public void RefreshCurPage()
        {
            RefreshPageByIndex(switchTagGroup.CurSelectIndex);
        }
        
        //触发指定索引的界面的更新函数
        public void RefreshPageByIndex(int index)
        {
            if (panelCache.TryGetValue(index, out var panel))
            {
                panel.OnRefreshSelf();
            }
        }
        
        #endregion
        
        #region 内部逻辑

        private List<PanelNode> TurnToNodeList(List<ValueTuple<int,string, string>> pageList)
        {
            var list = new List<PanelNode>();
            foreach (var node in pageList)
            {
                list.Add(new PanelNode() { uniqueId = node.Item1,name = node.Item2, objPath = node.Item3 });
            }
            return list;
        }

        private void OnSwitchPage(int index)
        {
            if (panelCache.ContainsKey(index))
            {
                ShowOnePanel(index);
            }
            else
            {
                var data = panelDataList[index];
                
                ToolScriptExtend.LoadPrefab($"UI/UIChildForm/{data.objPath}",(prefab) =>
                {
                    var obj = Instantiate(prefab, panelRoot);
                    var script = obj.GetComponent<SwitchPanelLogic>();
                    script.OnInit();
                    panelCache.Add(index, script);
                    ShowOnePanel(index);
                });
            }

            if (panelCache.TryGetValue(index, out var panel))
            {
                panel.OnReOpen();
            }
            callback?.Invoke(index);
        }

        //显示选中面板，隐藏未选中面板
        private void ShowOnePanel(int index)
        {
            foreach (var cache in panelCache)
            {
                var isSelect = cache.Key == index;
                cache.Value.SetActive(isSelect);
            }
        }
        
        //定时器逻辑
        private void BindTimerLogic()
        {
            //开启一个定时器
            Timers.Instance.Add(GetInstanceID().ToString(), 1, (a) =>
            {
                if (UseTimer)
                {
                    foreach (var cache in panelCache)
                    {
                        cache.Value.OnTimer();
                    }
                }
            },86400);
        }
        
        private void OnDestroy()
        {
            Release();
        }

        #endregion
    }
}