using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class UISwitchTagGroup : MonoBehaviour
    {
        private Transform tagRoot;
        private GameObject TagObj;
        private List<UISwitchTag> tagList;
        private int _curSelectIndex = -1; //当前选中的标签索引 
        public int CurSelectIndex => _curSelectIndex;
        
        private int _curSelectUniqueId = -1; //当前选中的标签唯一id
        public int CurSelectUniqueId => _curSelectUniqueId;
        private UnityAction<int> callback;

        /// <summary>
        /// 控制指定页签的UI表现
        /// </summary>
        /// <param name="index"></param>
        private void OnSelectView(int index)
        {
            var count = tagList.Count;
            for (var i = 0; i < count; i++)
            {
                var isActive = index == i;
                var tagScript = tagList[i];
                tagScript.SetSelectStatus(isActive);
            }
        }
        
        /// <summary>
        /// 组件初始化
        /// </summary>
        /// <param name="tagObj">标签预制体</param>
        /// <param name="tagRoot">标签实例化父节点</param>
        /// <param name="nameList">标签名称列表</param>
        /// <param name="_callback"></param>
        public void Init(GameObject tagObj,Transform _tagRoot,List<string> nameList, UnityAction<int> _callback = null)
        {
            Release();
            _curSelectIndex = -1;
            _curSelectUniqueId = -1;
            TagObj = tagObj;
            tagRoot = _tagRoot;
            callback = null;
            callback += _callback;
            tagList?.Clear();
            tagList = new List<UISwitchTag>();

            ToolScriptExtend.ClearAllChild(tagRoot);
            
            for (var i = 0; i < nameList.Count; i++)
            {
                var obj = Instantiate(TagObj, tagRoot);
                var script = obj.AddComponent<UISwitchTag>();
                script.Init(i, OnSelectLogic);
                tagList.Add(script);
            }
            SetTagNameList(nameList);

            OnSelectLogic(0);
        }

        //用于设置指定索引标签名字段
        public void SetTagName(int index, string _name)
        {
            if (index < 0 || index >= tagList.Count)
            {
                Debug.LogError("索引越界了！");
                return;
            }

            if (tagList[index] == null)
            {
                Debug.LogError($"索引为{index}标签对象不存在！");
                return;
            }

            var tagScript = tagList[index];
            tagScript.SetTagName(_name);
        }

        //传入标签名列表，用于设置标签名字段
        public void SetTagNameList(List<string> list)
        {
            for (var i = 0; i < list.Count; i++)
            {
                SetTagName(i, list[i]);
            }
        }

        /// <summary>
        /// 控制指定页签的UI逻辑响应
        /// </summary>
        /// <param name="index"></param>
        public void OnSelectLogic(int index)
        {
            if (_curSelectIndex == index)
            {
                return;
            }

            _curSelectIndex = index;
            _curSelectUniqueId = tagList[index].uniqueId;
            OnSelectView(index);
            callback?.Invoke(index);
        }
        
        //普通红点检测逻辑
        public void CheckNormalDotLogic(Func<int,bool> _callback)
        {
            for (var i = 0; i < tagList.Count; i++)
            {
                var result = _callback(i);
                tagList[i].ShowNormalRedDot(result);
            }
        }
        
        //设置数字红点逻辑
        public void CheckNumDotLogic(int uniqueId,int count)
        {
            foreach (var tag in tagList)
            {
                if (tag.uniqueId == uniqueId)
                {
                    tag.ShowNumRedDot(count);
                    break;
                }
            }
        }
        
        /// <summary>
        /// 标签是否解锁检测逻辑
        /// </summary>
        /// <param name="checkFunc"></param>
        /// <param name="clickFunc"></param>
        public void CheckTagUnlock(Func<int,bool> checkFunc,UnityAction clickFunc)
        {
            for (var i = 0; i < tagList.Count; i++)
            {
                var result = checkFunc(i);
                tagList[i].ShowUnlockMask(result);
                tagList[i].BindUnlockClick(clickFunc);
            }
        }

        //将标签列表滑动到表头
        public void ScrollToFirst()
        {
            var scroll = gameObject.GetComponent<ScrollRect>();
            if (scroll != null)
            {
                scroll.normalizedPosition = new Vector2(0, 1);
            }
        }
        
        //将标签列表滑动到表尾
        public void ScrollToEnd()
        {
            var scroll = gameObject.GetComponent<ScrollRect>();
            if (scroll != null)
            {
                scroll.normalizedPosition = new Vector2(1, 1);
            }
        }
        
        //给每个标签绑定唯一id
        public void BindUniqueId(List<int> idList)
        {
            var count = idList.Count;
            for (var i = 0; i < tagList.Count; i++)
            {
                if (i < count)
                {
                    tagList[i].uniqueId = idList[i];
                }
            }
        }
        
        //释放资源
        public void Release()
        {
            tagList?.Clear();
            if (tagRoot != null)
            {
                ToolScriptExtend.ClearAllChild(tagRoot);
            }
            callback = null;
        }

        private void OnDestroy()
        {
            Release();
        }
    }
}