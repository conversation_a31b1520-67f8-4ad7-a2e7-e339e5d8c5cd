using System.Collections.Generic;
using GameFramework.Fsm;
using GameFramework.Procedure;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class ProcedureMain : ProcedureBase
    {
        private IFsm<IProcedureManager> m_ProcedureOwner;
        public override bool UseNativeDialog { get; }
        private int? m_UIId = null;
        public void GotoBattle(int sceneId)
        {
            m_ProcedureOwner.SetData<VarInt32>("NextSceneId", sceneId);
            ChangeState<ProcedureChangeScene>(m_ProcedureOwner);
        }

        public void GoToWorldMap(int sceneId)
        {
            m_ProcedureOwner.SetData<VarInt32>("NextSceneId", sceneId);
            ChangeState<ProcedureChangeScene>(m_ProcedureOwner);
        }
        
        protected override void OnEnter(IFsm<IProcedureManager> procedureOwner)
        {
            base.OnEnter(procedureOwner);
            m_ProcedureOwner = procedureOwner;
            
            m_UIId = GameEntry.UI.OpenUIForm(EnumUIForm.UIMainFaceForm);

            GameEntry.UI.OpenUIForm(EnumUIForm.UIClickEffectForm);
        }

        protected override void OnLeave(IFsm<IProcedureManager> procedureOwner, bool isShutdown)
        {
            if (m_UIId != null)
                GameEntry.UI.CloseUIForm(m_UIId.Value);
            base.OnLeave(procedureOwner, isShutdown);
        }
    }
}
