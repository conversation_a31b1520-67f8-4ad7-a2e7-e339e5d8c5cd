using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using Coffee.UIEffects;
using Game.Hotfix.Config;
using GameFramework.Resource;
using Spine.Unity;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;
using Object = UnityEngine.Object;

namespace Game.Hotfix
{
    public static class ToolScriptExtend
    {
        private static StringBuilder stringBuilder = new StringBuilder();

        public static string GetLang(int langID)
        {
            language_config langConfig = GameEntry.LDLTable.GetTableById<language_config>(langID);
            if (langConfig == null)
            {
                return $"Err:{langID}";
            }

            string langStr = langConfig.GetLang();
            langStr = Regex.Replace(langStr, @"\\n", "\n");
            return langStr;
        }

        /// <summary>
        /// 获取多语言带参数
        /// </summary>
        /// <param name="langID">langID</param>
        /// <param name="args">可变参数</param>
        /// <returns>多语言字符串</returns>
        public static string GetLangFormat(int langID, params string[] args)
        {
            if (args.Length <= 0)
            {
                Debug.LogError("多语言传参错误！");
                return string.Empty;
            }

            language_config langConfig = GameEntry.LDLTable.GetTableById<language_config>(langID);
            if (langConfig == null)
            {
                return $"Err:{langID}";
            }

            string langStr = langConfig.GetLang();
            langStr = Regex.Replace(langStr, @"\\n", "\n");
            return Regex.Replace(langStr, @"\{(\d+)\}", match =>
            {
                int index = int.Parse(match.Groups[1].Value);
                return index < args.Length ? args[index] : match.Value;
            });
        }

        /// <summary>
        /// 获取多语言字符串
        /// </summary>
        /// <param name="langID">langId</param>
        /// <param name="args">参数字典</param>
        /// <returns>多语言字符串</returns>
        public static string GetLangFormat(int langID, Dictionary<string, object> args)
        {
            if (args == null)
            {
                Debug.LogError("多语言传参错误！");
                return string.Empty;
            }

            language_config langConfig = GameEntry.LDLTable.GetTableById<language_config>(langID);
            if (langConfig == null)
            {
                return $"Err:{langID}";
            }

            string langStr = langConfig.GetLang();
            langStr = Regex.Replace(langStr, @"\\n", "\n");
            return Regex.Replace(langStr, @"\{(\w+)\}", match =>
            {
                string key = match.Groups[1].Value;
                return args.TryGetValue(key, out object value) ? value.ToString() : match.Value;
            });
        }

        /// <summary>
        /// 获取属性名称多语言
        /// </summary>
        /// <param name="attr">属性枚举值</param>
        /// <returns>属性名称</returns>
        public static string GetAttrLang(int attr)
        {
            int langID = attr + 100000;
            language_config langConfig = GameEntry.LDLTable.GetTableById<language_config>(langID);
            string langStr = langConfig != null ? langConfig.GetLang() : $"Err:{langID}";
            return langStr;
        }

        /// <summary>
        /// 根据道具 ID 获取道具配置
        /// </summary>
        /// <param name="itemID">道具 ID</param>
        /// <returns>道具配置</returns>
        public static item_config GetItemConfig(itemid itemID)
        {
            return GetItemConfig((int)itemID);
        }

        /// <summary>
        /// 根据道具 ID 获取道具配置
        /// </summary>
        /// <param name="itemID">道具 ID</param>
        /// <returns>道具配置</returns>
        public static item_config GetItemConfig(int itemID)
        {
            item_config itemConfig = GameEntry.LDLTable.GetTableById<item_config>(itemID);
            return itemConfig;
        }

        /// <summary>
        /// 根据道具 ID 获取图标路径
        /// </summary>
        /// <param name="itemID">道具 ID</param>
        /// <returns>图标路径</returns>
        public static string GetItemIcon(itemid itemID)
        {
            return GetItemIcon((int)itemID);
        }

        /// <summary>
        /// 根据道具 ID 获取图标路径
        /// </summary>
        /// <param name="itemID">道具 ID</param>
        /// <returns>图标路径</returns>
        public static string GetItemIcon(int itemID)
        {
            item_config itemConfig = GameEntry.LDLTable.GetTableById<item_config>(itemID);
            string iconPath = itemConfig != null ? itemConfig.icon : $"GetItemIcon Err:{itemID}";
            return iconPath;
        }

        /// <summary>
        /// 根据道具 ID 获取道具名称
        /// </summary>
        /// <param name="itemID">道具 ID</param>
        /// <returns>道具名称</returns>
        public static string GetItemName(itemid itemID)
        {
            var itemConfig = GameEntry.LDLTable.GetTableById<item_config>(itemID);
            return itemConfig != null ? GetLang(itemConfig.name) : $"GetItemName Err:{itemID}";
        }

        /// <summary>
        /// 根据品质获取品质背景图片路径
        /// </summary>
        public static string GetQualityBg(quality quality)
        {
            return quality switch
            {
                quality.quality_white => "Sprite/ui_public/pingzhikuang_zhuangbei_grey.png",
                quality.quality_green => "Sprite/ui_public/pingzhikuang_zhuangbei_green.png",
                quality.quality_blue => "Sprite/ui_public/pingzhikuang_zhuangbei_blue.png",
                quality.quality_purple => "Sprite/ui_public/pingzhikuang_zhuangbei_purple.png",
                quality.quality_orange => "Sprite/ui_public/pingzhikuang_zhuangbei_yellow.png",
                quality.quality_red => "Sprite/ui_public/pingzhikuang_zhuangbei_red.png",
                _ => "Sprite/ui_public/pingzhikuang_zhuangbei_grey.png",
            };
        }

        /// <summary>
        /// 获取穿戴装备的英雄的品质背景图片路径
        /// </summary>
        /// <param name="quality">品质</param>
        /// <returns>品质框路径</returns>
        public static string GetWearingHeroQualityBg(quality quality)
        {
            return quality switch
            {
                quality.quality_white => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_yellow.png",
                quality.quality_green => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_yellow.png",
                quality.quality_blue => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_blue.png",
                quality.quality_purple => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_purlpep.png",
                quality.quality_orange => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_yellow.png",
                quality.quality_red => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_yellow.png",
                _ => "Sprite/ui_hero/heroshuxing_zhuangbeijingsheng_kuang_juese_yellow.png",
            };
        }

        /// <summary>
        /// 获取品质图标
        /// </summary>
        public static string GetQualityIcon(int quality)
        {
            return quality switch
            {
                1 => "Sprite/ui_public/icon_pinzhi_N.png",
                2 => "Sprite/ui_public/icon_pinzhi_R.png",
                3 => "Sprite/ui_public/icon_pinzhi_SR.png",
                4 => "Sprite/ui_public/icon_pinzhi_SSR.png",
                5 => "Sprite/ui_public/icon_pinzhi_UR.png",
                _ => "Sprite/ui_public/icon_pinzhi_N.png",
            };
        }

        /// <summary>
        /// 将数值格式化为带单位(K/M/G)的字符串
        /// </summary>
        /// <param name="tValue">要格式化的数值</param>
        /// <returns>格式化后的字符串</returns>
        public static string FormatNumberWithUnit(double tValue, bool hasDecimal = true)
        {
            int[] dp = new int[11] { 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1 };
            if (!hasDecimal)
            {
                for (int i = 0; i < dp.Length; i++)
                {
                    dp[i] = 0;
                }
            }

            string strRes = "0";

            for (int i = 0; i < 10; i++)
            {
                double bt = 1000 * Mathf.Pow(10, i);

                if (tValue < bt)
                {
                    int dpNum = dp[i];
                    double tp = 5 / Mathf.Pow(10, dpNum + 1);
                    double trVal = 0;
                    string strFormat = string.Empty;

                    if (i == 0 || i == 1)
                    {
                        strFormat = $"{{0:F{dpNum}}}";
                        trVal = tValue;
                    }
                    else if (2 <= i && i <= 3)
                    {
                        strFormat = $"{{0:F{dpNum}}}K";
                        trVal = tValue / 1000;
                    }
                    else if (4 <= i && i <= 6)
                    {
                        strFormat = $"{{0:F{dpNum}}}M";
                        trVal = tValue / 1000000;
                    }
                    else if (i > 6)
                    {
                        strFormat = $"{{0:F{dpNum}}}G";
                        trVal = tValue / 1000000000;
                    }

                    double e = trVal * Mathf.Pow(10, dpNum + 1) % 10;
                    if (e <= 0)
                    {
                        tp = 0;
                    }

                    double temp = trVal - tp;
                    strRes = string.Format(strFormat, temp);
                    return strRes;
                }
            }

            return strRes;
        }

        /// <summary>
        /// 将数值格式化为带分隔符(默认是逗号)的字符串
        /// </summary>
        /// <param name="number">要格式化的数值</param>
        /// <param name="separator">分隔符</param>
        /// <returns>格式化后的字符串</returns>
        public static string FormatNumberWithSeparator(double number, string separator = ",")
        {
            StringBuilder sb = new();
            string numStr = number.ToString();

            // 处理小数点
            int decimalIndex = numStr.IndexOf('.');
            string integerPart = decimalIndex >= 0 ? numStr[..decimalIndex] : numStr;
            string decimalPart = decimalIndex >= 0 ? numStr[decimalIndex..] : string.Empty;

            // 从右向左每三位添加分隔符
            for (int i = integerPart.Length - 1, count = 0; i >= 0; i--)
            {
                if (count > 0 && count % 3 == 0) //&& i != 0
                {
                    sb.Insert(0, separator);
                }

                sb.Insert(0, integerPart[i]);
                count++;
            }

            // 添加小数部分
            if (!string.IsNullOrEmpty(decimalPart))
            {
                sb.Append(decimalPart);
            }

            return sb.ToString();
        }

        /// <summary>
        /// 根据属性类型返回名字
        /// </summary>
        /// <param name="attributes_type">属性类型</param>
        /// <returns>属性lang名字</returns>
        public static string GetNameByAttrbuteType(attributes_type attrType)
        {
            int configId = (int)attrType;
            attributes_config attributesConfig = Game.GameEntry.LDLTable.GetTableById<attributes_config>(configId);
            int langID = attributesConfig?.langid ?? 0;
            return GetLang(langID);
        }

        public static float GetFightByAttrbuteType(attributes_type attrType)
        {
            attributes_config attributesConfig = Game.GameEntry.LDLTable.GetTableById<attributes_config>((int)attrType);
            return attributesConfig?.combat_effectiveness ?? 0f;
        }

        public static string GetNameByAttrbuteTarget(attributes_target attrTarget)
        {
            if (attrTarget == attributes_target.attributes_target_1)
            {
                return GetLang(711330);
            }
            else if (attrTarget == attributes_target.attributes_target_2)
            {
                return GetLang(711331);
            }
            else if (attrTarget == attributes_target.attributes_target_3)
            {
                return GetLang(711332);
            }
            else if (attrTarget == attributes_target.attributes_target_4)
            {
                return GetLang(711333);
            }
            else
            {
                return "";
            }
        }

        //解锁条件判断
        public static bool GetDemandUnlock(int configId)
        {
            var config = Game.GameEntry.LDLTable.GetTableById<demand_config>(configId);
            if (config != null)
            {
                if (config.hero_demand != null) //英雄要求
                {
                    var demand = config.hero_demand;
                    var isUnlock = true;
                    List<HeroModule> heroList = null;
                    if (demand.hero_id_demand == 0)
                    {
                        heroList ??= GameEntry.LogicData.HeroData.GetHeroModuleList(hero_services.hero_services_all);
                        foreach (var heroVo in heroList)
                        {
                            if (heroVo.starLv < demand.hero_star_demand || heroVo.level < demand.hero_level_demand)
                            {
                                isUnlock = false;
                                break;
                            }
                        }
                    }
                    else
                    {
                        var heroVo = GameEntry.LogicData.HeroData.GetHeroModule(demand.hero_id_demand);
                        if (heroVo == null || heroVo.starLv < demand.hero_star_demand ||
                            heroVo.level < demand.hero_level_demand)
                        {
                            isUnlock = false;
                        }
                    }

                    return isUnlock;
                }

                if (config.build_demand != null) //建筑要求
                {
                    bool isUnlock = GameEntry.LogicData.BuildingData.GetBuildingDemandIsUnlock(
                        config.build_demand.build_type_demand, config.build_demand.build_level_demand,
                        config.build_demand.build_num_demand);
                    if (!isUnlock)
                    {
                        return false;
                    }

                    return true;
                }

                if (config.tech_demand != 0) //科技要求
                {
                    return false;
                }

                if (config.pay_demand != 0) //付费要求
                {
                    return false;
                }

                if (config.dungeon_demand != 0)
                {
                    return GameEntry.LogicData.DungeonData.CurDungeonId >= config.dungeon_demand;
                }

                if (config.service_opening_days != 0) //开服天数要求
                {
                    var day = GameEntry.LDLNet.GetOpenServiceDays();
                    return day >= config.service_opening_days;
                }

                if (config.season_demand != string.Empty) //赛季要求
                {
                    return false;
                }

                if (config.VIP_demand != 0) //vip等级要求
                {
                    if (!GameEntry.LogicData.VipData.IsVipActive())
                    {
                        return false;
                    }

                    var vipLevel = GameEntry.LogicData.VipData.GetVipLevel();
                    if (vipLevel >= config.VIP_demand)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        //判断解锁列表
        public static bool CheckDemandUnlockList(List<int> list)
        {
            foreach (var demand in list)
            {
                if (!GetDemandUnlock(demand))
                {
                    return false;
                }
            }

            return true;
        }

        public static void SetGameObjectGrey(Transform parent, bool isGrey)
        {
            UIImage[] uiImages = parent.GetComponentsInChildren<UIImage>();
            foreach (UIImage uiImage in uiImages)
            {
                uiImage.SetImageGray(isGrey);
            }

            var shader = isGrey ? Shader.Find("UIShader/UIMaterialGrey") : Shader.Find("UI/Default");
            var imgs = parent.GetComponentsInChildren<Image>();
            foreach (var img in imgs)
            {
                var material = new Material(shader);
                img.material = material;
            }
        }

        /// <summary>
        /// 启用或禁用文本的外框和阴影效果
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="state">状态</param>
        public static void EnableOutlineAndUIShadow(Text text, bool state)
        {
            Outline outline = text.GetComponent<Outline>();
            UIShadow uiShadow = text.GetComponent<UIShadow>();
            outline.enabled = state;
            uiShadow.enabled = state;
        }

        public static void SetOutlineColorAndUIShadow(Text text, string outLineColorStr, string shadowColorStr = null)
        {
            Outline outline = text.GetComponent<Outline>();
            if (outline != null)
            {
                if (ColorUtility.TryParseHtmlString(outLineColorStr, out Color outLineColor))
                {
                    outline.effectColor = outLineColor;
                }
            }

            if (shadowColorStr != null)
            {
                UIShadow uiShadow = text.gameObject.GetOrAddComponent<UIShadow>();
                if (uiShadow != null)
                {
                    if (ColorUtility.TryParseHtmlString(shadowColorStr, out Color shadowColor))
                    {
                        uiShadow.effectColor = shadowColor;
                    }
                }
            }
        }

        /// <summary>
        /// 递归设置粒子系统的渲染层级
        /// </summary>
        /// <param name="parent">目标游戏物体</param>
        /// <param name="depth">渲染顺序</param>
        public static void SetParticleSystemSortingOrder(GameObject parent, int depth)
        {
            int origin = depth;
            var particleSystems = parent.GetComponentsInChildren<ParticleSystem>(true);
            foreach (var ps in particleSystems)
            {
                if (ps.TryGetComponent<ParticleSystemRenderer>(out var renderer))
                {
                    renderer.sortingOrder = origin + 1;
                    origin++;
                }
            }
        }

        /// <summary>
        /// 设置粒子系统渲染范围
        /// </summary>
        /// <param name="effObj">目标物体</param>
        /// <param name="maskRect">遮罩</param>
        public static void SetParticleSystemMaskSize(GameObject effObj, RectTransform maskRect)
        {
            Vector3[] corners = new Vector3[4];
            maskRect.GetWorldCorners(corners);
            var rect = new Vector4(corners[0].x, corners[0].y, corners[2].x, corners[1].y);

            var renderers = effObj.GetComponentsInChildren<ParticleSystemRenderer>();
            foreach (var renderer in renderers)
            {
                if (renderer.material)
                {
                    renderer.material.SetVector("_Area", rect);
                }
            }
        }

        //清除指定父节点下的所有子物体
        public static void ClearAllChild(Transform parentRoot)
        {
            if (parentRoot == null)
            {
                return;
            }

            var childCount = parentRoot.childCount;
            for (var i = childCount - 1; i >= 0; i--)
            {
                var child = parentRoot.GetChild(i);
                Object.DestroyImmediate(child.gameObject);
            }
        }

        /// <summary>
        /// 格式化时间字符串
        /// 超过一天显示: 6d 18:48:34
        /// 小于一天显示：18:48:34
        /// </summary>
        /// <param name="seconds">总秒数</param>
        /// <returns></returns>
        public static string FormatTime(int seconds)
        {
            TimeSpan span = new TimeSpan(0, 0, seconds);
            if (span.Days > 0)
            {
                stringBuilder.Clear();
                stringBuilder.Append(span.Days);
                stringBuilder.Append("d ");
                stringBuilder.Append(span.ToString(@"hh\:mm\:ss"));
                return stringBuilder.ToString();
            }
            else
            {
                return span.ToString(@"hh\:mm\:ss");
            }
        }

        /// <summary>
        /// 获取语言类型名字
        /// </summary>
        /// <param name="langId"></param>
        /// <returns></returns>
        public static string GetLangTypeName(int langType)
        {
            var config = GameEntry.LDLTable.GetTableById<language_type>(langType);
            var str = config != null ? GetLang(config.lang_id) : $"Err:{langType}";
            return str;
        }

        /// <summary>
        /// 加载预制体
        /// </summary>
        /// <param name="prefabPath">预制体路径</param>
        /// <param name="action">回调函数</param>
        public static void LoadPrefab(string prefabPath, UnityAction<GameObject> action)
        {
            GameEntry.Resource.LoadAsset($"Assets/ResPackage/Prefab/{prefabPath}.prefab", typeof(GameObject),
                new LoadAssetCallbacks((assetName, asset, duration, userData) =>
                {
                    if (asset is GameObject obj)
                    {
                        action?.Invoke(obj);
                    }
                }));
        }

        /// <summary>
        /// 获取配表中指定id的数据（只适用于id为int32类型的表）
        /// </summary>
        /// <param name="configId"></param>
        /// <param name="result"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns>true:找到了数据 false:未找到数据</returns>
        public static bool GetConfigById<T>(int configId, out T result) where T : tabtoy.ITableSerializable
        {
            if (GameEntry.LDLTable.HaseTable<T>())
            {
                var data = GameEntry.LDLTable.GetTableById<T>(configId);
                if (data != null)
                {
                    result = data;
                    return true;
                }
            }

            result = default(T);
            return false;
        }

        public static bool GetCommonTipsIsTodayCheckBox(string checkBoxKey)
        {
            if (string.IsNullOrEmpty(checkBoxKey))
            {
                return false;
            }

            // 获取本地时间的当天 0 点
            DateTime todayStartLocal = DateTime.Today;
            // 转换为 Unix 时间戳
            long zeroStampSeconds = new DateTimeOffset(todayStartLocal).ToUnixTimeSeconds();
            string prefsValue = GameEntry.Setting.GetString(checkBoxKey);
            return prefsValue == zeroStampSeconds.ToString();
        }


        //绑定按钮逻辑
        public static void BindBtnLogic(UIButton btn, UnityAction callback)
        {
            if (btn == null) return;
            btn.onClick.RemoveAllListeners();
            btn.onClick.AddListener(() => { callback?.Invoke(); });
        }

        /// <summary>
        /// 获取字符串长度(中文字符算3个)
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static int GetStrLength(string str)
        {
            var length = 0;
            foreach (char c in str)
            {
                if (c >= 0x4E00 && c <= 0x9FFF)
                {
                    length += 3;
                } // 中文
                else
                {
                    length += 1;
                }
            }

            return length;
        }

        //展示通用奖励获取界面
        //isSpecial:特殊处理，标题显示“领取成功”  其他默认显示“恭喜获得”
        public static void DisplayRewardGet(List<Article.Article> rewardList, bool isSpecial = false)
        {
            List<reward> rewards = new();
            foreach (var item in rewardList)
            {
                rewards.Add(new reward { item_id = (itemid)item.Code, num = item.Amount });
            }

            RewardGetParams param = new RewardGetParams()
            {
                isRewardSpecial = isSpecial,
                isNeedShowDesc = false,
                descStr = "",
                rewardList = rewards
            };

            GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, param);
        }

        /// <summary>
        /// 角色id组成本地缓存key
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string GetRolePlayerPrefsKeyById(string key)
        {
            string roleKey = key + GameEntry.RoleData.RoleID;
            return roleKey;
        }

        /// <summary>
        /// 展示英雄Spine动画
        /// </summary>
        /// <param name="heroId">英雄配表id</param>
        /// <param name="graphic">Spine UI组件</param>
        public static void ShowHeroSpine(int heroId, SkeletonGraphic graphic)
        {
            if (graphic == null) return;
            if (GetConfigById<hero_spine_config>(heroId, out var spineConfig))
            {
                GameEntry.Resource.LoadAsset(spineConfig.role_resources[0], typeof(Material),
                    new LoadAssetCallbacks((assetName, asset, duration, userData) =>
                    {
                        if (asset is Material material)
                        {
                            GameEntry.Resource.LoadAsset(spineConfig.role_resources[1], typeof(SkeletonDataAsset),
                                new LoadAssetCallbacks((assetName1, asset1, duration1, userData1) =>
                                {
                                    if (asset1 is SkeletonDataAsset skeletonDataAsset)
                                    {
                                        graphic.material = material;
                                        graphic.skeletonDataAsset = skeletonDataAsset;
                                        graphic.Initialize(true);
                                    }
                                }));
                        }
                    }));
            }
        }

        /// <summary>
        /// 根据配置设置spine的缩放大小和偏移位置
        /// </summary>
        /// <param name="heroId"></param>
        /// <param name="graphicObj"></param>
        /// <param name="posType">界面类型，根据不同的界面类型配置不同的位置和缩放数据：0 忽略；1=英雄头像小窗；2=英雄半身像；3=超值活动角色升星</param>
        public static void SetSpinePosAndScale(int heroId, GameObject graphicObj, int posType = 0)
        {
            if (graphicObj == null) return;
            if (!GetConfigById<hero_spine_config>(heroId, out var spineConfig)) return;
            if (posType == 0) return;
            var offsetList = spineConfig.role_offset;
            var scaleList = spineConfig.role_scale;
            var typeId = posType - 1;
            if (offsetList.Count > typeId && scaleList.Count > typeId)
            {
                var scaleData = scaleList[typeId];
                graphicObj.transform.localScale = new Vector3(float.Parse(scaleData.pos_x),
                    float.Parse(scaleData.pos_y), float.Parse(scaleData.pos_z));
                var offsetData = offsetList[typeId];
                var rect = graphicObj.GetComponent<RectTransform>();
                if (rect != null)
                {
                    rect.anchoredPosition = new Vector2(float.Parse(offsetData.pos_x),
                        float.Parse(offsetData.pos_y));
                }
            }
        }

        //重复利用已经实例化的item对象
        //数量少时进行实例化，数量多的部分进行隐藏
        //initUnHide:false表示是否初始化需要全部隐藏已有子物体，可能造成刷新时闪一下，可以设为true试试
        public static void RecycleOrCreate(GameObject template, Transform root, int needCount, bool initUnHide = false)
        {
            var curCount = root.childCount;
            if (!initUnHide)
            {
                for (var i = curCount - 1; i >= 0; i--)
                {
                    var child = root.GetChild(i);
                    child.gameObject.SetActive(false);
                }
            }

            if (curCount < needCount)
            {
                var offset = needCount - curCount;
                while (offset > 0)
                {
                    GameObject.Instantiate(template, root);
                    offset--;
                }
            }
            else if (curCount > needCount)
            {
                var offset = curCount - needCount;
                for (var i = needCount; i < curCount; i++)
                {
                    var child = root.GetChild(i);
                    child.gameObject.SetActive(false);
                }
            }

            for (var i = 0; i < needCount; i++)
            {
                var child = root.GetChild(i);
                child.gameObject.SetActive(true);
            }
        }

        /// <summary>
        /// 设置数字红点
        /// </summary>
        /// <param name="dotRoot">数字红点对象的父节点</param>
        /// <param name="dotCount">数字红点数值</param>
        public static void SetNumRedDot(Transform dotRoot, int dotCount)
        {
            var count = dotRoot.childCount;
            if (count == 0)
            {
                if (dotCount > 0)
                {
                    LoadPrefab("Common/NumDot", (template) =>
                    {
                        var obj = GameObject.Instantiate(template, dotRoot);
                        var txt = obj.transform.Find("txt").GetComponent<UIText>();
                        if (txt != null)
                        {
                            txt.text = dotCount.ToString();
                        }
                    });
                }
            }
            else
            {
                var dotTrans = dotRoot.GetChild(0);
                if (dotTrans == null) return;
                dotTrans.gameObject.SetActive(dotCount > 0);
                if (dotCount > 0)
                {
                    var txt = dotTrans.Find("txt").GetComponent<UIText>();
                    if (txt != null)
                    {
                        txt.text = dotCount.ToString();
                    }
                }
            }
        }

        /// <summary>
        /// 获取显示的字符串，不显示的删除
        /// </summary>
        /// <param name="showStr">字符串</param>
        /// <param name="startStr">要删除的字符串开头</param>
        /// <param name="endStr">要删除的字符串结尾</param>
        /// <returns></returns>
        public static string GetRealStr(string showStr, string startStr, string endStr)
        {
            showStr = showStr.Replace(endStr, "");
            while (true)
            {
                var startIndex = showStr.IndexOf(startStr);
                if (startIndex == -1) break;
                var endIndex = showStr.IndexOf(">", startIndex + 1);
                if (endIndex == -1) break;
                showStr = showStr.Remove(startIndex, endIndex - startIndex + 1);
            }

            return showStr;
        }

        /// <summary>
        /// 查找字符串所有匹配位置
        /// </summary>
        /// <param name="source">字符串</param>
        /// <param name="pattern">匹配字符串</param>
        /// <returns></returns>
        public static List<int> FindStrAllIndex(string source, string pattern)
        {
            var list = new List<int>();
            var index = 0;
            while ((index = source.IndexOf(pattern, index)) != -1)
            {
                list.Add(index);
                index += pattern.Length;
            }

            return list;
        }

        /// <summary>
        /// 设置UIItemModule对象上的文本大小
        /// 解决缩放itemObj后文本显示太小的问题
        /// </summary>
        /// <param name="size"></param>
        public static void SetItemObjTxtScale(GameObject itemObj, float size)
        {
            var module = itemObj.GetComponent<UIItemModule>();
            if (module != null)
            {
                var count = itemObj.transform.Find("count").GetComponent<Text>();
                if (count != null)
                {
                    count.transform.localScale = new Vector3(size, size, 1);
                }
            }
        }

        /// <summary>
        /// 设置通用奖励信息
        /// </summary>
        /// <param name="root">itemObj生成的父节点</param>
        /// <param name="obj">itemObj实例对象</param>
        /// <param name="id">道具id</param>
        /// <param name="count">道具数量</param>
        /// <param name="txtScale">itemObj中的文本scale</param>
        /// <param name="callback">点击自定义函数</param>
        public static void SetItemObjInfo(Transform root, GameObject obj, itemid id, int count, float txtScale = 1,
            Action<UIItemModule> callback = null)
        {
            var module = obj.GetComponent<UIItemModule>();
            if (module != null)
            {
                SetItemObjTxtScale(module.gameObject, txtScale);
                var itemModule = new ItemModule();
                itemModule.SetData(id, count);
                module.itemModule = itemModule;
                module.Init(root, id, count);
                module.DisplayInfo();
                module.SetClick(() =>
                {
                    if (callback == null)
                    {
                        module.OpenTips();
                    }
                    else
                    {
                        callback.Invoke(module);
                    }
                });
            }
        }

        /// <summary>
        /// 获取指定配表的整个数据
        /// </summary>
        /// <param name="result"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static bool GetTable<T>(out List<T> result) where T : tabtoy.ITableSerializable
        {
            if (GameEntry.LDLTable.HaseTable<T>())
            {
                var table = GameEntry.LDLTable.GetTable<T>();
                if (table != null)
                {
                    result = table;
                    return true;
                }
            }
            result = null;
            return false;
        }
        
        
        

    }
}