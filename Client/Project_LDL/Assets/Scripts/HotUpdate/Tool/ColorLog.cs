
#if UNITY_EDITOR
using Unity.Plastic.Newtonsoft.Json;
#endif

using UnityEngine;

namespace Game.Hotfix
{
    /// <summary>
    /// 彩色日志工具类
    /// </summary>
    public static class ColorLog
    {
        static readonly string pinkColorHex = "#fbbac1";
        static readonly string redColorHex = "#bf5959";
        static readonly string yellowColorHex = "#ffbb00";
        static readonly string blueColorHex = "#5489ce";
        static readonly string greenColorHex = "#3cb472";
        static readonly int fontSize = 15;
        public static bool isDebug = true;

        public static void Red(params object[] args)
        {
            LogError(redColorHex, args);
        }

        public static void Red(string title, params object[] args)
        {
            LogError(redColorHex, title, args);
        }

        public static void Yellow(params object[] args)
        {
            LogWarning(yellowColorHex, args);
        }

        public static void Yellow(string title, params object[] args)
        {
            LogWarning(yellowColorHex, title, args);
        }

        public static void Blue(params object[] args)
        {
            Log(blueColorHex, args);
        }

        public static void Blue(string title, params object[] args)
        {
            Log(blueColorHex, title, args);
        }

        public static void Green(params object[] args)
        {
            Log(greenColorHex, args);
        }

        public static void Green(string title, params object[] args)
        {
            Log(greenColorHex, title, args);
        }

        public static void Pink(params object[] args)
        {
            Log(pinkColorHex, args);
        }

        public static void Pink(string title, params object[] args)
        {
            Log(pinkColorHex, title, args);
        }

        static void Log(string color, params object[] args)
        {
            if (!isDebug) return;
            string content = args.Length > 0 ? string.Join(" ", args) : string.Empty;
            Debug.Log($"<size={fontSize}><color={color}>{content}</color></size>");
        }

        static void Log(string color, string title, params object[] args)
        {
            if (!isDebug) return;
            string content = args.Length > 0 ? string.Join(" ", args) : string.Empty;
            Debug.Log($"<size={fontSize}><color={color}><b>{title}</b> {content}</color></size>");
        }

        static void LogWarning(string color, params object[] args)
        {
            if (!isDebug) return;
            string content = args.Length > 0 ? string.Join(" ", args) : string.Empty;
            Debug.LogWarning($"<size={fontSize}><color={color}>{content}</color></size>");
        }

        static void LogWarning(string color, string title, params object[] args)
        {
            if (!isDebug) return;
            string content = args.Length > 0 ? string.Join(" ", args) : string.Empty;
            Debug.LogWarning($"<size={fontSize}><color={color}><b>{title}</b> {content}</color></size>");
        }

        static void LogError(string color, params object[] args)
        {
            if (!isDebug) return;
            string content = args.Length > 0 ? string.Join(" ", args) : string.Empty;
            Debug.LogError($"<size={fontSize}><color={color}>{content}</color></size>");
        }

        static void LogError(string color, string title, params object[] args)
        {
            if (!isDebug) return;
            string content = args.Length > 0 ? string.Join(" ", args) : string.Empty;
            Debug.LogError($"<size={fontSize}><color={color}><b>{title}</b> {content}</color></size>");
        }
        
        /// <summary>
        /// 协议数据打印
        /// </summary>
        /// <param name="isRequest">true：向服务端发送请求   false:服务端返回数据</param>
        /// <param name="protoName">协议名称意义</param>
        /// <param name="protoDetail">发送或返回数据的ToString()</param>
        public static void ProtoLog(bool isRequest, string protoName, string protoDetail = null)
        {
            var key = false;
#if UNITY_EDITOR
            if (!key) return;
            if (isRequest)
            {
                Debug.Log($"<color=#FF9100>发送{protoName}协议请求！发送数据:{protoDetail ?? "无"}</color>");
            }
            else
            {
                Debug.Log($"<color=#00F836>{protoName}数据成功返回！返回数据:{protoDetail ?? "无"}</color>");
            }
#endif
        }
        
        /// <summary>
        /// 协议数据打印,缩进格式打印
        /// </summary>
        /// <param name="isRequest">true：向服务端发送请求   false:服务端返回数据</param>
        /// <param name="protoName">协议名称意义</param>
        /// <param name="data">发送或返回数据</param>
        public static void ProtoLog(bool isRequest, string protoName, object data)
        {
            var key = false;
#if UNITY_EDITOR
            if (!key) return;
            var str = JsonConvert.SerializeObject(data,Unity.Plastic.Newtonsoft.Json.Formatting.Indented);
            if (isRequest)
            {
                Debug.Log($"<color=#FF9100>发送{protoName}协议请求！发送数据:{str ?? "无"}</color>");
            }
            else
            {
                Debug.Log($"<color=#00F836>{protoName}数据成功返回！返回数据:{str ?? "无"}</color>");
            }
#endif
        }
    }
}