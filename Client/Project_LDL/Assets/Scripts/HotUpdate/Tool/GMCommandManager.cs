using System;

namespace Game.Hotfix
{
    public class GMCommandManager : <PERSON><PERSON><GMCommandManager>
    {
        public void AddCommand()
        {
            IngameDebugConsole.DebugLogConsole.AddCommand<string, string>("cheat", "set some functions", CheatFunc2);
            IngameDebugConsole.DebugLogConsole.AddCommand<string, string, string>("cheat", "set some functions", CheatFunc3);
            IngameDebugConsole.DebugLogConsole.AddCommand<string, string, string, string>("cheat", "set some functions", CheatFunc4);
            IngameDebugConsole.DebugLogConsole.AddCommand<string, string, string, string, string>("cheat", "set some functions", CheatFunc5);
        }

        void CheatFunc2(string p0, string p1)
        {
            TriggerCommand(p0, p1);
        }

        void CheatFunc3(string p0, string p1, string p2)
        {
            TriggerCommand(p0, p1, p2);
        }

        void CheatFunc4(string p0, string p1, string p2, string p3)
        {
            TriggerCommand(p0, p1, p2, p3);
        }

        void CheatFunc5(string p0, string p1, string p2, string p3, string p4)
        {
            TriggerCommand(p0, p1, p2, p3, p4);
        }

        public void TriggerCommand(params string[] args)
        {
            OnReqRoleGM(args);
        }

        public void OnReqRoleGM(params string[] args)
        {
            var req = new Gm.RoleGMReq
            {
                Args = { args }
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.RoleGm, req, (message) =>
            {
                var resp = (Gm.RoleGMResp)message;
                ColorLog.Pink("指令回调 ", resp);
            });
        }
    }
}
