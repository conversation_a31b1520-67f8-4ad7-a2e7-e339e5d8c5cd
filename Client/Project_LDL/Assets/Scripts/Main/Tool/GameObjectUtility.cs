using UnityEngine;
using UnityEngine.EventSystems;

public static class GameObjectUtility
{
    public static void DestroyAllChild(this GameObject obj, string dontDestroyName = null)
    {
        foreach (Transform child in obj.transform)
        {
            if (!string.IsNullOrEmpty(dontDestroyName) && child.gameObject.name.Contains(dontDestroyName))
            {
                continue;
            }
            UnityEngine.Object.Destroy(child.gameObject);
        }

        obj.transform.DetachChildren();
    }

    public static void ResetTransform(Transform transform)
    {
        if (transform)
        {
            transform.localScale = Vector3.one;
            transform.localPosition = Vector3.zero;
            transform.localRotation = Quaternion.identity;
        }
    }

    public static void Bind(Transform parentTransform, Transform childTransform, bool resetChildTransform = true)
    {
        if (!childTransform)
            return;
        if (parentTransform)
            childTransform.SetParent(parentTransform);
        else
            childTransform.SetParent(null);
        if (resetChildTransform)
            ResetTransform(childTransform);
    }

    public static void UnBind(Transform childTransform, bool resetTransform = true)
    {
        if (childTransform)
        {
            childTransform.SetParent(null);
            if (resetTransform)
                ResetTransform(childTransform);
        }
    }

    public static void SetActiveSafe(this GameObject gameObj, bool b)
    {
        if (gameObj.activeSelf != b)
            gameObj.SetActive(b);
    }

    public static void SetLayer(this Transform transform, int layer)
    {
        if (transform)
        {
            Transform[] ts = transform.GetComponentsInChildren<Transform>();
            if (ts != null)
            {
                int count = ts.Length;
                for (int i = 0; i < count; i++)
                {
                    Transform t = ts[i];
                    t.gameObject.layer = layer;
                }
            }
        }
    }

        public static bool IsPointerOverGameObject()
        {
                // Debug.Log(Input.GetMouseButtonDown(0));
                // if (Input.GetMouseButtonDown(0) || (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began))
                {
#if UNITY_EDITOR
         return EventSystem.current.IsPointerOverGameObject();
#elif UNITY_ANDROID || UNITY_IOS
        if (Input.touchCount > 0)
        {
            return EventSystem.current.IsPointerOverGameObject(Input.GetTouch(0).fingerId);
        }
        return false;
#else
        return false;
#endif
                }
                // return false;
        }
        
        public static void SetBtnCircularLayout(Transform pp)
        {
            float angleIncrement = 34;
            Vector2 center = Vector2.zero;
            float radius = 290;
            float startAngle = -90;
            bool clockwise = true;
            
            var buttonCount = pp.transform.childCount;
            Vector2[] positions = new Vector2[buttonCount];

            var newStartAngle = -0.5f * ((buttonCount - 1) * angleIncrement) + startAngle;
        
            for (int i = 0; i < buttonCount; i++)
            {
                float angle = newStartAngle + i * angleIncrement * (clockwise ? 1 : -1);
                angle = angle % 360;
                if (angle < 0) angle += 360;

                float radian = angle * Mathf.Deg2Rad;

                float x = center.x + radius * Mathf.Cos(radian);
                float y = center.y + radius * Mathf.Sin(radian);

                positions[i] = new Vector2(x, y);
            
            
                var child = pp.transform.GetChild(i);
                var rt = child.GetComponent<RectTransform>();
                if (rt)
                {
                    rt.anchoredPosition = positions[i];
                }
            }
            
        }
}
