
using UnityEngine;
public static class PlayerPrefsEx
{
    public static void SetString(string key, string value)
    {
        PlayerPrefs.SetString(Rijndael.MD5String(key), Rijndael.Encrypt(value));
    }

    public static string GetString(string key, string defaultValue = "")
    {
        string encryptKey = Rijndael.MD5String(key);
        if (PlayerPrefs.HasKey(encryptKey))
        {
            string strEncrypt = PlayerPrefs.GetString(encryptKey);
            string value = Rijndael.Decrypt(strEncrypt);
            return value;
        }
        return defaultValue;
    }

    public static void SetInt(string key, int value)
    {
        PlayerPrefs.SetString(Rijndael.MD5String(key), Rijndael.Encrypt(value.ToString()));
    }

    public static int GetInt(string key, int defalutValue = 0)
    {
        string encryptKey = Rijndael.MD5String(key);
        if (PlayerPrefs.<PERSON><PERSON>ey(encryptKey))
        {
            string strEncrypt = PlayerPrefs.GetString(encryptKey);
            string strDecrypt = Rijndael.Decrypt(strEncrypt);
            int value;
            if (int.TryParse(strDecrypt, out value))
                return value;
        }
        return defalutValue;
    }

    public static void SetFloat(string key, float value)
    {
        PlayerPrefs.SetString(Rijndael.MD5String(key), Rijndael.Encrypt(value.ToString()));
    }

    public static float GetFloat(string key, float defalutValue = 0.0f)
    {
        string encryptKey = Rijndael.MD5String(key);
        if (PlayerPrefs.HasKey(encryptKey))
        {
            string strEncrypt = PlayerPrefs.GetString(encryptKey);
            string strDecrypt = Rijndael.Decrypt(strEncrypt);
            float value;
            if (float.TryParse(strDecrypt, out value))
                return value;
        }
        return defalutValue;
    }

    public static void DeleteKey(string key)
    {
        string encryptKey = Rijndael.MD5String(key);
        if (PlayerPrefs.HasKey(encryptKey))
        {
            PlayerPrefs.DeleteKey(encryptKey);
        }
    }

    public static void DeleteAll()
    {
        PlayerPrefs.DeleteAll();
    }
}
