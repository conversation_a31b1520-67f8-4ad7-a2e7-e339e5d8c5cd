using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class CustomBloomPass : ScriptableRenderPass
{
    const string k_Tag = "CustomBloom";
    RTHandle m_TempRT;
    RTHandle m_MaskRT;
    FilteringSettings m_Filtering;
    private RenderStateBlock m_RenderStateBlock;
    // 配置参数 

    CustomBloomVolume customBloomVolume;

   List<ShaderTagId>  shaderTags =   new List<ShaderTagId> () { new ShaderTagId("UniversalForward") ,new ShaderTagId("SRPDefaultUnlit") 
         };

    public CustomBloomPass(
        LayerMask bloomLayer)
    {
        m_Filtering = new FilteringSettings(RenderQueueRange.all, bloomLayer);
     //   m_RenderStateBlock = new RenderStateBlock(RenderStateMask.Depth);
     //   m_RenderStateBlock.depthState = new DepthState(true, CompareFunction.LessEqual);
    }

    public override void Configure(CommandBuffer cmd, RenderTextureDescriptor cameraDescriptor)
    {
        // 动态分辨率支持
        RTHandles.SetReferenceSize(cameraDescriptor.width, cameraDescriptor.height);

        int divider =  4;// customBloomVolume.resolution.value == CustomBloomVolume.Resolution.Low ? 4 : 2;
        int width = cameraDescriptor.width / divider;
        int height = cameraDescriptor.height / divider;

        m_TempRT = RTHandles.Alloc(
            width, height,
            depthBufferBits: DepthBits.None,
            colorFormat: cameraDescriptor.graphicsFormat,
            dimension: TextureDimension.Tex2D,
            enableRandomWrite: false,
            useDynamicScale: cameraDescriptor.useDynamicScale);

        m_MaskRT = RTHandles.Alloc(
            cameraDescriptor.width, cameraDescriptor.height,
            depthBufferBits: DepthBits.None,
            colorFormat: cameraDescriptor.graphicsFormat,
            dimension: TextureDimension.Tex2D,
            enableRandomWrite: false,
            useDynamicScale: cameraDescriptor.useDynamicScale);

        ConfigureTarget(m_MaskRT);
        ConfigureClear(ClearFlag.All, Color.clear);
    }

    public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
    {
        VolumeStack stack = VolumeManager.instance.stack;
        customBloomVolume = stack.GetComponent<CustomBloomVolume>();
        if (customBloomVolume == null || !customBloomVolume.IsActive() ||  customBloomVolume.bloomMaterial.value == null)  
        {
             return; 
        }

        renderPassEvent = customBloomVolume.renderEvent.value;
        RTHandle cameraTarget = renderingData.cameraData.renderer.cameraColorTargetHandle;

        var cmd = CommandBufferPool.Get(k_Tag);
        // 1) 渲染 LayerMask 到 Mask RT
        cmd.SetRenderTarget(m_MaskRT,renderingData.cameraData.renderer.cameraDepthTargetHandle);
        cmd.ClearRenderTarget(true, true, Color.clear);
        context.ExecuteCommandBuffer(cmd);
        context.Submit();
        cmd.Clear();

        var drawSettings = CreateDrawingSettings(shaderTags, ref renderingData, SortingCriteria.CommonTransparent);
        // 添加深度状态设置
   
        context.DrawRenderers(renderingData.cullResults, ref drawSettings, ref m_Filtering );//, ref m_RenderStateBlock

        // 2) 设置材质参数
       customBloomVolume.bloomMaterial.value.SetVector("_Parameter", new Vector4(customBloomVolume.blurSize.value * (customBloomVolume.resolution.value == CustomBloomVolume.Resolution.Low ? 0.5f : 1f), 0, customBloomVolume.threshold.value, customBloomVolume.intensity.value));

        // 3) 下采样 + 提取阈值
        cmd.Blit(m_MaskRT, m_TempRT, customBloomVolume.bloomMaterial.value, 1);

          // 4. 模糊迭代 (Pass 2 & 3 或 4 & 5)
        for (int i = 0; i <customBloomVolume.blurIterations.value; i++)
        {
            int vert = customBloomVolume.blurType.value == CustomBloomVolume.BlurType.Standard ? 2 : 4;
            int hor  = customBloomVolume.blurType.value == CustomBloomVolume.BlurType.Standard ? 3 : 5;
            cmd.Blit(m_TempRT, m_MaskRT, customBloomVolume.bloomMaterial.value, vert);
            cmd.Blit(m_MaskRT, m_TempRT, customBloomVolume.bloomMaterial.value, hor);
        }

        // 5. 合成回屏幕 (Pass 0)
        // Composite original color and bloom into a temp RT to avoid overwriting source mid-pass
        RTHandle compositeRT = m_MaskRT; // reuse mask RT as temp
        // Bind textures
        customBloomVolume.bloomMaterial.value.SetTexture("_Bloom", m_TempRT);
        // First blit with pass 0 into compositeRT
        cmd.Blit(cameraTarget, compositeRT,  customBloomVolume.bloomMaterial.value, 0);
        //Then blit compositeRT back to camera
        cmd.Blit(compositeRT, cameraTarget);

        context.ExecuteCommandBuffer(cmd);
        context.Submit();
        cmd.Clear();
        CommandBufferPool.Release(cmd);
    }

    public override void FrameCleanup(CommandBuffer cmd)
    {
        if (m_TempRT != null)
        {
            RTHandles.Release(m_TempRT);
            m_TempRT = null;
        }
        if (m_MaskRT != null)
        {
            RTHandles.Release(m_MaskRT);
            m_MaskRT = null;
        }
    }
}
