using System.IO;
using GameFramework;
using GameFramework.Event;

namespace Game
{
    public class LoadLDLConfigSuccessEventArgs : GameEventArgs
    {
        public static readonly int EventId = typeof(LoadLDLConfigSuccessEventArgs).GetHashCode();
        public override int Id
        {
            get { return EventId; }
        }

        public object UserData
        {
            get;
            private set;
        }

        public string LoadFlagKey
        {
            get;
            private set;
        }
        
        public static LoadLDLConfigSuccessEventArgs Create(string loadFlagKey,object userData)
        {
            LoadLDLConfigSuccessEventArgs e = ReferencePool.Acquire<LoadLDLConfigSuccessEventArgs>();
            e.LoadFlagKey = loadFlagKey;
            e.UserData = userData;

            return e;
        }

        public override void Clear()
        {
            UserData = null;
            LoadFlagKey = string.Empty;
        }
    }
}


